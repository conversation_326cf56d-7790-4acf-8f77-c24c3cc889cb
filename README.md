# 基础框架
## 入门指引
熟读文档

[Swoole](https://wiki.swoole.com/#/)

[Query Builder](https://laravel.com/docs/5.3/queries)

[Collection](https://learnku.com/docs/laravel/5.5/collections/1317)

## 目录结构
```
www  WEB部署目录（或者子目录）
├─app               应用目录
│  ├─Constant             常量目录
│  ├─Controller           控制器目录
│  ├─Exception            异常定义目录
│  ├─Model                模型目录
│  ├─Param                对象参数目录
│  ├─Response             接口响应目录
│  ├─Server               Swoole配置目录
│  ├─Logic                逻辑业务目录
│  ├─Service              公共服务目录
│  ├─Task                 Swoole task目录
│  ├─Tick                 定时器任务目录
│  ├─Utils                工具类目录
│  ├─Container.php        全局容器文件
│  ├─Input.php            输入参数文件
│  ├─MysqlConnection.php  Mysql连接对象文件
│  ├─RedisCache.php       Redis缓存文件
│  └─Session.php          Session文件
│
├─common            公共配置目录
│  ├─EnvConfig.php        App配置(可热更)
│  ├─config.php           Server配置(不可热更)
│  ├─functions.php        公共函数文件(不建议使用，建议utils.helper)
│  └─init.php             初始化配置文件
│
├─logs              日志目录
│
├─public            WEB目录（对外访问目录）
│  ├─index.php          
│  └─server.php         入口文件
|
├─srv               系统运行产生的文件(请勿清除)
├─tmp               临时目录(不重要,workStart时清除)
├─vendor            Composer类库目录
├─composer.json     composer 定义文件
├─README.md         README 文件
└─...                
```
## Gitlab提交规范
A 新增信息 (示例 A 新增了一个订单需求)

U 更新信息 (示例 U 修改了一个订单无法提交的bug)

D 删除信息 (示例 D 删除订单无效的信息)
## 编码风格 
遵循psr规范（除了变量名一律使用下划线命名）
## Model方法命名准则
1. 获取单条数据getData() : object
2. 获取多条数据getList() : [list: collection, total: count()]
3. 获取全部数据getAll() : Collection
4. 新增使用add
5. 编辑使用edit
6. 更新使用update
7. 软删除使用remove
8. 真删除使用delete
9. 如果有条件则附上get...ByAccount(), add...InPermission(), delete...NotInPermission() 视情况而定。
## sql
连接测试服务器的mysql即可
## nginx配置（参考）

```
server {
    listen       80;
    server_name  dms.zx.com;
    # 需要搭配前端项目运行
    root  /data/www/dms-web.zeda.cn/dist;
    access_log /data/nginx_logs/dms.zx.com.log main;
    index index.html;
    location = /index.html {
    }

    location = /favicon.ico {
    }

    location = / {
    }

    location ^~ /static/ {
    }

    location / {
        proxy_redirect      off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;#保留代理之前的host
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Real-IP $remote_addr;#保留代理之前的真实客户端ip
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header HTTP_X_FORWARDED_FOR $remote_addr;#在多级代理的情况下，记录每次代理之前的客户端真实ip
        proxy_redirect default;#指定修改被代理服务器返回的响应头中的location头域跟refresh头域数值
        proxy_connect_timeout 60s;
        proxy_read_timeout 1d;
        proxy_send_timeout 60s;
        add_header location 3;
        proxy_pass http://127.0.0.1:9501;
    }

}
```
## 后台配置
```
cd ./common
#需要添加一份Server配置
cp config.example.php config.php
#需要添加一份App配置
cp EnvConfig.example EnvConfig.php
#修改里面的数据库配置
```
## vue启动

```
cd ./web
#如果是编译
npm run build
#如果是开发
#需要添加一份本地配置
cp .env.development .env.development.local
vim .env.development.local
VUE_APP_BASE_API = 这是你基础api
VUE_APP_PROXY_TARGET = 匹配到基础api需要转发到后台的url(如'http://www.tempalte.com'）
npm run dev
```