{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4cef4424e71aa2594a093d589a6a4659", "packages": [{"name": "adbario/php-dot-notation", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "time": "2019-01-01T23:59:15+00:00"}, {"name": "alchemy/binary-driver", "version": "v5.2.0", "source": {"type": "git", "url": "https://github.com/alchemy-fr/BinaryDriver.git", "reference": "e0615cdff315e6b4b05ada67906df6262a020d22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alchemy-fr/BinaryDriver/zipball/e0615cdff315e6b4b05ada67906df6262a020d22", "reference": "e0615cdff315e6b4b05ada67906df6262a020d22", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"evenement/evenement": "^3.0|^2.0|^1.0", "php": ">=5.5", "psr/log": "^1.0", "symfony/process": "^2.3|^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0"}, "type": "library", "autoload": {"psr-0": {"Alchemy": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.lickmychip.com/"}, {"name": "Phraseanet Team", "email": "<EMAIL>", "homepage": "http://www.phraseanet.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jens-hausdorf.de", "role": "Maintainer"}], "description": "A set of tools to build binary drivers", "keywords": ["binary", "driver"], "time": "2020-02-12T19:35:11+00:00"}, {"name": "alibabacloud/cdn-20180510", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/cdn-20180510.git", "reference": "9e327cca24a50558d778eed89ac37f540cf3a421"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/cdn-20180510/zipball/9e327cca24a50558d778eed89ac37f540cf3a421", "reference": "9e327cca24a50558d778eed89ac37f540cf3a421", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/darabonba-openapi": "^0.1.0", "alibabacloud/endpoint-util": "^0.1.0", "alibabacloud/openapi-util": "^0.1.1", "alibabacloud/tea-utils": "^0.2.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\SDK\\Cdn\\*********\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Alibaba Cloud CDN (20180510) SDK Library for PHP", "time": "2020-12-30T03:13:51+00:00"}, {"name": "alibabacloud/client", "version": "1.5.29", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "2d0137828ef5c44664dcb8cc90eac4f545dd3301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/2d0137828ef5c44664dcb8cc90eac4f545dd3301", "reference": "2d0137828ef5c44664dcb8cc90eac4f545dd3301", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "clagiordano/weblibs-configmanager": "^1.0", "danielstjules/stringy": "^3.1", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^5.7.27", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Client\\": "src"}, "files": ["src/Functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "time": "2020-08-03T06:19:07+00:00"}, {"name": "alibabacloud/credentials", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/aliyun/credentials-php.git", "reference": "8c30db1ddf07318b9ee79aa7af46e0bd6be697da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/credentials-php/zipball/8c30db1ddf07318b9ee79aa7af46e0bd6be697da", "reference": "8c30db1ddf07318b9ee79aa7af46e0bd6be697da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "alibabacloud/tea": "^3.0", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Credentials\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Credentials for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "credentials", "library", "sdk", "tool"], "time": "2020-12-24T10:20:12+00:00"}, {"name": "alibabacloud/darabonba-openapi", "version": "0.1.8", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/darabonba-openapi.git", "reference": "c577dea4415b6812d52d9e970a517932eed4a997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/darabonba-openapi/zipball/c577dea4415b6812d52d9e970a517932eed4a997", "reference": "c577dea4415b6812d52d9e970a517932eed4a997", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/credentials": "^1.1", "alibabacloud/openapi-util": "^0.1.7", "alibabacloud/tea-utils": "^0.2.0", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"Darabonba\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi Client", "time": "2021-02-20T10:03:26+00:00"}, {"name": "alibabacloud/endpoint-util", "version": "0.1.1", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/endpoint-util.git", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/endpoint-util/zipball/f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "reference": "f3fe88a25d8df4faa3b0ae14ff202a9cc094e6c5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Endpoint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Endpoint Library for PHP", "time": "2020-06-04T10:57:15+00:00"}, {"name": "alibabacloud/openapi-util", "version": "0.1.7", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/openapi-util.git", "reference": "a8b795b61049ad3aac27434b19d637e128d596c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/openapi-util/zipball/a8b795b61049ad3aac27434b19d637e128d596c4", "reference": "a8b795b61049ad3aac27434b19d637e128d596c4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-utils": "^0.2", "lizhichao/one-sm": "^1.5", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\OpenApiUtil\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud OpenApi <PERSON>", "time": "2021-02-10T02:26:21+00:00"}, {"name": "alibabacloud/tea", "version": "3.1.21", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "379faffe240ee97134cf3f796cb28059f9fb7fa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/379faffe240ee97134cf3f796cb28059f9fb7fa9", "reference": "379faffe240ee97134cf3f796cb28059f9fb7fa9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"adbario/php-dot-notation": "^2.2", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "time": "2021-03-15T03:31:41+00:00"}, {"name": "alibabacloud/tea-utils", "version": "0.2.14", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-utils.git", "reference": "381df15cb4bdb58dbf596f94869ffd2ef680eddd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-utils/zipball/381df15cb4bdb58dbf596f94869ffd2ef680eddd", "reference": "381df15cb4bdb58dbf596f94869ffd2ef680eddd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alibabacloud/tea": "^3.1", "php": ">5.5"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\Utils\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea Utils for PHP", "time": "2021-02-02T10:10:58+00:00"}, {"name": "aliyuncs/oss-sdk-php", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "053d7ba9e798e4c09b9c5c1edab153d25ea9643a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/053d7ba9e798e4c09b9c5c1edab153d25ea9643a", "reference": "053d7ba9e798e4c09b9c5c1edab153d25ea9643a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "~1.0"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "time": "2019-11-15T11:05:42+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/ecf584f5b3a27929175ff0abdba52f0131bef795", "reference": "ecf584f5b3a27929175ff0abdba52f0131bef795", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "time": "2020-07-20T20:39:25+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.8", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "8a7ecad675253e4654ea05505233285377405215"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/8a7ecad675253e4654ea05505233285377405215", "reference": "8a7ecad675253e4654ea05505233285377405215", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2020-08-23T12:54:47+00:00"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2017-06-12T01:10:27+00:00"}, {"name": "doctrine/cache", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "13e3381b25847283a91948d04640543941309727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/13e3381b25847283a91948d04640543941309727", "reference": "13e3381b25847283a91948d04640543941309727", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "time": "2020-07-07T18:54:01+00:00"}, {"name": "doctrine/inflector", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "reference": "ec3a55242203ffa6a4b27c58176da97ff0a7aec1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2019-10-30T19:59:35+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/1890f9d7fde076b5a3ddcf579a802af05b2e781b", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.0"}, "time": "2022-02-03T13:40:04+00:00"}, {"name": "evenement/evenement", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "531bfb9d15f8aa57454f5f0285b18bec903b8fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/531bfb9d15f8aa57454f5f0285b18bec903b8fb7", "reference": "531bfb9d15f8aa57454f5f0285b18bec903b8fb7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "autoload": {"psr-0": {"Evenement": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "time": "2017-07-23T21:35:13+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "0b78f89d8e0bb9e380046c31adfa40347e9f663b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/0b78f89d8e0bb9e380046c31adfa40347e9f663b", "reference": "0b78f89d8e0bb9e380046c31adfa40347e9f663b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "time": "2020-02-14T23:51:21+00:00"}, {"name": "firebase/php-jwt", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d2113d9b2e0e349796e72d2a63cf9319100382d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d2113d9b2e0e349796e72d2a63cf9319100382d2", "reference": "d2113d9b2e0e349796e72d2a63cf9319100382d2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "time": "2021-06-23T19:00:23+00:00"}, {"name": "fukuball/jieba-php", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/fukuball/jieba-php.git", "reference": "a485a16e5424b69526f18d4a7d90009692f2284c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fukuball/jieba-php/zipball/a485a16e5424b69526f18d4a7d90009692f2284c", "reference": "a485a16e5424b69526f18d4a7d90009692f2284c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">= 5.3"}, "require-dev": {"php-coveralls/php-coveralls": "dev-master", "phpunit/phpunit": "~9.0", "squizlabs/php_codesniffer": "4.0.x-dev"}, "default-branch": true, "type": "library", "autoload": {"files": ["src/vendor/multi-array/MultiArray.php", "src/vendor/multi-array/Factory/MultiArrayFactory.php"], "psr-4": {"Fukuball\\Jieba\\": "src/class/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "fukuball", "email": "<EMAIL>"}], "description": "結巴中文分詞（PHP 版本）：做最好的 PHP 中文分詞、中文斷詞組件", "keywords": ["<PERSON><PERSON><PERSON>", "php"], "support": {"issues": "https://github.com/fukuball/jieba-php/issues", "source": "https://github.com/fukuball/jieba-php/tree/master"}, "time": "2022-08-15T06:50:32+00:00"}, {"name": "google/protobuf", "version": "v3.13.0", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "fddc6c2439b190284f207143f6d37bf5b651cea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/fddc6c2439b190284f207143f6d37bf5b651cea6", "reference": "fddc6c2439b190284f207143f6d37bf5b651cea6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": ">=4.8.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "time": "2020-08-15T00:44:58+00:00"}, {"name": "gregwar/captcha", "version": "v1.1.8", "source": {"type": "git", "url": "https://github.com/Gregwar/Captcha.git", "reference": "6088ad3db59bc226423ad1476a9f0424b19b1866"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Gregwar/Captcha/zipball/6088ad3db59bc226423ad1476a9f0424b19b1866", "reference": "6088ad3db59bc226423ad1476a9f0424b19b1866", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "ext-mbstring": "*", "php": ">=5.3.0", "symfony/finder": "*"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "<PERSON><PERSON>a", "autoload": {"psr-4": {"Gregwar\\": "src/<PERSON><PERSON>"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.gregwar.com/"}, {"name": "<PERSON>", "email": "jeremy.j.living<PERSON>@gmail.com"}], "description": "Captcha generator", "homepage": "https://github.com/Gregwar/Captcha", "keywords": ["bot", "<PERSON><PERSON>a", "spam"], "time": "2020-01-22T14:54:02+00:00"}, {"name": "guzzlehttp/command", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/command.git", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/command/zipball/2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "reference": "2aaa2521a8f8269d6f5dfc13fe2af12c76921034", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.2", "guzzlehttp/promises": "~1.3", "guzzlehttp/psr7": "~1.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}], "description": "Provides the foundation for building command-based web service clients", "support": {"issues": "https://github.com/guzzle/command/issues", "source": "https://github.com/guzzle/command/tree/1.0.0"}, "time": "2016-11-24T13:34:15+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/guzzle-services", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle-services.git", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle-services/zipball/9e3abf20161cbf662d616cbb995f2811771759f7", "reference": "9e3abf20161cbf662d616cbb995f2811771759f7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/command": "~1.0", "guzzlehttp/guzzle": "^6.2", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/konafets"}], "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "support": {"issues": "https://github.com/guzzle/guzzle-services/issues", "source": "https://github.com/guzzle/guzzle-services/tree/1.1.3"}, "time": "2017-10-06T14:32:02+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "illuminate/container", "version": "v5.8.36", "source": {"type": "git", "url": "https://github.com/illuminate/container.git", "reference": "b42e5ef939144b77f78130918da0ce2d9ee16574"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/container/zipball/b42e5ef939144b77f78130918da0ce2d9ee16574", "reference": "b42e5ef939144b77f78130918da0ce2d9ee16574", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "5.8.*", "illuminate/support": "5.8.*", "php": "^7.1.3", "psr/container": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.8-dev"}}, "autoload": {"psr-4": {"Illuminate\\Container\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Container package.", "homepage": "https://laravel.com", "time": "2019-08-20T02:00:23+00:00"}, {"name": "illuminate/contracts", "version": "v5.8.36", "source": {"type": "git", "url": "https://github.com/illuminate/contracts.git", "reference": "00fc6afee788fa07c311b0650ad276585f8aef96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/00fc6afee788fa07c311b0650ad276585f8aef96", "reference": "00fc6afee788fa07c311b0650ad276585f8aef96", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1.3", "psr/container": "^1.0", "psr/simple-cache": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.8-dev"}}, "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "time": "2019-07-30T13:57:21+00:00"}, {"name": "illuminate/database", "version": "v5.8.36", "source": {"type": "git", "url": "https://github.com/illuminate/database.git", "reference": "ac9ae2d82b8a6137400f17b3eea258be3518daa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/database/zipball/ac9ae2d82b8a6137400f17b3eea258be3518daa9", "reference": "ac9ae2d82b8a6137400f17b3eea258be3518daa9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/container": "5.8.*", "illuminate/contracts": "5.8.*", "illuminate/support": "5.8.*", "php": "^7.1.3"}, "suggest": {"doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "fzaninotto/faker": "Required to use the eloquent factory builder (^1.4).", "illuminate/console": "Required to use the database commands (5.8.*).", "illuminate/events": "Required to use the observers with Eloquent (5.8.*).", "illuminate/filesystem": "Required to use the migrations (5.8.*).", "illuminate/pagination": "Required to paginate the result set (5.8.*)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.8-dev"}}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Database package.", "homepage": "https://laravel.com", "keywords": ["database", "laravel", "orm", "sql"], "time": "2019-10-03T16:22:57+00:00"}, {"name": "illuminate/support", "version": "v5.8.36", "source": {"type": "git", "url": "https://github.com/illuminate/support.git", "reference": "df4af6a32908f1d89d74348624b57e3233eea247"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/support/zipball/df4af6a32908f1d89d74348624b57e3233eea247", "reference": "df4af6a32908f1d89d74348624b57e3233eea247", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "^1.1", "ext-json": "*", "ext-mbstring": "*", "illuminate/contracts": "5.8.*", "nesbot/carbon": "^1.26.3 || ^2.0", "php": "^7.1.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "suggest": {"illuminate/filesystem": "Required to use the composer class (5.8.*).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "ramsey/uuid": "Required to use Str::uuid() (^3.7).", "symfony/process": "Required to use the composer class (^4.2).", "symfony/var-dumper": "Required to use the dd function (^4.2).", "vlucas/phpdotenv": "Required to use the env helper (^3.3)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.8-dev"}}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Support package.", "homepage": "https://laravel.com", "time": "2019-12-12T14:16:47+00:00"}, {"name": "lizhichao/one-sm", "version": "1.9", "source": {"type": "git", "url": "https://github.com/lizhichao/sm.git", "reference": "2e4c57af85ffa763cd0896cc58b5596d2d283c46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lizhichao/sm/zipball/2e4c57af85ffa763cd0896cc58b5596d2d283c46", "reference": "2e4c57af85ffa763cd0896cc58b5596d2d283c46", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "type": "library", "autoload": {"psr-4": {"OneSm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "国密sm3", "keywords": ["php", "sm3"], "time": "2021-02-04T03:28:45+00:00"}, {"name": "markbaker/complex", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/c3131244e29c08d44fefb49e0dd35021e9e39dd2", "reference": "c3131244e29c08d44fefb49e0dd35021e9e39dd2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0|^5.0|^6.0|^7.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.0|^6.0|^7.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.4.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-08-26T19:47:57+00:00"}, {"name": "markbaker/matrix", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "182d44c3b2e3b063468f7481ae3ef71c69dc1409"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/182d44c3b2e3b063468f7481ae3ef71c69dc1409", "reference": "182d44c3b2e3b063468f7481ae3ef71c69dc1409", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "dev-master", "phploc/phploc": "^4", "phpmd/phpmd": "dev-master", "phpunit/phpunit": "^5.7|^6.0|7.0", "sebastian/phpcpd": "^3.0", "squizlabs/php_codesniffer": "^3.0@dev"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2020-08-28T19:41:55+00:00"}, {"name": "monolog/monolog", "version": "1.25.5", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1817faadd1846cd08be9a49e905dc68823bc38c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1817faadd1846cd08be9a49e905dc68823bc38c0", "reference": "1817faadd1846cd08be9a49e905dc68823bc38c0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-07-23T08:35:51+00:00"}, {"name": "mormat/php-formula-interpreter", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/mormat/php-formula-interpreter.git", "reference": "b8cf925e6543d99e9f5f1817746c1223a4035df6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mormat/php-formula-interpreter/zipball/b8cf925e6543d99e9f5f1817746c1223a4035df6", "reference": "b8cf925e6543d99e9f5f1817746c1223a4035df6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"symfony/class-loader": "2.1.*"}, "type": "library", "autoload": {"psr-0": {"FormulaInterpreter": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "mormat", "email": "<EMAIL>"}], "description": "Formula interpreter for PHP", "keywords": ["php"], "time": "2015-10-08T10:51:31+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.5", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e175b05e3e00977b85feb96a8cccb174ac63621f", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-11-18T15:30:42+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/3206e6b80b6d2479e148ee497e9f2bebadc919db", "reference": "3206e6b80b6d2479e148ee497e9f2bebadc919db", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"psr/http-message": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/1.0.0"}, "time": "2023-09-01T05:59:47+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/7a077416e8f39eb626dee4246e0af99dd9ace275", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"psr/log": "^1.0 || ^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v2.0.0"}, "time": "2023-05-03T06:18:28+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/42dae2cbd13154083ca6d70099692fef8ca84bfb", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2020-07-31T21:01:56+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "024473a478be9df5fdaca2c793f2232fe788e414"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/024473a478be9df5fdaca2c793f2232fe788e414", "reference": "024473a478be9df5fdaca2c793f2232fe788e414", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-02-12T12:17:51+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/b942d263c641ddb5190929ff840c68f78713e937", "reference": "b942d263c641ddb5190929ff840c68f78713e937", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2021-07-05T08:18:36+00:00"}, {"name": "nesbot/carbon", "version": "2.39.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "0a41ea7f7fedacf307b7a339800e10356a042918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/0a41ea7f7fedacf307b7a339800e10356a042918", "reference": "0a41ea7f7fedacf307b7a339800e10356a042918", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.35", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-3.x": "3.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2020-08-24T12:35:58+00:00"}, {"name": "neutron/temporary-filesystem", "version": "2.4", "source": {"type": "git", "url": "https://github.com/romainneutron/Temporary-Filesystem.git", "reference": "3c55497da8d7762fb4dcabc91d54a5de510e3c99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/romainneutron/Temporary-Filesystem/zipball/3c55497da8d7762fb4dcabc91d54a5de510e3c99", "reference": "3c55497da8d7762fb4dcabc91d54a5de510e3c99", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6 || ^7.0", "symfony/filesystem": "^2.3 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0.4"}, "type": "library", "autoload": {"psr-0": {"Neutron": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony filesystem extension to handle temporary files", "time": "2020-02-17T15:27:36+00:00"}, {"name": "nxp/math-executor", "version": "V2.2.0", "source": {"type": "git", "url": "https://github.com/neonxp/MathExecutor.git", "reference": "c396a882ffa5f7467947a6a19c2435a7b4cbad22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/neonxp/MathExecutor/zipball/c396a882ffa5f7467947a6a19c2435a7b4cbad22", "reference": "c396a882ffa5f7467947a6a19c2435a7b4cbad22", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.8", "phpunit/phpunit": ">=9.0"}, "type": "library", "autoload": {"psr-4": {"NXP\\": "src/NXP"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> 'NeonXP' <PERSON><PERSON><PERSON>", "email": "a.k<PERSON><PERSON><PERSON><PERSON>@mail.ru"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Simple math expressions calculator", "homepage": "http://github.com/NeonXP/MathExecutor", "keywords": ["calculator", "expression", "formula", "math", "mathmatics", "parser"], "support": {"issues": "https://github.com/neonxp/MathExecutor/issues", "source": "https://github.com/neonxp/MathExecutor/tree/V2.2.0"}, "time": "2022-04-26T20:14:59+00:00"}, {"name": "overtrue/pinyin", "version": "4.0.7", "source": {"type": "git", "url": "https://github.com/overtrue/pinyin.git", "reference": "083de406907d42df2e0feaaeb83f1a3c2e0095eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/pinyin/zipball/083de406907d42df2e0feaaeb83f1a3c2e0095eb", "reference": "083de406907d42df2e0feaaeb83f1a3c2e0095eb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.7", "friendsofphp/php-cs-fixer": "^2.16", "phpunit/phpunit": "~8.0"}, "type": "library", "extra": {"hooks": {"pre-commit": ["composer test", "composer fix-style"], "pre-push": ["composer test", "composer check-style"]}}, "autoload": {"psr-4": {"Overtrue\\Pinyin\\": "src/"}, "files": ["src/const.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>", "homepage": "http://github.com/overtrue"}], "description": "Chinese to pinyin translator.", "homepage": "https://github.com/overtrue/pinyin", "keywords": ["Chinese", "<PERSON><PERSON><PERSON>", "cn2pinyin"], "time": "2021-04-16T11:13:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "pclzip/pclzip", "version": "2.8.2", "source": {"type": "git", "url": "https://github.com/ivanlanin/pclzip.git", "reference": "19dd1de9d3f5fc4d7d70175b4c344dee329f45fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ivanlanin/pclzip/zipball/19dd1de9d3f5fc4d7d70175b4c344dee329f45fd", "reference": "19dd1de9d3f5fc4d7d70175b4c344dee329f45fd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "library", "autoload": {"classmap": ["pclzip.lib.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>"}], "description": "A PHP library that offers compression and extraction functions for Zip formatted archives", "homepage": "http://www.phpconcept.net/pclzip", "keywords": ["php", "zip"], "time": "2014-06-05T11:42:24+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v2.11.3", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "6353c5d2d3021a301914bc6566e695c99cfeb742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/6353c5d2d3021a301914bc6566e695c99cfeb742", "reference": "6353c5d2d3021a301914bc6566e695c99cfeb742", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": ">=5.6.3", "phpseclib/phpseclib": "^2.0.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^5.7|^6.5|^7.0", "squizlabs/php_codesniffer": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "time": "2020-05-13T13:56:11+00:00"}, {"name": "php-ffmpeg/php-ffmpeg", "version": "v0.15", "source": {"type": "git", "url": "https://github.com/PHP-FFMpeg/PHP-FFMpeg.git", "reference": "984dbd046b6d8c285f9e7419fc7645f197513bfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-FFMpeg/PHP-FFMpeg/zipball/984dbd046b6d8c285f9e7419fc7645f197513bfa", "reference": "984dbd046b6d8c285f9e7419fc7645f197513bfa", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"alchemy/binary-driver": "^1.5 || ~2.0.0 || ^5.0", "doctrine/cache": "^1.0", "evenement/evenement": "^3.0 || ^2.0 || ^1.0", "neutron/temporary-filesystem": "^2.1.1", "php": "^5.3.9 || ^7.0"}, "require-dev": {"sami/sami": "~1.0", "silex/silex": "~1.0", "symfony/phpunit-bridge": "^5.0.4"}, "suggest": {"php-ffmpeg/extras": "A compilation of common audio & video drivers for PHP-FFMpeg"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.7-dev"}}, "autoload": {"psr-0": {"FFMpeg": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.lickmychip.com/"}, {"name": "Phraseanet Team", "email": "<EMAIL>", "homepage": "http://www.phraseanet.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.karisch.guru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.strime.io/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jens-hausdorf.de"}], "description": "FFMpeg PHP, an Object Oriented library to communicate with AVconv / ffmpeg", "keywords": ["audio", "audio processing", "avconv", "avprobe", "ffmpeg", "ffprobe", "video", "video processing"], "time": "2020-03-23T09:32:09+00:00"}, {"name": "php-webdriver/webdriver", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/php-webdriver/php-webdriver.git", "reference": "fb0fc4cb01c70a7790a5fcc91d461b88c83174a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-webdriver/php-webdriver/zipball/fb0fc4cb01c70a7790a5fcc91d461b88c83174a2", "reference": "fb0fc4cb01c70a7790a5fcc91d461b88c83174a2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-curl": "*", "ext-json": "*", "ext-zip": "*", "php": "^5.6 || ~7.0", "symfony/polyfill-mbstring": "^1.12", "symfony/process": "^2.8 || ^3.1 || ^4.0 || ^5.0"}, "replace": {"facebook/webdriver": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "ondram/ci-detector": "^2.1 || ^3.5", "php-coveralls/php-coveralls": "^2.0", "php-mock/php-mock-phpunit": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^5.7", "sebastian/environment": "^1.3.4 || ^2.0 || ^3.0", "sminnee/phpunit-mock-objects": "^3.4", "squizlabs/php_codesniffer": "^3.5", "symfony/var-dumper": "^3.3 || ^4.0 || ^5.0"}, "suggest": {"ext-SimpleXML": "For Firefox profile creation"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.8.x-dev"}}, "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}, "files": ["lib/Exception/TimeoutException.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP client for Selenium WebDriver. Previously facebook/webdriver.", "homepage": "https://github.com/php-webdriver/php-webdriver", "keywords": ["Chromedriver", "geckodriver", "php", "selenium", "webdriver"], "time": "2020-10-06T19:10:04+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.1.7", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "2c2370ba3df7034f9eb7b8f387c97b52b2ba5ad0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/2c2370ba3df7034f9eb7b8f387c97b52b2ba5ad0", "reference": "2c2370ba3df7034f9eb7b8f387c97b52b2ba5ad0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-filter": "*", "php": ">=5.5.0"}, "require-dev": {"doctrine/annotations": "^1.2", "friendsofphp/php-cs-fixer": "^2.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2020-07-14T18:50:27+00:00"}, {"name": "phpoffice/common", "version": "0.2.9", "source": {"type": "git", "url": "https://github.com/PHPOffice/Common.git", "reference": "edb5d32b1e3400a35a5c91e2539ed6f6ce925e4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Common/zipball/edb5d32b1e3400a35a5c91e2539ed6f6ce925e4d", "reference": "edb5d32b1e3400a35a5c91e2539ed6f6ce925e4d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"pclzip/pclzip": "^2.8", "php": ">=5.3.0"}, "require-dev": {"phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.36 || ^7.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\Common\\": "src/Common/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}], "description": "PHPOffice Common", "homepage": "http://phpoffice.github.io", "keywords": ["common", "component", "office", "php"], "time": "2018-07-13T14:12:34+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "reference": "f79611d6dc1f6b7e8e30b738fc371b392001dbfd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.2", "php": "^7.1", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.3", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-04-27T08:12:48+00:00"}, {"name": "phpoffice/phpword", "version": "0.17.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "b8346af548d399acd9e30fc76ab0c55c2fec03a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/b8346af548d399acd9e30fc76ab0c55c2fec03a5", "reference": "b8346af548d399acd9e30fc76ab0c55c2fec03a5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-xml": "*", "php": "^5.3.3 || ^7.0", "phpoffice/common": "^0.2.9", "zendframework/zend-escaper": "^2.2"}, "require-dev": {"dompdf/dompdf": "0.8.*", "ext-gd": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^2.2", "mpdf/mpdf": "5.7.4 || 6.* || 7.*", "php-coveralls/php-coveralls": "1.1.0 || ^2.0", "phploc/phploc": "2.* || 3.* || 4.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.36 || ^7.0", "squizlabs/php_codesniffer": "^2.9", "tecnickcom/tcpdf": "6.*"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.18-dev"}}, "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "http://phpoffice.github.io", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "time": "2019-10-01T20:43:33+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.28", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/d1ca58cf33cb21046d702ae3a7b14fdacd9f3260", "reference": "d1ca58cf33cb21046d702ae3a7b14fdacd9f3260", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0", "sami/sami": "~2.0", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "time": "2020-07-08T09:08:33+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "qcloud/cos-sdk-v5", "version": "v2.1.6", "source": {"type": "git", "url": "https://github.com/tencentyun/cos-php-sdk-v5.git", "reference": "376f5e727a92210789f12b9541ec8d2d8227f633"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentyun/cos-php-sdk-v5/zipball/376f5e727a92210789f12b9541ec8d2d8227f633", "reference": "376f5e727a92210789f12b9541ec8d2d8227f633", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "~6.3", "guzzlehttp/guzzle-services": "~1.1", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Qcloud\\Cos\\": "src/Qcloud/Cos/"}, "files": ["src/Qcloud/Cos/Common.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud COS", "keywords": ["cos", "php", "qcloud"], "support": {"issues": "https://github.com/tencentyun/cos-php-sdk-v5/issues", "source": "https://github.com/tencentyun/cos-php-sdk-v5/tree/v2.1.6"}, "time": "2021-05-11T09:59:16+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "f3cff96a19736714524ca0dd1d4130de73dbbbc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/f3cff96a19736714524ca0dd1d4130de73dbbbc4", "reference": "f3cff96a19736714524ca0dd1d4130de73dbbbc4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^6.5 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "time": "2020-05-12T15:16:56+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.2", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "9e013b376939c0d4029f54150d2a16f3c67a5797"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/9e013b376939c0d4029f54150d2a16f3c67a5797", "reference": "9e013b376939c0d4029f54150d2a16f3c67a5797", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2024-12-10T13:12:19+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.11", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b27f491309db5757816db672b256ea2e03677d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b27f491309db5757816db672b256ea2e03677d30", "reference": "b27f491309db5757816db672b256ea2e03677d30", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2020-05-30T18:50:54+00:00"}, {"name": "symfony/finder", "version": "v4.4.11", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "2727aa35fddfada1dd37599948528e9b152eb742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/2727aa35fddfada1dd37599948528e9b152eb742", "reference": "2727aa35fddfada1dd37599948528e9b152eb742", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-07-05T09:39:30+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/1c302646f6efc070cd46856e600e5e0684d6b454", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "5dcab1bc7146cf8c1beaa4502a3d9be344334251"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/5dcab1bc7146cf8c1beaa4502a3d9be344334251", "reference": "5dcab1bc7146cf8c1beaa4502a3d9be344334251", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php70": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-08-04T06:02:08+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "a6977d63bf9a0ad4c65cd352709e230876f9904a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/a6977d63bf9a0ad4c65cd352709e230876f9904a", "reference": "a6977d63bf9a0ad4c65cd352709e230876f9904a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "0dd93f2c578bdc9c72697eaa5f1dd25644e618d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/0dd93f2c578bdc9c72697eaa5f1dd25644e618d3", "reference": "0dd93f2c578bdc9c72697eaa5f1dd25644e618d3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "639447d008615574653fb3bc60d1986d7172eaae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/639447d008615574653fb3bc60d1986d7172eaae", "reference": "639447d008615574653fb3bc60d1986d7172eaae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/process", "version": "v4.4.11", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "65e70bab62f3da7089a8d4591fb23fbacacb3479"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/65e70bab62f3da7089a8d4591fb23fbacacb3479", "reference": "65e70bab62f3da7089a8d4591fb23fbacacb3479", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-07-23T08:31:43+00:00"}, {"name": "symfony/translation", "version": "v4.4.11", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "a8ea9d97353294eb6783f2894ef8cee99a045822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/a8ea9d97353294eb6783f2894ef8cee99a045822", "reference": "a8ea9d97353294eb6783f2894ef8cee99a045822", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-07-23T08:31:43+00:00"}, {"name": "symfony/translation-contracts", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "a5db6f7707fd35d137b1398734f2d745c8616ea2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/a5db6f7707fd35d137b1398734f2d745c8616ea2", "reference": "a5db6f7707fd35d137b1398734f2d745c8616ea2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-07-06T13:19:58+00:00"}, {"name": "tencentcloud/tencentcloud-sdk-php", "version": "3.0.395", "source": {"type": "git", "url": "https://github.com/TencentCloud/tencentcloud-sdk-php.git", "reference": "8e2301a54b9f702f0eb727d8ae474989d0b853da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TencentCloud/tencentcloud-sdk-php/zipball/8e2301a54b9f702f0eb727d8ae474989d0b853da", "reference": "8e2301a54b9f702f0eb727d8ae474989d0b853da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0.1", "guzzlehttp/psr7": "^1.4", "php": ">=5.6.0"}, "type": "library", "autoload": {"classmap": ["src/QcloudApi/QcloudApi.php"], "psr-4": {"TencentCloud\\": "./src/TencentCloud"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "coolli", "email": "<EMAIL>", "homepage": "https://cloud.tencent.com/document/sdk/PHP", "role": "Developer"}], "description": "TencentCloudApi php sdk", "homepage": "https://github.com/TencentCloud/tencentcloud-sdk-php", "time": "2021-05-21T10:38:57+00:00"}, {"name": "the-tinderbox/clickhouse-builder", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/the-tinderbox/ClickhouseBuilder.git", "reference": "1a68a1e8b3597be12c9a230f4b9e9f7296ba5188"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/the-tinderbox/ClickhouseBuilder/zipball/1a68a1e8b3597be12c9a230f4b9e9f7296ba5188", "reference": "1a68a1e8b3597be12c9a230f4b9e9f7296ba5188", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.1|^8.0", "the-tinderbox/clickhouse-php-client": "^3.0"}, "require-dev": {"illuminate/config": "5.*", "illuminate/database": "5.*", "illuminate/events": "5.*", "illuminate/pagination": "5.*", "mockery/mockery": "^0.9.9", "phpunit/phpunit": "^6.1"}, "type": "library", "extra": {"laravel": {"providers": ["Tinderbox\\ClickhouseBuilder\\Integrations\\Laravel\\ClickhouseServiceProvider"]}}, "autoload": {"psr-4": {"Tinderbox\\ClickhouseBuilder\\": "src"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "FacedSID", "email": "<EMAIL>"}, {"name": "Evsign", "email": "<EMAIL>"}], "description": "Clickhouse sql query builder", "time": "2021-09-10T16:43:04+00:00"}, {"name": "the-tinderbox/clickhouse-php-client", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/the-tinderbox/ClickhouseClient.git", "reference": "25c4e918bd30fb181add4c8db46e6d5aba510bf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/the-tinderbox/ClickhouseClient/zipball/25c4e918bd30fb181add4c8db46e6d5aba510bf0", "reference": "25c4e918bd30fb181add4c8db46e6d5aba510bf0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.0|^7.0", "php": "^7.1|^8.0"}, "require-dev": {"mockery/mockery": "^0.9|^1.4", "php-coveralls/php-coveralls": "^2.2", "phpunit/phpcov": "^5.0|^6.0|^7.0|^8.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "bin": ["bin/ccat_linux", "bin/ccat_darwin"], "type": "library", "autoload": {"psr-4": {"Tinderbox\\Clickhouse\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "FacedSID", "email": "<EMAIL>"}], "description": "Clickhouse client over HTTP", "time": "2021-04-23T18:56:33+00:00"}, {"name": "ua-parser/uap-php", "version": "v3.9.7", "source": {"type": "git", "url": "https://github.com/ua-parser/uap-php.git", "reference": "7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ua-parser/uap-php/zipball/7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a", "reference": "7efc2f05b7d9817a59132e5d2e5ca91a1c071f6a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer/ca-bundle": "^1.1", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "<8", "symfony/console": "^2.0 || ^3.0 || ^4.0", "symfony/filesystem": "^2.0 || ^3.0 || ^4.0", "symfony/finder": "^2.0 || ^3.0 || ^4.0", "symfony/yaml": "^2.0 || ^3.0 || ^4.0"}, "suggest": {"symfony/console": "Required for CLI usage - ^2.0 || ^3.0 || ^4.0", "symfony/filesystem": "Required for CLI usage - 2.0 || ^3.0 || ^4.0", "symfony/finder": "Required for CLI usage - ^2.0 || ^3.0 || ^4.0", "symfony/yaml": "Required for CLI usage - ^4.0 || ^5.0"}, "bin": ["bin/uaparser"], "type": "library", "autoload": {"psr-4": {"UAParser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A multi-language port of Browserscope's user agent parser.", "time": "2020-02-21T09:54:14+00:00"}, {"name": "vlucas/valitron", "version": "v1.4.9", "source": {"type": "git", "url": "https://github.com/vlucas/valitron.git", "reference": "81515dcc951e1f636a1a18ece2f4154dfa123438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/valitron/zipball/81515dcc951e1f636a1a18ece2f4154dfa123438", "reference": "81515dcc951e1f636a1a18ece2f4154dfa123438", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.5 || ^6.5"}, "suggest": {"ext-mbstring": "It can support the multiple bytes string length."}, "type": "library", "autoload": {"psr-4": {"Valitron\\": "src/Valitron"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Simple, elegant, stand-alone validation library with NO dependencies", "homepage": "http://github.com/vlucas/valitron", "keywords": ["valid", "validation", "validator"], "time": "2020-12-01T11:14:45+00:00"}, {"name": "zendframework/zend-escaper", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-escaper.git", "reference": "3801caa21b0ca6aca57fa1c42b08d35c395ebd5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-escaper/zipball/3801caa21b0ca6aca57fa1c42b08d35c395ebd5f", "reference": "3801caa21b0ca6aca57fa1c42b08d35c395ebd5f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2", "zendframework/zend-coding-standard": "~1.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev", "dev-develop": "2.7.x-dev"}}, "autoload": {"psr-4": {"Zend\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "keywords": ["ZendFramework", "escaper", "zf"], "abandoned": "laminas/laminas-escaper", "time": "2019-09-05T20:03:20+00:00"}], "packages-dev": [{"name": "swoole/ide-helper", "version": "4.4.25", "source": {"type": "git", "url": "https://github.com/swoole/ide-helper.git", "reference": "447bc776f3467031979675de9ff18cca4e666869"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swoole/ide-helper/zipball/447bc776f3467031979675de9ff18cca4e666869", "reference": "447bc776f3467031979675de9ff18cca4e666869", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require-dev": {"guzzlehttp/guzzle": "~6.5.0", "laminas/laminas-code": "~3.4.0", "squizlabs/php_codesniffer": "~3.5.0", "symfony/filesystem": "~4.0"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Team Swoole", "email": "<EMAIL>"}], "description": "IDE help files for Swoole.", "time": "2021-04-09T15:39:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"fukuball/jieba-php": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-json": "*", "ext-zip": "*", "ext-pdo": "*", "ext-gd": "*", "ext-curl": "*", "ext-bcmath": "*", "ext-pcntl": "*", "ext-redis": "*", "ext-openssl": "*", "ext-zlib": "*", "ext-posix": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}