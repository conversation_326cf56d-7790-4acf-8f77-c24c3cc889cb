<?php

use App\Exception\AppException;


/**
 * 获取请求时间戳
 *
 * @return int
 */
function request_time()
{
    return time();
}

function dd(...$arv)
{
    echo json_encode($arv, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    throw new AppException('The breakpoint');
}

function dd_var_dump(...$arv)
{
    var_dump(...$arv);
    throw new AppException('The breakpoint');
}

function runOperator($left_value, $operator, $right_value): bool
{
    switch ($operator) {
        case '>':
            return $left_value > $right_value;
        case '<':
            return $left_value < $right_value;
        case '>=':
            return $left_value >= $right_value;
        case '<=':
            return $left_value <= $right_value;
        case '=':
            return $left_value == $right_value;
        case '!=':
            return $left_value != $right_value;
        case 'between':
            if (is_array($right_value) && count($right_value) == 2) {
                return ($left_value >= $right_value[0] && $left_value <= $right_value[1]);
            } else {
                throw new AppException('error right_value');
            }
        case 'in':
            if (is_array($right_value)) {
                return in_array($left_value, $right_value);
            } else {
                throw new AppException('error right_value');
            }
        default:
            throw new AppException('error operator');
    }
}

