<?php
// 该文件可以热更
namespace Common;

/**
 * 配置类
 *
 * Class EnvConfig
 * @package Common
 */
class EnvConfig
{

    /**
     * 是否开启debug 关闭后异常会被自动捕获
     */
    const DEBUG = true;

    /**
     * http(s)://your.domain
     */
    const DOMAIN = '';


    /**
     * https证书路径
     */
    const CURL_CERT_FILE_PATH = ROOT_DIR . '/cert/curl/cacert.pem';


    /**
     * URL后缀
     */
    const URL_SUR_FIX = 'html';

    /**
     * AES密钥
     */
    const AES_KEY = 'n3KW@g6%';

    /**
     * 能打印sql的用户id
     */
    const PRINT_SQL_USER_ID = [
        1,   //超管
        17,  //仇俊文
    ];

    /**
     * 上传路径
     */
    const UPLOAD_PATH = '/upload';

    /**
     * 存放身份证目录名
     */
    const IDCARD_DIR_NAME = 'id_card';

    /**
     * 存放头像目录名
     */
    const AVATAR_DIR_NAME = 'avatar';
    /**
     * 存放落地页文件
     */
    const PAGE_FILE_DIR_NAME = 'page';

    /**
     * 存放素材图片目录名
     */
    const MATERIAL_IMG_DIR_NAME = 'adsimg/material';

    /**
     * 存放ICON图片目录名
     */
    const ICON_IMG_DIR_NAME = 'adsimg/icon';

    /**
     * 存放素材视频目录名
     */
    const MATERIAL_VIDEO_DIR_NAME = 'adsvideo/material';

    /**
     * 存放素材Zip目录名
     */
    const MATERIAL_ZIP_DIR_NAME = 'adszip/material';


    /**
     * 存放财务对账目录名
     */
    const FINANCE_DIR_NAME = 'finance';


    /**
     * 存放素材域名
     */
    const MATERIAL_DOMAIN_NAME = 'http://localhost';

    const PLATFORM_MAP = [
            'TW' => '贪玩',
            'XINXIN' =>'新新',
            'WANZI' =>'丸子',
    ];



    /*
    |--------------------------------------------------------------------------
    | 游戏类型配置
    |--------------------------------------------------------------------------
    |
    */
    const PLAT_ID_YY = 1;  // 页游
    const PLAT_ID_SY = 2;  // 手游
    const PLAT_ID_H5 = 3;  // H5

    // 映射
    const PLAT_ID_MAP = [
        self::PLAT_ID_YY => '页游',
        self::PLAT_ID_SY => '手游',
        self::PLAT_ID_H5 => 'H5',
    ];

    /*
   |--------------------------------------------------------------------------
   | 微信
   |--------------------------------------------------------------------------
   |
   */
    const WECHAT = [
    ];

    /*
    |--------------------------------------------------------------------------
    | toutiao配置
    |--------------------------------------------------------------------------
    |
    */
    const TOUTIAO = [
        '9k' => [
            'app_id' => '',
            'secret' =>  '',
        ]
    ];

    const TENCENT = [
        'xinxin' => [
            'app_id' => '',
            'secret' =>  '',
        ]
    ];

    const BAIDU = [
        'xinxin' => [
            'username' => [
                'password' => '',
                'token' => ''
            ]
        ]
    ];

    /*
    |--------------------------------------------------------------------------
    | MySQL配置
    |--------------------------------------------------------------------------
    |
    | 默认使用default配置connection，需要使用其他库的时候，可以动态修改connection。
    |
    */

    const MYSQL = [

        'default' => [
            'driver' => 'mysql',
            'host' => '*************',
            'database' => 'zeda',
            'username' => 'ceshi',
            'password' => 'test123456',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '',
            'options' => [
               \PDO::ATTR_PERSISTENT => true
            ],
        ],


        'datahub' => [
            'driver' => 'mysql',
            'host' => 'am-bp163f2ks5ohal27a90650o.ads.aliyuncs.com',
            'database' => 'tanwan_datahub',
            'username' => 'rw_tanwan_test',
            'password' => 'pWXW5r9Qky2D2g3GM5dUA43jmRWi70',
            'port' => 10011,
            'charset' => 'utf8',
            'options' => [
                // 此项针对阿里云的分析型数据库MySQL版 特别开启
                \PDO::ATTR_EMULATE_PREPARES => true,
                \PDO::ATTR_PERSISTENT => true
            ],
            'prefix' => '',
        ],

        'datahub_ly' => [
            'driver' => 'mysql',
            'host' => 'am-bp163f2ks5ohal27a90650o.ads.aliyuncs.com',
            'database' => 'tanwan_datahub_ly',
            'username' => 'rw_tanwan_test',
            'password' => 'pWXW5r9Qky2D2g3GM5dUA43jmRWi70',
            'port' => 10011,
            'charset' => 'utf8',
            'options' => [
                // 此项针对阿里云的分析型数据库MySQL版 特别开启
                \PDO::ATTR_EMULATE_PREPARES => true,
                \PDO::ATTR_PERSISTENT => true
            ],
            'prefix' => '',
        ],

        'data_media' => [
            'driver' => 'mysql',
            'host' => 'am-bp163f2ks5ohal27a90650o.ads.aliyuncs.com',
            'database' => 'tanwan_datamedia',
            'username' => 'tanwan_datahub',
            'password' => 'D0xPSA7i*y#4CaEZ',
            'port' => 3306,
            'charset' => 'utf8',
            'options' => [
                // 此项针对阿里云的分析型数据库MySQL版 特别开启
                \PDO::ATTR_EMULATE_PREPARES => true,
                \PDO::ATTR_PERSISTENT => true,
            ],
            'prefix' => '',
        ],
       'agency' => [
            'driver' => 'mysql',
            'host' => '*************',
            'database' => 'agency',
            'username' => 'ceshi',
            'password' => 'test123456',
            'port' => 3306,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '',
            'options' => [
                \PDO::ATTR_PERSISTENT => true,
            ],
        ],
    ];


    /*
    |--------------------------------------------------------------------------
    | Redis Database
    |--------------------------------------------------------------------------
    */

    const REDIS = [
        'default' => [
            'host' => '*************',
            'port' => '6379',
            'auth' => '2B9fMHppy1NdEBGQ',
            'database' => '0'
        ]
    ];

    /*
    |--------------------------------------------------------------------------
    | MongoDb Database
    |--------------------------------------------------------------------------
    */

    const MONGODB = [
        'default' => 'mongodb://127.0.0.1:27017/kefu'
    ];


    const IP_WHITE_LIST = [
        '*************'
    ];

}