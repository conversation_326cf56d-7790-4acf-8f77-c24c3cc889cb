<?php
// 注意该文件不能热重启，请修改后手动重启
define('MEMORY_SIZE', '20480M');
/*
|--------------------------------------------------------------------------
| Swoole配置
|--------------------------------------------------------------------------
|
*/
define('SWOOLE', [
    'host' => '0.0.0.0',
    'port' => 9501,
    'daemonize' => false,
    'worker_num' => 48,
    'reactor_num' => 48, // 考虑到操作系统调度存在一定程度的性能损失，可以设置为CPU核数*2，以便最大化利用CPU的每一个核
    'task_worker_num' => 128,
    'max_wait_time' => 0,
    'dispatch_mode' => 2,
    'task_ipc_mode' => 3, // 争抢模式
    'task_max_request' => 10000, // 设置task进程的最大任务数
    'buffer_output_size' => 1024 * 1024 *1024, //必须为数字
    'package_max_length' => 1024 * 1024 *1024, //上传的最大长度
]);