<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\Model\HttpModel\Volcengine\PutObject;
use App\Model\SqlModel\DataMedia\OdsLianShanYunMaterialFileLog;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use Common\EnvConfig;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();
//$list = MysqlConnection::getConnection('data_media')
//    ->query()
//    ->selectRaw('source.id')
//    ->selectRaw('source.platform')
//    ->selectRaw('source.material_id')
//    ->selectRaw('source.signature')
//    ->selectRaw('source.filename')
//    ->selectRaw('source.insert_time')
//    ->fromSub(function (Builder $builder) {
//        $builder->select([
//            'ods_material_file_log.id',
//            'ods_material_file_log.platform',
//            'ods_material_file_log.material_id',
//            'ods_material_file_log.signature',
//            'ods_material_file_log.filename',
//            'ods_material_file_log.insert_time',
//            'ods_material_theme.theme_id',
//            'ods_material_theme.theme_pid',
//        ])->from('ods_material_file_log')
//            ->join('ods_material_log', function (JoinClause $join) {
//                $join->on('ods_material_file_log.platform', '=', 'ods_material_log.platform');
//                $join->on('ods_material_file_log.material_id', '=', 'ods_material_log.material_id');
//            })
//            ->join('ods_material_theme', function (JoinClause $join) {
//                $join->on('ods_material_theme.platform', '=', 'ods_material_log.platform');
//                $join->on('ods_material_theme.theme_id', '=', 'ods_material_log.theme_id');
//            })
//            ->where('ods_material_file_log.file_type', 2)
//            ->where('ods_material_file_log.source_signature', '')
//            ->where('ods_material_file_log.is_ext', '0')
//            ->where('ods_material_file_log.platform', 'TW')
//            ->whereBetween('ods_material_file_log.insert_time', ['2022-01-01', '2023-05-01']);
//    }, 'source')
//    ->whereIn('source.theme_pid', function (Builder $builder) {
//        $builder->select('theme_pid')
//            ->from('dwd_media_ad3_common_hour_data_log')
//            ->leftJoinSub(function (Builder $builder) {
//                $builder->select([
//                    'ods_material_theme.theme_pid',
//                    'ods_material_theme.theme_id',
//                    'ods_material_file_log.signature'
//                ])
//                    ->from('ods_material_file_log')
//                    ->join('ods_material_log', function (JoinClause $join) {
//                        $join->on('ods_material_file_log.platform', '=', 'ods_material_log.platform');
//                        $join->on('ods_material_file_log.material_id', '=', 'ods_material_log.material_id');
//                    })
//                    ->join('ods_material_theme', function (JoinClause $join) {
//                        $join->on('ods_material_theme.platform', '=', 'ods_material_log.platform');
//                        $join->on('ods_material_theme.theme_id', '=', 'ods_material_log.theme_id');
//                    })
//                    ->groupBy('signature');
//            }, 'file', 'dwd_media_ad3_common_hour_data_log.signature', '=', 'file.signature')
//            ->where('dwd_media_ad3_common_hour_data_log.cost', '>', 0)
//            ->where('dwd_media_ad3_common_hour_data_log.cost_date', '>=', '2023-06-01')
//            ->groupBy('theme_pid');
//    })
//    ->whereIn('source.signature', function (Builder $builder) {
//        $builder->select('signature')
//            ->from('dwd_media_ad3_common_hour_data_log')
//            ->where('dwd_media_ad3_common_hour_data_log.cost', '>', 0)
//            ->whereIn('dwd_media_ad3_common_hour_data_log.media_type', [1, 2, 3, 4])
//            ->groupBy('signature')
//            ->havingRaw('SUM( cost ) > 500');
//    })
//    ->whereNotIn('source.id', function (Builder $builder) {
//        $builder->select('file_id')
//            ->from('ods_lianshanyun_material_file_log')
//            ->where('ods_lianshanyun_material_file_log.file_type', '=', 2);
//    })
//    ->get();

$list = MysqlConnection::getConnection('data_media')
    ->query()
    ->select([
        'ods_material_file_log.id',
        'ods_material_file_log.platform',
        'ods_material_file_log.material_id',
        'ods_material_file_log.signature',
        'ods_material_file_log.url',
        'ods_material_file_log.filename',
        'ods_material_file_log.insert_time'
    ])
    ->from('ods_material_file_log')
    ->leftJoin('ods_lianshanyun_material_file_log', function (JoinClause $join) {
        $join->on('ods_material_file_log.signature', '=', 'ods_lianshanyun_material_file_log.signature');
    })
    ->where('ods_material_file_log.is_del', 0)
    ->where('ods_material_file_log.is_ext', 0)
    ->where('ods_material_file_log.file_type', 2)
    ->whereNull('ods_lianshanyun_material_file_log.signature')
    ->where('ods_material_file_log.insert_time', '>=', '2023-05-01 00:00:00')
    ->dd();

var_dump($list);
exit;

$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

foreach ($list as $info) {
    // 上传到连山云
    try {
        if (!file_exists("$upload_path/$info->platform/$info->material_id/$info->filename")) {
            echo "文件id:{$info->id}不存在  跳过" . PHP_EOL;
        } else {
            $crc = (new PutObject())->putObject(
                "$upload_path/$info->platform/$info->material_id/$info->filename",
                $info->filename,
                $info->signature
            );
            (new OdsLianShanYunMaterialFileLog())->add([
                'file_id' => $info->id,
                'platform' => $info->platform,
                'material_id' => $info->material_id,
                'file_type' => MaterialFileModel::FILE_TYPE_VIDEO,
                'signature' => $info->signature,
                'lsy_hash_crc64ecma' => $crc,
                'notify_tos' => 1,
                'bucket_name' => EnvConfig::VOLCENGINE['bucket_name'],
                'object_name' => $info->signature . '.mp4',
                'upload_tos_time' => date('Y-m-d H:i:s')
            ]);
            echo "文件id:{$info->id}上传成功" . PHP_EOL;
        }
    } catch (Throwable $e) {
        echo date('Y-m-d H:i:s') . $e->getMessage();
    }
}

