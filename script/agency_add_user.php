<?php
/**
 * 对外后台添加用户或者添加用户权限
 */


use App\Exception\AppException;
use App\Model\SqlModel\Agency\RankAgentModel;
use App\Model\SqlModel\Agency\RankRoutePermissionModel;
use App\Model\SqlModel\Agency\UserModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\MysqlConnection;
use App\Param\DMS\AgencyUserParam;
use App\Service\OuterService;

require dirname(__DIR__) . '/vendor/autoload.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();


$options = getopt("t:u:p:a:s:");
$type = $options['t'] ?? '';
$user = $options['u'] ?? '';
$agent_list = $options['a'] ?? '';
$s_type = $options['s'] ?? '';
$platform = $options['p'] ?? '';

$helper = <<<EOT
  -t         类型 1添加用户 2添加用户权限
  -u         账号名account
  -a         渠道列表，用逗号分隔
  -s         统计口径，1=按平台 2=按游戏 3=按根游戏
  -p         平台platform
  
  EOT;

if (!$type) {
    echo 'type参数类型错误，退出执行', PHP_EOL;
    echo $helper;
    exit;
}

$user_model = new UserModel();
// 判断添加类型
$service = new OuterService();
if ($type == 1) {
    if (!$user || !$s_type || !$platform || !$agent_list) {
        echo '参数不完整，退出执行', PHP_EOL;
        exit;
    }
    $user_model = new UserModel();
    $user_data = [
        'account' => $user,
        'name' => $user,
        'statistic_caliber' => $s_type,
        'platform' => $platform,
        'creator' => '戴焕其',
        'editor' => '戴焕其',
        'route_list' => '1,2',
        'agent_id' => $agent_list,
        'password' => '',
    ];
    $user_param = new AgencyUserParam($user_data);
    // 判断账号是否存在
    $user_info = $user_model->getDataByAccount($user_param->account);
    if ($user_info) {
        echo '对外账号已存在, 请勿重复添加', PHP_EOL;
        exit;
    }

    try {
        MysqlConnection::getConnection('agency')->beginTransaction();
    } catch (Exception $e) {
        echo '事务开启异常', PHP_EOL;
        exit;
    }

    try {
        $user_id = $user_model->add($user_param);

        $agent_id_list = explode(',', $agent_list);
        $agent_info_list = (new AgentModel())->getDataByPlatformAgentIdList($platform, $agent_id_list);

        if ($agent_info_list->isEmpty()) {
            throw new AppException('渠道id不存在，退出执行');
        }
        if (count($agent_id_list) !== $agent_info_list->count()) {
            throw new AppException('部分渠道id不存在，退出执行');
        }
        $agent_list = [];
        foreach ($agent_info_list as $item) {
            $agent_list[] = [
                'platform' => $platform,
                'agent_group_id' => $item->agent_group_id,
                'agent_id' => $item->agent_id,
            ];
        }
        // 处理渠道权限
        $agent_model = new RankAgentModel();

        $agent_add_result = $agent_model->addMultiple($user_id, $agent_list);
        if (!$agent_add_result) {
            echo '添加失败！失败原因：添加渠道权限失败', PHP_EOL;
            MysqlConnection::getConnection('agency')->rollBack();
        }
        MysqlConnection::getConnection('agency')->commit();
    } catch (\Throwable $e) {
        echo $e->getMessage(), PHP_EOL;
        exit;
    }

    echo '添加用户成功，用户ID:' . $user_id, PHP_EOL;
} else if ($type == 2) {
    if (!$user || !$agent_list || !$platform) {
        echo '参数不完整，退出执行', PHP_EOL;
        exit;
    }
    // 判断账号是否存在
    $user_info = $user_model->getDataByAccount($user);
    if (!$user_info) {
        echo '对外账号不存在', PHP_EOL;
        exit;
    }

    try {
        MysqlConnection::getConnection('agency')->beginTransaction();
    } catch (Exception $e) {
        echo '事务开启异常', PHP_EOL;
        exit;
    }

    try {

        $agent_id_list = explode(',', $agent_list);
        $agent_info_list = (new AgentModel())->getDataByPlatformAgentIdList($platform, $agent_id_list);

        if ($agent_info_list->isEmpty()) {
            throw new AppException('渠道id不存在，退出执行');
        }
        if (count($agent_id_list) !== $agent_info_list->count()) {
            throw new AppException('部分渠道id不存在，退出执行');
        }
        if ($intersect = array_intersect($agent_id_list, explode(',', $user_info['agent_id_list']))) {
            throw new AppException('渠道id: ' . implode(',', $intersect) . '已存在，退出执行');
        }

        $add_agent_list = [];
        foreach ($agent_info_list as $item) {
            $add_agent_list[] = [
                'platform' => $platform,
                'agent_group_id' => $item->agent_group_id,
                'agent_id' => $item->agent_id,
            ];
        }
        // 处理渠道权限
        $agent_model = new RankAgentModel();

        $agent_add_result = $agent_model->addMultiple($user_info['id'], $add_agent_list);
        if (!$agent_add_result) {
            echo '添加失败！失败原因：添加渠道权限失败', PHP_EOL;
            MysqlConnection::getConnection('agency')->rollBack();
        }
        // 更新用户的渠道列表
        $new_agent_id_list = $user_info['agent_id_list'] . ',' . $agent_list;
        $user_model->updateAgentIdList($user_info['id'], $new_agent_id_list);
        MysqlConnection::getConnection('agency')->commit();
    } catch (\Throwable $e) {
        echo $e->getMessage(), PHP_EOL;
        exit;
    }

    echo '渠道添加成功', PHP_EOL;
    echo '用户ID:' . $user_info['id'], PHP_EOL;
    echo '添加的渠道ID:' . $agent_list, PHP_EOL;
} else {
    echo 'type参数类型错误，退出执行', PHP_EOL;
    echo $helper;
    exit;
}
