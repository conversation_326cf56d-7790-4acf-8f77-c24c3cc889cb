<?php
/**
 * 修复标签的问题
 */


use App\Model\HttpModel\Aliyun\DashScope\Embeddings\EmbeddingsModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\RoutePermissionModel;
use App\Model\HttpModel\Aliyun\DashVector\Collections\CollectionsModel;
use App\MysqlConnection;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageSummary;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$service = new GroupAssistantService();

$service->initUserChatInfo();
var_dump('任务完成');
echo PHP_EOL;
exit;
$logger = \App\Utils\Helpers::getLogger('tmp_bot_message');

// 获取群列表数据
$auth_model = new AuthModel();

// 先获取access_token
$tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
    GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
// 获取到群聊天列表
$chat_model = new ChatsModel();

$chat_list = $chat_model->chatList($tenant_access_token);
$logger->info('获取到聊天群列表');
$service = new GroupAssistantService();

$service->initUserChatInfo();
var_dump('任务完成');
echo PHP_EOL;
exit;

foreach ($chat_list as $item) {
    $chat_id = $item['chat_id'];

    if ($chat_id !== 'oc_e4bf54ccfbd78a40150a7b4380f06a39') {
        continue;
    }
    // 先获取access_token
    $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
        GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
    // 拉取历史消息
    // 先去获取聊天记录
    $start_time = strtotime(date("Y-m-d H:i:s", strtotime("-365 day")));
    $end_time = strtotime(date("Y-m-d H:i:s"));
    $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time, 50, 'ByCreateTimeAsc', 40000);

    $logger->info('获取到了消息历史，开始解析聊天记录', ['count' => count($message_list)]);
    $message_model = new FeiShuMessageModel();

    // 解析聊天记录
    foreach ($message_list as $message) {
        try {
            if (in_array($message['msg_type'], ['post', 'file', 'image'])) {
                // 这些类型的消息才需要重新处理
                continue;
            }
            $format_message = (new GroupAssistantService())->formatmessage($message, $tenant_access_token, $chat_id);
            // 过滤掉的消息不需要处理
            if (!$format_message) {
                $logger->info('过滤消息');
                continue;
            }

            // 列表是找不到union_id的，得去转换一下
            $open_id = $message['sender']['id'];
            $union_id = FeiShuService::getUnionIdByOpenId($open_id);
            // 找不到，则去飞书接口找一下,通过消息详情去曲线获取
            if (!$union_id) {
                $message_detail = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token, 'union_id');
                $union_id = $message_detail['data']['items'][0]['sender']['id'];
            }
            $insert_data = [
                'message_id'          => $message['message_id'],
                'chat_id'             => $chat_id,
                'decrypt'             => json_encode($message),
                'format_message'      => json_encode($format_message),
                'msg_type'            => $message['msg_type'],
                'message_create_time' => intval($message['create_time'] / 1000),
                'parent_id'           => $message['parent_id'] ?? '',
                'union_id'            => $union_id,
                'cut_off'             => $format_message['cut_off'],
                'at'                  => $format_message['at'],
            ];
            $message_model->replaceone($insert_data);

            $logger->info('消息入库完成', ['message_id' => $message['message_id']]);
        } catch (\Throwable $throwable) {
            $logger->error($throwable->getMessage(), ['message_id' => $message['message_id'], 's' => $throwable->getTraceAsString()]);
        }

    }
    $logger->info('单群处理完成', ['chat_id' => $chat_id]);
}
$logger->info('任务完成');

exit;


