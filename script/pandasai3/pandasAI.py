import os
from pandasai import Agent, SmartDataframe
import pandas as pd
import sys
import json
from custom_llm import CustomOpenAI
from custom_llm import CustomAzureOpenAI
from pandasai.core.response.chart import ChartResponse
from pandasai.core.response.string import StringResponse
from pandasai.core.response.dataframe import DataFrameResponse
from pandasai.core.response.number import NumberResponse
from pandasai.core.response.error import ErrorResponse
import uuid

import matplotlib.pyplot as plt
import matplotlib
user_defined_path = os.path.join(os.path.dirname(__file__), "../../srv/data_bot/pandasai_img")


# 解决中文乱码
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams["font.family"] = "sans-serif"
# 解决负号无法显示的问题
plt.rcParams['axes.unicode_minus'] = False


def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))

def read_json_file(filename):
    if not os.path.isfile(filename):
        raise FileNotFoundError(f"File '{filename}' does not exist.")

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
            return data
    except json.JSONDecodeError:
        raise ValueError(f"File '{filename}' is not a valid JSON file.")

def process_response(response):
    """处理 PandasAI 的返回结果"""
    if isinstance(response, ChartResponse):
        random_uuid = str(uuid.uuid4())  # 生成一个随机的 UUID，并转换为字符串
        img_path = f"{user_defined_path}/{random_uuid}.png"
        response.save(img_path) # 保存图片
        return {
            'type': 'plot',
            'value': img_path # ChartResponse 对象中的图片路径
        }
    if isinstance(response, NumberResponse) or isinstance(response, StringResponse):
        return {
            'type': 'string',
            'value': response.value
        }
    if isinstance(response, DataFrameResponse):
        return {
            'type': 'dataframe',
            'value': response.value().to_json(orient='records', force_ascii=False),
            'message': "返回数据框结果"
        }
    return {
        'type' : 'error',
        'value': response.value
    }

def check_file_type(filename):
    if filename.endswith('.json'):
        return 'json'
    elif filename.endswith('.csv'):
        return 'csv'
    else:
        return 'unknown'


if __name__ == '__main__':
    if len(sys.argv) != 3:
        json_return(-1, '错误的传参', {})
        sys.exit(1)

    data_filename = sys.argv[1]
    query_filename = sys.argv[2]

    try:

        # 加载 json 数据
        file_type = check_file_type(data_filename)
        if file_type == 'csv':
            # 先读取 CSV 文件为 DataFrame
            df = pd.read_csv(data_filename)
        elif file_type == 'json':
            json_data = read_json_file(data_filename)
            # # 将 JSON 数据转换为 DataFrame
            df = pd.DataFrame(data=json_data["data_list"], columns=json_data["column_list"])
        else:
            json_return(-1, '错误的文件类型', {})
            sys.exit(1)

        # 用户查询的数据
        query_data = read_json_file(query_filename)
        query = query_data["content"]
        history_message_list = query_data["history_message_list"]


        # 如果 DataFrame 的行数大于 1，则去掉第一行合计行
        if len(df) > 1:
            df = df.iloc[1:]

        llm = CustomOpenAI(
            api_token="a784bd8d-87f0-4f9b-bcb6-e080deb6a868",
            api_base="https://ark.cn-beijing.volces.com/api/v3",
            model="deepseek-r1-250528",
            history_message_list = history_message_list,
        )
#         # 微软的 OpenAI
#         llm = CustomAzureOpenAI(
#             api_token="69e6ad9453fb4bd2b0f387efd37d940e",
#             azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com",
#             api_version="2024-09-01-preview",
#             deployment_name="gpt-4o"
#         )
        sdf = SmartDataframe(df, config={
            "llm": llm,
            "plotting_backend": "matplotlib",  # 使用原生版本
            "save_charts": True,
            "save_charts_path": user_defined_path,
            "open_charts": False,
            "enable_cache": False,
            "security": "none"
            })

        response = sdf.chat(query)

        # print("=== 返回结果 ===")
        # print(type(response))

        response = process_response(response)

        json_return(0, 'success', response)
    except Exception as e:
        json_return(-1, str(e), {})



