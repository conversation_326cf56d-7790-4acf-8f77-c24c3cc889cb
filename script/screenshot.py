from selenium import webdriver
from selenium.webdriver.common.by import By
from time import sleep
import sys
import time
import json
from  selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

img_path_dir = '/data/www/script.zx.com/srv/upload/screenshot/'
dms_url = 'https://wxapi.zx.com/dataSumPush.html' #

class Screen:
    def __init__(self):
        self.is_debug = 0
        option = webdriver.ChromeOptions()  # 默认Chrome浏览器
        option.add_argument('--headless')
        option.add_argument('--no-sandbox')
        option.add_argument('--window-size=1920x1080')
        option.add_argument('--start-maximized')
        # option.add_argument('--disable-gpu')
        option.add_argument("--disable-dev-shm-usage")
        self.driver = webdriver.Chrome(options=option)  # 当前浏览器驱动对象
        # self.driver.maximize_window()
    
    def screen_process(self):
        self.print_log("##screen process##")
        target_url = dms_url + "?" + "user_id=" + self.user_id + "&theme=" + self.theme + "&start_time=" + self.start_time + "&code=" + self.code
        self.driver.get(target_url)
        self.print_log("##title" + self.driver.title)

        # 判断表格是否已经渲染完成
        finish_tag_path = '//div[@finish]'
        finish_tag = self.find_ele(finish_tag_path)
        # 轮询60次，总共120s，参考实际情况增加轮询次数
        for i in range(300):
            self.print_log(i)
            finish_tag_value = finish_tag.get_attribute("finish")
            self.print_log(finish_tag_value)
            self.print_log("-------------")
            if finish_tag_value == "true" :
                break
            else :
                if i == 30 : 
                    raise Exception("data loading fail, timeout")
                sleep(2)

        # 查找元素
        xpath = '//div[@id="data-sum-report"]' # 要截图的节点id
        self.print_log("xpath: " + xpath)
        search_tag = self.find_ele(xpath)
        self.driver.execute_script("return arguments[0].scrollIntoView(true);", search_tag)
        
        # 判断元素是否加载完成，可视
        self.is_loading_success(search_tag)

        # 取出页面的宽度和高度
        page_width = search_tag.size.get('width')
        page_height = search_tag.size.get('height') + 72 # 此处因为页面有个边距，统计的高度不包含上边距，所以会导致某些时刻截图不全，详情问前端曾振鸿
        self.print_log("page_width: " + str(page_width) + " page_height:" + str(page_height))

        # 设置窗口大小
        self.driver.set_window_size(page_width, page_height)
        sleep(1) # 设置完等它个一秒吧,防止错截

        img_path = img_path_dir + self.user_id + '_' + time.strftime("%Y%m%d_%H%M%S") + '.png'
        self.print_log(img_path)
        search_tag.screenshot(img_path)
        return img_path

    def find_ele(self, xpath):
        try:
            return self.driver.find_element(By.XPATH, xpath)
        except:
            raise Exception("element no exist" + xpath)

    def is_loading_success(self, ele):
        try:
            WebDriverWait(self.driver, 20).until(EC.visibility_of(ele))
            return True
        except:
            raise Exception("element loading fail, timeout")

    def initParam(self):
        param = sys.argv
        self.user_id = param[1]
        self.theme = param[2]
        self.start_time = param[3]
        self.code = param[4]

    def print_log(self, string):
        if(self.is_debug) :
            print(string)

    def json_return(self, code, message, data):
        return_data = {'code':code, 'message':message, 'data': data}
        print(json.dumps(return_data))

    def close(self):
        # 关闭浏览器
        self.driver.quit()


if __name__ == '__main__':
    screen_class = Screen()
    try:
        screen_class.initParam()
        img_path = screen_class.screen_process()
        screen_class.json_return(0, 'success', {'img_path': img_path})
    except Exception as e:
        screen_class.json_return(-1, str(e), {})
        screen_class.close()
    finally:
        screen_class.close()