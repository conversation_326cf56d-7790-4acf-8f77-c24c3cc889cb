<?php

use App\MysqlConnection;
use App\Logic\DSP\GamePackMQLogic;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

class monitorGamePack extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {

            $game_pack_mq_logic = new GamePackMQLogic();

            try {
                $game_pack_mq_logic->monitorTask();
            } catch (ErrorException $e) {
                echo 'game_pack监控出错' . $e->getMessage() . PHP_EOL;
            }

            if ($this->run) sleep(120);
        }
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, monitorGamePack::class);
$multi_process->start();
