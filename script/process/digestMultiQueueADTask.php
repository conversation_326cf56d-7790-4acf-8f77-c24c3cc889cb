<?php

use App\Constant\BatchADTaskProcess;
use App\Constant\MediaType;
use App\Logic\DSP\ADTaskMQLogic;
use App\Model\RabbitModel\BatchADTaskMQModel;
use App\MysqlConnection;
use App\Param\ADServing\ADTaskMQDataParam;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

$map = BatchADTaskProcess::batchADTaskProcessMap();

$media_process_num = count($map);

$process_num = $media_process_num + 10;

/**
 * @script_desc 广告任务消费
 *
 */
class digestMultiQueueADTask extends RabbitMQProcess
{

    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        global $map, $media_process_num;
        if ($idx > $media_process_num) {
            $this->mq_model = (new BatchADTaskMQModel())->setQueueId(
                $idx - $media_process_num,
                'all',
                'all'
            );
        } else {
            $process_info = $map[$idx];
            $this->mq_model = (new BatchADTaskMQModel())->setQueueId(
                $process_info['queue_index'],
                $process_info['platform'],
                MediaType::MEDIA_TYPE_MAP[(int)($process_info['media_type'] ?? 0)]
            );
        }
    }

    public function worker($data)
    {
        $res = (new ADTaskMQLogic())->digestTask(new ADTaskMQDataParam($data));
        if (!$res) {
            var_dump($data);
        }
        return $res;
    }
}

$multi_process = new MultiProcess($process_num, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMultiQueueADTask::class);
$multi_process->start();
