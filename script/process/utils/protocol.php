<?php

const TYPE_STRING = 1;
const TYPE_NUMBER = 2;

const PROTO_REPLACE = 1;
const PROTO_QUERY = 2;
const PROTO_QUERY_RESULT = 3;

// new
const PROTO_NEW_REPLACE = 11;
const PROTO_NEW_QUERY = 12;
const PROTO_NEW_UPDATE = 13;
const PROTO_NEW_QUERY_RESULT = 14;

const PROTOCOL = [

    PROTO_REPLACE => [
        'server' => TYPE_STRING,
        'sql' => TYPE_STRING
    ],

    PROTO_QUERY => [
        'server' => TYPE_STRING,
        'sql' => TYPE_STRING
    ],

    PROTO_QUERY_RESULT => [
        'result' => TYPE_STRING
    ],

    PROTO_NEW_REPLACE => [
        'server' => TYPE_STRING,
        'query' => TYPE_STRING,
        'bindings' => TYPE_STRING
    ],

    PROTO_NEW_QUERY => [
        'server' => TYPE_STRING,
        'query' => TYPE_STRING,
        'bindings' => TYPE_STRING
    ],

    PROTO_NEW_UPDATE => [
        'server' => TYPE_STRING,
        'query' => TYPE_STRING,
        'bindings' => TYPE_STRING
    ],

    PROTO_NEW_QUERY_RESULT => [
        'code' => TYPE_NUMBER,
        'result' => TYPE_STRING
    ]
];
 