<?php

/**
 * 文件共享锁
 * 需要在子进程创建之前实例化
 * 不支持windows平台
 * 
 * __construct($filename)
 *  创建文件锁载体，该文件不可以存在。
 * 
 * shLock($block = true)
 *  创建共享锁
 *  $block 是否等待锁成功
 * 
 * exLock($block = true)
 *  创建排他锁
 *  $block 是否等待锁成功
 * 
 * unLock()
 *  释放锁
 * 
 * destroy()
 *  销毁锁
 */

use App\Exception\AppException;

require_once dirname(__FILE__) . '/AbstractLock.php';

class FileLock extends AbstractLock
{
    private $file_fp;

    public function __construct($filename)
    {
        if (file_exists($filename)) {
            throw new AppException("$filename is already exists.");
        }

        $fp = fopen($filename, "wb");
        if (false == $fp) {
            throw new AppException("create file: {$filename} fail.");
        }

        unlink($filename);
        $this->file_fp = $fp;
    }

    public function shLock($block = true)
    {
        $op = LOCK_SH;
        if (!$block) {
            $op |= LOCK_NB;
        }
        return $this->locked = flock($this->file_fp, $op);
    }

    public function exLock($block = true)
    {
        $op = LOCK_EX;
        if (!$block) {
            $op |= LOCK_NB;
        }
        return $this->locked = flock($this->file_fp, $op);
    }

    public function unLock()
    {
        $ret = flock($this->file_fp, LOCK_UN);
        $this->locked = !$ret;
        return $ret;
    }

    public function destroy()
    {
        if ($this->locked) {
            $this->unLock();
        }
        fclose($this->file_fp);
    }
}
