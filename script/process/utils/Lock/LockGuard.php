<?php

require_once dirname(__FILE__) . '/AbstractLock.php';

class LockGuard
{
    private $lock;
    private $block;

    public function __construct(AbstractLock $lock, $ex, $block)
    {
        if ($ex) {
            $lock->exLock($block);
        } else {
            $lock->shLock($block);
        }
        $this->lock = $lock;
        $this->block = $block;
    }

    public function __destruct()
    {
        $this->lock->unLock();
    }
}
