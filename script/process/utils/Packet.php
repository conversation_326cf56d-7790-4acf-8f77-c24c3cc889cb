<?php

class Packet
{
    private $protocol = [];

    public function __construct($protocol)
    {
        $this->protocol = $protocol;
    }

    public function encode($proto_idx, $data)
    {
        if (!isset($this->protocol[$proto_idx])) {
            echo "can't found {$proto_idx}." . PHP_EOL;
            return false;
        }

        $pack_str = $this->packetInt($proto_idx);

        $proto = $this->protocol[$proto_idx];
        foreach ($proto as $field_name => $type) {
            if ($type === TYPE_STRING) {
                $pack_str .= $this->packetString($data[$field_name], strlen($data[$field_name]));
            } else if ($type === TYPE_NUMBER) {
                $pack_str .= $this->packetInt($data[$field_name]);
            }
        }
        return $pack_str;
    }

    public function decode($str)
    {
        $data = [];
        $offset = 0;

        $proto_idx = $this->unpackInt($str, $offset);

        if (!isset($this->protocol[$proto_idx])) {
            echo "can't found {$proto_idx}." . PHP_EOL;
            return false;
        }

        $proto = $this->protocol[$proto_idx];
        foreach ($proto as $field_name => $type) {
            if ($type === TYPE_STRING) {
                $val = $this->unpackString($str, $offset);
            } else if ($type === TYPE_NUMBER) {
                $val = $this->unpackInt($str, $offset);
            }
            $data[$field_name] = $val;
        }

        return [$proto_idx, $data];
    }

    public function packetInt($number)
    {
        return pack('l', $number);
    }

    public function packetString($str, $len)
    {
        $data = '';
        $data .= $this->packetInt($len);
        $data .= pack("a{$len}", $str);
        return $data;
    }

    public function unpackInt($data, &$offset)
    {
        $ret = unpack('l', $data, $offset);
        $offset += 4;
        return $ret[1];
    }

    public function unpackString($data, &$offset)
    {
        $len = $this->unpackInt($data, $offset);
        $ret = unpack("a{$len}", $data, $offset);
        $offset += $len;
        return $ret[1];
    }
}
