<?php

use App\Logic\DSP\VideoClipComposeMQLogic;
use App\Model\RabbitModel\VideoClipComposeMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 视频混剪组合
 *
 */
class digestVideoClipCompose extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new VideoClipComposeMQModel();
    }

    public function worker($data)
    {
        return (new VideoClipComposeMQLogic())->digestTask($data);
    }
}

$multi_process = new MultiProcess(4, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestVideoClipCompose::class);
$multi_process->start();
