<?php

use App\Model\SqlModel\Database\ZDConnection;
use App\MysqlConnection;

require_once dirname(__DIR__) . '/../common/init.php';
require_once dirname(__DIR__) . '/process/utils/common.php';
require_once dirname(__DIR__) . '/process/utils/Packet.php';
require_once dirname(__DIR__) . '/process/utils/protocol.php';
require_once dirname(__DIR__) . '/process/core/MultiProcess.php';
require_once dirname(__DIR__) . '/process/core/Process.php';
require_once dirname(__DIR__) . '/process/core/Socket.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 4;
const DAEMONIZE = true;


class DBProcess extends Process
{
    /**@var DBService */
    private $parent_process = null;
    private $socket_map = [];
    private $process_map = [];

    public function __construct(&$parent_process)
    {
        $this->parent_process = $parent_process;
        $this->socket_map = [];
        $this->process_map = [
            PROTO_REPLACE => 'replace',
            PROTO_QUERY => 'query',
            PROTO_NEW_QUERY => 'newQuery',
            PROTO_NEW_REPLACE => 'newReplace',
            PROTO_NEW_UPDATE => 'newUpdate'
        ];
    }

    public function handleSignal($signo)
    {
        echo "child receive $signo signal." . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function init()
    {
        MysqlConnection::setConnection();
        pcntl_signal(SIGTERM, array(&$this, "handleSignal"), false);
    }

    protected function worker($idx)
    {
        $pid = $this->pid;
        $leader = false;
        $socks = [];
        $read_list = [];
        $write_list = [];
        $except_list = [];
        $timeout_sec = 0;
        $timeout_usec = 500000;
        $listen_sock = $this->parent_process->getListenSocket();

        while ($this->run) {
            if (!$leader) {
                $leader = $this->parent_process->acquire();
                if ($leader) {
                    echo "$pid is leader" . PHP_EOL;
                    $socks[] = $listen_sock;
                }
            }

            $read_list = $socks;
            if (count($read_list) === 0) {
                sleep(1);
                continue;
            }
            $active_num = socket_select($read_list, $write_list, $except_list, $timeout_sec, $timeout_usec);
            if (false === $active_num) {
                echo 'Unable to call select: ' . socket_strerror(socket_last_error()) . PHP_EOL;
                continue;
            }
            if (0 === $active_num) {
                continue;
            }
            if ($leader && in_array($listen_sock, $read_list)) {
                $key = array_search($listen_sock, $socks);
                unset($socks[$key]);
                $this->parent_process->release();
                $leader = false;
            }
            foreach ($read_list as $sock) {
                if ($sock === $listen_sock) {
                    $client = socket_accept($listen_sock);
                    if (!$client) {
                        echo 'Unable to accept socket: ' . socket_strerror(socket_last_error()) . PHP_EOL;
                        continue;
                    }
                    echo "pid $pid client $client connected." . PHP_EOL;
                    $socks[] = $client;
                    $o = new Socket($client);
                    $res_id = get_resource_id($client);
                    $this->socket_map[$res_id] = $o;
                    continue;
                }

                $res_id = get_resource_id($sock);
                $o = $this->socket_map[$res_id];
                if (!$this->do($o)) {
                    $key = array_search($sock, $socks);
                    unset($socks[$key]);
                    unset($this->socket_map[$res_id]);
                    $o->close();
                    echo "client $sock close." . PHP_EOL;
                }
            }
        }
    }

    /**
     * @param Socket $socket
     * @return int
     */
    protected function do($socket)
    {
        $packet = $this->parent_process->getPacket();
        $response = $socket->readPacket();
        $code = $response['code'];
        if ($code === Socket::CLOSED) {
            return false;
        }
        if ($code === Socket::INCOMPLETE) {
            return true;
        }

        $data = $response['data'];
        $result = $packet->decode($data);
        if (!$result) {
            return false;
        }
        [$proto_idx, $data] = $result;

        $method = $this->process_map[$proto_idx];
        try {
            $this->$method($socket, $data);
        } catch (Throwable $e) {
            return true;
        }
        return true;
    }

    protected function replace($socket, $data)
    {
        $server = $data['server'];
        $sql = $data['sql'];

        /**@var ZDConnection */
        $connection = MysqlConnection::getConnection($server);
        $connection->insert($sql);
    }

    protected function query($socket, $data)
    {
        $server = $data['server'];
        $sql = $data['sql'];

        /**@var ZDConnection */
        $connection = MysqlConnection::getConnection($server);
        $collection = $connection->select($sql);
        $result = serialize($collection);
        $response = [
            'result' => $result
        ];
        $packet = $this->parent_process->getPacket();
        $pack_str = $packet->encode(PROTO_QUERY_RESULT, $response);
        $socket->sendPacket($pack_str);
    }

    protected function newQuery($socket, $data)
    {
        $this->run($socket, $data, function($server, $query, $bindings) {
            $connection = MysqlConnection::getConnection($server);
            return $connection->select($query, $bindings);
        });
    }

    protected function newReplace($socket, $data)
    {
        $this->run($socket, $data, function($server, $query, $bindings) {
            /**@var ZDConnection */
            $connection = MysqlConnection::getConnection($server);
            return $connection->insert($query, $bindings);
        });
    }

    protected function newUpdate($socket, $data)
    {
        $this->run($socket, $data, function($server, $query, $bindings) {
            /**@var ZDConnection */
            $connection = MysqlConnection::getConnection($server);
            return $connection->update($query, $bindings);
        });
    }

    private function run($socket, $data, $callback)
    {
        $server = $data['server'];
        $query = $data['query'];
        $str_bindings = $data['bindings'];
        $bindings = unserialize($str_bindings);
        $code = 0;
        $error = null;

        try {
            $collection = $callback($server, $query, $bindings);
            $result = serialize($collection);
        } catch (Throwable $e) {
            $error = [
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ];
            $code = -1;
            $result = serialize($error);
        }
        $response = [
            'code' => $code,
            'result' => $result
        ];
        $packet = $this->parent_process->getPacket();
        $pack_str = $packet->encode(PROTO_NEW_QUERY_RESULT, $response);
        $socket->sendPacket($pack_str);
        if ($code < 0) {
            throw new Exception($error['message'], intval($error['code']));
        }
    }
}

class DBService extends MultiProcess
{
    private $listen_sock = null;
    private $sock_file = '/tmp/db.sock';
    private $backlog = 125;
    private $sem_signal = null;
    private $packet = null;

    public function __construct($worker_num = 4, $daemon = true, $sock_file = '/tmp/db.sock', $backlog = 125)
    {
        parent::__construct($worker_num, $daemon);
        $this->listen_sock = null;
        $this->sock_file = $sock_file;
        $this->backlog = $backlog;
        $this->sem_signal = null;
        $this->packet = null;
    }

    public function init()
    {
        if (file_exists($this->sock_file)) {
            unlink($this->sock_file);
        }

        $this->packet = new Packet(PROTOCOL);
        $this->initMask();
        $this->initSocket();
        $this->initSemaphore();
    }

    public function getListenSocket()
    {
        return $this->listen_sock;
    }

    /**
     * @return Packet
     */
    public function getPacket()
    {
        return $this->packet;
    }

    public function acquire()
    {
        return sem_acquire($this->sem_signal, true);
    }

    public function release()
    {
        return sem_release($this->sem_signal);
    }

    public function destroy()
    {
        sem_remove($this->sem_signal);
    }

    private function initMask()
    {
        umask(0022);
    }

    private function initSocket()
    {
        $sock = socket_create(AF_UNIX, SOCK_STREAM, 0);
        if (!is_resource($sock)) {
            echo 'Unable to create socket: ' . socket_strerror(socket_last_error()) . PHP_EOL;
            exit;
        }

        if (!socket_bind($sock, $this->sock_file)) {
            echo 'Unable to bind socket: ' . socket_strerror(socket_last_error()) . PHP_EOL;
            exit;
        }
        if (!socket_listen($sock, $this->backlog)) {
            echo 'Unable to listen socket: ' . socket_strerror(socket_last_error()) . PHP_EOL;
            exit;
        }
        $this->listen_sock = $sock;
        return true;
    }

    private function initSemaphore()
    {
        $sem_id = ftok(__FILE__, 'a');

        if (!($sem_signal = sem_get($sem_id, 1))) {
            echo "create semaphore fail.";
            exit;
        }

        $this->sem_signal = $sem_signal;
        return true;
    }

    protected function spawnWorker($idx)
    {
        $pid = pcntl_fork();
        if ($pid < 0) {
            echo "fork process fail." . PHP_EOL;
            exit;
        }
        if ($pid === 0) {
            $o = new DBProcess($this);
            $o->init();
            $o->start($idx);
            exit;
        }
        return $pid;
    }
}

$service = new DBService(PROCESS_NUM, DAEMONIZE);
$service->on(DBService::EVT_PARENT_INITIALIZE, array(&$service, 'init'));
$service->start();
$service->destroy();