<?php

use App\Logic\DSP\ADAnalysisMQLogic;
use App\Model\RabbitModel\ADChangeMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 广告删除
 *
 */
class digestADChangeADDeleteTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new ADChangeMQModel())->setChangeADDelete();
    }

    public function worker($data)
    {
        try {
            return (new ADAnalysisMQLogic())->digestChangeADDeleteTask($data);
        } catch (ErrorException $e) {
            echo '消化基本报表广告删除数据任务队列出错' . $e->getMessage() . "\n";
        } catch (Exception $exception) {
            echo '消化基本报表广告删除数据任务队列失败：' . $exception->getMessage() . "\n";
        }

        return false;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestADChangeADDeleteTask::class);
$multi_process->start();
