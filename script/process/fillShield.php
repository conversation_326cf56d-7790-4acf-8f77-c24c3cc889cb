<?php

use App\Logic\DSP\ShieldedLogic;
use App\Model\RedisModel\ShieldedModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';
require dirname(__DIR__) . '/process/structure/Shielded.php';

date_default_timezone_set('PRC');

class fillShield extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    protected function worker($idx)
    {
        $model = new ShieldedModel();
        $shield = new Shielded();

        while ($this->run) {
            $func = null;
            $data = $model->popShieldTask();
            if (!$data) {
                goto NEXT;
            }

            $msg = json_encode($data, JSON_UNESCAPED_UNICODE);
            echo "accept new message: {$msg}" . PHP_EOL;

            $shield_type = $data['shield_type'];

            if ($shield_type == ShieldedLogic::SHIELDED_WORD) {
                $func = "shieldWord";
            } else if ($shield_type == ShieldedLogic::SHIELDED_USER) {
                $func = "shieldUser";
            }

            $pid = pcntl_fork();
            if ($pid < 0) {
                $model->pushOriginTask($data);
                goto NEXT;
            }

            if ($pid == 0) {
                $model->disconnect();
                $shield->$func($data);
                exit(0);
            }

            NEXT:
            sleep(2);
            $status = 0;
            pcntl_waitpid(-1, $status, WNOHANG);
        }
    }
}



$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, fillShield::class);
$multi_process->start();
