<?php

use App\MysqlConnection;
use App\Logic\DSP\GamePackMQLogic;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 泽达脚本服务器运行
 *
 */
class fillGamePack extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {

            $game_pack_mq_logic = new GamePackMQLogic();

            try {
                $game_pack_mq_logic->produceTask();
            } catch (ErrorException $e) {
                echo 'game_pack任务队列采集出错' . $e->getMessage() . "\n";
            }

            if ($this->run) {
                echo date('Y-m-d H:i:s') . " 扫描完成,无新数据,休眠20秒" . PHP_EOL;
                sleep(20);
            }
        }
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, fillGamePack::class);
$multi_process->start();
