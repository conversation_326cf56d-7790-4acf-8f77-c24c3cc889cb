<?php

use App\Logic\DSP\ToutiaoStarOrderMQLogic;
use App\Model\RabbitModel\ToutiaoStarOrderMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 头条星图订单操作
 *
 */

class digestToutiaoStarOrderOperateTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new ToutiaoStarOrderMQModel();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        return (new ToutiaoStarOrderMQLogic())->digest($data);
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestToutiaoStarOrderOperateTask::class);
$multi_process->start();
