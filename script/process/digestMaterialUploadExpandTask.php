<?php

use App\Logic\DSP\MaterialUploadExpandMQLogic;
use App\Model\RabbitModel\MaterialUploadExpandTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材拓展上传
 *
 */
class digestMaterialUploadExpandTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new MaterialUploadExpandTaskMQModel();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        try {
            return (new MaterialUploadExpandMQLogic())->digestDelayTask($data);
        } catch (ErrorException $e) {
            echo '上传素材扩展队列出错' . $e->getMessage() . "\n";
        }

        return false;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialUploadExpandTask::class);
$multi_process->start();
