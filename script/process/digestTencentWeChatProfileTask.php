<?php

use App\Logic\DSP\MaterialPushMQLogic;
use App\Logic\DSP\TencentWeChatProfileMQLogic;
use App\Model\RabbitModel\TencentWeChatProfileTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 微信头像新建
 *
 */
class digestTencentWeChatProfileTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new TencentWeChatProfileTaskMQModel();
    }

    public function worker($data)
    {
        try {
            return (new TencentWeChatProfileMQLogic())->digestTask($data);
        } catch (ErrorException $e) {
            echo date('Y-m-d H:i:s') . '腾讯朋友圈任务队列出错' . $e->getMessage() . "\n";
        }

        return true;
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestTencentWeChatProfileTask::class);
$multi_process->start();
