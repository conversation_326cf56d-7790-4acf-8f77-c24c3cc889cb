<?php

use App\Logic\API\AwemeLogic;
use App\Logic\DSP\MaterialUploadMQLogic;
use App\Model\RabbitModel\AwemeVideoSyncMQModel;
use App\Model\RabbitModel\MaterialUploadTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材上传
 *
 */
class digestAwemeVideoSyncTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new AwemeVideoSyncMQModel();
    }

    public function worker($data)
    {
        try {
            return (new AwemeLogic())->digestTask($data);
        } catch (ErrorException $e) {
            echo '消耗素材上传媒体任务队列出错' . $e->getMessage() . "\n";
        }

        return true;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestAwemeVideoSyncTask::class);
$multi_process->start();
