<?php

use App\Logic\DSP\VideoClipAttributeMQLogic;
use App\Model\RabbitModel\VideoClipAttributeMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 混剪评估
 *
 */
class digestVideoClipAttribute extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new VideoClipAttributeMQModel();
    }

    public function worker($data)
    {
        return (new VideoClipAttributeMQLogic())->digestTask($data);
    }
}

$multi_process = new MultiProcess(4, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestVideoClipAttribute::class);
$multi_process->start();
