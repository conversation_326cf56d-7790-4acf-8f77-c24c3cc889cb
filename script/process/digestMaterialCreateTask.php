<?php

use App\Logic\DSP\MaterialCreateMQLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材创建
 *
 */
class digestMaterialCreateTask extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {
            $start_time = time();

            $material_create_mq_logic = new MaterialCreateMQLogic();

            try {
                $material_create_mq_logic->digestTask();
            } catch (ErrorException $e) {
                echo '素材补全信息任务队列出错' . $e->getMessage() . "\n";
            }
            $spend_time = time() - $start_time;
            if ($spend_time > 1) {
                $date = date("Y-m-d");
                echo "[$date] child {$n} pid {$this->pid} spend time {$spend_time}." . PHP_EOL;
            }
        }
    }
}

$multi_process = new MultiProcess(5, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialCreateTask::class);
$multi_process->start();
