<?php

use App\Logic\DSP\ADAnalysisMQLogic;
use App\Model\RabbitModel\ADChangeMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 广告修改预算
 *
 */
class digestADChangeBudgetTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new ADChangeMQModel())->setChangeBudget();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        try {
            return (new ADAnalysisMQLogic())->digestDelayChangeBudgetTask($data);
        } catch (ErrorException $e) {
            echo '消耗修改广告budget任务队列出错' . $e->getMessage() . "\n";
        } catch (Exception $exception) {
            echo 'budget错误：' . $exception->getMessage() . "\n";
        }

        return false;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestADChangeBudgetTask::class);
$multi_process->start();
