<?php

use App\Logic\DSP\SiteMQLogic;
use App\Model\RabbitModel\SiteMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 广告配置创建延迟任务，如生成渠道包，检测包状态与落地页状态
 *
 */
class digestDelaySiteTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new SiteMQModel();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        return (new SiteMQLogic())->digestDelaySiteTask($data);
    }
}

$multi_process = new MultiProcess(8, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestDelaySiteTask::class);
$multi_process->start();

