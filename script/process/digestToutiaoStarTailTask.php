<?php

use App\Logic\DSP\ToutiaoStarTaskMQLogic;
use App\Model\RabbitModel\ToutiaoStarTailTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 头条星图任务执行后续队列消耗
 *
 */

class digestToutiaoStarTailTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new ToutiaoStarTailTaskMQModel();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        return (new ToutiaoStarTaskMQLogic())->digestTailTask($data);
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestToutiaoStarTailTask::class);
$multi_process->start();
