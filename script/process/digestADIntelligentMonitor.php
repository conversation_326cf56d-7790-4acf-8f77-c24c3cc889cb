<?php

use App\Constant\InitIntelligentMonitor;
use App\Logic\DSP\ADIntelligentMonitorLogic;
use App\Model\RabbitModel\ADIntelligentMonitorMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

$default_num = InitIntelligentMonitor::DEFAULT_CONSUMER_NUM;

$worker_num = $argv[1] ?? $default_num;
$worker_num = ($worker_num <= $default_num) ? $default_num : $worker_num;

/**
 * @script_desc 智创组合
 */
class digestADIntelligentMonitor extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new ADIntelligentMonitorMQModel());
    }

    public function handleSignal($signo)
    {
        echo "child $this->pid accept signal $signo" . PHP_EOL;
        if ($signo == SIGTERM) {
            $this->run = false;
        }
    }

    public function worker($data): bool
    {
        try {
            $res = (new ADIntelligentMonitorLogic())->doBindTargetLogic($data['data_list'], $data['extend_data']);
        } catch (RedisException $e) {
            $res = true;
        }
        if (!$res) {
            var_dump($data);
        }
        return $res;
    }
}

$multi_process = new MultiProcess($worker_num, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestADIntelligentMonitor::class);
$multi_process->start();
