<?php

require_once dirname(__DIR__) . '/core/Socket.php';
require_once dirname(__DIR__) . '/utils/Packet.php';
require_once dirname(__DIR__) . '/utils/protocol.php';


$now = time();
$packet = new Packet(PROTOCOL);
$sock = socket_create(AF_UNIX, SOCK_STREAM, 0);
socket_connect($sock, '/tmp/db.sock');
$socket = new Socket($sock);


function parse($response) {
    global $packet;
    $str = $response['data'];
    [$proto_idx, $result] = $packet->decode($str);
    $result = $result['result'];
    return unserialize($result);
}

// replace
$data = [
    'server' => 'default',
    'sql' => "replace into `order` values(162, 'DBServer 测试', 1, 1, 'DBService 测试', {$now}, {$now}, {$now}, '', {$now})"
];
$pack = $packet->encode(PROTO_REPLACE, $data);
$socket->sendPacket($pack);

// query
$data = [
    'server' => 'default',
    'sql' => "select * from `order` where id = 162"
];
$pack = $packet->encode(PROTO_QUERY, $data);
$dd = $socket->call($pack, 'parse');

var_dump($dd);
