<?php

use App\Logic\DSP\MaterialPushMQLogic;
use App\Model\RabbitModel\MaterialPushTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材推送(与素材上传不一样，推送是给试玩素材)
 *
 */
class digestMaterialPushTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new MaterialPushTaskMQModel());
    }

    public function worker($data)
    {
        try {
            return (new MaterialPushMQLogic())->digestTask($data);
        } catch (ErrorException $e) {
            echo date('Y-m-d H:i:s') . '素材文件授权任务队列出错' . $e->getMessage() . "\n";
        }

        return false;
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialPushTask::class);
$multi_process->start();
