<?php

use App\Logic\DSP\MaterialCreateMQLogic;
use App\Logic\DSP\MaterialExpandMQLogic;
use App\Model\RabbitModel\MaterialExpandTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材扩展
 *
 */
class digestMaterialExpandTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new MaterialExpandTaskMQModel());
    }

    public function worker($data)
    {
        try {
            return (new MaterialExpandMQLogic())->digestTask($data);
        } catch (ErrorException $e) {
            echo date('Y-m-d H:i:s') . '素材文件拓展任务队列出错' . $e->getMessage() . "\n";
        }

        return true;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialExpandTask::class);
$multi_process->start();
