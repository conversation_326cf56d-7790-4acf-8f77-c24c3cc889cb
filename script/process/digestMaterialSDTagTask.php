<?php

use App\Logic\DSP\MaterialFileSDTagsTaskMQLogic;
use App\Model\RabbitModel\MaterialFileSDTagTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材sd标签
 *
 */
class digestMaterialSDTagTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new MaterialFileSDTagTaskMQModel());
    }

    public function worker($data)
    {
        try {
            return (new MaterialFileSDTagsTaskMQLogic())->digestTask($data);
        } catch (ErrorException $e) {
            echo date('Y-m-d H:i:s') . '素材文件SD标签任务队列出错' . $e->getMessage() . "\n";
        }

        return true;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialSDTagTask::class);
$multi_process->start();
