<?php

use App\Logic\DSP\MaterialFileAnalyseTaskMQLogic;
use App\Model\RabbitModel\MaterialFileAnalyseTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 视频混剪组合
 *
 */
class digestMaterialFileAnalyseTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new MaterialFileAnalyseTaskMQModel();
        $this->queue_type = RabbitMQProcess::MQ_TYPE_DELAY;
    }

    public function worker($data)
    {
        try {
            return (new MaterialFileAnalyseTaskMQLogic())->digestTask($data);
        } catch (Exception $exception) {
            echo '素材洞察分析错误：' . $exception->getMessage() . "\n";
        }
    }
}

$multi_process = new MultiProcess(4, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialFileAnalyseTask::class);
$multi_process->start();
