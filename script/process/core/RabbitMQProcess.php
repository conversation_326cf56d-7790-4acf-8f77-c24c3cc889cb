<?php

use App\Exception\AppException;
use App\Model\RabbitModel\AbstractRabbitModel;

require_once dirname(__DIR__) . '/utils/common.php';
require_once dirname(__FILE__) . '/Process.php';

abstract class RabbitMQProcess extends Process
{
    const MQ_TYPE_COMMON = 'common';
    const MQ_TYPE_DELAY = 'delay';


    protected $pid = 0;
    protected $idx = 0;

    /**
     * @var AbstractRabbitModel
     */
    protected $mq_model;

    protected $queue_type = self::MQ_TYPE_COMMON;

    /**
     * return true to ack the $data
     * @param $data
     * @return bool
     */
    abstract protected function worker($data);

    abstract protected function setMQModel($idx);

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->mq_model->stopConsumer();
                break;
        }
    }

    public function start($idx = 1)
    {
        if ($this->daemon()) {
            $this->pid = daemonize();
        } else {
            $this->pid = posix_getpid();
        }
        $this->idx = $idx;
        $this->setMQModel($idx);

        if ($this->mq_model) {
            try {
                switch ($this->queue_type) {
                    case self::MQ_TYPE_DELAY:
                        $this->mq_model->delayDigest(Closure::fromCallable([$this, 'worker']));
                        break;
                    default:
                        $this->mq_model->digest(Closure::fromCallable([$this, 'worker']));
                        break;
                }

            } catch (Throwable $e) {
                echo date('Y-m-d H:i:s') . '  ' . $this->mq_model->getQueue() . '出错,' . $e->getMessage() . PHP_EOL;
                echo $e->getTraceAsString() . PHP_EOL;
            }
        } else {
            throw new AppException('需要设置RabbitMQ model');
        }
    }
}