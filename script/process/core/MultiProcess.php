<?php

require_once dirname(__DIR__) . '/utils/common.php';


class MultiProcess
{
    const EVT_PARENT_INITIALIZE = 0x01;          // 父进程初始化
    const EVT_WORKER_HANDLER = 0x02;             // 子进程工作函数

    private $worker_num = 0;
    private $daemon = true;
    private $worker_pids = [];
    private $handler = [];
    private $pid = 0;
    private $run = false;
    private $run_once = false;

    public function __construct($worker_num, $daemon = true)
    {
        $this->worker_num = $worker_num;
        $this->daemon = $daemon;
        $this->worker_pids = [];
        $this->handler = [
            self::EVT_PARENT_INITIALIZE => null,
            self::EVT_WORKER_HANDLER => null
        ];
        $this->pid = 0;
        $this->run = false;
        $this->run_once = false;
    }

    public function on($event, $handler)
    {
        $this->handler[$event] = $handler;
    }

    public function status()
    {
        return $this->run;
    }

    public function start()
    {
        $status = 0;

        if ($this->daemon) {
            $this->pid = daemonize();
        } else {
            $this->pid = posix_getpid();
        }

        pcntl_async_signals(true);
        $handler = $this->handler[self::EVT_PARENT_INITIALIZE];
        if ($handler) {
            $handler($this);
        }

        $this->run = true;

        pcntl_sigprocmask(SIG_BLOCK, [SIGTERM]);

        for ($i = 1; $i <= $this->worker_num; ++$i) {
            $pid = $this->spawnWorker($i);
            $this->worker_pids[] = $pid;
        }

        pcntl_signal(SIGTERM, array(&$this, "handleSignal"), false);
        pcntl_signal(SIGCHLD, array(&$this, "handleSignal"), false);

        pcntl_sigprocmask(SIG_UNBLOCK, [SIGTERM]);

        $ret = [];
        while ($this->worker_pids) {
            $pid = pcntl_wait($status, 0);
            if ($pid === -1) {
                continue;
            }
            echo "exit pid: $pid remain worker count: " . (count($this->worker_pids) - 1) . PHP_EOL;
            $idx = array_search($pid, $this->worker_pids);
            $ret[$idx] = $status;
            if (!$this->run || $this->run_once) {
                unset($this->worker_pids[$idx]);
                continue;
            }
            $new_pid = $this->spawnWorker($idx + 1);
            echo "create new worker. pid: $new_pid" . PHP_EOL;
            $this->worker_pids[$idx] = $new_pid;
        }
        return $ret;
    }

    public function handleSignal($signo)
    {
        echo "parent receive $signo signal." . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                foreach ($this->worker_pids as $pid) {
                    posix_kill($pid, SIGTERM);
                }
                $this->run = false;
                break;
        }
    }

    public function runOnce()
    {
        $this->run_once = true;
    }

    protected function spawnWorker($idx)
    {
        $pid = pcntl_fork();
        if ($pid < 0) {
            echo "fork process fail." . PHP_EOL;
            exit;
        }
        if ($pid === 0) {
            pcntl_sigprocmask(SIG_UNBLOCK, [SIGTERM]);
            $handler = $this->handler[self::EVT_WORKER_HANDLER];
            /** @var Process $o */
            $o = new $handler();
            $o->init();
            $o->setWorkerNum($this->worker_num);
            $o->start($idx);
            exit;
        }
        return $pid;
    }
}
