<?php

require_once dirname(__DIR__) . '/utils/common.php';

abstract class Process
{
    protected $run = false;
    protected $pid = 0;
    protected $worker_num = 0;
    protected $once = false;
    protected $idx = 0;

    abstract public function handleSignal($signo);
    abstract public function init();
    abstract protected function worker($idx);

    protected function daemon()
    {
        return false;
    }

    public function enableSignal($signo)
    {
        pcntl_signal($signo, array(&$this, "handleSignal"), false);
    }

    public function alarm($second)
    {
        pcntl_alarm($second);
    }

    public function setWorkerNum($num)
    {
        $this->worker_num = $num;
    }

    public function setOnce()
    {
        $this->once = true;
    }

    public function start($idx = 1)
    {
        if ($this->daemon()) {
            $this->pid = daemonize();
        } else {
            $this->pid = posix_getpid();
        }
        $this->run = true;
        $this->idx = $idx;
        $this->worker($idx);
        exit;
    }

    /**
     * 通知父进程，不复活自己
     * 该函数会影响到其他子进程
     * A进程调用该函数，那么B进程退出后也不会复活。
     * 该函数需要等待一段时间，让父进程通知自己退出。
     * ！！！注意：单进程不可调用该函数
     */
    public function goDie($sleep = 5)
    {
        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep($sleep);
    }
}
