<?php

require_once dirname(__DIR__) . '/utils/Packet.php';


class Socket
{
    const COMPLETE = 0;
    const INCOMPLETE = 1;
    const CLOSED = 2;
    const HEADER_SIZE = 4;

    /**@var resource */
    private $sock = null;
    private $packet = null;

    public function __construct($sock)
    {
        $this->sock = $sock;
        $this->packet = new Packet([]);
    }

    public function readPacket()
    {
        $offset = 0;
        RETRY_HEADER:
        $header_data = socket_read($this->sock, self::HEADER_SIZE);
        if ($header_data === false) {
            $errno = socket_last_error($this->sock);
            // interrupt
            if ($errno === 4) {
                goto RETRY_HEADER;
            }
            return [
                'code' => self::CLOSED
            ];
        }
        if (strlen($header_data) == 0) {
            return [
                'code' => self::CLOSED
            ];
        }

        $data = '';
        $recv_len = 0;
        $len = $this->packet->unpackInt($header_data, $offset);
        $remain_len = $len;
        while ($recv_len < $len) {
            $msg = socket_read($this->sock, $remain_len);
            if ($msg === false) {
                $errno = socket_last_error($this->sock);
                // interrupt
                if ($errno === 4) {
                    continue;
                } else {
                    return [
                        'code' => self::CLOSED
                    ];
                }
            }
            $data .= $msg;
            $recv_len = strlen($data);
            $remain_len = $len - $recv_len;
        }

        return [
            'code' => self::COMPLETE,
            'data' => $data
        ];
    }

    public function sendPacket($data)
    {
        $len = strlen($data);
        $header = $this->packet->packetInt($len);
        $data = $header . $data;
        socket_write($this->sock, $data);
    }

    public function call($data, $parseFun = null, $args = null)
    {
        $this->sendPacket($data);
        $data = $this->readPacket();
        return is_callable($parseFun) ? $parseFun($data, $args) : $data;
    }

    public function close()
    {
        if ($this->sock) {
            socket_close($this->sock);
        }
    }

    /**
     * @param string $path
     * @return Socket
     */
    static public function connect($path)
    {
        $fd = socket_create(AF_UNIX, SOCK_STREAM, 0);
        if ($fd === false) {
            return null;
        }
        $ret = socket_connect($fd, $path);
        if (!$ret) {
            socket_close($fd);
            return null;
        }

        $sock = new Socket($fd);
        return $sock;
    }
}
