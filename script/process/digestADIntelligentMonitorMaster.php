<?php

use App\Constant\InitIntelligentMonitor;
use App\MysqlConnection;
use App\Utils\Helpers;
use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 这个脚本不需要使用MQPross，不需要restart，只需要kill即可
 *
 */
class digestADIntelligentMonitorMaster extends Process
{
    const DEFAULT_CHECK_SPACE = 60;

    const MESSAGE_THRESHOLD = 500;

    const MAX_CONSUMER_NUM = 50;

    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child $this->pid accept signal $signo" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {
            try {
                $result = Helpers::getCurl(
                    'http://127.0.0.1:15672/api/queues/ad_intelligent_monitor_task/ad_intelligent_monitor_task',
                    [
                        'user_pwd' => EnvConfig::RABBITMQ['default']['user'] . ':' . EnvConfig::RABBITMQ['default']['password']
                    ]
                );
                if ($result) {
                    $result = json_decode($result, true);
                    $message_num = (int)($result['messages'] ?? 0);

                    $already_exist_consumer_num = 0;
                    $exec_status_order = __DIR__ . '/manager.sh status digestADIntelligentMonitor.php';
                    $status_result = exec($exec_status_order);
                    if ($status_result) {
                        $status_result_array = explode('=>', $status_result);
                        $status_result = end($status_result_array);
                        $status_result_array = explode(' ', $status_result);
                        $already_exist_consumer_num = (int)$status_result_array[1];
                        $already_exist_consumer_num--;
                    }

                    if ($message_num > self::MESSAGE_THRESHOLD) {
                        $worker_num = InitIntelligentMonitor::DEFAULT_CONSUMER_NUM + ceil(($message_num - self::MESSAGE_THRESHOLD) / 50);
                        if ($worker_num >= self::MAX_CONSUMER_NUM) {
                            $worker_num = self::MAX_CONSUMER_NUM;
                        }

                        if ($already_exist_consumer_num > $worker_num) {
                            echo date('Y-m-d H:i:s') . '-已经检查一轮,积存消息数:' . $message_num . ',已有消费者数:' . $already_exist_consumer_num . '不进行动态控制-1' . PHP_EOL;
                            sleep(self::DEFAULT_CHECK_SPACE);
                            continue;
                        }

                        $exec_kill_order = __DIR__ . '/manager.sh kill digestADIntelligentMonitor.php';
                        exec($exec_kill_order);

                        $exec_start_order = __DIR__ . "/manager.sh start digestADIntelligentMonitor.php $worker_num";
                        exec($exec_start_order);
                        echo date('Y-m-d H:i:s') . '-已经检查一轮,积存消息数:' . $message_num . ',消费者数修改为' . $worker_num . PHP_EOL;
                    } else {
                        if ($message_num <= 0) {
                            if ($already_exist_consumer_num > InitIntelligentMonitor::DEFAULT_CONSUMER_NUM) {
                                $exec_kill_order = __DIR__ . '/manager.sh kill digestADIntelligentMonitor.php';
                                exec($exec_kill_order);

                                $exec_start_order = __DIR__ . '/manager.sh start digestADIntelligentMonitor.php';
                                exec($exec_start_order);
                                echo date('Y-m-d H:i:s') . '-已经检查一轮,积存消息数:' . $message_num . ',已有消费者数:' . $already_exist_consumer_num . '消费者数修改为' . InitIntelligentMonitor::DEFAULT_CONSUMER_NUM . PHP_EOL;
                            } else {
                                echo date('Y-m-d H:i:s') . '-已经检查一轮,无需处理,积存消息数:' . $message_num . ',已有消费者数:' . $already_exist_consumer_num . PHP_EOL;
                            }
                        } else {
                            echo date('Y-m-d H:i:s') . '-已经检查一轮,积存消息数:' . $message_num . ',已有消费者数:' . $already_exist_consumer_num . '不进行动态控制-2' . PHP_EOL;
                        }
                    }
                } else {
                    echo date('Y-m-d H:i:s') . '请求失败' . PHP_EOL;
                }
            } catch (Throwable $e) {
                echo date('Y-m-d H:i:s') . '报错' . $e->getMessage() . PHP_EOL;
            }
            sleep(self::DEFAULT_CHECK_SPACE);
        }
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestADIntelligentMonitorMaster::class);
$multi_process->start();
