<?php

use App\MysqlConnection;
use App\Service\SwitchLDYService;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 这个脚本不需要运行，如果运行了请停止
 *
 */
class digestLDYTask extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {
            try {
                (new SwitchLDYService())->syncLDY();

                sleep(10);
            } catch (Throwable $e) {
                echo date('Y-m-d H:i:s') . '  落地页切换队列出错' . $e->getMessage() . "\n";
            }
        }
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestLDYTask::class);
$multi_process->start();
