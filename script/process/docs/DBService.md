### 简介
DBService是脚本进程和MySQL数据库中间的一个中间件，用来转发sql语句。

### 解决的痛点
随着接入的媒体增多，需要拉取媒体数据脚本也会增多，按照目前多进程的模式，MySQL连接数量也会随着上升。如果持续下去，则会出现MySQL拒绝连接的问题，影响主业务。

### 原理
1. 同样采用多进程架构，每一个工作进程维护一份MySQL连接，所以MySQL连接数 = 工作进程数 = PROCESS_NUM。
2. 由父进程创建listen_fd，工作进程争抢listen_fd的监听，利用信号量保持只有一个进程可以获取到该监听，避免出现惊群问题。
3. 工作进程采用Reactor并发模型处理。

### API
请参考script/process/unittest/DBServerTest.php

### 示例
请参考script/process/fillMPAccount.php

### 建议
大媒体不需要通过该中转。毕竟会多一层内存拷贝。
