### 启动管理脚本注意事项

#### 使用方式
```
bash manager.sh start|stop|restart|status|show-log
```

也可以启动和关闭指定进程
```
bash manager.sh start filename
bash manager.sh stop filename
```

不指定则`开启`所有

#### 注意事项
此脚本将会启动script/process目录下，所有php脚本。
1. php进程模型:
    - 单进程：必须是守护进程。
    - 多进程：父进程必须为守护进程。

2. start
    - 开启相关进程，如果进程已经启动，则忽略。
    - 如果没有指定脚本，则启动所有脚本。
    - 默认将进程的标准输出和错误输出重定向到/tmp/filename.log文件下。

3. stop
    - 关闭相关进程，如果进程为关闭，则忽略。
    - 必须指定脚本进程。
    - 该脚本将会发送SIGTERM信号到相关进程。该信号的默认处理是直接停止进程。
    - 如果进程需要平滑关闭，则需要监听`SIGTERM`信号，并且设置信号处理函数
    - 该命令会等待相关进程退出，最大等待`900秒`。

4. restart
    - 重启指定脚本。

5. status
    - 查看所有进程的运行状态。

6. show-log
    - 查看指定脚本日志。底层命令为`tail -f`

```bash
# running number 后面得这个数字代表进程数量 worker + manager
[root@zeda-script process]# sh manager.sh status
✔ fill*.php ============================> running 6.
× fillADTargetingData.php ===========================> not run.
```

#### 特别注意
因为线上和测试服的php编译时都加入了`--enable-sigchild`编译参数。在多进程模式下，如果父进程不关心子进程的状态改变，则可以不用设置`SIGCHLD`信号处理函数，因为会被php内核处理。如果需要手动管理子进程，则必须设置`SIGCHLD`处理函数。
