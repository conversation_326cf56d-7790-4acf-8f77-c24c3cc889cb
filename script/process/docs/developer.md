## 开发文档
**注意：该模型不可和`Swoole`混用**

### 运行条件：
1. php版本>=7.1.0。
2. php支持posix。

### 目录结构
``` bash
.
├── core           # 基础开发库，多进程模型，单进程模型
├── docs           # 文档
├── utils          # 一些工具函数
├── unittest       # 单元测试脚本
├── *.php          # 脚本
└── manager.sh     # 进程控制脚本（查阅readme.md）
```

### 多进程模型
#### \core\MultiProcess
> __construct($worker_num, $daemon = true)
```php
// 创建4个工作进程，父进程为守护进程
$process_manager = new MultiProcess(4, true);
```
> on() 设置相关回调函数
- MultiProcess::EVT_PARENT_INITIALIZE 父进程启动，子进程未创建时的回调。一些共享资源可以在此创建。
- MultiProcess::EVT_WORKER_HANDLER 子进程类继承自`\core\Process`
```php
// 设置父进程初始化回调
$multi_process->on(MultiProcess::EVT_PARENT_INITIALIZE, 'init_parent');
// 设置子进程类
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'MPReportProcess');
```
> status() 查看父进程状态

> start() 运行脚本
1. 如果父进程为守护进程，则调用daemonize函数，创建守护进程。
2. 如果指定了父进程初始化函数，则调用该函数。
3. 添加 SIGTERM 信号屏蔽字
4. 创建子进程 -> 子进程删除 SIGTERM 信号屏蔽字
5. 设置父进程信号处理函数
6. 删除 SIGTERM 信号屏蔽字
7. 进入主循环，监听子进程退出。

示例
```php
class MPReportProcess extends Process {};
$multi_process = new MultiProcess(10, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'MPReportProcess');
// 如果该进程只是处理一次性任务则添加下面这句代码
// 必须在start之前调用
$multi_process->runOnce();
$multi_process->start();
```

### 单进程模型
#### \core\Process
> daemon(): bool 是否为守护进程。默认为false
```php
protected function daemon()
{
    return false;
}
```
> enableSignal($signo) 开启信号监听
```php
// 开启SIGTERM信号监听
// SIGKILL和SIGSTOP不能监听
$process = new Process();
$process->enableSignal(SIGTERM);
```
> alarm($second) 设置定时器
```php
$process->alarm(10);
```
> start($idx = 1) 启动进程, $idx进程编号
```php
$process->start(1);
```
> goDie($sleep = 5) 子进程通知父进程退出
```php
// 注: 单进程模式不可调用
$process->goDie();
```
> 抽象函数
```php
// 子进程初始化处理
abstract public function init();
// 信号处理函数
abstract public function handleSignal($signo);
// 逻辑处理
abstract protected function worker($idx);
```
示例
```php
class MPReportProcess extends Process
{
    public function init() {
        $this->enableSignal(SIGTERM);
        $this->enableSignal(SIGALRM);
        $this->alarm(60);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo) {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
            case SIGALRM:
                $this->reload_yesterday = true;
                break;
        }
    }

    protected function worker($n) {
        // do something
    }
}

$process = new MPReportProcess();
$process->init();
$process->start(1);
```

### 注意
在父进程中如果申请了共享资源，比如共享内存，信号量等。需要在父进程结束后手动释放这些资源，即使设置了`auto_release = 1`。因为这会影响进程退出。[参考资料](https://segmentfault.com/q/1010000006236619)
