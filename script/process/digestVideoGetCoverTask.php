<?php

use App\Logic\DSP\VideoGetCoverMQLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 智能封面脚本，这个脚本不需要运行，如果运行了请停止
 *
 */
class digestVideoGetCoverTask extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        while ($this->run) {
            $start_time = time();

            $video_get_cover_mq_logic = new VideoGetCoverMQLogic();

            try {
                $video_get_cover_mq_logic->digestTask();
            } catch (ErrorException $e) {
                echo '获取视频智能封面任务队列出错' . $e->getMessage() . "\n";
            }
            $spend_time = time() - $start_time;
            if ($spend_time > 1) {
                $date = date("Y-m-d");
                echo "[$date] child {$n} pid {$this->pid} spend time {$spend_time}." . PHP_EOL;
            }
        }
    }
}

$multi_process = new MultiProcess(6, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestVideoGetCoverTask::class);
$multi_process->start();
