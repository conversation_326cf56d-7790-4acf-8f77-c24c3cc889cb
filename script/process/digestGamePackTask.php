<?php

use App\MysqlConnection;
use App\Logic\DSP\GamePackMQLogic;
use App\Param\GamePackParam;
use Common\EnvConfig;
use App\Model\RabbitModel\GamePackMQModel;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

$process_total_num        = 0;
$process_num_platform_map = [];
foreach (EnvConfig::PACK_PROCESS as $platform => $num) {
    $tmp                      = array_fill(0, $num, $platform);
    $process_num_platform_map = array_merge($process_num_platform_map, $tmp);
    $process_total_num        += $num;
}

/**
 * @script_desc 泽达脚本服务器运行
 *
 */
class digestGamePackTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        global $process_num_platform_map;
        $platform = $process_num_platform_map[($idx - 1)] ?? 'default';
        $this->mq_model = (new GamePackMQModel())->setQueueId($platform);
    }

    public function worker($data)
    {
        return (new GamePackMQLogic())->digestTask($this->idx - 1, new GamePackParam($data));
    }
}

$multi_process = new MultiProcess($process_total_num, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestGamePackTask::class);
$multi_process->start();
