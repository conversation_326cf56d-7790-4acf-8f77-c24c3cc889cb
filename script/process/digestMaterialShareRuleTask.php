<?php

use App\Logic\DSP\MaterialShareRuleMQLogic;
use App\Model\RabbitModel\MaterialShareRuleMatchMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 素材共享规则
 *
 */
class digestMaterialShareRuleTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = (new MaterialShareRuleMatchMQModel())->setMatchMaterialChange();
    }

    public function worker($data)
    {
        try {
            return (new MaterialShareRuleMQLogic())->digestMatchMaterialDataTask($data);
        } catch (ErrorException $e) {
            echo '消化素材匹配任务队列出错' . $e->getMessage() . "\n";
        } catch (Throwable $exception) {
            echo '消化素材匹配任务队列失败：' . $exception->getMessage() . "\n";
        }

        return false;
    }
}

$multi_process = new MultiProcess(1, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMaterialShareRuleTask::class);
$multi_process->start();
