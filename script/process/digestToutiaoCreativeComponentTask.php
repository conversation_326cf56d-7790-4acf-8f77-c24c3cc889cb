<?php

use App\Logic\DSP\ToutiaoCreativeComponentTaskMQLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 头条创意组件任务
 *
 */
class digestToutiaoCreativeComponentTask extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function worker($n)
    {
        while ($this->run) {
            $start_time = time();
            try {
                (new ToutiaoCreativeComponentTaskMQLogic())->digestTask();
            } catch (ErrorException $e) {
                echo '消耗头条创意组件任务队列出错' . $e->getMessage() . "\n";
            }
            $spend_time = time() - $start_time;
            if ($spend_time > 1) {
                $date = date("Y-m-d");
                echo "[$date] child {$n} pid {$this->pid} spend time {$spend_time}." . PHP_EOL;
            }
        }
    }


    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestToutiaoCreativeComponentTask::class);
$multi_process->start();
