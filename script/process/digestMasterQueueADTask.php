<?php

use App\Logic\DSP\ADTaskMasterMQLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/Process.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 这个脚本不需要使用MQPross，不需要restart，只需要kill即可
 *
 */
class digestMasterQueueADTask extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    public function worker($n)
    {
        $ad_master_mq_logic = new ADTaskMasterMQLogic();
        while ($this->run) {
            try {
                $ad_master_mq_logic->digestTask();
            } catch (Throwable $e) {
                echo date('Y-m-d H:i:s') . '广告master队列出错' . $e->getMessage() . "\n";
            }
        }
    }
}

$multi_process = new MultiProcess(8, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestMasterQueueADTask::class);
$multi_process->start();
