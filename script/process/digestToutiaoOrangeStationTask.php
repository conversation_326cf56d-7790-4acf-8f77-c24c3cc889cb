<?php

use App\Logic\DSP\ToutiaoOrangeStationMQLogic;
use App\Model\RabbitModel\ToutiaoOrangeStationTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 头条橙子建站赠送新建
 *
 */
class digestToutiaoOrangeStationTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new ToutiaoOrangeStationTaskMQModel();
    }

    public function worker($data)
    {
        return (new ToutiaoOrangeStationMQLogic())->digestTask($data);
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestToutiaoOrangeStationTask::class);
$multi_process->start();
