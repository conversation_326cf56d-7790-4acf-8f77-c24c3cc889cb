#! /bin/bash


php=$(which php)
shellDir=$(dirname $(readlink -f "$0"))

logDir='/data/log'

barLen=50
g_s='>'
service_startup_timeout=900

if [ "$TERM" != "dumb" ]; then
  wrap="\r"
  failColor="\033[1;35;31m"
  successColor="\033[1;32;40m"
  infoColor="\033[0m"
fi




function progressBar() {
    str=$1
    strLen=$2
    s=$3
    times=$4
    while [ $times -gt 0 ]; do
        echo -ne "\r$str $s"
        s="=$s"
        let times--
        sleep 0.1
    done
}

function waitForPid() {
    pid="$1"
    i=0

    while test $i -ne $service_startup_timeout ; do
        ps -p $pid >> /dev/null 2>&1
        if [ $? -ne 0 ]; then
            break
        fi

        echo -n "."
        i=`expr $i + 1`
        sleep 1
    done
    echo ""
    if [ $i -eq $service_startup_timeout ]; then
        echo "wait too long. please check manually."
    fi
}

function startProcess() {
    findFile=$shellDir/$1
    for file in `find $findFile -maxdepth 1 -type f -name '*.php'`; do
        fileName=$(basename $file)
        processNum=$(ps -ef|grep "php $fileName"|grep -v $0|grep -v "grep"|wc -l);
        if [ $processNum -ne 0 ]; then
            echo "process $fileName is running."
            continue
        else
            echo "start process $fileName."
            logName=${fileName%.*}.log
            if [ $# -eq 2 ]; then
              $php $file $2 >> $logDir"/"$logName 2>&1
            else
              $php $file >> $logDir"/"$logName 2>&1
            fi
        fi
    done;
}

function stopProcess() {
    findFile=$shellDir/$1
    for file in `find $findFile -maxdepth 1 -type f -name '*.php'`; do
        file=$(basename $file)
        processNum=$(ps -ef|grep $file|grep -v $0|grep -v "grep"|wc -l);
        if [ $processNum -eq 0 ]; then
            continue
        fi
        # 只有一个进程，直接kill掉
        if [ $processNum -eq 1 ]; then
            for pid in `ps -ef|grep $file|grep -v $0|grep -v 'grep'|awk '{print $2}'`; do
                kill -TERM $pid
                echo "send SIGTERM signal to $file($pid)"
            done
            waitForPid $pid
            continue
        fi
        # 多个进程，找出父进程，kill父进程pid
        for pidInfo in `ps -ef|grep $file|grep -v $0|grep -v 'grep'|awk '{print $2"-"$3}'`; do
            pid=$(echo $pidInfo|cut -d "-" -f1)
            ppid=$(echo $pidInfo|cut -d "-" -f2)
            if [ $ppid -ne 1 ]; then
                continue
            fi
            kill -TERM $pid
            echo "send SIGTERM signal to $file(parent: $pid)"
            waitForPid $pid
        done
    done;
}

function killProcess() {
    findFile=$shellDir/$1
    for file in `find $findFile -maxdepth 1 -type f -name '*.php'`; do
        file=$(basename $file)
        processNum=$(ps -ef|grep $file|grep -v $0|grep -v "grep"|wc -l);
        if [ $processNum -eq 0 ]; then
            echo "kill $1 fail total $processNum"
            continue
        fi
        # 进程存在
        if [ $processNum -ge 1 ]; then
            processIdList=""

            for processId in `ps -ef | grep "$php $findFile" | grep -v 'grep' | awk '{print $2}'`; do
                processIdList="$processIdList $processId"
            done

            echo "kill -9 $processIdList"
            kill -9 $processIdList
        fi
    done;
}

function showStatus() {
    findFile=$shellDir/$1
    for file in `find $findFile -maxdepth 1 -type f -name '*.php'`; do
        # 获取脚本注释并截取
        code_comment=$(cat $file |grep 'script_desc')
        desc=${code_comment#*@script_desc}

        file=$(basename $file)
        fileLen=${#file}
        s=$g_s
        ((times=barLen-fileLen))
        for i in `seq 1 $times`; do s="=$s"; done

        if [ "$TERM" != "dumb" ]; then
          progressBar $file $fileLen $g_s $times
        fi

        processNum=$(ps -ef|grep $file|grep -v $0|grep -v "grep"|wc -l);
        if [ $processNum -eq 0 ]; then
            echo -e "$wrap$failColor × $file $infoColor $s$failColor $processNum not run. $infoColor  $desc"
        else
            echo -e "$wrap$successColor ✔ $file $infoColor $s$successColor $processNum running.$infoColor  $desc"
        fi
    done;
}

function showLog() {
    file=$1
    logName=${file%.*}.log
    fullName=$logDir"/"$logName
    tail -f $fullName
}

function showParamError() {
    echo "bash $0 start|stop|restart|status|show-log"
}

if [ $# -lt 1 ]; then
    showParamError
    exit
fi

case $1 in
    start)
        if [ $# -eq 2 ]; then
            startProcess $2
        else
           if [ $# -eq 3 ]; then
              startProcess $2 $3
           else
              startProcess
           fi
        fi
    ;;
    stop)
        if [ $# -lt 2 ]; then
            echo -e "\033[1;35;31m× stop 必须指定进程\033[0m"
            exit
        fi
        stopProcess $2
    ;;
    kill)
        if [ $# -lt 2 ]; then
            echo -e "\033[1;35;31m× stop 必须指定进程\033[0m"
            exit
        fi
        killProcess $2
    ;;
    status)
        showStatus $2
    ;;
    cold-restart)
      if [ $# -lt 2 ]; then
          echo -e "\033[1;35;31m× stop 必须指定进程\033[0m"
          exit
      fi
      killProcess $2

      if [ $# -eq 3 ]; then
         startProcess $2 $3
      else
         startProcess $2
      fi
    ;;
    restart)
        if [ $# -lt 2 ]; then
            echo -e "\033[1;35;31m× stop 必须指定进程\033[0m"
            exit
        fi
        stopProcess $2

        if [ $# -eq 3 ]; then
           startProcess $2 $3
        else
           startProcess $2
        fi
    ;;
    show-log)
        if [ $# -lt 2 ]; then
            echo -e "\033[1;35;31m× show-log 必须指定进程\033[0m"
            exit
        fi
        showLog $2
    ;;
    *)
        showParamError
        exit
    ;;
esac
