<?php

use App\Logic\DSP\ToutiaoBirdClueProfileMQLogic;
use App\Model\RabbitModel\ToutiaoBirdClueProfileTaskMQModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';
require dirname(__DIR__) . '/process/core/MultiProcess.php';
require dirname(__DIR__) . '/process/core/RabbitMQProcess.php';

date_default_timezone_set('PRC');

/**
 * @script_desc 头条青鸟线索新建
 *
 */
class digestToutiaoBirdClueProfileTask extends RabbitMQProcess
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    protected function setMQModel($idx)
    {
        $this->mq_model = new ToutiaoBirdClueProfileTaskMQModel();
    }

    public function worker($data)
    {
        return (new ToutiaoBirdClueProfileMQLogic())->digestTask($data);
    }
}

$multi_process = new MultiProcess(2, true);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, digestToutiaoBirdClueProfileTask::class);
$multi_process->start();
