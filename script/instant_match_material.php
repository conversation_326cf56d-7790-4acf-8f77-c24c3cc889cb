<?php

use App\Exception\AppException;
use App\Logic\DSP\MaterialShareRuleMQLogic;
use App\Model\SqlModel\DataMedia\OdsRankMaterialModel;
use App\Model\SqlModel\Zeda\MaterialModel;
use App\Model\SqlModel\Zeda\MaterialShareRuleModel;
use App\MysqlConnection;
use App\Task\MaterialTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();

/**
 * 处理素材匹配即时共享规则
 */
function handleInstantRule()
{
    $params = getopt('p:');
    if (!isset($params['p'])) {
        print_r('-p传入需要匹配素材规则的平台'.PHP_EOL);
        die;
    }
    $platform = $params['p'];
    $share_rule_model = new MaterialShareRuleModel();
    $share_rules = $share_rule_model->getAllByRuleType(MaterialShareRuleModel::INSTANT_RULE_TYPE);
    if ($share_rules->isEmpty()) {
        $msg = __FUNCTION__ . '-getAllByRuleType() 方法无即时共享规则数据';
        Helpers::getLogger('material-match')->error($msg);
        throw new AppException($msg);
    }
    $material_model = new MaterialModel();
    $material_list = $material_model->getAll(false, $platform);
    if (!$material_list) {
        $msg = __FUNCTION__ . '-getAllJoinMaterialFile() 方法无素材数据';
        Helpers::getLogger('material-match')->error($msg);
        throw new AppException($msg);
    }

    $ods_rank_material_insert = [];
    ob_start();
    foreach ($share_rules as $key => $rule) {
        $share_rule_model->updateRuleMatchStatus($rule->id, [
            'match_status' => MaterialShareRuleModel::RUNNING_MATCH
        ]);
        $rule = MaterialTask::transJsonToArray($rule);
        foreach ($material_list as $k => $material) {
            //因为遍历对象是引用传值，为了防止重复修改对象所以只在第一次进行转换
            if (0 === $key) {
                $material = MaterialTask::transJsonToArray($material);
            }
            $result = MaterialTask::validateInstantRulesAndHandleMatch($rule, $material);
            if (false === $result) {
                continue;
            }
            $ods_rank_material_insert[] = $result;
            if (is_int($k / 100)) {
                echo "素材规则ID：$rule->id, 素材ID：$material->material_id 匹配完成" . PHP_EOL;
                ob_flush();
            }
        }
        //更新规则状态为匹配完成
        $share_rule_model->updateRuleMatchStatus($rule->id, [
            'last_match_time' => time(),
            'match_status' => MaterialShareRuleModel::MATCH_COMPLETE
        ]);
    }
    if ($ods_rank_material_insert) {
        $ods_rank_material_insert = array_merge(...$ods_rank_material_insert);
//        (new MaterialShareRuleMQLogic())->produceRankMaterialInsertDataTask($rank_material_insert);
//        (new MaterialShareRuleMQLogic())->produceOdsRankMaterialInsertDataTask($ods_rank_material_insert);
        $ods_rank_material_model = new OdsRankMaterialModel();
        $ods_rank_material_model->add($ods_rank_material_insert);
    }

    print_r('Instant Rules Match Finish');
    ob_end_flush();
}
handleInstantRule();