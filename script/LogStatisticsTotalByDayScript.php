<?php
/*
 * 操作日志分析
 */

use App\Model\SqlModel\Tanwan\DMSUserLogStatisticModel;
use App\MysqlConnection;
use App\ElasticsearchConnection;
use App\Model\SqlModel\Zeda\RankPlatformModel;
use App\Model\SqlModel\Zeda\UserLevelModel;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();
ElasticsearchConnection::setConnection();

class LogStatisticsTotalByDayScript
{
    public $debug = 1;
    public $start_date_time;
    public $end_date_time;
    public $start_date_time_format;
    public $end_date_time_format;
    public $es_group_by_size;//es聚合分组默认个数，es默认是10条

    /**
     * @var array 等级对应平台表
     * @example
     * [
     *      '{level}-{rank_id}' => ['TW', 'WZ'],
     *      '1-1' => ['TW', 'WZ'],
     * ]
     */
    public $rank_platform_data;

    /**
     * @var array 用户所属module及关系
     * @example
     * [
     *      'dsp-1' => [
     *                      'module' => 'dsp',
     *                      'user_id' => 1,
     *                      'level' => 1',
     *                      'rank_id' => 2',
     *                      'platform' => '贪玩',
     *                      'department' => '手游运营',
     *                      'department_group' => '运营广丰组',
     *                      'department_group_position' => '运营B组',
     *                 ]
     * ]
     */
    public $user_level_data;

    public function __construct($start_date_time, $end_date_time)
    {
        $this->start_date_time = $start_date_time;
        $this->end_date_time = $end_date_time;

        if (!$this->start_date_time || !$this->end_date_time) {
            echo "缺少日期时间".PHP_EOL;die;
        }

        echo "rank_platform、user_level 数据准备ing".PHP_EOL;
        $this->formatDateTime();
        $this->getRankPlatformData();
        $this->getUserLevelData();
    }

    protected function formatDateTime()
    {
        //只查询日志发生时间前后的10秒，提高查询效率。 es查询的时区问题（差8小时），需要将时间回退且按下方的格式
        $this->start_date_time_format = date('Y-m-d\TH:i:s', strtotime($this->start_date_time)-(3600*8));
        $this->end_date_time_format = date('Y-m-d\TH:i:s', strtotime($this->end_date_time)-(3600*8));
    }

    protected function getGroupByDataFromEs()
    {
        $query_condition = [
            'size' => 0,
            "query" => [
                'bool' => [
                    'must' => [
                        [
                            'range' => [
                                "@timestamp" => [
                                    //时间格式：2022-05-01T00:00:00
                                    "gte" => $this->start_date_time_format,
                                    "lt" => $this->end_date_time_format
                                ]
                            ]
                        ]
                    ],
                    'must_not' => [
                        // 操作日志排除api调用的数据
                        [
                            'term' => [
                                'module' => 'api'
                            ]
                        ],
                        // 排除module为空的，因为module为空的话没法做后面的统计
                        [
                            'term' => [
                                'module' => 'null'
                            ]
                        ]
                    ]
                ]
            ],
            "aggs" => [
                "result" => [
                    "terms" => [
                        "script" => [
                            "inline" => "doc['module.keyword'].toString() + '|' + doc['user_id.keyword'].toString() + '|' + doc['user_name.keyword'].toString() + '|' + doc['p_router.keyword'].toString() + '|' + doc['router.keyword'].toString() + '|' + doc['api.keyword'].toString() + '|' + doc['op_type.keyword'].toString()",
                            "lang" => "painless"
                        ],
                        'size' => $this->es_group_by_size ?? 100
                    ]
                ]
            ]
        ];

        return (new \App\Model\ElasticSearchModel\LogModel())->getAggregationsData($query_condition);
    }

    protected function getRankPlatformData()
    {
        $rank_platform_model = (new RankPlatformModel());
        $rank_platform_data = $rank_platform_model->getAll()->toArray();
        foreach ($rank_platform_data as $item) {
            $key = "{$item->level}-{$item->rank_id}";
            $list[$key][] = $item->platform;
        }

        $this->rank_platform_data = $list ?? [];
    }

    protected function getUserLevelData()
    {
        $user_level_model = (new UserLevelModel());
        $all_user_level = $user_level_model->getAllUserLevel();
        foreach ($all_user_level as $user_data) {
            $rank_id = 0;
            if ($user_data->level == 1) {
                $rank_id = $user_data->platform_id;
            }

            if ($user_data->level == 2) {
                $rank_id = $user_data->department_id;
            }

            if ($user_data->level == 3) {
                $rank_id = $user_data->department_group_id;
            }

            if ($user_data->level == 4) {
                $rank_id = $user_data->department_group_position_id;
            }

            if ($user_data->level == 5) {
                $rank_id = $user_data->department_group_position_worker_id;
            }

            $key = "{$user_data->module}-{$user_data->user_id}";

            $list[$key] = [
                'module' => $user_data->module,
                'user_id' => $user_data->user_id,
                'level' => $user_data->level,
                'rank_id' => $rank_id,
                'platform' => $user_data->platform_name,
                'department' => $user_data->department_name,
                'department_group' => $user_data->department_group_name,
                'department_group_position' => $user_data->department_group_position_name,
            ];
        }

        $this->user_level_data = $list ?? [];
    }

    public function operateLogStatistic()
    {
        $result = $this->getGroupByDataFromEs();

        foreach ($result as $item) {
            // 格式化es聚合数据 例子：[dms]|[5005]|[严小莹]|[/dms/market]|[/dms/market/overview]|[/dms/market/getExceptionList]|[2]
            $data = explode('|', $item['key']);
            foreach ($data as $key => $datum) {
                $datum = str_replace(']', '', $datum);
                $datum = str_replace('[', '', $datum);
                $data[$key] = $datum;
            }

            list($module, $user_id, $username, $p_router, $router, $api, $type) = $data;
            $count = $item['doc_count'];

            if (empty($user_id) || $user_id == 'null') {
                continue;
            }

            $unique_key = "{$module}-$user_id";
            $level = $this->user_level_data[$unique_key]['level'] ?? 0;
            $rank_id = $this->user_level_data[$unique_key]['rank_id'] ?? 0;
            $rank_key = "{$level}-{$rank_id}";

            $new_result[] = [
                'date' => date('Y-m-d', strtotime($this->start_date_time)),
                'module' => $module,
                'user_id' => $user_id,
                'username' => $username,
                'p_router' => $p_router,
                'router' => $router,
                'api' => $api,
                'type' => (int)$type,
                'num' => $count,
                // 职位相关参数
                'level' => $level,
                'rank_id' => $rank_id,
                'platform' => $this->user_level_data[$unique_key]['platform'] ?? '',
                'department' => $this->user_level_data[$unique_key]['department'] ?? '',
                'department_group' => $this->user_level_data[$unique_key]['department_group'] ?? '',
                'department_group_position' => $this->user_level_data[$unique_key]['department_group_position'] ?? '',
                'data_platform' => implode(',', $this->rank_platform_data[$rank_key] ?? []),
            ];

        }
        $this->addLogStatisticsData($new_result ?? []);
    }

    public function addLogStatisticsData(array $insert_data)
    {
        if ($this->debug == 1) return ;
        try {
            $log_statistics_model = new DMSUserLogStatisticModel();
            $log_statistics_model->add($insert_data);
        } catch (Exception $exception) {
            echo $exception->getMessage() . PHP_EOL;
        }
    }
}

$last_date = date('Y-m-d', strtotime("-1 day"));
$start_date = $argv[1] ?? $last_date;
echo '-------开始任务' . $start_date.PHP_EOL;

$start_date_time = date('Y-m-d 00:00:00', strtotime($start_date));
$end_date_time = date('Y-m-d 23:59:59', strtotime($start_date));

$class = new LogStatisticsTotalByDayScript($start_date_time, $end_date_time);
$class->es_group_by_size = 1000000;
$class->debug = 0;
echo "操作日志整理".PHP_EOL;
$class->operateLogStatistic();
echo '--------完成任务' . $start_date.PHP_EOL;

