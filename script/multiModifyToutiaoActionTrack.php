<?php

use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

$sql = <<< EoT

SELECT
account_id,
creator,
        site_id,
        agent_id,
        JSON_EXTRACT( site_config, "$.game_id" ) AS game_id,
        ad2_id,
        update_time,
        JSON_EXTRACT( site_config, "$.action_track_url" ) AS action_track_url,
        CONCAT(
                REPLACE ( JSON_EXTRACT( site_config, '$.action_track_url' ), '"', '' ),
                '&appid=',
        REPLACE ( JSON_EXTRACT( site_config, "$.game_id" ), '"', '' ),
        '&adid=',
        REPLACE ( JSON_EXTRACT( site_config, "$.agent_id" ), '"', '' ),
        '&cid=',
        REPLACE ( JSON_EXTRACT( site_config, "$.site_id" ), '"', '' )
        ) AS real_action_track_url 
FROM
        `zeda`.`ad_task` 
WHERE
        media_agent_type = 0 
        AND platform = 'wanzi' 
        AND media_type = 1 
        AND update_time BETWEEN '2022-10-17 11:00' 
        AND '2022-10-17 11:50' 
        AND state_code = 9 
HAVING
        locate( 'appid', action_track_url ) = 0

EoT;

MysqlConnection::setConnection();

$data = MysqlConnection::getConnection()->select($sql);
$data = collect($data);
$account_ids = $data->pluck('account_id')->unique();
$access_token = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids)->keyBy('account_id');
//var_dump($data, $access_token);return;
$model = new AdModel();
foreach ($data as $value) {
    $req_data['advertiser_id'] = $value->account_id;
    $req_data['ad_id'] = $value->ad2_id;
    $req_data['action_track_url'] = [$value->real_action_track_url];
    try {
        $model->update($req_data, $access_token[$value->account_id]->access_token);
    }catch (AppException $e) {
        file_put_contents('/Users/<USER>/Downloads/1.txt', '广告：' . $value->ad2_id . '错误信息：' . $e->getMessage(), FILE_APPEND);
    }
}

return;
