<?php


use App\Controller\DMS\CostInputController;
use App\Logic\LogEsLogic;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\Feishu\Aily\ChatModel;
use App\Model\SqlModel\Zeda\EnterpriseDidiEmployeeModel;
use App\Model\SqlModel\Zeda\RankPermissionAll1Model;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Model\SqlModel\Zeda\RankRoutePermissionModel;
use App\MysqlConnection;
use App\Param\LogSearchParam;
use App\Service\EnterpriseDiDiService;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


// 设置MySQL连接对象
MysqlConnection::setConnection();
$chat_model = new ChatModel();
$redis = \App\Struct\RedisCache::getInstance();
\App\ElasticsearchConnection::setConnection();


// 创建申请单
$employee_no = 'TW0985';
$perorder_money_quota = 10000; // 多少钱自己填
$data = (new EnterpriseDidiEmployeeModel())->getData($employee_no);
if ($data->isEmpty()) {
    var_dump('不在员工配置表里面');
    exit;
}

$data = (array)$data->first();
// 创建申请单
$business_trip_detail = [
    'start_time'           => date("Y-m-d H:i:s"),
    'end_time'             => date("Y-m-d H:i:s", strtotime("+8 hour")),
    'perorder_money_quota' => $perorder_money_quota,
    'trip_times'           => 1,
];

$result = (new ApprovalModel())->create($data['company'], $data['regulation_id'], $data['phone'], $employee_no, 3, $business_trip_detail);

var_dump($result);
exit;

// 取消申请单
$service = new EnterpriseDiDiService();
$approval_id_list = [
    "1125965843132999",
];
$service->cancelApproval($approval_id_list);

echo "取消成功", PHP_EOL;
exit;


