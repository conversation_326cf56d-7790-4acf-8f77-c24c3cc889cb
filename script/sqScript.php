<?php

use App\MysqlConnection;
use App\Constant\MediaType;
use App\Service\MediaAD\MediaBaidu;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

$now = date('Y-m-d H:i:s');

echo "任务开始$now", PHP_EOL;

// 设置MySQL连接对象
MysqlConnection::setConnection();
$connection_data_media = 'data_media';
$list = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_media_sdk')
    ->where(['media_type' => MediaType::BAIDU])
    ->get();

$media_baidu = new MediaBaidu();

foreach ($list as $value) {
    $ext_data = json_decode($value->ext, true);
    if ($ext_data) {
        echo "{$value->game_id} start" . PHP_EOL;
        $data = $ext_data;
        $data['app_logo'] = '';
        $data['app_screen_shots'] = [];
        $data['copyright'] = [];
        if (strpos($ext_data['app_logo'], "baidu.com") === false) {
            $data['app_logo'] = $ext_data['app_logo'] ? $media_baidu->uploadSdkImage($ext_data['app_logo']) : '';
            sleep(1);
        } else {
            $data['app_logo'] = $ext_data['app_logo'];
        }
        if ($ext_data['copyright']) {
            foreach ($ext_data['copyright'] as $copyright) {
                if (strpos($copyright, "baidu.com") === false) {
                    $data['copyright'][] = $media_baidu->uploadSdkImage($copyright);
                    sleep(1);
                } else {
                    $data['copyright'][] = $copyright;
                }

            }
        }
        if ($ext_data['app_screen_shots']) {
            foreach ($ext_data['app_screen_shots'] as $app_screen_shots) {
                if (strpos($app_screen_shots, "baidu.com") === false) {
                    $data['app_screen_shots'][] = $media_baidu->uploadSdkImage($app_screen_shots);
                    sleep(1);
                } else {
                    $data['app_screen_shots'][] = $app_screen_shots;
                }

            }
        }
        $value->ext = json_encode($data);
        echo "{$value->game_id} finish" . PHP_EOL;
        MysqlConnection::getConnection($connection_data_media)
            ->table('ods_media_sdk')
            ->where(['media_type' => MediaType::BAIDU])
            ->where(['game_id' => $value->game_id, 'platform' => $value->platform])
            ->update(['ext' => $value->ext, 'update_time' => date('Y-m-d H:i:s')]);
    }
}


$now = date('Y-m-d H:i:s');;


echo "任务完成$now", PHP_EOL;

