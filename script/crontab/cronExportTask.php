<?php
/**
 * 0 8 * * *  php cronExportTask.php
 */

use App\Constant\RouteID;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\MysqlConnection;
use App\Param\ExportFileRecodeParam;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
// 设置MySQL连接对象
MysqlConnection::setConnection();

$task_config = [
    '付费预估' => [
        'route_id' => RouteID::PAY_ESTIMATE,
        'aggregation_time' => '按日',
        'dimension' => ["platform", "root_game_id", "os"],
        'dimension_type' => 2,
        'download_filename' => '付费预估 - 数据中心报表-' . date("YmdHis"),
        'start_time' => date("Y-m-d", strtotime('-90 day')),
        'end_time' => date("Y-m-d", strtotime('-8 day')),
        'ios_addition' => 0,
        'target' => ["day_1", "day_3", "day_7", "day_15", "day_30", "day_60", "day_90", "day_120", "day_180", "day_240", "day_300", "day_360"],
        'user_id' => 4125,
        'type' => '回本率',
    ],
    '按子-付费预估' => [
        'route_id' => RouteID::PAY_ESTIMATE,
        'aggregation_time' => '按日',
        'dimension' => ["platform", "root_game_id", "os"],
        'dimension_type' => 1,
        'download_filename' => '付费预估按子 - 数据中心报表-' . date("YmdHis"),
        'start_time' => date("Y-m-d", strtotime('-90 day')),
        'end_time' => date("Y-m-d", strtotime('-8 day')),
        'ios_addition' => 0,
        'target' => ["day_1", "day_3", "day_7", "day_15", "day_30", "day_60", "day_90", "day_120", "day_180", "day_240", "day_300", "day_360"],
        'user_id' => 4125,
        'type' => '回本率',
    ],

    '流水预估' => [
        'route_id' => RouteID::FLOW_ESTIMATE,
        'aggregation_time' => '按日',
        'dimension' => ["platform", "plat_id", "root_game_id", "main_game_id"],
        'download_filename' => '流水预估 - 数据中心报表-' . date("YmdHis"),
        'start_date' => date("Y-m-01"),
        'end_date' => date("Y-m-t"),
        'cost_start_date' => '2015-01-01',
        'cost_end_date' => date("Y-m-t"),
        'flow_type' => 1,
        'estimate_trend' => 1,
        'user_id' => 4125,
    ],
];

$logger = Helpers::getLogger('cron_export');
foreach ($task_config as $name => $config_param) {
    try {
        add_export_task($config_param);
        $logger->info($name . '任务添加完成');
    } catch (\Throwable $exception) {
        $logger->err($name . '任务添加失败，失败信息:' . $exception->getMessage());
    }

}


/**
 * 添加导出任务
 */
function add_export_task($data)
{
    $route_id = $data['route_id'];
    $user_id = $data['user_id'];

    // 下载的文件名
    $download_filename = $data['download_filename'] . '.csv';
    // 源文件名称
    $source_filename = 'market/' . date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . $user_id) . '.csv';


    // 入库的数据
    $data = [
        'param' => json_encode($data),
        'user_id' => $user_id,
        'route_id' => $route_id,
        'download_filename' => $download_filename,
        'source_filename' => $source_filename,
    ];
    $file_param = new ExportFileRecodeParam($data);

    $model = new ExportFileTaskModel();

    $model->add($file_param);
}
