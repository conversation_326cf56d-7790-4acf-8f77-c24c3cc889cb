<?php
/**
 * 1 12 * * *
 * 日志归档
 */

use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$log_map = [
    parse_url(EnvConfig::DOMAIN)['host'] => LOG_DIR . '/*.log',
];

foreach ($log_map as $dir_name => $log_path) {
    $archive_path = "/data/log/{$dir_name}";
    $log_files = glob($log_path);
    $the_date_of_last_month = strtotime('-1 month', strtotime(date('Y-m-d 00:00:00')));
    foreach ($log_files as $log) {
        $log_date = basename($log, ".log");
        $log_timestamp = strtotime($log_date);
        if ($log_timestamp === false) {
            // 不是日期文件 无需归档
            continue;
        }
        if ($log_timestamp < $the_date_of_last_month) {
            $log_year = date('Y', $log_timestamp);
            $log_month = date('n', $log_timestamp);
            $log_day = date('j', $log_timestamp);

            $archive_month_path = implode('/', [$archive_path, $log_year, $log_month]);
            if (!is_dir($archive_month_path)) {
                mkdir($archive_month_path, 0755, true);
            }
            $archive_log = "$archive_month_path/$log_day.log";
            rename($log, $archive_log);
        }
    }
}