<?php
/**
 * 同步所有的分成比例到ADB
 *
 * Created by PhpStorm.
 * User: Melody
 * Date: 2021/4/16
 * Time: 10:53
 */

use App\Logic\DMS\OperationProfitLogic;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$logic = new OperationProfitLogic();

Helpers::getLogger('sync-divide-percent')->info('任务开始');
$start_date = 1393344000;
$to_date = strtotime(date("Y-m-d", time())) + 86400;     // 第二天0点 也就是要跑当天的数据
try {
    $logic->syncDividePercentToADB($start_date, $to_date);
} catch (\Throwable $exception) {
    Helpers::getLogger('sync-divide-percent')->err('同步失败，失败详情：' . $exception->getMessage());
}

