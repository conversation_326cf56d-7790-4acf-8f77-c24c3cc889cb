<?php

//
// 定时同步 game表
// */5 * * * * php
//


use App\Model\SqlModel\Zeda\V2DimGameIdModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
$logger = \App\Utils\Helpers::getLogger('sync-game');

$logger->info('任务开始');


try {

    MysqlConnection::setConnection();
    $time = date('Y-m-d H:i:s', time());

    $update_time = \App\Struct\RedisCache::getInstance()->get('sync-game-time');
    if (!$update_time) {
        // 随便给个初始化时间，反正已经全量同步过了
        $update_time = '2024-01-01 00:00:00';
    }

    $all_list = (new \App\Model\SqlModel\Tanwan\V2DimGameIdModel())->getListByTime($update_time);
    $logger->info('数据获取结束,开始入表');
    $data = [];
    foreach ($all_list as $item) {
        $data[] = [
            'plat_id'                => $item->plat_id,
            'plat_name'              => $item->plat_name,
            'root_game_id'           => $item->root_game_id,
            'root_game_name'         => $item->root_game_name,
            'main_game_id'           => $item->main_game_id,
            'main_game_name'         => $item->main_game_name,
            'game_id'                => $item->game_id,
            'game_name'              => $item->game_name,
            'platform'               => $item->platform,
            'os'                     => $item->os,
            'is_channel'             => $item->is_channel,
            'is_sale'                => $item->is_sale,
            'app_name'               => $item->app_name,
            'app_package_name'       => $item->app_package_name,
            'sale_agent'             => $item->sale_agent,
            'app_id'                 => $item->app_id,
            'contract_game_name'     => $item->contract_game_name,
            'audit_type'             => $item->audit_type,
            'contract_game_id'       => $item->contract_game_id,
            'ext'                    => $item->ext,
            'package_size'           => $item->package_size,
            'permission_information' => $item->permission_information,
            'real_app_version'       => $item->real_app_version,
            'clique_id'              => $item->clique_id,
            'clique_name'            => $item->clique_name,
            'team'                   => $item->team,
            'creator'                => $item->creator,
            'back_type'              => $item->back_type,
        ];
    }

    (new V2DimGameIdModel())->replace($data);

    // 设置一下 update_time
    \App\Struct\RedisCache::getInstance()->set('sync-game-time', $time);

    $logger->info('入表结束,任务完成');

} catch (\Throwable $exception) {
    $logger->error('game表同步任务出错', ['message' => $exception->getMessage()]);
}