<?php

/*
 * 0 4 * * *
 * 产品评级
 */

use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

echo date('Y-m-d H:i:s') . '--scoring product begin' . PHP_EOL;

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_hub = 'datahub';

$product_score_model = MysqlConnection::getConnection($connection_name)->table('product_score')->where(['is_del' => 0]);
$product_score_rule = $product_score_model->get();

foreach ($product_score_rule as $value) {
    //已产生过的
    if ($value->gen_time) {
        continue;
    }
    //结束时间还在7天内
    if ((time() - strtotime($value->end_date.' 23:59:59')) < 86400*$value->days ) {
        continue;
    }

    //
    $platform = $value->platform;
    $os = $value->os;
    $root_game_id = $value->root_game_id;
    $start_date = $value->start_date;
    $end_date = $value->end_date;
    $days = $value->days;
    $num = (int) (strtotime($end_date) - strtotime($start_date)) / 86400;
    $cost = 10000 * min(5, $num);
    $datas = [];

    //对比的游戏列表
    //$compare_games = MysqlConnection::getConnection($connection_data_hub)->select();
    //$root_game_ids = array_map(function ($item){return $item->root_game_id;}, $compare_games);
    $query = "WITH tt AS (
        SELECT * FROM 
        (
            SELECT
                ad.platform,
                game.root_game_id,
                game.os,
                SUM( ad.money ) AS cost_money,
                RANK() OVER( ORDER BY SUM( ad.money ) DESC) AS rank
            FROM
                v2_dwd_day_cost_log AS ad
                INNER JOIN v2_dim_game_id AS game ON ad.platform = game.platform
                AND ad.game_id = game.game_id
                INNER JOIN v2_dim_agent_id AS agent ON ad.platform = agent.platform
                AND ad.agent_id = agent.agent_id
                AND ad.tdate BETWEEN agent.agent_leader_start_time
                AND agent.agent_leader_end_time
            WHERE
                game.os = '". $os ."'
                AND ad.tdate BETWEEN '". $start_date ."' AND '". $end_date. "'
            HAVING
                cost_money >= ". $cost ."
                OR ( ad.platform = '".$platform."' AND game.root_game_id = ".$root_game_id." )    
            GROUP BY
                ad.platform,
                game.root_game_id,
                game.os
            ORDER BY
                game.os,
                cost_money DESC
        )
        HAVING
            rank <= 10
            OR ( platform = '".$platform."' AND root_game_id = ".$root_game_id." )
    ) ";

    //付费友好
    $pay_friendly_targets_sql = $query . "SELECT
           platform,
           os,
           root_game_id,
           SUM( IF ( total_pay BETWEEN 1 AND 100, 1, 0 ) ) AS '1~100',
           SUM( IF ( total_pay BETWEEN 1 AND 100, 1, 0 ) ) / COUNT( uid ) AS low_pay_account,
           SUM( IF ( total_pay BETWEEN 101 AND 1000, 1, 0 ) ) AS '101~1000',
           SUM( IF ( total_pay BETWEEN 101 AND 1000, 1, 0 ) ) / COUNT( uid ) AS mid_pay_account,
           SUM( IF ( total_pay > 1000, 1, 0 ) ) AS '>1000',
           SUM( IF ( total_pay > 1000, 1, 0 ) ) / COUNT( uid ) AS high_pay_account,
           COUNT( uid ) AS pay_uid_count
        FROM
           (
           SELECT
              pay.platform,
              game.os,
              game.root_game_id,
              pay.uid AS uid,
              SUM( pay.pay_money ) AS total_pay
           FROM
              v2_ods_pay_order_log AS pay
              INNER JOIN v2_dim_game_id AS game ON pay.game_id = game.game_id
              AND pay.platform = game.platform
              INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON pay.platform = reg.platform
              AND game.root_game_id = reg.root_game_id
              AND pay.uid = reg.uid
              INNER JOIN v2_dim_agent_id AS ag ON pay.agent_id = ag.agent_id
              AND ag.platform = pay.platform
              AND reg.root_game_reg_time BETWEEN ag.agent_leader_start_time
              AND ag.agent_leader_end_time
              INNER JOIN tt AS dd ON pay.platform = dd.platform
		      AND game.root_game_id = dd.root_game_id
           WHERE
              game.os = '". $os ."'
              AND pay.order_status_id = 1
              AND DATE( reg.root_game_reg_time ) BETWEEN '". $start_date ."' AND '". $end_date. "'
              AND DATE( pay.pay_time ) BETWEEN '". $start_date ."' AND DATE_ADD('". $end_date. "',INTERVAL ". $days ." DAY)
           GROUP BY
              pay.platform,
              pay.os,
              game.root_game_id,
              pay.uid
           )
        GROUP BY
           platform,
           os,
           root_game_id";
    $pay_friendly_targets = MysqlConnection::getConnection($connection_data_hub)->select($pay_friendly_targets_sql);
    $average_pay_low = $average_pay_mid = $average_pay_high = [];
    $self_low_pay = $self_mid_pay = $self_high_pay = 0;
    foreach ($pay_friendly_targets as $target) {
        $average_pay_low[] = $target->low_pay_account;
        $average_pay_mid[] = $target->mid_pay_account;
        $average_pay_high[] = $target->high_pay_account;
        if ((int)$target->root_game_id === (int)$root_game_id && $target->platform === $platform) {
            $self_low_pay = $target->low_pay_account;
            $self_mid_pay = $target->mid_pay_account;
            $self_high_pay = $target->high_pay_account;
        }
        $datas[$target->platform .'-'. $target->root_game_id]['product_score_id'] = $value->id;
        $datas[$target->platform .'-'. $target->root_game_id]['platform'] = $target->platform;
        $datas[$target->platform .'-'. $target->root_game_id]['os'] = $target->os;
        $datas[$target->platform .'-'. $target->root_game_id]['root_game_id'] = $target->root_game_id;
        $root_game = (new V2DimGameIdModel())->getDataByRootGameId($target->platform, $target->root_game_id);
        $datas[$target->platform .'-'. $target->root_game_id]['root_game_name'] = $root_game->root_game_name;
        $datas[$target->platform .'-'. $target->root_game_id]['low_pay_account'] = $target->low_pay_account;
        $datas[$target->platform .'-'. $target->root_game_id]['mid_pay_account'] = $target->mid_pay_account;
        $datas[$target->platform .'-'. $target->root_game_id]['high_pay_account'] = $target->high_pay_account;
    }
    //低、中、高付费占比是否高于平均值
    $pay_friendly_low = ( $self_low_pay > array_sum($average_pay_low) / count($average_pay_low)) ? 1 : 0;
    $pay_friendly_mid = ( $self_mid_pay > array_sum($average_pay_mid) / count($average_pay_mid)) ? 1 : 0;
    $pay_friendly_high = ( $self_high_pay > array_sum($average_pay_high) / count($average_pay_high)) ? 1 : 0;
    //var_dump($pay_friendly_low, $pay_friendly_mid, $pay_friendly_high);


    //付费上限
    $pay_limit_top_sql = $query . "SELECT
        platform,
        os,
        root_game_id,
        avg( total_pay ) AS total_pay_avg,
        stddev( total_pay ) AS total_pay_stddev,
        min( total_pay ) AS total_pay_min,
        max( total_pay ) AS total_pay_max,
        count( uid ) AS top_pay_uid_count
    FROM
	(
        SELECT
            t.*,
            row_number ( ) OVER ( PARTITION BY platform, os, root_game_id ORDER BY t.total_pay DESC ) AS rank,
            COUNT( t.uid ) OVER ( PARTITION BY platform, os, root_game_id ) AS num
        FROM
            (
            SELECT
                pay.platform,
                game.os,
                game.root_game_id,
                pay.uid AS uid,
                SUM( pay.pay_money ) AS total_pay
            FROM
                v2_ods_pay_order_log AS pay
                INNER JOIN v2_dim_game_id AS game ON pay.game_id = game.game_id
                AND pay.platform = game.platform
                INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON pay.platform = reg.platform
                AND game.root_game_id = reg.root_game_id
                AND pay.uid = reg.uid
                INNER JOIN v2_dim_agent_id AS ag ON pay.agent_id = ag.agent_id
                AND ag.platform = pay.platform
                AND reg.root_game_reg_time BETWEEN ag.agent_leader_start_time
                AND ag.agent_leader_end_time
                INNER JOIN tt AS dd ON pay.platform = dd.platform
                AND game.root_game_id = dd.root_game_id
            WHERE
                game.os = '". $os ."'
                AND pay.order_status_id = 1
                AND DATE( reg.root_game_reg_time ) BETWEEN '". $start_date ."' AND '". $end_date. "'
                AND DATE( pay_time ) BETWEEN '". $start_date ."' AND DATE_ADD('". $end_date. "',INTERVAL ". $days ." DAY)
            GROUP BY
                pay.platform,
                game.os,
                game.root_game_id,
                pay.uid
            ) AS t
        ORDER BY
            t.platform,
            t.os,
            t.root_game_id,
            t.total_pay DESC,
            rank
    )
    WHERE
        rank <= (num / 10)
    GROUP BY
        platform,
        os,
        root_game_id
    ORDER BY 
	    total_pay_avg DESC";
    $pay_limit_top_ranks = MysqlConnection::getConnection($connection_data_hub)->select($pay_limit_top_sql);
    $i = 0;
    foreach ($pay_limit_top_ranks as $item) {
        $i++;
        $datas[$item->platform .'-'. $item->root_game_id]['pay_limit_top'] = $item->total_pay_avg;
        $datas[$item->platform .'-'. $item->root_game_id]['pay_limit_top_rank'] = $i;
    }
    $rank = array_search($platform .'-'. $root_game_id, array_map(function ($item){
        return $item->platform .'-'. $item->root_game_id;
    },
    $pay_limit_top_ranks)) + 1;
    $total = count($pay_limit_top_ranks);
    $high = intdiv($total, 3);
    $mid = round($total * (2/3));
    $pay_limit_top = $rank <= $high ? 3 : ($rank <= $mid ? 2 : 1);


    //付费频率
    $pay_frequency_sql = $query . "SELECT
        platform,
        os,
        root_game_id,
        pay_days,
        COUNT( uid ) AS pay_uid_count,
        SUM( pay_times ) AS pay_times_count,
        ROUND( avg( pay_times ), 2 ) AS avg_pay_times
    FROM
        (
        SELECT
            pay.platform,
            game.os,
            game.root_game_id,
            pay.uid,
            DATE( reg.root_game_reg_time ) AS reg_date,
            DATE( pay.pay_time ) AS pay_date,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) ) AS pay_days,
            COUNT( DISTINCT pay.order_id ) AS pay_times
        FROM
            v2_ods_pay_order_log AS pay
            INNER JOIN v2_dim_game_id AS game ON pay.platform = game.platform
            AND pay.game_id = game.game_id
            INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON pay.platform = reg.platform
            AND game.root_game_id = reg.root_game_id
            AND pay.uid = reg.uid
            INNER JOIN v2_dim_agent_id AS agent ON pay.platform = agent.platform
            AND pay.agent_id = agent.agent_id
            AND reg.root_game_reg_time BETWEEN agent.agent_leader_start_time
            AND agent.agent_leader_end_time
            INNER JOIN tt AS dd ON pay.platform = dd.platform
            AND game.root_game_id = dd.root_game_id
        WHERE
            game.os = '". $os ."'
            AND pay.order_status_id = 1
            AND pay.pay_time BETWEEN '". $start_date.' 00:00:00' ."' AND DATE_ADD('". $end_date.' 23:59:59'. "',INTERVAL  ". $days ."  DAY)
            AND reg.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
        GROUP BY
            pay.platform,
            game.os,
            game.root_game_id,
            pay.uid,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) )
        ORDER BY
            pay.platform,
            game.os,
            game.root_game_id,
            pay.uid,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) )
        ) as pgrad
    WHERE
        pay_days BETWEEN 0 AND 7
    GROUP BY
        platform,
        os,
        root_game_id,
        pay_days
    ORDER BY
        platform,
        os,
        root_game_id,
        pay_days";
    $pay_frequency_ranks = MysqlConnection::getConnection($connection_data_hub)->select($pay_frequency_sql);
    $total_array = [];
    foreach ($pay_frequency_ranks as $item) {
        $total_array[$item->platform .'-'. $item->root_game_id][$item->pay_days] = $item->avg_pay_times;
    }
    $pay_frequency = getRank($total_array, $platform .'-'. $root_game_id, $datas, 'pay_frequency');
    //var_dump([$pay_frequency]);


    //付费率
    $pay_rate_sql = $query . "SELECT
        t1.platform,
        t1.os,
        t1.root_game_id,
        pay_days,
        login_uid_count,
        pay_uid_count,
        pay_uid_count / login_uid_count AS pay_account
    FROM
        (
        SELECT
            login.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( login.login_date, DATE( login.root_game_reg_time ) ) AS login_days,
            COUNT( DISTINCT login.uid ) AS login_uid_count
        FROM
            v2_dwd_day_root_game_uid_login_log AS login
            INNER JOIN v2_dim_game_id AS game ON login.platform = game.platform
            AND login.game_id = game.game_id
            INNER JOIN v2_dim_agent_id AS agent ON login.platform = game.platform
            AND login.agent_id = agent.agent_id
            AND login.root_game_reg_time BETWEEN agent.agent_leader_start_time
            AND agent.agent_leader_end_time
            INNER JOIN tt AS dd ON login.platform = dd.platform
            AND game.root_game_id = dd.root_game_id
        WHERE
            game.os = '". $os ."'
            AND login.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
            AND login.login_date BETWEEN '". $start_date ."' AND DATE_ADD('". $end_date. "',INTERVAL  ". $days ."  DAY)
            AND login.login_date - DATE( login.root_game_reg_time ) >= 0
        GROUP BY
            login.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( login.login_date, DATE( login.root_game_reg_time ) )
        ORDER BY
            login.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( login.login_date, DATE( login.root_game_reg_time ) )
        ) AS t1
        INNER JOIN (
        SELECT
            pay.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) ) AS pay_days,
            COUNT( DISTINCT pay.uid, pay.order_id ) AS pay_uid_count
        FROM
            v2_ods_pay_order_log AS pay
            INNER JOIN v2_dim_game_id AS game ON pay.platform = game.platform
            AND pay.game_id = game.game_id
            INNER JOIN v2_dim_agent_id AS agent ON pay.agent_id = agent.agent_id
            AND pay.platform = agent.platform
            INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON pay.platform = reg.platform
            AND pay.uid = reg.uid
            AND pay.game_id = reg.game_id
            AND game.root_game_id = reg.root_game_id
            AND reg.root_game_reg_time BETWEEN agent.agent_leader_start_time
            AND agent.agent_leader_end_time
            INNER JOIN tt AS dd ON pay.platform = dd.platform
            AND game.root_game_id = dd.root_game_id
        WHERE
            game.os = '". $os ."'
            AND reg.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
            AND pay.pay_time BETWEEN '". $start_date.' 00:00:00' ."' AND DATE_ADD('". $end_date.' 23:59:59'. "',INTERVAL  ". $days ."  DAY)
            AND pay.order_status_id = 1
        GROUP BY
            pay.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) )
        ORDER BY
            pay.platform,
            game.os,
            game.root_game_id,
            DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) )
        ) AS t2 ON t1.platform = t2.platform
        AND t1.os = t2.os
        AND t1.root_game_id = t2.root_game_id
        AND t1.login_days = t2.pay_days
    ORDER BY
        t1.platform,
        t1.os,
        t1.root_game_id,
        pay_days";
    $pay_rate_ranks = MysqlConnection::getConnection($connection_data_hub)->select($pay_rate_sql);
    $total_array = [];
    foreach ($pay_rate_ranks as $item) {
        $total_array[$item->platform .'-'. $item->root_game_id][$item->pay_days] = $item->pay_account;
    }
    $pay_rate = getRank($total_array, $platform .'-'. $root_game_id,$datas, 'pay_rate');
    //var_dump([$pay_rate]);


    //付费粘性(注册7天内充值次数)
    $pay_stickiness_sql = $query . "SELECT
        platform,
        os,
        root_game_id,
        avg( pay_day_times ) AS avg_pay_day
    FROM
            (
            SELECT
                platform,
                os,
                root_game_id,
                uid,
                COUNT( pay_days ) AS pay_day_times
            FROM
                (
                SELECT
                    pay.platform,
                    game.os,
                    game.root_game_id,
                    pay.uid,
                    DATE( reg.root_game_reg_time ) AS reg_date,
                    DATE( pay.pay_time ) AS pay_date,
                    DATEDIFF( DATE( pay.pay_time ), DATE( reg.root_game_reg_time ) ) AS pay_days
                FROM
                    v2_ods_pay_order_log AS pay
                    INNER JOIN v2_dim_game_id AS game ON pay.platform = game.platform
                    AND pay.game_id = game.game_id
                    INNER JOIN v2_dim_agent_id AS agent ON pay.platform = agent.platform
                    AND pay.agent_id = agent.agent_id
                    INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON pay.platform = reg.platform
                    AND game.root_game_id = reg.root_game_id
                    AND pay.uid = reg.uid
                    AND reg.root_game_reg_time BETWEEN agent.agent_leader_start_time
                    AND agent.agent_leader_end_time
                    INNER JOIN tt AS dd ON pay.platform = dd.platform
                    AND game.root_game_id = dd.root_game_id
                WHERE
                    game.os = '". $os ."'
                    AND reg.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
                    AND pay.pay_time BETWEEN '". $start_date.' 00:00:00' ."' AND DATE_ADD('". $end_date.' 23:59:59'. "',INTERVAL ". $days ."  DAY)
                    AND pay.order_status_id = 1
                GROUP BY
                    pay.platform,
                    game.os,
                    game.root_game_id,
                    pay.uid,
                    pay_days
                ORDER BY
                    pay.platform,
                    game.os,
                    game.root_game_id,
                    pay.uid,
                    pay_days
                )
            WHERE
                pay_days < ". $days ."
            GROUP BY
                platform,
                os,
                root_game_id,
                uid
            )
    GROUP BY
        platform,
        os,
        root_game_id
    ORDER BY
         avg_pay_day
    DESC";
    $pay_stickiness_ranks = MysqlConnection::getConnection($connection_data_hub)->select($pay_stickiness_sql);
    $i = 0;
    foreach ($pay_stickiness_ranks as $item) {
        $i++;
        $datas[$item->platform .'-'. $item->root_game_id]['pay_stickiness'] = $item->avg_pay_day;
        $datas[$item->platform .'-'. $item->root_game_id]['pay_stickiness_rank'] = $i;
    }
    $rank = array_search($platform .'-'. $root_game_id, array_map(function ($item){
        return $item->platform .'-'. $item->root_game_id;
    }, $pay_stickiness_ranks)) + 1;
    $total = count($pay_stickiness_ranks);
    $high = intdiv($total, 3);
    $mid = round($total * (2/3));
    $pay_stickiness = $rank <= $high ? 3 : ($rank <= $mid ? 2 : 1);
    //var_dump([$rank,$pay_stickiness]);


    //回本节奏、回本风险
    $roi_tempo_risk_sql = $query . "SELECT
        p.platform,
        p.os,
        p.root_game_id,
        p.game_reg_date,
        r.count_reg_uid,
        p.pay_day,
        p.day_pay_money,
        p.total_day_pay_money,
        c.day_cost_money,
        c.day_cost_money / r.count_reg_uid AS cost_per_uid,
        CONCAT( ( p.total_day_pay_money / c.day_cost_money ) * 100, '%' ) AS roi
    FROM
        (
        SELECT
            pay.platform,
            g.os,
            g.root_game_id,
            pay.game_reg_date,
            num.num AS pay_day,
            SUM( IF ( num.num = pay.start_pay_days, day_pay_money, 0 ) ) AS day_pay_money,
            SUM( pay.total_day_pay_money ) AS total_day_pay_money
        FROM
            v2_dws_day_root_game_pay_log AS pay
            INNER JOIN v2_dim_game_id AS g ON pay.platform = g.platform
            AND pay.game_id = g.game_id
            INNER JOIN v2_dim_agent_id AS a ON pay.platform = a.platform
            AND pay.agent_id = a.agent_id
            AND pay.game_reg_date BETWEEN a.agent_leader_start_time
            AND a.agent_leader_end_time
            INNER JOIN v2_dim_num_id AS num ON num.num BETWEEN pay.start_pay_days
            AND pay.end_pay_days
            INNER JOIN tt AS dd ON pay.platform = dd.platform
            AND g.root_game_id = dd.root_game_id
        WHERE
            g.os = '". $os ."'
            AND num.num IN (". implode(',' , range(1, $days)) .")
            AND pay.game_reg_date BETWEEN '". $start_date ."' AND '". $end_date. "'
        GROUP BY
            pay.platform,
            g.os,
            g.root_game_id,
            pay.game_reg_date,
            pay_day
        ORDER BY
            pay.platform,
            g.os,
            g.root_game_id,
            pay.game_reg_date,
            pay_day
        ) AS p
        LEFT JOIN (
        SELECT
            cost.platform,
            g.os,
            g.root_game_id,
            cost.tdate AS date,
            SUM( cost.money ) AS day_cost_money
        FROM
            v2_dwd_day_cost_log AS cost
            INNER JOIN v2_dim_game_id AS g ON cost.platform = g.platform
            AND cost.game_id = g.game_id
            INNER JOIN v2_dim_agent_id AS a ON cost.platform = a.platform
            AND cost.agent_id = a.agent_id
            AND cost.tdate BETWEEN a.agent_leader_start_time
            AND a.agent_leader_end_time
            INNER JOIN tt AS dd ON cost.platform = dd.platform
            AND g.root_game_id = dd .root_game_id
        WHERE
            g.os = '". $os ."'
            AND cost.tdate BETWEEN '". $start_date ."' AND '". $end_date. "'
        GROUP BY
            cost.platform,
            g.os,
            g.root_game_id,
            date
        ORDER BY
            cost.platform,
            g.os,
            g.root_game_id,
            date
        ) AS c ON p.platform = c.platform
        AND p.os = c.os
        AND p.root_game_id = c.root_game_id
        AND p.game_reg_date = c.date
        LEFT JOIN (
        SELECT
            a.platform,
            g.os,
            g.root_game_id,
            DATE( reg.root_game_reg_time ) AS reg_date,
            COUNT( DISTINCT a.platform, g.root_game_id, reg.uid ) AS count_reg_uid
        FROM
            v2_dwd_root_game_uid_reg_log AS reg
            INNER JOIN v2_dim_game_id AS g ON reg.platform = g.platform
            AND reg.game_id = g.game_id
            INNER JOIN v2_dim_agent_id AS a ON reg.agent_id = a.agent_id
            AND a.platform = reg.platform
            AND reg.platform = a.platform
            AND reg.root_game_reg_time BETWEEN a.agent_leader_start_time
            AND a.agent_leader_end_time
            INNER JOIN tt AS dd ON reg.platform = dd.platform
            AND g.root_game_id = dd.root_game_id
        WHERE
            g.os = '". $os ."'
            AND DATE( reg.root_game_reg_time ) BETWEEN '". $start_date ."' AND '". $end_date. "'
        GROUP BY
            a.platform,
            g.os,
            g.root_game_id,
            reg_date
        ORDER BY
            a.platform,
            g.os,
            g.root_game_id,
            reg_date
        ) AS r ON p.platform = r.platform
        AND p.os = r.os
        AND p.root_game_id = r.root_game_id
        AND p.game_reg_date = r.reg_date
    GROUP BY
        p.platform,
        p.os,
        p.root_game_id,
        p.game_reg_date,
        p.pay_day
    ORDER BY
        p.platform,
        p.os,
        p.root_game_id,
        p.game_reg_date,
        p.pay_day";
    $roi_tempo_risk_ranks = MysqlConnection::getConnection($connection_data_hub)->select($roi_tempo_risk_sql);
    $total_tempo1_array = $total_tempo7_array = $total_risk_array = [];
    foreach ($roi_tempo_risk_ranks as $item) {
        if ($item->roi) {
            if ((int)$item->pay_day === 1) {
                $total_tempo1_array[$item->platform .'-'. $item->root_game_id][] = $item->roi;
            }
            if ((int)$item->pay_day === $days) {
                $total_tempo7_array[$item->platform .'-'. $item->root_game_id][] = $item->roi;
            }
        }
        if (!isset($total_risk_array[$item->platform .'-'. $item->root_game_id][$item->game_reg_date]) && $item->cost_per_uid > 0) {
            $total_risk_array[$item->platform .'-'. $item->root_game_id][$item->game_reg_date] = $item->cost_per_uid;
        }
    }
    $roi_tempo1 = getRank($total_tempo1_array, $platform .'-'. $root_game_id,$datas, 'roi_tempo1');
    $roi_tempo7 = getRank($total_tempo7_array, $platform .'-'. $root_game_id,$datas, 'roi_tempo7');
    $roi_risk = getRank($total_risk_array, $platform .'-'. $root_game_id,$datas, 'roi_risk');
    //var_dump([$roi_tempo1,$roi_tempo7,$roi_risk]);


    //登录频率
    $login_frequency_sql = $query . "SELECT
        platform,
        os,
        root_game_id,
        login_days,
        COUNT( uid ) AS login_uid_count,
        SUM( login_times ) AS total_login_times,
        ROUND(SUM( login_times ) / COUNT( uid ), 2)  AS login_times_per_login_uid
    FROM
        (
        SELECT
            login.platform,
            game.os,
            game.root_game_id,
            login.uid,
            DATE( reg.root_game_reg_time ) AS reg_date,
            DATE( login.login_time ) AS login_date,
            DATEDIFF( DATE( login.login_time ), DATE( reg.root_game_reg_time ) ) AS login_days,
            COUNT( 1 ) AS login_times
        FROM
            v2_ods_uid_login_log AS login
            INNER JOIN v2_dim_game_id AS game ON login.platform = game.platform
            AND login.game_id = game.game_id
            INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON login.platform = reg.platform
            AND game.root_game_id = reg.root_game_id
            AND login.uid = reg.uid
            INNER JOIN v2_dim_agent_id AS agent ON login.platform = agent.platform
            AND login.agent_id = agent.agent_id
            AND reg.root_game_reg_time BETWEEN agent.agent_leader_start_time
            AND agent.agent_leader_end_time
            INNER JOIN tt AS dd ON login.platform = dd.platform
            AND game.root_game_id = dd.root_game_id
        WHERE
            game.os = '". $os ."'
            AND reg.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
            AND login.login_time BETWEEN '". $start_date.' 00:00:00' ."' AND DATE_ADD('". $end_date.' 23:59:59'. "',INTERVAL ". $days ."  DAY)
        GROUP BY
            login.platform,
            game.os,
            game.root_game_id,
            login.uid,
            DATE( reg.root_game_reg_time ),
            DATE( login.login_time ),
            DATEDIFF( DATE( login.login_time ), DATE( reg.root_game_reg_time ) )
        ORDER BY
            login.platform,
            game.os,
            game.root_game_id,
            login.uid,
            DATE( reg.root_game_reg_time ),
            DATE( login.login_time ),
            DATEDIFF( DATE( login.login_time ), DATE( reg.root_game_reg_time ) )
        ) login
    WHERE
        login_days >= 0
    GROUP BY
        platform,
        os,
        root_game_id,
        login_days
    ORDER BY
        platform,
        os,
        root_game_id,
        login_days";
    $login_frequency_ranks = MysqlConnection::getConnection($connection_data_hub)->select($login_frequency_sql);
    $total_array = [];
    foreach ($login_frequency_ranks as $item) {
        $total_array[$item->platform .'-'. $item->root_game_id][$item->login_days] = $item->login_times_per_login_uid;
    }
    $login_frequency = getRank($total_array, $platform .'-'. $root_game_id,$datas, 'login_frequency');
    //var_dump([$login_frequency]);


    //用户在线时长
    $online_hours_sql = $query . "SELECT
        platform,
        os,
        root_game_id,
        online_date,
        AVG( online_seconds ) AS avg_online_seconds
    FROM
        (
        SELECT
            game.platform,
            game.os,
            game.root_game_id,
            sdk.uid,
            DATEDIFF( DATE( sdk.online_time ), DATE( reg.root_game_reg_time ) ) AS online_date,
            SUM( UNIX_TIMESTAMP( offline_time ) - UNIX_TIMESTAMP( online_time ) ) AS online_seconds
        FROM
            v2_dwd_sdk_heartbeat_log AS sdk
            INNER JOIN v2_dim_game_id AS game ON sdk.platform = game.platform
            AND sdk.game_id = game.game_id
            INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON sdk.platform = reg.platform
            AND game.root_game_id = reg.root_game_id
            AND sdk.uid = reg.uid
            INNER JOIN v2_dim_agent_id AS agent ON sdk.platform = agent.platform
            AND sdk.agent_id = agent.agent_id
            AND reg.root_game_reg_time BETWEEN agent.agent_leader_start_time
            AND agent.agent_leader_end_time
            INNER JOIN tt AS dd ON sdk.platform = dd.platform
            AND game.root_game_id = dd.root_game_id
        WHERE
            game.os = '". $os ."'
            AND sdk.offline_time <= DATE_ADD('". $end_date.' 23:59:59'. "',INTERVAL ". $days ."  DAY) AND sdk.online_time >= '". $start_date.' 00:00:00' ."'
            AND sdk.heart_token != ''
            AND reg.root_game_reg_time BETWEEN '". $start_date.' 00:00:00' ."' AND '". $end_date.' 23:59:59'. "'
            AND DATEDIFF( DATE( sdk.online_time ), DATE( reg.root_game_reg_time ) ) >= 0
        GROUP BY
            game.platform,
            game.os,
            game.root_game_id,
            sdk.uid,
            online_date
        HAVING
		    online_seconds < 43200    
        ORDER BY
            game.platform,
            game.os,
            game.root_game_id,
            sdk.uid,
            online_date
        )
    WHERE
	    online_date BETWEEN 0 AND ($days -1)    
    GROUP BY
        platform,
        os,
        root_game_id,
        online_date
    ORDER BY
        platform,
        os,
        root_game_id,
        online_date";
    $online_hours_ranks = MysqlConnection::getConnection($connection_data_hub)->select($online_hours_sql);
    $total_array = [];
    foreach ($online_hours_ranks as $item) {
        $total_array[$item->platform .'-'. $item->root_game_id][$item->online_date] = $item->avg_online_seconds;
    }
    $total_array_sort = [];
    foreach ($total_array as $key => $item) {
        $total_array_sort[$key] = array_sum($item) / count($item);
    }
    $online_hours = number_format($total_array_sort[$platform .'-'. $root_game_id] / 3600, 2);
    //var_dump([$online_hours]);


    //付费、非付费用户留存
    $stay_pay_none_pay_sql = $query . "SELECT
        is_pay,
        platform,
        os,
        root_game_id,
        login_days,
        avg(retention_rate) as rate
    FROM
        (
        SELECT
            *,
            FIRST_VALUE ( login_uid_count ) OVER ( PARTITION BY is_pay, platform, os, root_game_id, reg_date ORDER BY login_date ) AS reg_uid_count,
            login_uid_count / FIRST_VALUE ( login_uid_count ) OVER ( PARTITION BY is_pay, platform, os, root_game_id, reg_date ORDER BY login_date ) AS retention_rate
        FROM
            (
            SELECT
                login.platform,
                g.os,
                g.root_game_id,
                DATE( reg.root_game_reg_time ) AS reg_date,
                login.login_date,
                DATEDIFF(login.login_date, DATE( reg.root_game_reg_time )) AS login_days,
                COUNT( DISTINCT login.uid ) AS login_uid_count,
            IF
                ( reg.root_game_total_pay_times > 0, 1, 0 ) AS is_pay
            FROM
                v2_dwd_day_root_game_uid_login_log AS login
                INNER JOIN v2_dwd_root_game_uid_reg_log AS reg ON login.platform = reg.platform
                AND login.root_game_id = reg.root_game_id
                AND login.uid = reg.uid
                AND login.agent_id = reg.agent_id
                AND login.game_id = reg.game_id
                INNER JOIN v2_dim_game_id AS g ON login.platform = g.platform
                AND login.game_id = g.game_id
                INNER JOIN v2_dim_agent_id AS a ON login.platform = a.platform
                AND login.agent_id = a.agent_id
                AND reg.root_game_reg_time BETWEEN a.agent_leader_start_time
                AND a.agent_leader_end_time
                INNER JOIN tt AS dd ON login.platform = dd.platform
                AND g.root_game_id = dd.root_game_id
            WHERE
                g.os = '". $os ."'
                AND DATE( reg.root_game_reg_time ) BETWEEN '". $start_date ."' AND '". $end_date. "'
                AND login.login_date BETWEEN '". $start_date ."' AND DATE_ADD('". $end_date. "',INTERVAL ". $days ."  DAY)
                AND DATEDIFF(login.login_date, DATE( reg.root_game_reg_time )) >= 0
                AND login.root_game_reg_time > '2001-01-01'
            GROUP BY
                is_pay,
                login.platform,
                g.os,
                g.root_game_id,
                reg_date,
                login.login_date
            ORDER BY
                is_pay,
                login.platform,
                g.os,
                g.root_game_id,
                reg_date,
                login.login_date
            )
        ORDER BY
            is_pay,
            platform,
            os,
            root_game_id,
            reg_date,
            login_date
        )
    GROUP BY
        is_pay,
        platform,
        os,
        root_game_id,
        login_days
    ORDER BY
        is_pay,
        platform,
        os,
        root_game_id,
        login_days";
    $stay_pay_none_pay_ranks = MysqlConnection::getConnection($connection_data_hub)->select($stay_pay_none_pay_sql);
    $total_pay_array = $total_none_pay_array = [];
    foreach ($stay_pay_none_pay_ranks as $item) {
        if ((int)$item->is_pay === 1) {
            $total_pay_array[$item->platform .'-'. $item->root_game_id][$item->login_days] = $item->rate;
            if (in_array($item->login_days, [2,3,7,15,21,30])) {
                $datas[$item->platform .'-'. $item->root_game_id]['stay_pay_'.$item->login_days] = $item->rate;
            }
            if ((int)$item->login_days === $days && !in_array($days, [2,3,7,15,21,30])) {
                $datas[$item->platform .'-'. $item->root_game_id]['stay_pay_N'] = $item->rate;
            }
        } elseif ((int)$item->is_pay === 0) {
            $total_none_pay_array[$item->platform .'-'. $item->root_game_id][$item->login_days] = $item->rate;
            if (in_array($item->login_days, [2,3,7,15,21,30])) {
                $datas[$item->platform .'-'. $item->root_game_id]['stay_none_pay_'.$item->login_days] = $item->rate;
            }
            if ((int)$item->login_days === $days && !in_array($days, [2,3,7,15,21,30])) {
                $datas[$item->platform .'-'. $item->root_game_id]['stay_none_pay_N'] = $item->rate;
            }
        }
    }
    $stay_pay = getRank($total_pay_array, $platform .'-'. $root_game_id, $datas, 'stay_pay');
    $stay_none_pay = getRank($total_none_pay_array, $platform .'-'. $root_game_id, $datas, 'stay_none_pay');
    //var_dump([$stay_pay,$stay_none_pay]);


    //更新记录指标
    echo "update product score=============>\n";
    if (isset($datas[$platform .'-'. $root_game_id])) {
        $pay_score = getScore($pay_limit_top)*0.3 + getScore($pay_frequency)*0.2 + getScore($pay_stickiness)*0.2 + getScore($pay_rate)*0.3;
        $return_score= getScore($roi_tempo1)*0.4 + getScore($roi_tempo7)*0.4 + getScoreReverse($roi_risk)*0.2;
        $stickiness_score = getScore($login_frequency)*0.3 + getScore($stay_pay)*0.4 + getScore($stay_none_pay)*0.3;
        $score = $pay_score*0.4 + $return_score*0.4 + $stickiness_score*0.2;
        $data = [
            'gen_time' => date('Y-m-d H:i:s'),
            'pay_friendly_low' => $pay_friendly_low,
            'pay_friendly_mid' => $pay_friendly_mid,
            'pay_friendly_high' => $pay_friendly_high,
            'pay_limit_top' => $pay_limit_top,
            'pay_frequency' => $pay_frequency,
            'pay_stickiness' => $pay_stickiness,
            'pay_rate' => $pay_rate,
            'roi_tempo1' => $roi_tempo1,
            'roi_tempo7' => $roi_tempo7,
            'roi_risk' => $roi_risk,
            'login_frequency' => $login_frequency,
            'online_hours' => $online_hours,
            'stay_pay' => $stay_pay,
            'stay_none_pay' => $stay_none_pay,
            'score' => $score
        ];
        //var_dump($data);
        MysqlConnection::getConnection($connection_name)->table('product_score')->where(['id' => $value->id])->update($data);
        foreach ($datas as $data) {
            $data['insert_time'] = date('Y-m-d H:i:s');
            $data['update_time'] = date('Y-m-d H:i:s');
            MysqlConnection::getConnection($connection_name)->table('product_score_detail')->insert($data);
        }
    }
}

echo date('Y-m-d H:i:s') . '--scoring product end' . PHP_EOL;

function getRank($total_array, $root_game_id, &$datas, $name, $reverse = false)
{
    $total_array_sort = [];
    foreach ($total_array as $key => $item) {
        $total_array_sort[$key] = array_sum($item) / count($item);
    }
    $reverse ? asort($total_array_sort) : arsort($total_array_sort);//var_dump($total_array_sort);
    $i=0;
    foreach ($total_array_sort as $key => $value) {
        $i++;
        if (!in_array($name, ['stay_pay','stay_none_pay'])) {
            $datas[$key][$name] = $value;
        }
        $datas[$key][$name.'_rank'] = $i;
    }

    $rank = array_search($root_game_id, array_keys($total_array_sort)) + 1;
    $total = count($total_array_sort);
    $high = intdiv($total, 3);
    $mid = round($total * (2/3));
    return $rank <= $high ? 3 : ($rank <= $mid ? 2 : 1);
}

function getScore($rank)
{
    switch ($rank) {
        case 3:
            return 10;
        case 2:
            return 5;
        case 1:
            return 1;
        default:
            return 0;
    }
}

function getScoreReverse($rank)
{
    switch ($rank) {
        case 1:
            return 10;
        case 2:
            return 5;
        case 3:
            return 1;
        default:
            return 0;
    }
}
