<?php

//  更新云文档，20分钟跑一次
// */20 * * * * php cronExportFeishuFileTask.php
//  @server 120.55.83.156 zx-dms

use App\ElasticsearchConnection;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\MysqlConnection;
use App\Task\ExportFileTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

try {
    (new \App\Service\GroupAssistant\MessageFormat())->pollDocList();
} catch (\Exception $e) {
    Helpers::getLogger('doc_update')->error('轮询云文档发送异常:' . $e->getMessage());
}

