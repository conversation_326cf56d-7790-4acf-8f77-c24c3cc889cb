<?php
/**
 * 0 3 * * *
 * 跑回游戏支付的历史数据
 * 默认同步最近一年数据
 * @server ************ zx-script
 */

use App\MysqlConnection;
use App\Task\HistoryPayDataTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$task = new HistoryPayDataTask();
$logger = Helpers::getLogger('all-game-history');

try {
    $task->syncAllHistoryGameData($logger);
} catch (\Throwable $exception) {
    $logger->error('刷新运营利润报错', ['message' => $exception->getMessage()]);
}

exit;
