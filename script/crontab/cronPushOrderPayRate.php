<?php

//
// 微信推送订单成功率
// 每分钟推送
// * * * * * php cronPushOrderPayRate.php
//

use App\Logic\OrderPayRateDataLogic;
use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\DatahubLY\OrderPayRatePushDataLyModel;
use App\Model\SqlModel\Tanwan\OrderPayRatePushDataDmsModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\OrderPayRatePushConfigModel;
use App\MysqlConnection;
use App\Task\NoticeTask;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__).'/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$logger = Helpers::getLogger("wechat-push");


$panel_date = date("Y-m-d");
$last_panel_date = date("Y-m-d", strtotime("-1 days"));
$time = strtotime(date("Y-m-d 00:00:00"));
$panel_date_time = time();
$config_model = new OrderPayRatePushConfigModel();
$push_log = [];

$cur_hour = intval(date("H"));
$cur_min = intval(date("i"));
if ($cur_hour == 0) {
    $now = date("Y年m月d日（统计日期）", strtotime('-1 day'));
    $first = "昨日订单成功率";
} else {
    $first = "今日订单成功率";
    $now = date("Y年m月d日（统计日期）")."-".$cur_hour.":00";
}
$list = $config_model->getListByTime($cur_min);

$process_num = 5;
$size = ceil(count($list) / $process_num);
$chunk_list = $list->chunk($size);

for ($n = 1; $n <= $process_num; $n++) {
    $pid = pcntl_fork();
    if ($pid < 0) {
        $logger->error("创建进程失败");
        exit;
    }
    if ($pid === 0) {
        $logger->info("push process {$n}");
        $logger->info("push begin".(date('Y-m-d H:i:s')));

        if (isset($chunk_list[$n - 1])) {
            pushMessage(
                $chunk_list[$n - 1],
                $cur_hour,
                $panel_date,
                $last_panel_date,
                $panel_date_time,
                $first,
                $now,
                $logger
            );
        }
        $logger->info("push end".(date('Y-m-d H:i:s')));
        exit;
    }
}
function pushMessage($list, $cur_hour, $panel_date, $last_panel_date, $panel_date_time, $first, $now, $logger)
{
    MysqlConnection::setConnection();

    $t1 = microtime(true);
    $notice_task = new NoticeTask();
    $logic = new OrderPayRateDataLogic();
    foreach ($list as $item) {
        $theme = $item->theme;
        $user_id = $item->user_id;
        $username = $item->username;

        if ((int)$item->push_hour === 0) {
            //按天0点推送
            if ($cur_hour !== 0) {
                continue;
            }
        } else {
            //按X小时推送
            //当天的先不推送
            if (date('Y-m-d', $item->create_time) === date('Y-m-d')) {
                continue;
            }
            //当前小时数 % $item->push_hour = 0
            if ($cur_hour % $item->push_hour !== 0) {
                continue;
            }
        }

        $key = $user_id.$theme;
        if (isset($push_log[$key])) {
            continue;
        }
        $push_log[$key] = true;

        if ($item->module === 'dms') {
            $permission = fix_dms_permission($item->config);
            $data_model = new OrderPayRatePushDataDmsModel();
            $panel_data = $data_model->getPanelData($permission, $panel_date, $cur_hour);
            $last_panel_data = $data_model->getPanelData($permission, $last_panel_date, $cur_hour);
        } else {
            $permission = fix_ly_permission($item->config);
            $data_model = new OrderPayRatePushDataLyModel();
            $panel_data = $data_model->getPanelData($permission, $panel_date, $cur_hour);
            $last_panel_data = $data_model->getPanelData($permission, $last_panel_date, $cur_hour,);
        }
        $order_count = $panel_data->sum_order_count ?? 0;
        $last_order_count = $last_panel_data->sum_order_count ?? 0;
        $order_pay_count = $panel_data->sum_order_pay_count ?? 0;
        $last_order_pay_count = $last_panel_data->sum_order_pay_count ?? 0;
        $order_pay_rate = $panel_data->sum_order_pay_rate ?? 0;
        $last_order_pay_rate = $last_panel_data->sum_order_pay_rate ?? 0;

        $order_count_circle = Math::circle($order_count, $last_order_count);
        $order_pay_count_circle = Math::circle($order_pay_count, $last_order_pay_count);
        $order_pay_rate_circle = Math::circle($order_pay_rate, $last_order_pay_rate);

        if ($order_count_circle < 0) {
            $order_count_circle = '（↓' . abs($order_count_circle) . '%）';
        } else {
            $order_count_circle = '（↑' . $order_count_circle . '%）';
        }

        if ($order_pay_count_circle < 0) {
            $order_pay_count_circle = '（↓' . abs($order_pay_count_circle) . '%）';
        } else {
            $order_pay_count_circle = '（↑' . $order_pay_count_circle . '%）';
        }

        if ($order_pay_rate_circle < 0) {
            $order_pay_rate_circle = '（↓' . abs($order_pay_rate_circle) . '%）';
        } else {
            $order_pay_rate_circle = '（↑' . $order_pay_rate_circle . '%）';
        }
        $sum_data
            = "总订单数：{$order_count}{$order_count_circle}\n支付订单数：{$order_pay_count}{$order_pay_count_circle}\n支付成功率：{$order_pay_rate}{$order_pay_rate_circle}";

        $url = $logic->getPushUrl($panel_date_time, $theme, $user_id);
        $notice_task->pushWeChat(
            [
                'user_id'     => $user_id,
                'type'        => NoticeMessageModel::TYPE_WECHAT_DATA,
                'template_id' => TemplateModel::STATISTICS_TEMPLATE,
                'url'         => $url,
                'config_name' => 'qingbaoju',
                'data'        => [
                    "first"    => ["value" => "{$first}", "color" => "#000000"],
                    "keyword1" => ["value" => "{$username} - {$theme}", "color" => "#000000"],
                    "keyword2" => ["value" => "{$now}", "color" => "#000000"],
                    "keyword3" => ["value" => "$sum_data", "color" => "#169115"],
                    "remark"   => ["value" => "点击进入查看详情", "color" => "#000000"],
                ],
            ]
        );


        // 中旭推送
        if ($cur_hour == 0) {
            $now = date("Y-m-d", strtotime('-1 day'));
        } else {
            $now = date("Y-m-d H:i");
        }
        $url = $logic->getZXPushUrl($panel_date_time, $theme, $user_id);
        $notice_task->pushWeChat(
            [
                'user_id'     => $user_id,
                'type'        => NoticeMessageModel::TYPE_WECHAT_DATA,
                'template_id' => TemplateModel::ZX_STATISTICS_TEMPLATE,
                'url'         => $url,
                'config_name' => 'zx_qbj',
                'data'        => [
                    "thing6" => ["value" => "{$username} - {$theme}", "color" => "#000000"],
                    "time7" => ["value" => "{$now}", "color" => "#000000"],
                    "thing10" => ["value" => "$sum_data", "color" => "#169115"],
                ],
            ]
        );
    }


    $t2 = microtime(true);
    $logger->info("耗时：".round($t2 - $t1, 4));
}

function fix_dms_permission($config)
{
    $permissions = [];
    $config = json_decode($config, true);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $type = $item['type'];
            $info = $item['info'];

            foreach ($info as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $os = $data['os'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = [
                        'main_game_id' => $main_game_id,
                        'os'           => $os,
                    ];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge(
                        $type_info[$platform]['main_game_id'],
                        $main_game_id
                    );
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $permissions[$sub_theme][$type] = [
                'game' => $type_info,
            ];
        }
    }

    return $permissions;
}

function fix_ly_permission($config)
{
    $permissions = [];
    $config = json_decode($config, true);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $type = $item['type'];
            $info = $item['info'];

            foreach ($info as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $os = $data['os'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = [
                        'main_game_id' => $main_game_id,
                        'os'           => $os,
                    ];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge(
                        $type_info[$platform]['main_game_id'],
                        $main_game_id
                    );
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $permissions[$sub_theme][$type] = [
                'game' => $type_info,
            ];
        }
    }

    return $permissions;
}