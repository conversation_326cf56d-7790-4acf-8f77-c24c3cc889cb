<?php
/**
 * 腾讯三级数值条件监控脚本，整点执行，每15分钟执行一次
 */

use App\Constant\ADFieldsENToCNMap;
use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Tencent\V3\DynamicCreatives\DynamicCreativeModel;
use App\Model\RedisModel\MonitorCalcRuleRedisModel;
use App\Model\SqlModel\DataMedia\OdsAD3BindCalcRuleModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdHisLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\MonitorAD3CalcOperateLogModel;
use App\Model\SqlModel\Zeda\MonitorADCalcRuleModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();

const CURRENT_FREQUENCY = [15, 30, 60, 120, 180];
/**
 * 调度不同频率的执行频率和次序
 */
function manageExecuteFrequency()
{
    //避开整点
    sleep(120);
    ob_start();
    echo date('Y-m-d H:i:s') . "开始执行广告数值条件匹配" . PHP_EOL;
    ob_flush();
    $monitor_calc_redis_model = new MonitorCalcRuleRedisModel();
//    $frequency = $monitor_calc_redis_model->get('last_frequency');
    $execute_minute = $monitor_calc_redis_model->get(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY, MonitorCalcRuleRedisModel::EXECUTE_MINUTE);
    if (false === $execute_minute) {
        $execute_minute = 15;
        $monitor_calc_redis_model->setOrUpdate(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY, MonitorCalcRuleRedisModel::EXECUTE_MINUTE, $execute_minute);
    }
    //每次检查一下是不是0点
    if ('00:0' === substr(date('H:i'), 0, 4)) {
        echo "本次执行：1440分钟(每天0点)频率" . PHP_EOL;
        ob_flush();
        monitorCalcMain(1440);
        $monitor_calc_redis_model->setOrUpdate(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY, MonitorCalcRuleRedisModel::EXECUTE_MINUTE, 0);
    }

    //增加15分钟
    $monitor_calc_redis_model->incrMinute(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY);
    $global_begin_time = microtime(true);
    foreach (CURRENT_FREQUENCY as $fre) {
        if ($execute_minute % $fre === 0) {
            //本次执行15分钟频率的
            echo "开始执行：" . $fre . "分钟频率" . PHP_EOL;
            ob_flush();
            $begin_time = microtime(true);
            monitorCalcMain($fre);
            $end_time = microtime(true);
            echo $fre . "分钟频率规则执行结束，执行时间：" . (string)round($end_time - $begin_time, 4) . PHP_EOL;
            ob_flush();
        }
    }
    $global_end_time = microtime(true);

    echo "匹配结束, 本轮总执行时间：" . (string)round($global_end_time - $global_begin_time, 4) . PHP_EOL . PHP_EOL;
    ob_end_flush();
}

/**
 * 监控数值规则主处理函数
 * @param $frequency
 */
function monitorCalcMain($frequency)
{
    $calc_rule_model = new MonitorADCalcRuleModel();
    //根据不同的频率执行
    $all_calc_rules = $calc_rule_model->getTencentAD3RulesByFrequency($frequency);
    if ($all_calc_rules->isEmpty()) {
        return;
    }
    $monitor_calc_redis_model = new MonitorCalcRuleRedisModel();
    $time_range = $monitor_calc_redis_model->get(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY, $frequency);
    $monitor_calc_redis_model->setOrUpdate(MonitorCalcRuleRedisModel::TENCENT_AD3_HASH_KEY, $frequency, date('Y-m-d H:i:s'));
    $all_calc_info = (new OdsAD3BindCalcRuleModel())->getAD3CalcAndInfo($time_range);
    if ($all_calc_info->isEmpty()) {
        return;
    }
    $all_need_be_notice = [];
    $all_need_be_operate = [];
    $account_ids = [];
    $all_rule_ids = [];
    $match_rule_ids = [];
    foreach ($all_calc_rules as $rule) {
        $all_rule_ids[] = $rule->id;
        //开始全部置为执行状态
        $calc_rule_model->updateRuleExecuteStatus([$rule->id], [
            'execute_status' => MonitorADCalcRuleModel::RUNNING_EXECUTE
        ]);
        $attr_condition = json_decode($rule->attr_condition, true);
        $calc_condition = json_decode($rule->calc_condition, true);
        $calc_target = json_decode($rule->calc_target, true);
        $weighting_target = json_decode($rule->weighting_target, true);
        foreach ($all_calc_info as $info) {
            if ((int)$info->calc_rule_id !== (int)$rule->id) {
                continue;
            }
            //处理属性条件
            if (false === handleAttrCondition($attr_condition, $info)) {
                //不符合状态改为执行完成更新上次执行时间
                $calc_rule_model->updateRuleExecuteStatus([$rule->id], [
                    'execute_status' => MonitorADCalcRuleModel::EXECUTE_COMPLETE,
                    'last_execute_time' => time(),
                ]);
                continue;
            }

            //处理数值条件
            if ($calc_condition) {
                if (false === handleCalcCondition($calc_condition, $info)) {
                    //不符合状态改为执行完成更新上次执行时间
                    $calc_rule_model->updateRuleExecuteStatus([$rule->id], [
                        'execute_status' => MonitorADCalcRuleModel::EXECUTE_COMPLETE,
                        'last_execute_time' => time(),
                    ]);
                    continue;
                }
            }

            //数值目标，加权系数处理
            if ($calc_target && $calc_target['value'] && $weighting_target) {
                //所有计算系数保存一个数组里
                $all_coefficient = [];
                foreach ($weighting_target as $item) {
                    $weighting_target_value = getCalculateResult($item, $info);
                    if (1 === (int)$item['calculate_type']) {
                        $coefficient = Math::div($weighting_target_value, $item['target_value']);
                    } else {
                        $coefficient = Math::div($item['target_value'], $weighting_target_value);
                    }

                    //判断加权系数是否超过限制
                    if ($coefficient < $item['min_weighting_coefficient']) {
                        $coefficient = $item['min_weighting_coefficient'];
                    } elseif ($coefficient > $item['max_weighting_coefficient']) {
                        $coefficient = $item['max_weighting_coefficient'];
                    }

                    $all_coefficient[] = $coefficient;
                }
                unset($item);

                //数值目标计算值
                $calc_target_value = getCalculateResult($calc_target, $info);

                foreach ($all_coefficient as $item) {
                    $calc_target_value *= $item;
                }
                unset($item);

                if (false === judgeCalcCondition($calc_target_value, $calc_target)) {
                    $calc_rule_model->updateRuleExecuteStatus([$rule->id], [
                        'execute_status' => MonitorADCalcRuleModel::EXECUTE_COMPLETE,
                        'last_execute_time' => time(),
                    ]);

                    continue;
                }
            }

            //保存可以后续进行操作的规则ID
            if (!in_array($rule->id, $match_rule_ids)) {
                $match_rule_ids[] = $rule->id;
            }

            //仅发送通知
            if (!isset($all_need_be_notice[$rule->creator_id][$rule->id])) {
                if (2 === (int)$rule->operate_type) {
                    $all_need_be_notice[$rule->creator_id][$rule->id] = "规则名：{$rule->name} 已有计划符合本条数值监控规则, 请及时处理";
                    continue 2;
                } elseif (3 === (int)$rule->operate_type) {
                    $all_need_be_notice[$rule->creator_id][$rule->id] = "规则名：{$rule->name} 已有计划符合本条数值监控规则, 并将按照规则配置立即执行对应操作";
                }
            }

            $need_be_operated['calc_rule_name'] = $rule->name;
            $need_be_operated['calc_rule_id'] = $rule->id;
            $need_be_operated['media_type'] = $info->media_type;
            $need_be_operated['account_id'] = $info->account_id;
            $need_be_operated['ad3_id'] = $info->ad3_id;
            $need_be_operated['platform'] = $info->platform;
            $need_be_operated['ad_name'] = $info->ad_name;
            $need_be_operated['port_version'] = $info->port_version;
            $need_be_operated['account_name'] = $info->account_name;
            $need_be_operated['agent_leader'] = $info->agent_leader;
            $need_be_operated['current_switch'] = $info->configured_status;
            $need_be_operated['switch'] = $rule->switch;
//            $need_be_operated['current_budget'] = $info->budget;
//            $need_be_operated['budget'] = $rule->budget;
//            $need_be_operated['budget_operator'] = $rule->budget_operator;
//            $need_be_operated['current_cpa_bid'] = $info->cpa_bid;
//            $need_be_operated['cpa_bid'] = $rule->cpa_bid;
//            $need_be_operated['cpa_bid_operator'] = $rule->cpa_bid_operator;
//            $need_be_operated['current_schedule_time'] = $info->schedule_time;
//            $need_be_operated['schedule_time'] = $rule->schedule_time;
//            $need_be_operated['schedule_time_operator'] = $rule->schedule_time_operator;
//            $need_be_operated['modify_time'] = $info->ad2_modify_time;
            $need_be_operated['site_id'] = $info->site_id;
            $need_be_operated['os'] = $info->os;
            $need_be_operated['plat_id'] = $info->plat_id;
            $need_be_operated['app_id'] = $info->app_id;
            $need_be_operated['creator'] = $rule->creator;
            $all_need_be_operate[] = $need_be_operated;

            if (!in_array($info->account_id, $account_ids)) {
                $account_ids[] = $info->account_id;
            }
        }
        unset($info);
    }
    unset($rule);
    //保证所有不符合规则的都能正确修改状态
    if ($all_rule_ids) {
        //得到不符合的数值规则ID
        $unmatched_rule_ids = array_diff($all_rule_ids, $match_rule_ids);
        $calc_rule_model->updateRuleExecuteStatus($unmatched_rule_ids, [
            'execute_status' => MonitorADCalcRuleModel::EXECUTE_COMPLETE,
            'last_execute_time' => time(),
        ]);
    }

    if ($all_need_be_notice) {
        try {
            sendFeishuMsg($all_need_be_notice);
        } catch (AppException $e) {
            Helpers::getLogger('Monitor-Calc')->error('飞书通知发送失败, ' . $e->getMessage());
        }
    }
    if (!$account_ids || !$all_need_be_operate) {
        return;
    }
//var_dump($all_need_be_operate);die;
    $all_operate_log = operateDiffMediaAD($all_need_be_operate, $account_ids);
    if (!$all_operate_log) {
        $msg = '无可记录的操作日志';
        Helpers::getLogger('Monitor-Calc')->error($msg);
        throw new AppException($msg);
    }
    (new MonitorAD3CalcOperateLogModel())->add($all_operate_log);
}


function sendFeishuMsg($all_need_be_notice) {
    $user_ids = array_keys($all_need_be_notice);
    $user_info = (new UserModel())->getDataByUserIds($user_ids);
    if ($user_info->isEmpty()) {
        return;
    }
    $feishu_info = [];
    foreach ($user_info as $info) {
        $feishu_union = explode('-', $info->feishu_union_id);
        if (!$info->feishu_union_id || false === $feishu_union) {
            continue;
        }
        $feishu_info[$info->id] = ['platform' => $feishu_union[0], 'union_id' => $feishu_union[1]];
    }

    $developer_info = (new MediaDeveloperAccountModel())->getFeishuSubscriptList()->keyBy('platform');
    foreach ($all_need_be_notice as $user_id => $rule_msg) {
        if (!isset($feishu_info[$user_id]) ||
            !isset($developer_info[$feishu_info[$user_id]['platform']]->app_id) ||
            !isset($developer_info[$feishu_info[$user_id]['platform']]->app_secret)) {
            Helpers::getLogger('Monitor-Calc')->info('飞书通知发送失败, user_id:' . $user_id . '有问题');
            continue;
        }
        $union_id = $feishu_info[$user_id]['union_id'];
        $app_id = $developer_info[$feishu_info[$user_id]['platform']]->app_id;
        $app_secret = $developer_info[$feishu_info[$user_id]['platform']]->app_secret;
        foreach ($rule_msg as $rule_id => $msg_content) {
            $app_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $app_id, $app_secret);
            $content_data = [
                'zh_cn' => [
                    'title' => "您所创建的数值监控规则",
                    "content" => [
                        [
                            ['tag' => "text", 'text' => "规则ID：{$rule_id}"]
                        ],
                        [
                            ['tag' => "text", 'text' => $msg_content]
                        ],
//                    [
//                        ['tag' => "text", 'text' => $content]
//                    ],
                    ]
                ]
            ];

            $model = new MessageModel();
            $content_data = json_encode($content_data);
            $model->message($app_access_token, 'union_id', $union_id, $content_data, 'post');
        }

    }
}

/**
 * 操作不同的媒体广告并记录操作记录
 * @param $all_need_be_operate
 * @param $account_ids
 * @return array
 */
function operateDiffMediaAD($all_need_be_operate, $account_ids)
{
    $access_token_info = (new MediaAccountModel())->getAccessTokenInAccountIds($account_ids)->keyBy('account_id');
    $tencent_http_model = new DynamicCreativeModel();
    $calc_rule_model = new MonitorADCalcRuleModel();
    //按照优先级排列好的顺序遍历，内部执行
    $all_operate_log = [];
    $rule_ids = [];
    foreach ($all_need_be_operate as $value) {
        //这里保证所有符合规则的，无论有没有找到access_token的都可以正确修改状态
        if (!in_array($value['calc_rule_id'], $rule_ids)) {
            $rule_ids[] = $value['calc_rule_id'];
        }
        if (!isset($access_token_info[$value['account_id']])) {
            continue;
        }
//        foreach ($access_token_info as $v) {
//            if ((int)$v->account_id !== (int)$value['account_id']) {
//                continue;
//            }
        $insert_operate_log = [];
        $insert_operate_log['operate_time'] = date('Y-m-d H:i:s');
        $insert_operate_log['calc_rule_id'] = $value['calc_rule_id'];
        $insert_operate_log['calc_rule_name'] = $value['calc_rule_name'];
        $insert_operate_log['media_type'] = $value['media_type'];
        $insert_operate_log['account_name'] = $value['account_name'];
        $insert_operate_log['account_id'] = $value['account_id'];
        $insert_operate_log['ad3_name'] = $value['ad_name'];
        $insert_operate_log['ad3_id'] = $value['ad3_id'];
        $insert_operate_log['agent_leader'] = $value['agent_leader'];
        $insert_operate_log['switch'] = 0;
//        $insert_operate_log['budget'] = 0;
//        $insert_operate_log['cpa_bid'] = 0;
//        $insert_operate_log['schedule_time'] = 0;
//        $insert_operate_log['old_schedule_time'] = '';
//        $insert_operate_log['new_schedule_time'] = '';
        $insert_operate_log['status_before_operate'] = '';
        $insert_operate_log['operate_detail'] = '';
        //广告开关
        if ($value['switch']) {
            $insert_operate_log = handleTencentUpdate($value,
                $access_token_info[$value['account_id']], $tencent_http_model, $insert_operate_log);
        }
        $all_operate_log[] = $insert_operate_log;

    }
    unset($value);
    $calc_rule_model->updateRuleExecuteStatus($rule_ids, [
        'execute_status' => MonitorADCalcRuleModel::EXECUTE_COMPLETE,
        'last_execute_time' => time(),
    ]);
//    if (!$all_operate_log) {
//        return [];
//    }
    return $all_operate_log ?: [];
}

/**
 * 匹配属性条件
 * @param $attr_condition
 * @param $calc_info
 * @return bool
 */
function handleAttrCondition($attr_condition, $calc_info)
{
    //处理属性条件
    foreach ($attr_condition as $v) {
        switch ($v['name']) {
            //处理计划状态,将计划状态先聚合起来作为查询条件提高查询效率
            case 'ad3_status':
                $ad3_configured_status = [];
                if ($v['ad3_configured_status']) {
                    foreach ($v['ad3_configured_status'] as $value) {
                        $ad3_configured_status = array_merge(explode(',', $value), $ad3_configured_status);
                    }
                    unset($value);
                    if (!in_array($calc_info->configured_status, $ad3_configured_status)) {
                        return false;
                    }
                }
                if ($v['ad3_system_status']) {
                    $sys_status = $v['ad3_system_status'];
                    if (!in_array($calc_info->system_status, $sys_status)) {
                        return false;
                    }
                }
                break;
            //之后扩展属性条件....
        }
    }
    return true;
}

/**
 * 匹配数值条件
 * @param $calc_condition
 * @param $calc_info
 * @return bool
 */
function handleCalcCondition($calc_condition, $calc_info)
{
    foreach ($calc_condition as $v) {
        $result = getCalculateResult($v, $calc_info);
        if (false === judgeCalcCondition($result, $v)) {
            return false;
        }
    }
    return true;
}

/**
 * 获取指标计算结果
 * @param $condition
 * @param $calc_info
 * @return int|string
 */
function getCalculateResult($condition, $calc_info)
{
    switch ($condition['name']) {
        //首日arppu
        case 'first_day_arppu':
            if (1 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->today_first_day_pay_money;
                $first_day_pay_count = $calc_info->today_first_day_pay_count;
            } elseif (2 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->three_day_first_day_pay_money;
                $first_day_pay_count = $calc_info->three_day_first_day_pay_count;
            } else {
                $first_day_pay_money = $calc_info->all_first_day_pay_money;
                $first_day_pay_count = $calc_info->all_first_day_pay_count;
            }
            $result = Math::div($first_day_pay_money, $first_day_pay_count);
            break;
        //首日ltv
        case 'first_day_ltv':
            if (1 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->today_first_day_pay_money;
                $reg_uid_count = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->three_day_first_day_pay_money;
                $reg_uid_count = $calc_info->three_day_reg_uid_count;
            } else {
                $first_day_pay_money = $calc_info->all_first_day_pay_money;
                $reg_uid_count = $calc_info->all_reg_uid_count;
            }
            $result = Math::div($first_day_pay_money, $reg_uid_count);
            break;
        //首日付费率
        case 'first_day_pay_rate':
            if (1 === (int)$condition['date_type']) {
                $first_day_pay_count = $calc_info->today_first_day_pay_count;
                $reg_uid_count = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $first_day_pay_count = $calc_info->three_day_first_day_pay_count;
                $reg_uid_count = $calc_info->three_day_reg_uid_count;
            } else {
                $first_day_pay_count = $calc_info->all_first_day_pay_count;
                $reg_uid_count = $calc_info->all_reg_uid_count;
            }
            $result = Math::rate($first_day_pay_count, $reg_uid_count);
            break;
        //累计付费率
        case 'pay_rate':
            if (1 === (int)$condition['date_type']) {
                $total_pay_count = $calc_info->today_total_pay_count;
                $reg_uid_count = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $total_pay_count = $calc_info->three_day_total_pay_count;
                $reg_uid_count = $calc_info->three_day_reg_uid_count;
            } else {
                $total_pay_count = $calc_info->all_total_pay_count;
                $reg_uid_count = $calc_info->all_reg_uid_count;
            }
            $result = Math::rate($total_pay_count, $reg_uid_count);
            break;
        //消耗
        case 'cost':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_cost;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_cost;
            } else {
                $result = $calc_info->all_cost;
            }
            break;
        //消耗天数
        case 'cost_date_count':
            if (1 === (int)$condition['date_type']) {
                $cost_date_hour_count = $calc_info->today_cost_date_hour_count;
            } elseif (2 === (int)$condition['date_type']) {
                $cost_date_hour_count = $calc_info->three_day_cost_date_hour_count;
            } else {
                $cost_date_hour_count = $calc_info->all_cost_date_hour_count;
            }

            $result = Math::div($cost_date_hour_count, 24);
            break;
        //预算消耗进度
        case 'cost_process':
            //预算消耗进度只有当天
            $result = Math::rate($calc_info->today_ori_cost, $calc_info->budget);
            break;
        //点击率
        case 'click_rate':
            if (1 === (int)$condition['date_type']) {
                $click = $calc_info->today_click;
                $show = $calc_info->today_show;
            } elseif (2 === (int)$condition['date_type']) {
                $click = $calc_info->three_day_click;
                $show = $calc_info->three_day_show;
            } else {
                $click = $calc_info->all_click;
                $show = $calc_info->all_show;
            }
            $result = Math::rate($click, $show);
            break;
        case 'cpc':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $click = $calc_info->today_click;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $click = $calc_info->three_day_click;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $click = $calc_info->all_click;
            }
            $result = Math::div($ori_cost, $click);
            break;
        case 'cpm':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $show = $calc_info->today_show;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $show = $calc_info->three_day_show;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $show = $calc_info->all_show;
            }
            $result = Math::div($ori_cost * 1000, $show);
            break;
        //转化率
        case 'convert_rate':
            if (1 === (int)$condition['date_type']) {
                $convert = $calc_info->today_convert;
                $click = $calc_info->today_click;
            } elseif (2 === (int)$condition['date_type']) {
                $convert = $calc_info->three_day_convert;
                $click = $calc_info->three_day_click;
            } else {
                $convert = $calc_info->all_convert;
                $click = $calc_info->all_click;
            }
            $result = Math::rate($convert, $click);
            break;
        //转化数
        case 'convert':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_convert;
            } else {
                $result = $calc_info->all_convert;
            }
            break;
        //深度转化数
        case 'deep_convert':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_deep_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_deep_convert;
            } else {
                $result = $calc_info->all_deep_convert;
            }
            break;
        //深度转化率
        case 'deep_convert_rate':
            if (1 === (int)$condition['date_type']) {
                $deep_convert = $calc_info->today_deep_convert;
                $convert = $calc_info->today_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $deep_convert = $calc_info->three_day_deep_convert;
                $convert = $calc_info->three_day_convert;
            } else {
                $deep_convert = $calc_info->all_deep_convert;
                $convert = $calc_info->all_convert;
            }

            $result = Math::rate($deep_convert, $convert);
            break;
        //媒体注册数
        case 'reg_count':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_register;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_register;
            } else {
                $result = $calc_info->all_register;
            }
            break;
        //媒体注册率
        case 'reg_rate':
            if (1 === (int)$condition['date_type']) {
                $register = $calc_info->today_register;
                $click = $calc_info->today_click;
            } elseif (2 === (int)$condition['date_type']) {
                $register = $calc_info->three_day_register;
                $click = $calc_info->three_day_click;
            } else {
                $register = $calc_info->all_register;
                $click = $calc_info->all_click;
            }

            $result = Math::rate($register, $click);
            break;
        //转化成本
        case 'cost_per_convert':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $convert = $calc_info->today_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $convert = $calc_info->three_day_convert;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $convert = $calc_info->all_convert;
            }
            $convert = $convert ?: 1;
            $result = Math::div($ori_cost, $convert);
            break;
        //深度转化成本
        case 'deep_convert_cost':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $deep_convert = $calc_info->today_deep_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $deep_convert = $calc_info->three_day_deep_convert;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $deep_convert = $calc_info->all_deep_convert;
            }
            $deep_convert = $deep_convert ?: 1;
            $result = Math::div($ori_cost, $deep_convert);
            break;
        //出价计费比
        case 'bid_billing_rate':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $convert = $calc_info->today_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $convert = $calc_info->three_day_convert;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $convert = $calc_info->all_convert;
            }
            $result = Math::div(Math::div($ori_cost, $convert), $calc_info->cpa_bid);
            break;
        //深度出价计费比
        case 'deep_bid_billing_rate':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $deep_convert = $calc_info->today_deep_convert;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $deep_convert = $calc_info->three_day_deep_convert;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $deep_convert = $calc_info->all_deep_convert;
            }
            $result = Math::div(Math::div($ori_cost, $deep_convert), $calc_info->cpa_bid);
            break;
        //关键行为数
        case 'game_addiction':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_game_addiction;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_game_addiction;
            } else {
                $result = $calc_info->all_game_addiction;
            }
            break;
        //关键行为率
        case 'game_addiction_rate':
            if (1 === (int)$condition['date_type']) {
                $game_addiction = $calc_info->today_game_addiction;
                $active = $calc_info->today_active;
            } elseif (2 === (int)$condition['date_type']) {
                $game_addiction = $calc_info->three_day_game_addiction;
                $active = $calc_info->three_day_active;
            } else {
                $game_addiction = $calc_info->all_game_addiction;
                $active = $calc_info->all_active;
            }
            $result = Math::rate($game_addiction, $active);
            break;
        //关键行为成本
        case 'game_addiction_cost':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $game_addiction = $calc_info->today_game_addiction;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $game_addiction = $calc_info->three_day_game_addiction;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $game_addiction = $calc_info->all_game_addiction;
            }
            $game_addiction = $game_addiction ?: 1;
            $result = Math::div($ori_cost, $game_addiction);
            break;
        //业务注册数
        case 'reg_uid_count':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_reg_uid_count;
            } else {
                $result = $calc_info->all_reg_uid_count;
            }
            break;
        //业务注册成本
        case 'cost_per_reg':
            if (1 === (int)$condition['date_type']) {
                $cost = $calc_info->today_cost;
                $reg_uid_count = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $cost = $calc_info->three_day_cost;
                $reg_uid_count = $calc_info->three_day_reg_uid_count;
            } else {
                $cost = $calc_info->all_cost;
                $reg_uid_count = $calc_info->all_reg_uid_count;
            }
            $reg_uid_count = $reg_uid_count ?: 1;
            $result = Math::div($cost, $reg_uid_count);
            break;
        //首日付费成本
        case 'cost_per_first_day_pay':
            if (1 === (int)$condition['date_type']) {
                $cost = $calc_info->today_cost;
                $first_day_pay_count = $calc_info->today_first_day_pay_count;
            } elseif (2 === (int)$condition['date_type']) {
                $cost = $calc_info->three_day_cost;
                $first_day_pay_count = $calc_info->three_day_first_day_pay_count;
            } else {
                $cost = $calc_info->all_cost;
                $first_day_pay_count = $calc_info->all_first_day_pay_count;
            }
            $first_day_pay_count = $first_day_pay_count ?: 1;
            $result = Math::div($cost, $first_day_pay_count);
            break;
        //首日付费人数
        case 'first_day_pay_count':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_first_day_pay_count;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_first_day_pay_count;
            } else {
                $result = $calc_info->all_first_day_pay_count;
            }
            break;
        //累计付费人数
        case 'total_pay_count':
            if (1 === (int)$condition['date_type']) {
                $result = $calc_info->today_total_pay_count;
            } elseif (2 === (int)$condition['date_type']) {
                $result = $calc_info->three_day_total_pay_count;
            } else {
                $result = $calc_info->all_total_pay_count;
            }
            break;
        //累计付费成本
        case 'cost_per_pay':
            if (1 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->today_ori_cost;
                $total_pay_count = $calc_info->today_total_pay_count;
            } elseif (2 === (int)$condition['date_type']) {
                $ori_cost = $calc_info->three_day_ori_cost;
                $total_pay_count = $calc_info->three_day_total_pay_count;
            } else {
                $ori_cost = $calc_info->all_ori_cost;
                $total_pay_count = $calc_info->all_total_pay_count;
            }
            $total_pay_count = $total_pay_count ?: 1;
            $result = Math::div($ori_cost, $total_pay_count);
            break;
        //次留率
        case 'rate_day_stay_2':
            if (1 === (int)$condition['date_type']) {
                $second_login_count = $calc_info->today_second_login_count;
                $reg_uid_count = $calc_info->today_reg_uid_count;
            } elseif (2 === (int)$condition['date_type']) {
                $second_login_count = $calc_info->three_day_second_login_count;
                $reg_uid_count = $calc_info->three_day_reg_uid_count;
            } else {
                $second_login_count = $calc_info->all_second_login_count;
                $reg_uid_count = $calc_info->all_reg_uid_count;
            }
            $result = Math::rate($second_login_count, $reg_uid_count);
            break;
        //首日ROI
        case 'first_day_roi':
            if (1 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->today_first_day_pay_money;
                $cost = $calc_info->today_cost;
            } elseif (2 === (int)$condition['date_type']) {
                $first_day_pay_money = $calc_info->three_day_first_day_pay_money;
                $cost = $calc_info->three_day_cost;
            } else {
                $first_day_pay_money = $calc_info->all_first_day_pay_money;
                $cost = $calc_info->all_cost;
            }
            $result = Math::rate($first_day_pay_money, $cost);
            break;
        case 'total_roi':
            if (1 === (int)$condition['date_type']) {
                $total_pay_money = $calc_info->today_total_pay_money;
                $cost = $calc_info->today_cost;
            } elseif (2 === (int)$condition['date_type']) {
                $total_pay_money = $calc_info->three_day_total_pay_money;
                $cost = $calc_info->three_day_cost;
            } else {
                $total_pay_money = $calc_info->all_total_pay_money;
                $cost = $calc_info->all_cost;
            }
            $result = Math::rate($total_pay_money, $cost);
            break;
        default:
            $msg = '规则ID：' . $calc_info->calc_rule_id . ' 数值指标中数据错误，' . json_encode($condition);
            Helpers::getLogger('Monitor-Calc')->error($msg);
            throw new AppException($msg);
    }

    return $result;
}

/**
 * 处理腾讯三级广告修改
 * @param $bind_info
 * @param $access_token_info
 * @param DynamicCreativeModel $model
 * @param $insert_operate_log
 * @return mixed
 */
function handleTencentUpdate($bind_info, $access_token_info, DynamicCreativeModel $model,
    $insert_operate_log)
{
//    $operate_success_detail = $operate_failed_detail = '';
    $request_data = [];
//    $update_value = '';
//    switch ($update_type) {
//        //二级广告开关
//        case 'configured_status':
    $switch_status = isset(ADFieldsENToCNMap::EN_TO_CN[$bind_info['current_switch']]) ?
        ADFieldsENToCNMap::EN_TO_CN[$bind_info['current_switch']] : $bind_info['current_switch'];
    $insert_operate_log['status_before_operate'] .= "开关状态：{$switch_status} ";
    if ('open' === $bind_info['switch']) {
        $insert_operate_log['switch'] = 1;
        $request_data['configured_status'] = 'AD_STATUS_NORMAL';
        $operate_success_detail = "开关：开启成功--";
    } else {
        $insert_operate_log['switch'] = 2;
        $request_data['configured_status'] = 'AD_STATUS_SUSPEND';
        $operate_success_detail = "开关：暂停成功--";
    }
    $update_value = $request_data['configured_status'];
    $operate_failed_detail = '开关调整失败，错误信息：';
//            break;
//    }
    $request_data['account_id'] = (int)$bind_info['account_id'];
    $request_data['dynamic_creative_id'] = (int)$bind_info['ad3_id'];
    try {
        $modify_time = date('Y-m-d H:i:s');
        $model->update($request_data, $access_token_info->access_token);
        if ($update_value) {
            $update = [];
            $update[$request_data['account_id']] = [$request_data['dynamic_creative_id']];
            $update_set = "CAST ( {$update_value} AS DECIMAL ( 12, 2 ) )";
            (new OdsTencentAdHisLogModel())->getLatestByADInfoAndInsert(
                $update,
                'configured_status',
                $update_set,
                $modify_time
            );
        }

        $insert_operate_log['operate_detail'] .= $operate_success_detail;
    } catch (AppException $e) {
        $response_data['message'] = $e->getMessage();
        $response_data['code'] = $e->getCode();
        $insert_operate_log['operate_detail'] .= $operate_failed_detail .
                                                 json_encode($response_data, JSON_UNESCAPED_UNICODE) . '--';
    }

    return $insert_operate_log;
}

/**
 * 判断数值条件
 * @param $result
 * @param $calc_condition
 * @return bool
 */
function judgeCalcCondition($result, $calc_condition)
{

    switch ($calc_condition['operator']) {
        case 'lt':
            $match = $result < $calc_condition['value'][0];
            break;
        case 'gt':
            $match = $result > $calc_condition['value'][0];
            break;
        case 'in':
            $match = ($result > $calc_condition['value'][0]) &&
                ($result < $calc_condition['value'][1]);
            break;
        default:
            return false;
    }

    return $match;
}

manageExecuteFrequency();
//monitorCalcMain(15);