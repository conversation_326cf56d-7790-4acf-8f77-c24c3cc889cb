<?php
//
// 消息日程事件
// 通过sh脚本+crontab控制频率
//

use App\MysqlConnection;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Task\GroupAssistantTask;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$service = new GroupAssistantService();
try {
    (new GroupAssistantTask())->handlerSchedule();
} catch (\Throwable $e) {
    \App\Utils\Helpers::getLogger('group_assistant')->error('脚本运行出错' . $e->getMessage());
}
