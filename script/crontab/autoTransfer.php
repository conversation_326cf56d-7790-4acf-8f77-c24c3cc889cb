<?php
/**
 * 自动转账监控脚本，整点执行，每30分钟执行一次
 */

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\CustomerCenter\AdvertiserModel as TransferModel;
use App\Model\HttpModel\Toutiao\Advertiser\AdvertiserModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\RedisModel\AutoTransferRuleRedisModel;
use App\Model\SqlModel\DataMedia\DwdAD2EffectBackDataLogModel;
use App\Model\SqlModel\DataMedia\OdsAutoTransferOperateLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAccountLogModel;
use App\Model\SqlModel\Zeda\AutoTransferRuleModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\ADAnalysisToutiaoTransferParam;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
const CACHE_EXPIRE_TIME = 300;
/**
 * 调度不同频率的执行频率和次序
 */
function manageExecuteFrequency()
{
    //延迟整点三分钟执行
    sleep(180);
    ob_start();
    echo date('Y-m-d H:i:s') . "开始执行头条自动转账匹配" . PHP_EOL;
    ob_flush();
    $auto_transfer_redis_model = new AutoTransferRuleRedisModel();
    $frequency = $auto_transfer_redis_model->hget(AutoTransferRuleRedisModel::HASH_KEY, 'auto_transfer_last_frequency');
    if (!$frequency) {
        $frequency = 30;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'auto_transfer_last_frequency', $frequency);
    }
    if (30 === (int)$frequency) {
        //如果上次执行的是30分钟频率的，则本次30分钟和60分钟频率的都执行
        echo "本次执行：30分钟和60分钟频率的自动转账匹配" . PHP_EOL . "开始执行：30分钟频率" . PHP_EOL;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'auto_transfer_last_frequency', 60);
        ob_flush();
        autoTransferMainHandle(30);
        echo "开始执行：60分钟频率规则" . PHP_EOL;
        ob_flush();
        autoTransferMainHandle(60);
    } elseif (60 === (int)$frequency) {
        //如果上次执行的是60分钟频率的，则本次只执行30分钟频率的
        echo "开始执行：30分钟频率规则" . PHP_EOL;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'auto_transfer_last_frequency', 30);
        ob_flush();
        autoTransferMainHandle(30);
    }
    echo "头条自动转账匹配结束" . PHP_EOL . PHP_EOL;
    ob_end_flush();
}

function autoTransferMainHandle($frequency)
{
    $auto_transfer_rule_model = new AutoTransferRuleModel();
    $media_account = new MediaAccountModel();
    $redis_model = new AutoTransferRuleRedisModel();
    $all_auto_transfer_rule = $auto_transfer_rule_model->getAllByFrequency($frequency, MediaType::TOUTIAO);
    if ($all_auto_transfer_rule->isEmpty()) {
        return;
    }
    $update_account_ids = [];
    ob_start();
    foreach ($all_auto_transfer_rule as $one_rule) {
        echo '开始执行头条自动转账 规则名：' . $one_rule->name . ' 规则ID：' . $one_rule->id . PHP_EOL;
        ob_flush();
        $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
            'execute_status' => AutoTransferRuleModel::RUNNING_EXECUTE,
            'last_execute_time' => time(),
        ]);
        //检查时间
        if (time() < strtotime($one_rule->start_time) || time() > strtotime($one_rule->end_time)) {
            $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                'last_execute_time' => time(),
            ]);
            continue;
        }

        $all_account_ids = [];
        $execution_account = json_decode($one_rule->execution_account, true);
        //聚合所有account id
        foreach ($execution_account as $item) {
            $all_account_ids = array_merge($all_account_ids, $item['use_account_id']);
            $all_account_ids[] = $item['reserve_account_id'];
        }
        unset($item);
//        $all_account_ids = array_merge($use_account_ids, $reserve_account_ids);
        $all_account_info = getAccountInfoByAccountIds($all_account_ids);
        $use_account_ids = [];
        $reserve_account_ids = [];
        //检查是否同结算主体&代理商ID
        foreach ($execution_account as $k => $account_set) {
            //备款账户都没有那么整个集合都要丢弃
            if (!isset($all_account_info[$account_set['reserve_account_id']])) {
                unset($k);
                continue;
            }
            $reserve_account_transfer_key = $all_account_info[$account_set['reserve_account_id']]->transferable_key;
            foreach ($account_set['use_account_id'] as $key => $one_account) {
                if ($reserve_account_transfer_key !==$all_account_info[$one_account]->transferable_key) {
                    //不是同transferable_key&&代理商ID&&结算主体就从集合中去掉
                    unset($account_set['use_account_id'][$key]);
                }
            }

            $use_account_ids = array_merge($use_account_ids, $account_set['use_account_id']);
            $reserve_account_ids[] = $account_set['reserve_account_id'];
        }
        //检查余额
        if ($balance_condition = json_decode($one_rule->balance_condition, true)) {
            $check_balance = checkBalance($use_account_ids, $reserve_account_ids, $balance_condition, $all_account_info);
            if (false === $check_balance) {
                $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                    'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                    'last_execute_time' => time(),
                ]);
                continue;
            }
            $use_account_ids = $check_balance['match_use_account'];
            $reserve_account_ids = $check_balance['match_reserve_account'];
        }
        //检查数值条件
        if ($calc_condition = json_decode($one_rule->calc_condition, true)) {
            $check_calc = checkCalc($use_account_ids, $calc_condition);
            if (false === $check_calc) {
                $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                    'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                    'last_execute_time' => time(),
                ]);
                continue;
            }
            $use_account_ids = array_diff($use_account_ids, $check_calc);
        }

        if (!$use_account_ids) {
            $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                'last_execute_time' => time(),
            ]);
            continue;
        }
        //以上通过即进行转账操作
        $result = executeTransfer($one_rule, $use_account_ids, $reserve_account_ids, $all_account_info);
        $update_account_ids = array_merge($update_account_ids, $result['update_account_ids']);
        //更新media_account表账号当日已转账数额
        if ($result['need_update_daily_transfer_amount']) {
            $all_update_data = $redis_model->hGetAll(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT);
            if ($all_update_data) {
                $media_account->updateAccountDailyTransferAmount(
                    'today_transfer_in_amount',
                    $all_update_data
                );
            }

        }
        //更新规则执行状态
        $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
            'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
            'last_execute_time' => time(),
        ]);
    }
    //投递redis更新account
    $update_account_ids = array_flip(array_flip($update_account_ids));
    if ($update_account_ids) {
        (new ToutiaoTaskModel())->account(array_values($update_account_ids));
    }
}

/**
 * 执行转账
 * @param $rule
 * @param $use_account_ids
 * @param $reserve_account_ids
 * @param $all_account_info
 * @return array[]
 */
function executeTransfer($rule, $use_account_ids, $reserve_account_ids, $all_account_info)
{
    $http_model = new TransferModel;
    $advertiser_model = new AdvertiserModel();
    $redis_model = new AutoTransferRuleRedisModel();
    $operate_log_model = new OdsAutoTransferOperateLogModel();
    $media_account_model = new MediaAccountModel();
    $all_account_ids = array_merge($use_account_ids, $reserve_account_ids);
    //防止SQL超长
    $all_account_ids = collect($all_account_ids)->chunk(2000);
    $access_token_info = [];
    foreach ($all_account_ids as $one_part) {
        $one_part_info = $media_account_model->getAccessTokenInAccountIds($one_part, MediaType::TOUTIAO);
        $access_token_info = $one_part_info->merge($access_token_info);
    }
    $access_token_info = $access_token_info->keyBy('account_id');
    $update_account_ids = [];
    $daily_transfer_amount = false;
    $execution_account = json_decode($rule->execution_account, true);
    foreach ($execution_account as $item) {
        if (!in_array($item['reserve_account_id'], $reserve_account_ids)) {
            continue;
        }
        $all_record = [];
        foreach ($item['use_account_id'] as $one_account_id) {
            if (!in_array($one_account_id, $use_account_ids)) {
                continue;
            }

            if ('RECHARGE' === $rule->transfer_type) {
                //充值
                $advertiser_id = (int)$item['reserve_account_id'];
                $target_advertiser_id = (int)$one_account_id;
            } else {
                //退款
                $advertiser_id = (int)$one_account_id;
                $target_advertiser_id = (int)$item['reserve_account_id'];
            }
            $record = [];
            $record['platform'] = $access_token_info[$advertiser_id]->platform;
            $record['auto_transfer_rule_id'] = $rule->id;
            $record['auto_transfer_rule_name'] = $rule->name;
            $record['media_type'] = $access_token_info[$advertiser_id]->media_type;
            $record['rule_creator'] = $rule->creator;
            $record['account_id'] = $advertiser_id;
            $record['account_name'] = $access_token_info[$advertiser_id]->account_name;
            $record['target_account_id'] = $target_advertiser_id;
            $record['target_account_name'] = $access_token_info[$target_advertiser_id]->account_name;

            $access_token = $access_token_info[$advertiser_id]->access_token;
            try {
                $transferable_fund = $advertiser_model->getTransferableFund($advertiser_id, $access_token);
                if (array_diff(array_values(ADAnalysisToutiaoTransferParam::ENUM_TO_FIELD_NAME), array_keys($transferable_fund))) {
                    throw new AppException('获取可转帐金额媒体接口返回数据错误');
                }
                $all_account_info[$advertiser_id]->valid_grant = $grant_valid = $transferable_fund['grant_valid'];
                $all_account_info[$advertiser_id]->universal_prepay_valid = $universal_prepay_valid = $transferable_fund['universal_prepay_valid'];
                $all_account_info[$advertiser_id]->brand_prepay_valid = $brand_prepay_valid = $transferable_fund['brand_prepay_valid'];
                $all_account_info[$advertiser_id]->bid_prepay_valid = $bid_prepay_valid = $transferable_fund['bid_prepay_valid'];
                $all_account_info[$advertiser_id]->universal_credit_valid = $universal_credit_valid = $transferable_fund['universal_credit_valid'];
                $all_account_info[$advertiser_id]->brand_credit_valid = $brand_credit_valid = $transferable_fund['brand_credit_valid'];
                $all_account_info[$advertiser_id]->bid_credit_valid = $bid_credit_valid = $transferable_fund['bid_credit_valid'];
            } catch (AppException $e) {
                $grant_valid = $all_account_info[$advertiser_id]->valid_grant;
                $universal_prepay_valid = $all_account_info[$advertiser_id]->universal_prepay_valid;
                $brand_prepay_valid = $all_account_info[$advertiser_id]->brand_prepay_valid;
                $bid_prepay_valid = $all_account_info[$advertiser_id]->bid_prepay_valid;
                $universal_credit_valid = $all_account_info[$advertiser_id]->universal_credit_valid;
                $brand_credit_valid = $all_account_info[$advertiser_id]->brand_credit_valid;
                $bid_credit_valid = $all_account_info[$advertiser_id]->bid_credit_valid;
            }

            if ('RECHARGE' === $rule->transfer_type) {
                $amount = (float)Math::decimal($item['amount'], 2);
            } else {
                $amount = $grant_valid + $universal_prepay_valid + $brand_prepay_valid + $bid_prepay_valid +
                    $universal_credit_valid + $brand_credit_valid + $bid_credit_valid;
            }

            //总可转帐余额
            $balance_valid = $grant_valid + $universal_prepay_valid + $brand_prepay_valid + $bid_prepay_valid +
                $universal_credit_valid + $brand_credit_valid + $bid_credit_valid;
            //验证转账额度
            if ((float)$redis_model->hGet(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT, $target_advertiser_id) + (float)$amount >
                $access_token_info[$target_advertiser_id]->daily_transfer_in_limit) {
                //达到转账限额
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $amount;
                $record['transfer_type'] = 0;
                $record['transaction_seq'] = 0;
                $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账失败，收款账号达到每日转账限额';
                $record['status'] = 2;
                $all_record[] = $record;
                if ('RECHARGE' === $rule->transfer_type) {
                    //充值，用款账户额度满了，跳出一层
                    continue;
                } else {
                    //退款，备款账户满了，跳出两层
                    $operate_log_model->add($all_record);
                    continue 2;
                }
            }
            //拦截退款时，退款账户cash_valid和grant_valid都是0的情况
            //可用总金额不足，停止本次转账. 转账金额为现金且可用现金小于转账金额。转账金额为赠款且可用赠款小于转账金额
            if ((0.0 === (float)$balance_valid || $balance_valid < $amount) ||
                ('CASH' === $rule->amount_type && ($universal_prepay_valid + $brand_prepay_valid + $bid_prepay_valid +
                        $universal_credit_valid + $brand_credit_valid + $bid_credit_valid) < $amount) ||
                ('GRANT' === $rule->amount_type && $grant_valid < $amount)) {
                //达到转账限额
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $amount;
                $record['transfer_type'] = 0;
                $record['transaction_seq'] = 0;
                $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账失败，总可用余额不足';
                $record['status'] = 2;
                $all_record[] = $record;
                if ('RECHARGE' === $rule->transfer_type) {
                    //充值，备款账户余额不足， 跳出两层
                    $operate_log_model->add($all_record);
                    continue 2;
                } else {
                    //退款，用款账户余额不足，跳出一层
                    continue;
                }
            }

            $transfer_cash = 0;
            $transfer_grant = 0;
            switch (true) {
                case 'CASH' === $rule->amount_type:
                    $transfer_cash = $amount;
                    break;
                case 'GRANT' === $rule->amount_type:
                    $transfer_grant = $amount;
                    break;
                default :
                    //先转赠款再转现金
                    if ($amount > $grant_valid) {
                        $transfer_cash = $amount - $grant_valid;
                        $transfer_grant = $grant_valid;
                    } else {
                        $transfer_cash = 0;
                        $transfer_grant = $amount;
                    }
            }
//            if ('CASH' === $rule->amount_type) {
//                $transfer_cash = $amount;
//            } elseif ('GRANT' === $rule->amount_type) {
//                $transfer_grant = $amount;
//            } else {
//                //先转赠款再转现金
//                if ($amount > $grant_valid) {
//                    $transfer_cash = $amount - $grant_valid;
//                    $transfer_grant = $grant_valid;
//                } else {
//                    $transfer_cash = 0;
//                    $transfer_grant = $amount;
//                }
//            }

            if ((float)$transfer_grant) {
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $transfer_grant;
                $record['transfer_type'] = ADAnalysisToutiaoTransferParam::TRANSFER_TYPE['GRANT'];

                $response = [];
                try {
                    $response = $http_model->transfer(
                        $advertiser_id,
                        $target_advertiser_id,
                        $transfer_grant,
                        'GRANT',
                        $access_token
                    );
                } catch (AppException $e) {
                    //如果因为赠款不能全部转出，如果媒体返回估算的可转金额，则根据媒体返回错误提示中的可转金额再次尝试转赠款
                    try {
                        if (false === strpos($e->getMessage(), '根据试算')) {
                            throw new AppException($e->getMessage(), $e->getCode());
                        }
                        //获取媒体返回错误中的可转金额
                        $start = mb_strpos($e->getMessage(), '[');
                        $end = mb_strpos($e->getMessage(), ']');
                        $data = json_decode(mb_substr($e->getMessage(), $start, $end - $start + 1), true);
                        if (!is_array($data) || count($data) <= 1 || $data[1] <= 0) {
                            throw new AppException($e->getMessage(), $e->getCode());
                        }
                        $transfer_grant = $data[1];
                        $response = $http_model->transfer(
                            $advertiser_id,
                            $target_advertiser_id,
                            $transfer_grant,
                            'GRANT',
                            $access_token
                        );
                    } catch (AppException $exception) {
                        $record['transaction_seq'] = 0;
                        $error['message'] = $e->getMessage();
                        $error['code'] = $e->getCode();
                        $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，失败原因：" .
                            json_encode($error, JSON_UNESCAPED_UNICODE);
                        $record['status'] = 2;
                        //如果转赠款失败，并且没有指定转账金额的类型，则改为全部转现金
                        if (!$rule->amount_type) {
                            $transfer_cash = $amount;
                        }
                    }
                }

                if ($response) {
                    //如果转账成功
                    $record['transaction_seq'] = $response['transaction_seq'] ?? 0;
                    $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账成功';
                    $record['status'] = 1;
                    //缓存账号和目标账号信息到redis
                    $account_info = $all_account_info[$advertiser_id];
                    $account_info->grant = $account_info->grant - $transfer_grant;
                    $account_info->valid_grant = $account_info->valid_grant - $transfer_grant;
                    $account_info->balance = $account_info->balance - $transfer_grant;
                    $account_info->valid_balance = $account_info->valid_balance - $transfer_grant;
                    $redis_model->hMSet(
                        AutoTransferRuleRedisModel::BALANCE_PREFIX . $advertiser_id,
                        (array)$account_info
                    );
                    $redis_model->expire(AutoTransferRuleRedisModel::BALANCE_PREFIX . $advertiser_id, CACHE_EXPIRE_TIME);

                    $target_account_info = $all_account_info[$target_advertiser_id];
                    $target_account_info->grant = $target_account_info->grant + $transfer_grant;
                    $target_account_info->valid_grant = $target_account_info->valid_grant + $transfer_grant;
                    $target_account_info->balance = $target_account_info->balance + $transfer_grant;
                    $target_account_info->valid_balance = $target_account_info->valid_balance + $transfer_grant;
                    $redis_model->hMSet(
                        AutoTransferRuleRedisModel::BALANCE_PREFIX . $target_advertiser_id,
                        (array)$target_account_info
                    );
                    $redis_model->expire(AutoTransferRuleRedisModel::BALANCE_PREFIX . $target_advertiser_id, CACHE_EXPIRE_TIME);

                    $update_account_ids[] = $advertiser_id;
                    $update_account_ids[] = $target_advertiser_id;
                    //如果转账成功，重新计算需要转的现金
                    $transfer_cash = $amount - $transfer_grant;
                    $daily_transfer_amount = true;
                    //更新当日转账额度
                    $current_amount = $redis_model->hGet(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT, $target_advertiser_id);
                    $redis_model->hSet(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT,
                        $target_advertiser_id, $current_amount ? (float)$current_amount + (float)$transfer_grant : (float)$transfer_grant);
                }
                $all_record[] = $record;
            }

            if ((float)$transfer_cash) {
                //循环转账六种现金
                foreach (ADAnalysisToutiaoTransferParam::TRANSFER_SORT as $transfer_type) {
                    //钱转完就跳出去结束喽
                    if ($transfer_cash === 0) {
                        break;
                    }

                    $transfer_type_field = ADAnalysisToutiaoTransferParam::ENUM_TO_FIELD_NAME[$transfer_type];
                    if (0.0 === (float)$$transfer_type_field) {
                        continue;
                    }
                    $transfer_amount = $$transfer_type_field >= $transfer_cash ? $transfer_cash : $$transfer_type_field;

                    $record['operate_time'] = date('Y-m-d H:i:s');
                    $record['amount'] = $transfer_amount;
                    $record['transfer_type'] = ADAnalysisToutiaoTransferParam::TRANSFER_TYPE[$transfer_type];
                    $response = [];
                    try {
                        $response = $http_model
                            ->transfer($advertiser_id, $target_advertiser_id, $transfer_amount, $transfer_type, $access_token);
                    } catch (AppException $e) {
                        //如果因为赠款不能全部转出，如果媒体返回估算的可转金额，则根据媒体返回错误提示中的可转金额再次尝试转赠款
                        try {
                            if (false === strpos($e->getMessage(), '根据试算')) {
                                throw new AppException($e->getMessage(), $e->getCode());
                            }
                            //获取媒体返回错误中的可转金额
                            $start = mb_strpos($e->getMessage(), '[');
                            $end = mb_strpos($e->getMessage(), ']');
                            $data = json_decode(mb_substr($e->getMessage(), $start, $end - $start + 1), true);
                            if (!is_array($data) || count($data) <= 1 || $data[1] <= 0) {
                                throw new AppException($e->getMessage(), $e->getCode());
                            }
                            $transfer_amount = $data[1];
                            $response = $http_model->transfer(
                                $advertiser_id,
                                $target_advertiser_id,
                                $transfer_amount,
                                $transfer_type,
                                $access_token
                            );
                        } catch (AppException $exception) {
                            $record['transaction_seq'] = 0;
                            $error['message'] = $e->getMessage();
                            $error['code'] = $e->getCode();
                            $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，失败原因：" .
                                json_encode($error, JSON_UNESCAPED_UNICODE);
                            $record['status'] = 2;
                        }

//                            $record['transaction_seq'] = 0;
//                            $error['message'] = $e->getMessage();
//                            $error['code'] = $e->getCode();
//                            $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，失败原因：" .
//                                json_encode($error, JSON_UNESCAPED_UNICODE);
//                            $record['status'] = 2;
                    }

                    if ($response) {
                        //转一部分减一部分，$transfer_cash就是剩余循环要转的钱
                        $transfer_cash = $transfer_cash - $transfer_amount;
                        //如果转账成功
                        $record['transaction_seq'] = $response['transaction_seq'] ?? 0;
                        $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账成功';
                        $record['status'] = 1;
                        //缓存账号和目标账号信息到redis
                        $account_info = $all_account_info[$advertiser_id];
                        $account_info->$transfer_type_field = $$transfer_type_field - $transfer_amount;
//                        $account_info->valid_cash = $account_info->valid_cash - $transfer_amount;
                        $account_info->balance = $account_info->balance - $transfer_amount;
                        $account_info->valid_balance = $account_info->valid_balance - $transfer_amount;
                        $redis_model->hMSet(
                            AutoTransferRuleRedisModel::BALANCE_PREFIX . $advertiser_id,
                            (array)$account_info
                        );
                        $redis_model->expire(AutoTransferRuleRedisModel::BALANCE_PREFIX . $advertiser_id, CACHE_EXPIRE_TIME);

                        $target_account_info = $all_account_info[$target_advertiser_id];
                        $target_account_info->$transfer_type_field = $target_account_info->$transfer_type_field + $transfer_amount;
//                        $target_account_info->valid_cash = $target_account_info->valid_cash + $transfer_amount;

                        $target_account_info->balance = $target_account_info->balance + $transfer_amount;
                        $target_account_info->valid_balance = $target_account_info->valid_balance + $transfer_amount;
                        $redis_model->hMSet(
                            AutoTransferRuleRedisModel::BALANCE_PREFIX . $target_advertiser_id,
                            (array)$target_account_info
                        );
                        $redis_model->expire(AutoTransferRuleRedisModel::BALANCE_PREFIX . $target_advertiser_id, CACHE_EXPIRE_TIME);

                        $update_account_ids[] = $advertiser_id;
                        $update_account_ids[] = $target_advertiser_id;
                        $daily_transfer_amount = true;
                        $current_amount = $redis_model->hGet(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT, $target_advertiser_id);
                        $redis_model->hSet(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT,
                            $target_advertiser_id, $current_amount ? (float)$current_amount + (float)$transfer_amount : (float)$transfer_amount);
                    }
                    $all_record[] = $record;
                }
            }
        }
        $operate_log_model->add($all_record);
    }

    return [
        'update_account_ids' => $update_account_ids,
        'need_update_daily_transfer_amount' => $daily_transfer_amount,
    ];
}

/**
 * 检查数值条件
 * @param $use_account_ids
 * @param $calc_condition
 * @return bool|array
 */
function checkCalc($use_account_ids, $calc_condition)
{
    $account_cost_data = (new DwdAD2EffectBackDataLogModel())
        ->getAccountCalcInAccountIds($use_account_ids, MediaType::TOUTIAO)
        ->keyBy('account_id');

    $field = ['cost', 'avg_day_cost', 'first_day_roi', 'count_ad2'];
    $not_match_use_account = [];
    $check_gt = false;
    foreach ($calc_condition as $item) {
        //如果没有查到消耗数据，三个消耗指标的条件且要求大于xxx，则必定不符合，用款账户全部不匹配
        if (in_array($item['name'], $field) && 'gt' === $item['operator']) {
            $check_gt = true;
            break;
        }
    }
    if ($account_cost_data->isEmpty()) {
        //如果没有查到消耗数据，三个消耗指标的条件且要求大于xxx，则必定不符合，用款账户全部不匹配
        if (true === $check_gt) {
            return false;
        }
    } else {
        foreach ($use_account_ids as $one_account_id) {
            //没查到账号的消耗数据，且数值条件规则有
            if (!isset($account_cost_data[$one_account_id])) {
                if (true === $check_gt) {
                    $not_match_use_account[] = $one_account_id;
                    continue;
                }
            }

            foreach ($calc_condition as $item) {
                switch ($item['name']) {
                    case 'count_ad2':
                        if (1 === (int)$item['date_type']) {
                            //当天
                            $result = $account_cost_data[$one_account_id]->today_count_ad2 ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            //前三天
                            $result = $account_cost_data[$one_account_id]->three_day_count_ad2 ?? 0;
                        } else {
                            //总
                            $result = $account_cost_data[$one_account_id]->all_count_ad2 ?? 0;
                        }
                        break;
                    //消耗
                    case 'cost':
                        if (1 === (int)$item['date_type']) {
                            //当天
                            $result = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            //前三天
                            $result = $account_cost_data[$one_account_id]->three_day_cost ?? 0;
                        } else {
                            //总
                            $result = $account_cost_data[$one_account_id]->all_cost ?? 0;
                        }

                        break;
                    //平均日消耗
                    case 'avg_day_cost':
                        if (1 === (int)$item['date_type']) {
                            $result = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } else {
                            $result = Math::div($account_cost_data[$one_account_id]->three_day_cost ?? 0, 3);
                        }
                        break;
                    //首日ROI
                    case 'first_day_roi':
                        if (1 === (int)$item['date_type']) {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->today_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->three_day_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->three_day_cost ?? 0;
                        } else {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->all_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->all_cost ?? 0;
                        }

                        $result = Math::rate($first_day_pay_money, $cost);
                        break;
                    //过去x小时无消耗
                    case 'last_hours_no_cost':
                        $result = strtotime($account_cost_data[$one_account_id]->last_cost_date_hour ?? '1970-01-01 08:00:00');
                        $item['value'] = time() - $item['value'] * 3600;
                        $item['operator'] = 'lt';
                        break;
                    //过去x天无消耗
                    case 'last_days_no_cost':
                        $result = strtotime($account_cost_data[$one_account_id]->last_cost_date_hour ?? '1970-01-01 08:00:00');
                        //模拟组装judgeCondition()函数的判断所需格式， 一起判断
                        $item['value'] = time() - $item['value'] * 86400;
                        $item['operator'] = 'lt';
                        break;
                    //过去x天无转入转出
                    case 'last_days_no_transfer':
                        $result = strtotime($account_cost_data[$one_account_id]->last_transaction_time ?? '1970-01-01 08:00:00');
                        //模拟组装judgeCondition()函数的判断所需格式， 一起判断
                        $item['value'] = time() - $item['value'] * 86400;
                        $item['operator'] = 'lt';
                        break;
                    default:
                        $msg = '数值指标中数据错误';
                        Helpers::getLogger('Auto-transfer')->error($msg);
                        throw new AppException($msg);
                }
                if (false === judgeCondition($result, $item)) {
                    $not_match_use_account[] = $one_account_id;
                    continue 2;
                }
            }

        }

        if ($account_cost_data->count() === count($not_match_use_account)) {
            return false;
        }
    }

    return $not_match_use_account;
}

/**
 * 检查余额条件
 * @param $use_account_ids
 * @param $reserve_account_ids
 * @param $balance_condition
 * @param $account_info
 * @return bool|array
 */
function checkBalance($use_account_ids, $reserve_account_ids, $balance_condition, $account_info)
{
    //循环检查余额和规则是否匹配, 不符合返回不符合的账号ID集合
    $match_use_account = [];
    $match_reserve_account = [];
    foreach ($balance_condition as $item) {
        $judge_account_ids = $item['name'] === 'reserve_account_balance' ? $reserve_account_ids : $use_account_ids;
        foreach ($judge_account_ids as $account_id) {
            if (true === judgeCondition($account_info[$account_id]->valid_balance, $item)) {
                if ($item['name'] === 'reserve_account_balance') {
                    $match_reserve_account[] = $account_id;
                } else {
                    $match_use_account[] = $account_id;
                }
            }
        }
        unset($account_id);
    }
    unset($item);

    //如果通过验证的备款或用款账号列表某一个为空, 直接返回false结束
    if (!$match_use_account || !$match_reserve_account) {
        return false;
    }

    return [
        'match_use_account' => $match_use_account,
        'match_reserve_account' => $match_reserve_account,
    ];
}

/**
 * 根据账号ID获取账号信息
 * @param $all_account_ids
 * @return array
 */
function getAccountInfoByAccountIds($all_account_ids)
{
    //获取账户信息
    $redis_model = new AutoTransferRuleRedisModel();
    $account_model = new OdsToutiaoAccountLogModel();
    $redis_balance = [];
    //先检查redis中有没有余额的缓存，为了保证数据最准确
    foreach ($all_account_ids as $one_acc_id) {
        $balance = $redis_model->hGetAll(AutoTransferRuleRedisModel::BALANCE_PREFIX . $one_acc_id);
        if ($balance) {
            $redis_balance[(string)$one_acc_id] = (object)$balance;
        }
    }
    unset($one_acc_id);
    //如果redis里拿不到所有的账号余额信息, 则拿不到的部分从数据库拿
    if (count($redis_balance) !== count($all_account_ids)) {
        $get_from_db_account = array_diff($all_account_ids, array_keys($redis_balance));
        $get_from_db_account = collect($get_from_db_account)->chunk(4000);
        $db_balance = [];

        foreach ($get_from_db_account as $one_set) {
            $one_part_db_balance = $account_model->getAccountInfosInAccountIds($one_set->toArray(), false);
            $db_balance = $one_part_db_balance->merge($db_balance);
        }
        $account_info = array_replace($db_balance->keyBy('account_id')->toArray(), $redis_balance);
    } else {
        $account_info = $redis_balance;
    }

    return $account_info;
}

/**
 * 判断数值条件
 * @param $result
 * @param $calc_condition
 * @return bool
 */
function judgeCondition($result, $calc_condition)
{

    switch ($calc_condition['operator']) {
        case 'lt':
            $match = $result < $calc_condition['value'];
            break;
        case 'gt':
            $match = $result > $calc_condition['value'];
            break;
        case 'eq':
            $match = $result == $calc_condition['value'];
            break;
        default:
            return false;
    }

    return $match;
}

manageExecuteFrequency();