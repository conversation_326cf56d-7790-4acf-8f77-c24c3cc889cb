<?php

// 
// 定时添加屏蔽词 屏蔽用户
// */30 * * * * php
// 

use App\Constant\MediaType;
use App\Logic\DSP\ShieldedLogic;
use App\Model\SqlModel\DataMedia\OdsToutiaoAwemeBannedModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoCommentTermsBannedModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$start_time = time();

MysqlConnection::setConnection();

$create_time = strtotime(date("Y-m-d H:i:00", "-30 min"));

$conn = MysqlConnection::getConnection('default');
$list = $conn
    ->table('media_account')
    ->select('account_id')
    ->where('state', 1)
    ->where('media_type', MediaType::TOUTIAO)
    ->where('create_time', '>=', $create_time)
    ->get();

$accounts = $list->pluck('account_id')->toArray();

$terms = [];
$aweme_ids = [];
$nicknames = [];

$term_model = new OdsToutiaoCommentTermsBannedModel();
$term_list = $term_model->getGlobalTerms();
foreach ($term_list as $item) {
    $terms[] = $item->term;
}

$aweme_model = new OdsToutiaoAwemeBannedModel();
$aweme_list = $aweme_model->getGlobalUsers();
foreach ($aweme_list as $item) {
    if ($item->banned_type == OdsToutiaoAwemeBannedModel::BANNED_CUSTOM) {
        $nicknames[] = $item->nickname_keyword;
    } else if ($item->banned_type == OdsToutiaoAwemeBannedModel::BANNED_AWEME) {
        $aweme_ids[] = $item->aweme_id;
    }
}


$logic = new ShieldedLogic();

if ($accounts && $terms) {
    $logic->addWordBackground(ShieldedLogic::SYS_OPTION_ID, ShieldedLogic::SYS_OPTION_NAME, OdsToutiaoCommentTermsBannedModel::TYPE_GLOBAL, $accounts, $terms);
}

if ($accounts && $nicknames) {
    $logic->addUserBackground(ShieldedLogic::SYS_OPTION_ID, ShieldedLogic::SYS_OPTION_NAME, OdsToutiaoAwemeBannedModel::BANNED_CUSTOM, OdsToutiaoAwemeBannedModel::TYPE_GLOBAL, $accounts, $nicknames);
}

if ($accounts && $aweme_ids) {
    $logic->addUserBackground(ShieldedLogic::SYS_OPTION_ID, ShieldedLogic::SYS_OPTION_NAME, OdsToutiaoAwemeBannedModel::BANNED_AWEME, OdsToutiaoAwemeBannedModel::TYPE_GLOBAL, $accounts, $aweme_ids);
}

$cnt = count($accounts);
$spend_time = time() - $start_time;
echo "finish. update account({$cnt}) spend: {$spend_time}." . PHP_EOL;
