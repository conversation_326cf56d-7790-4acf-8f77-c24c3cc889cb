<?php

/**
 * @script_name 定时拉取飞书用户信息
 * @crontab_expression 0 0 * * *
 * @crontab_desc 每天凌晨十二点
 */

use App\Logic\FeishuLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

try {
    (new FeishuLogic())->getEmployeesList();
} catch (Exception $e) {
    \App\Utils\Helpers::getLogger('feishu_employee')->error('任务出错，错误信息：' . $e->getMessage());
}

// 做个缓存 只拿群聊助手的
$redis = \App\Struct\RedisCache::getInstance();
$model = new \App\Model\SqlModel\Zeda\FeiShuUserModel();

$list = $model->getAllUserIDByAppID(\App\Service\GroupAssistant\GroupAssistantService::APP_ID);

$open_id_key = 'feishu_open_id';
$union_id_key = 'feishu_union_id';
$open_id_to_union_id_key = 'feishu_open_id_to_union_id';


$open_id_list = [];
$union_id_list = [];
$open_id_to_union_id_list = [];
foreach ($list as $item) {
    $open_id_list[$item->open_id] = $item->name;
    $union_id_list[$item->union_id] = $item->name;
    $open_id_to_union_id_list[$item->open_id] = $item->union_id;
}
if ($list->isNotEmpty()) {
    $redis->hMSet($open_id_key, $open_id_list);
    $redis->hMSet($union_id_key, $union_id_list);
    $redis->hMSet($open_id_to_union_id_key, $open_id_to_union_id_list);
}


