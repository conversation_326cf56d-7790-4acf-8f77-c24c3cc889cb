<?php
/*
 * 0 3 * * *
 * 素材规则定时匹配
 */

use App\Exception\AppException;
use App\Logic\DSP\MaterialShareRuleMQLogic;
use App\Model\SqlModel\DataMedia\OdsMaterialLogModel;
use App\Model\SqlModel\DataMedia\OdsRankMaterialModel;
use App\Model\SqlModel\Zeda\MaterialShareRuleModel;
use App\Model\SqlModel\Zeda\RankMaterialModel;
use App\MysqlConnection;
use App\Task\MaterialTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();

/**
 * 投递定时任务到队列
 */
function deliveryTimingRuleMatchData()
{
    $queue_data['match_type'] = MaterialTask::TIMING_MATCH_MATERIAL;
    (new MaterialShareRuleMQLogic())->produceMatchMaterialDataTask($queue_data);
}

/**
 * 处理素材匹配定时共享规则
 */
//function handleTimingRule()
//{
//    ob_start();
//    echo date('Y-m-d H:i:s') . '--Timing Rules Match begin' . PHP_EOL;
//    ob_flush();
//    $share_rule_model = new MaterialShareRuleModel();
//    $share_rules = $share_rule_model->getAllByRuleType(MaterialShareRuleModel::TIMING_RULE_TYPE);
//    if ($share_rules->isEmpty()) {
//        $msg = __FUNCTION__ . '-getAllByRuleType() 方法无定时共享规则数据';
//        Helpers::getLogger('material-match')->error($msg);
//        throw new AppException($msg);
//    }
//    $material_log_model = new OdsMaterialLogModel();
//    $material_list = $material_log_model->getAll(true);
//    if ($material_list->isEmpty()) {
//        $msg = __FUNCTION__ . '-getAllJoinMaterialFile() 方法无素材数据';
//        Helpers::getLogger('material-match')->error($msg);
//        throw new AppException($msg);
//    }
//
//    $materials = [];
//    foreach ($material_list as $k => $material) {
//        //因为遍历对象是引用传值，为了防止重复修改对象所以先进行转换
//        $material = MaterialTask::transJsonToArray($material);
//        $materials[] = $material;
//    }
//    $rank_material_insert = [];
//    $ods_rank_material_insert = [];
//    foreach ($share_rules as $key => $rule) {
//        $share_rule_model->updateRuleMatchStatus($rule->id, [
//            'match_status' => MaterialShareRuleModel::RUNNING_MATCH
//        ]);
//        $rule = MaterialTask::transJsonToArray($rule);
//        $result = MaterialTask::validateTimingRulesAndHandleMatch($rule, $materials);
//        if (false === $result) {
//            //更新规则状态为匹配完成
//            $share_rule_model->updateRuleMatchStatus($rule->id, [
//                'last_match_time' => time(),
//                'match_status' => MaterialShareRuleModel::MATCH_COMPLETE
//            ]);
//            continue;
//        }
//        $rank_material_insert[] = $result['rank_material_insert'];
//        $ods_rank_material_insert[] = $result['ods_rank_material_insert'];
//        //更新规则状态为匹配完成
//        $share_rule_model->updateRuleMatchStatus($rule->id, [
//            'last_match_time' => time(),
//            'match_status' => MaterialShareRuleModel::MATCH_COMPLETE
//        ]);
//    }
//    if ($rank_material_insert && $ods_rank_material_insert) {
//        $rank_material_insert = array_merge(...$rank_material_insert);
//        $ods_rank_material_insert = array_merge(...$ods_rank_material_insert);
////        (new MaterialShareRuleMQLogic())->produceRankMaterialInsertDataTask($rank_material_insert);
////        (new MaterialShareRuleMQLogic())->produceOdsRankMaterialInsertDataTask($ods_rank_material_insert);
////        $rank_material_model = new RankMaterialModel();
////        $ods_rank_material_model = new OdsRankMaterialModel();
//        (new RankMaterialModel())->add($rank_material_insert);
//        (new OdsRankMaterialModel())->add($ods_rank_material_insert);
//    }
//
//    echo date('Y-m-d H:i:s') . '--Timing Rules Match Finish' . PHP_EOL;
//    ob_end_flush();
//}
deliveryTimingRuleMatchData();