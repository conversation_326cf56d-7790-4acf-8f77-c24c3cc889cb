<?php

// */10 * * * *  10分钟运行一次
// 同步adb的页游渠道和广告位导入到mysql

use App\Constant\AgentGroup;
use App\Constant\MediaType;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\AgentLeaderChangeLogModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\MysqlConnection;
use App\Param\AgentParam;
use App\Service\OuterService;
use App\Struct\RedisCache;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$platform = "TW";
$user_id = 261;
$username = '中旭未来';
$now = date("Y-m-d H:i:s");
$offset_time = date("Y-m-d H:i:s", strtotime("-15 minutes"));
$logger = Helpers::getLogger('sync-yy-site');

try {
    if (!lock()) {
        echo "----------{$now} 上次任务执行中，本次任务跳过----------\n";
        return;
    }
} catch (Throwable $e) {
    $logger->error('redis lock error');
    return;
}

$agent_model = new AgentModel();
$agent_adb_model = new V2DimAgentIdModel();
$media_account_model = new MediaAccountModel();
$agent_list = $agent_adb_model->getYYAgentListByPlatformUpdateTime($platform, $offset_time);

$site_model = new SiteModel();
$site_adb_model = new V2DimSiteIdModel();

echo "----------{$now} 开始----------\n";

foreach ($agent_list as $item) {
    // 获取账号的主体
    $company = '';
    if ($item->account_id > 0) {
        $media_account = $media_account_model->getDataByAccountId($item->account_id, $item->media_type_id);
        $company = $media_account->company ?? '';
    }

    $param = new AgentParam([
        'platform' => $item->platform,
        'media_type' => MediaType::OTHER,
        'account_id' => $item->account_id,
        'agent_id' => $item->agent_id,
        'agent_name' => $item->agent_name,
        'agent_group' => AgentGroup::OTHER,
        'agent_leader' => $item->agent_leader,
        'company' => $company,
        'own' => 1,
        'user_name' => '',
        'user_pwd' => '123456',
        'create_time' => 0,
        'for_ad' => 0, // 不用于批量广告投放
    ]);

    $agent_data = [
        'platform' => $param->platform,
        'media_type' => $param->media_type,
        'account_id' => $param->account_id,
        'agent_id' => $item->agent_id,
        'agent_name' => $param->agent_name,
        'agent_group' => $param->agent_group,
        'agent_group_id' => $param->agent_group,
        'agent_leader' => $param->agent_leader,
        'agent_type' => $param->agent_type,
        'company' => $param->company,
        'own' => $param->own,
        'account_type' => $param->account_type,
        'id_card' => $param->id_card,
        'id_img' => $param->id_img,
        'user_name' => $param->user_name,
        'user_pwd' => $param->user_pwd,
        'bank_holder' => $param->bank_holder,
        'bank_area' => $param->bank_area,
        'bank_name' => $param->bank_name,
        'bank_card_number' => $param->bank_card_number,
        'person' => $param->person,
        'qq' => $param->qq,
        'email' => $param->email,
        'mobile' => $param->mobile,
        'tel' => $param->tel,
        'address' => $param->address,
        'detail_address' => $param->detail_address,
        'protocol_number' => $param->protocol_number,
        'protocol_type' => $param->protocol_type,
        'ad_number' => $param->ad_number,
        'creator_id' => $user_id,
        'creator' => $username,
        'create_time' => strtotime($item->insert_time),
        'update_time' => strtotime($item->update_time),
        'state' => $param->state,
        'pay_type' => $param->pay_type,
        'statistic_caliber' => $param->statistic_caliber,
        'for_ad' => $param->for_ad,
    ];

    try {
        (new AgentModel())->replace($agent_data);
        addAgentLeaderChangeData($item);
        echo "--------- $item->agent_id 插入成功----------\n";
    } catch (Throwable $e) {
        echo "--------- $item->agent_id 插入失败：{$e->getMessage()}----------\n";
    }
}

$now = date("Y-m-d H:i:s");
echo "---------{$now} agent完成----------\n";

$need_to_replace_site_list = [
    100286,100356,100197,100154,100278,100190,100295,100306,100105,100157,100019,100086,100034,100062,100222,100225,
    100342,100285,100271,100391,100370,100381,100215,100296,100072,100193,100058,100253,100218,100377,100080,100073,
    100055,100282,100343,100257,100002,100044,100119,100229,100303,100364,100349,100136,100069,100359,100065,100373,
    100012,100371,100005,100268,100123,100076,100090,100066,100048,100272,100115,100165,100083,100147,100144,100158,
    100172,100357,100204,100200,100130,100009,100420,100292,100137,100122,100087,100360,100140,100324,100406,100186,
    100126,100038,100129,100310,100094,100059,100201,100239,100363,100143,100224,100289,100352,100353,100236,100366,
    100151,100211,100395,100214,100300,100052,100207,100016,100293,100264,100243,100023,100037,100320,100161,100410,
    100374,100413,100367,100232,100339,100133,100051,100176,100335,100228,100101,100299,100219,100417,100000,100361,
    100315,100328,100127,100308,100304,100095,100230,100191,100028,100194,100024,100392,100312,100063,100187,100240,
    100088,100109,100098,100163,100290,100347,100170,100142,100184,100265,100077,100213,100173,100226,100307,100233,
    100276,100071,100250,100041,100275,100318,100329,100159,100134,100007,100294,100092,100319,100017,100108,100251,
    100393,100305,100067,100407,100179,100237,100241,100006,100301,100382,100350,100372,100060,100070,100004,100385,
    100261,100056,100208,100248,100183,100365,100403,100177,100376,100326,100013,100390,100099,100379,100332,100212,
    100138,100112,100386,100045,100156,100020,100322,100074,100198,100091,100205,100014,100166,100321,100148,100155,
    100042,100325,100368,100003,100141,100209,100378,100084,100254,100113,100297,100021,100106,100255,100396,100314,
    100418,100162,100336,100346,100311,100010,100116,100081,100234,100120,100035,100169,100046,100049,100400,100031,
    100244,100146,100075,100152,100399,100389,100280,100375,100180,100223,100102,100247,100027,100145,100085,100414,
    100227,100258,100279,100114,100405,100383,100411,100103,100355,100168,100111,100064,100267,100415,100050,100288,
    100079,100277,100333,100175,100256,100404,100262,100206,100330,100419,100100,100362,100242,100416,100025,100369,
    100203,100216,100274,100284,100015,100185,100182,100384,100316,100380,100022,100188,100029,100121,100097,100125,
    100174,100217,100192,100221,100266,100135,100096,100202,100231,100394,100291,100036,100117,100298,100323,100287,
    100040,100150,100153,100402,100011,100139,100401,100047,100309,100388,100033,100317,100061,100110,100397,100354,
    100167,100283,100210,100238,100132,100351,100337,100344,100245,100178,100043,100259,100341,100313,100131,100053,
    100008,100093,100032,100128,100273,100196,100089,100164,100327,100246,100149,100281,100252,100331,100057,100412,
    100260,100054,100030,100039,100195,100249,100387,100269,100408,100160,100124,100270,100348,100338,100425,100118,
    100345,100001,100220,100104,100334,100235,100263,100171,100398,100082,100181,100340,100018,100078,100199,100068,
    100358,100189,100107,100026,100409,100421,100422,100423,100424
];

$site_list = $site_adb_model->getYYSiteListByPlatformUpdateTime($platform, $offset_time);
if ($site_list->count() > 10000) {
    $logger->error('sync over 10000 site, please check adb cache');
    return;
}

$outer_service = new OuterService();
$site_list_chunk = $site_list->chunk(200);
foreach ($site_list_chunk as $site_list) {
    $insert_data = [];
    $site_ids = [];
    foreach ($site_list as $item) {
        // 已有广告位 更新广告位名
        $site_info = $site_model->getDataByPlatformSiteId($item->platform, $item->site_id);
        if (!empty($site_info) && !in_array($item->site_id, $need_to_replace_site_list)) {
            $update_data = [
                'site_name' => $item->site_name
            ];

            try {
                $site_model->edit($item->platform, $item->site_id, $update_data);
                echo "--------- $item->site_id 更新成功----------\n";
            } catch (Throwable $e) {
                echo "--------- $item->site_id 更新失败：{$e->getMessage()}----------\n";
            }
        } else {
            $insert_data[] = [
                'platform' => $item->platform,
                'site_id' => $item->site_id,
                'site_name' => $item->site_name,
                'account_id' => $item->account_id,
                'user_name' => '',
                'media_type' => MediaType::OTHER,
                'agent_group' => AgentGroup::OTHER,
                'agent_id' => $item->agent_id,
                'game_id' => 0,
                'game_type' => '',
                'game_pack' => 0,
                'ori_game_id' => 0,
                'package' => '',
                'app_android_channel_package_id' => '',
                'convert_type' => '',
                'convert_source_type' => '',
                'convert_data_type' => '',
                'convert_id' => 0,
                'action_track_type' => 0,
                'action_track_url' => '',
                'display_track_url' => '',
                'download_url' => '',
                'ad_turn' => 0,
                'ad_price' => 0,
                'ad_pop_zk' => 0,
                'auto_download' => 0,
                'auto_download_second' => 0,
                'cps_divide_rate' => 0,
                'forbid_tuitan' => 0,
                'template_type' => 'nojump.html',
                'template_address' => '',
                'pay_type' => $item->settlement_type === '' ? 0 : $outer_service->switchSettlementPayType($item->settlement_type),
                'upt_state' => 1,
                'upt_rate' => 0,
                'pay_discount' => 0,
                'reg_discount' => 0,
                'is_third' => 0,
                'deep_external_action' => '',
                'app_name' => '',
                'image_token' => '',
                'appid' => '',
                'akey' => '',
                'ext' => json_encode((object)[]),
                'state' => 1,
                'creator' => $username,
                'creator_id' => $user_id,
                'ad_pop_zk_type' => 0,
                'create_time' => strtotime($item->insert_time),
                'update_time' => strtotime($item->update_time),
                'convert_toolkit' => '',
                'is_callback_with_money_amount' => 1
            ];
            $site_ids[] = $item->site_id;
        }
    }

    if ($insert_data) {
        $site_ids_str = implode(",", $site_ids);
        try {
            $site_model->replace($insert_data);
            echo "--------- $site_ids_str 插入成功----------\n";
        } catch (Throwable $e) {
            echo "--------- $site_ids_str 插入失败：{$e->getMessage()}----------\n";
        }
    }
}
$now = date("Y-m-d H:i:s");
echo "---------{$now} site完成----------\n";

try {
    unlock();
} catch (Throwable $e) {
    $logger->error('redis unlock error');
}

function addAgentLeaderChangeData($item)
{
    $leader_change_data = [
        'platform' => $item->platform,
        'agent_id' => $item->agent_id,
        'agent_leader' => $item->agent_leader,
        'start_time' => strtotime($item->agent_leader_start_time),
        'end_time' => strtotime($item->agent_leader_end_time)
    ];
    (new AgentLeaderChangeLogModel())->customAdd($leader_change_data);
}

/**
 * 获取执行锁
 * @return bool  获锁成功返回true 否则返回false
 * @throws RedisException
 */
function lock()
{
    $key = 'sync_yy_agent_site';
    if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 1800])) {
        return true;
    } else {
        return false;
    }
}

/**
 * 解锁
 * @throws RedisException
 */
function unlock()
{
    $key = 'sync_yy_agent_site';
    RedisCache::getInstance()->del($key);
}
