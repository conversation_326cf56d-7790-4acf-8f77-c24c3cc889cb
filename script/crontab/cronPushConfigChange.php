<?php
/**
 * 10 * * * *
 * 每天定时更新推送配置
 */

use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
// 设置MySQL连接对象
MysqlConnection::setConnection();

// 拿一个月之前到
try {
    $last_month_date = date('Y-m-d', strtotime('-30 day'));
    $last_date = date('Y-m-d', strtotime('-1 day'));

    $sql = "WITH clique_merge AS (SELECT * FROM VALUES(11, 11), (133, 11 ) AS t (clique_id, map_id)),
     cost AS (SELECT COALESCE
                     (cm.map_id, g.clique_id) AS                 c_id,
                     sum(
                             IF
                             (t.tdate = '$last_date', money, 0)) today_cost
              FROM v2_dwd_day_cost_log t
                       INNER JOIN v2_dim_game_id g ON t.platform = g.platform
                  AND t.game_id = g.game_id
                       LEFT JOIN clique_merge cm ON g.clique_id = cm.clique_id
              WHERE t.platform = 'TW'
                AND t.tdate BETWEEN '$last_month_date'
                  AND CURRENT_DATE
              GROUP BY c_id
              ORDER BY sum(money) DESC
              LIMIT 10)
SELECT COALESCE
       (cm.map_id, g.clique_id) AS                                                c_id,
       max(
               IF
               (cm.map_id = g.clique_id OR cm.map_id IS NULL, g.clique_name, '')) c_name,
       GROUP_CONCAT(DISTINCT platform, '-', root_game_id, '-', main_game_id)      game_config
FROM v2_dim_game_id g
         LEFT JOIN clique_merge cm ON g.clique_id = cm.clique_id
         INNER JOIN cost ON COALESCE(cm.map_id, g.clique_id) = cost.c_id
WHERE g.platform = 'TW'
GROUP BY c_id
ORDER BY today_cost DESC";

    $list = MysqlConnection::getConnection('datahub')->select($sql);

    $sub_config = [];
    foreach ($list as $row) {
        [$unique_root_games, $unique_main_games] = getRootGameAndMainGame($row->game_config);

        // 开始拼接配置
        $info = [[
                     'platform'     => 'TW',
                     'ag_name'      => [],
                     'alg_name'     => [],
                     'os'           => [],
                     'main_game_id' => $unique_main_games,
                     'root_game_id' => $unique_root_games,
                 ]];

        $item_config = [
            'info'                       => $info,
            'is_apportioned'             => 0,
            'month_total_pay_money_goal' => '',
            'sum'                        => 0,
            'type'                       => $row->c_name,
        ];
        $sub_config[] = $item_config;
    }

    // 还要加上一个最终的TW所有游戏汇总
    [$unique_root_games, $unique_main_games] = getAllGame();
    $info = [[
                 'platform'     => 'TW',
                 'ag_name'      => [],
                 'alg_name'     => [],
                 'os'           => [],
                 'main_game_id' => $unique_main_games,
                 'root_game_id' => $unique_root_games,
             ]];

    $item_config = [
        'info'                       => $info,
        'is_apportioned'             => 0,
        'month_total_pay_money_goal' => '',
        'sum'                        => 0,
        'type'                       => '贪玩平台合计',
    ];
    $sub_config[] = $item_config;

    // 拼接配置 json
    $config = [[
                   'sub_config' => $sub_config,
                   'sub_theme'  => '子主题1'
               ]];


    // 更新配置
    $user_id = 15; // 15
    $theme = '主要产品每日数据汇总'; //主要产品每日数据汇总
    $module = 'dms';
    $config = json_encode($config, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

    (new WechatPushConfigNewModel())->updateConfigByPK($user_id, $module, $theme, $config);
    \App\Utils\Helpers::getLogger('update_config')->info('配置更新成功', [
        'user_id' => $user_id,
        'module'  => $module,
        'theme'   => $theme,
    ]);

    // 牟哥也推一下
    $user_id = 156;
    (new WechatPushConfigNewModel())->updateConfigByPK($user_id, $module, $theme, $config);
    \App\Utils\Helpers::getLogger('update_config')->info('配置更新成功', [
        'user_id' => $user_id,
        'module'  => $module,
        'theme'   => $theme,
    ]);

} catch (\Exception $e) {
    \App\Utils\Helpers::getLogger('update_config')->error('配置更新失败', [
        'message' => $e->getMessage(),
    ]);
}


function getRootGameAndMainGame($game_config)
{
    // 将字符串分割为数组
    $items = explode(',', $game_config);

    // 初始化数组来存储根游戏和主游戏
    $root_games = [];
    $main_games = [];

    // 遍历每个项目
    foreach ($items as $item) {
        // 分割每个项目
        list($platform, $root_game, $main_game) = explode('-', $item);

        // 将根游戏和主游戏添加到数组中
        $root_games[] = $root_game;
        $main_games[] = $main_game;
    }

    // 去重
    $unique_root_games = array_values(array_unique($root_games));
    $unique_main_games = array_values(array_unique($main_games));

    return [$unique_root_games, $unique_main_games];
}


function getAllGame()
{
    $last_day = date('Y-m-d', strtotime('-3 day'));
    $today = date('Y-m-d');
    $sql = "SELECT
	platform,
	root_game_id,
	main_game_id
FROM
	v2_dim_game_id 
WHERE
	platform = 'TW' 
	AND root_game_id IN (
	SELECT DISTINCT
		g.root_game_id 
	FROM
		v2_ods_uid_login_log t
		INNER JOIN v2_dim_game_id g USING ( platform, game_id ) 
	WHERE
		t.login_time BETWEEN '{$last_day}' 
		AND '{$today}' 
		AND t.platform = 'TW' 
	) 
GROUP BY
	platform,
	root_game_id,
	main_game_id";


    $list = MysqlConnection::getConnection('datahub')->select($sql);

    $root_games = [];
    $main_games = [];
    // 遍历每个项目
    foreach ($list as $item) {

        // 将根游戏和主游戏添加到数组中
        $root_games[] = $item->root_game_id;
        $main_games[] = $item->main_game_id;
    }

    // 去重
    $unique_root_games = array_values(array_unique($root_games));
    $unique_main_games = array_values(array_unique($main_games));

    return [$unique_root_games, $unique_main_games];
}







