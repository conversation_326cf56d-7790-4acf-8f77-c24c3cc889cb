<?php

/**
 * 导出文件任务
 * /1 * * * * php cronExportFeishuFileTask.php
 * @server 120.55.83.156 zx-dms
 */

use App\ElasticsearchConnection;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\MysqlConnection;
use App\Task\ExportFileTask;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
ClickhouseConnection::setConnection();
ElasticsearchConnection::setConnection();

$task = new ExportFileTask();
$start = time();
while (time() - $start < 60) {
    $task->export();
    sleep(1);
}

