<?php

// */2 * * * *
// 每隔2分钟 运行财务对账任务

use App\MysqlConnection;
use App\Task\FinanceCheckTask;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

try {
    $task = new FinanceCheckTask();
    $task->check();
} catch (\Throwable $e) {
    var_dump(date('Y-m-d H:i:s') . '-' . $e->getMessage(), '——' . $e->getTraceAsString() . PHP_EOL);
}
