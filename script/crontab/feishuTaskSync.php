<?php
/**
 * 拉取飞书任务
 * 0 3,12,17 * * * php feishuTaskSync.php
 * @server 120.55.83.156 zx-dms
 */

use App\Constant\MediaType;
use App\Constant\Platform;
use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\OAModel;
use App\Model\HttpModel\Feishu\Task\TaskListModel;
use App\Model\RedisModel\UserFeishuModel;
use App\Model\SqlModel\Zeda\FeishuTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Param\FeishuTaskDetailParam;
use App\Param\MediaAccountListParam;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

$logger = Helpers::getLogger('feishu-task');
/**
 * 1.读取已经授权的用户，也就几个用户（球哥和其他人）
 * 2.通过他们的user token调用清单列表接口，获取清单唯一id（各个用户之间，清单id可能会重复），整理出清单ID列表
 * 3.获取清单的任务列表。 根据步骤2整理的清单列表id，获取每个清单下面有什么任务id，整理成user-任务GUIDs的关系
 * 4.获取任务详情。根据步骤3整理的数据，结合user—token，去获取任务详情，整理对应的字段，然后存入数据库，完事
 */
// 获取预读取的授权用户列表
$user_list = getPreReadingUserData();

// 用户名feishu key关系
$user_feishu_key_map = getUserFeishuKeyMap();

[$app_id, $app_secret] = getMediaAccountConfig();

$filter_tasklist_guid_map = ['069cff2a-9e6e-4487-9437-6e8c1e09f414'];// 过滤敏感清单:HG_BAK

try {
    // 读取授权用户的清单列表
    $insert_guids = [];
    $task_list = [];
    $task_model = new TaskListModel();
    $feishu_task_model = new FeishuTaskModel();
    foreach ($user_list as $user_id => $access_token) {
        $task_list_data = $task_model->taskLists($access_token, 'union_id')['items'] ?? [];
        foreach ($task_list_data as $item) {
            // 不同的授权用户下有可能有同一个清单，所以清单只读取一次
            if (in_array($item['guid'], $task_list) || in_array($item['guid'], $filter_tasklist_guid_map)) {
                continue;
            } else {
                $task_list[] = $item['guid'];
            }

            $tasklist_guid_name_map[$item['guid']] = $item['name'];
            $tasklist_guid_member_map[$item['guid']] = getTaskListMembers($access_token , $item['members'], $item['creator']);

            // 根据清单列表获取任务列表
            $page_token = null;
            $i = 0;
            $guids_list = [];
            while (true) {
                $tasks_result = $task_model->getTasksByGuid($access_token, $item['guid'], 100, $page_token);
                $items = $tasks_result['items'];
                $page_token = $tasks_result['page_token'];
                $guids = array_column($items, 'guid');
                $guids_list = array_merge($guids_list, $guids);

                if (!$tasks_result['has_more']) {
                    break;
                }

                // 防止飞书的接口返回分页数据状态有问题，导致死循环，防一手
                $i++;
                if ($i > 100) {
                    break;
                }
            }

            // 根据任务列表获取任务详情
            foreach ($guids_list as $key => $guid) {
                $members_name = $tasklist_guid_name = $follower_members_name = $tasklist_members = [];
                try {
                    $task_detail = $task_model->taskDetail($access_token, $guid, 'union_id');
                    $comments_list = $task_model->getComments($access_token, 'task', $guid, 'asc', 'union_id')['items'];
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'return empty response') !== FALSE) {
                        echo $e->getMessage().PHP_EOL;
                        continue;
                    } elseif (strpos($e->getMessage(), 'Gateway timeout') !== FALSE) {
                        echo $e->getMessage().PHP_EOL;
                        continue;
                    } else {
                        throw $e;
                    }
                }
                $param = new FeishuTaskDetailParam($task_detail['task']);
                // 负责人union_id映射
                $members_name = memberIdToMemberName($param->members);
                $param->setMembersName($members_name);
                // 关注人union_id映射
                $follower_members_name = memberIdToMemberName($param->follower_members);
                $param->setFollowerMembers($follower_members_name);

                foreach ($param->tasklist_guid as $tasklist_guid) {
                    $tasklist_guid_name[] = $tasklist_guid_name_map[$tasklist_guid] ?? '未知清单';
                    $tasklist_members = array_merge($tasklist_guid_member_map[$tasklist_guid] ?? [], $tasklist_members);
                }
                // 设置清单协作者和清单名称
                $param->setTaskListMembers($tasklist_members);
                $param->setTaskListGuidName($tasklist_guid_name);
                $param->setCreator(getUsernameByUnionId($param->creator) ?? $param->creator);
                $param->setFromUser($user_id);
                $param->setComments($comments_list);

                $logger->info("飞书任务id：{$guid}", $param->toArray());
                $feishu_task_model->replace($param->toInsertData());
                $insert_guids[] = $param->guid;

                // 获取子任务
                $sub_task_list = $task_model->getSubTasks($access_token, $guid, 'union_id');
                foreach ($sub_task_list['items'] as $sub_task) {
                    $sub_tasklist_guid_name = $sub_tasklist_members  = [];
                    $sub_param = new FeishuTaskDetailParam($sub_task);
                    $sub_comments_list = $task_model->getComments($access_token, 'task', $sub_param->guid, 'asc', 'union_id')['items'];
                    // 负责人union_id映射
                    $members_name = memberIdToMemberName($sub_param->members);
                    $sub_param->setMembersName($members_name);
                    // 关注人union_id映射
                    $follower_members_name = memberIdToMemberName($sub_param->follower_members);
                    $sub_param->setFollowerMembers($follower_members_name);

                    $sub_param->tasklist_guid = array_unique(array_merge($param->tasklist_guid, $sub_param->tasklist_guid)); // 子任务用子任务+父任务的清单归属
                    foreach ($sub_param->tasklist_guid as $sub_tasklist_guid) {
                        $sub_tasklist_guid_name[] = $tasklist_guid_name_map[$sub_tasklist_guid] ?? '未知清单';
                        $sub_tasklist_members = array_merge($tasklist_guid_member_map[$sub_tasklist_guid] ?? [], $sub_tasklist_members);
                    }
                    // 设置清单协作者
                    $sub_param->setTaskListMembers($sub_tasklist_members);
                    $sub_param->setTaskListGuidName($sub_tasklist_guid_name); // 子任务用父任务的清单归属
                    $sub_param->setCreator(getUsernameByUnionId($sub_param->creator) ?? $sub_param->creator);
                    $sub_param->setFromUser($user_id);
                    $sub_param->setComments($sub_comments_list);

                    $logger->info("飞书子任务id：{$sub_param->guid}, 父任务id: {$guid}", $sub_param->toArray());
                    $feishu_task_model->replace($sub_param->toInsertData());
                    $insert_guids[] = $sub_param->guid;
                }
            }
        }
    }

    $feishu_task_model->updateDeleteStatus(array_unique($insert_guids));

} catch (Exception $exception) {
    $logger->notice("飞书任务出错啦", ['msg' => $exception->getMessage(), 'code' => $exception->getCode()]);
    throw new AppException($exception->getMessage());
}


/**
 * @return array
 */
function getUserFeishuKeyMap(): array
{
    $user_feishu_key_data = [];
    $user_feishu_union_data = (new UserModel())->getAllByState(UserModel::STATE_ACTIVE)->where('feishu_union_id', '!=', '')->sortByDesc('last_login_time');
    foreach ($user_feishu_union_data as $item) {
        if (isset($user_feishu_key_data[$item->feishu_union_id])) {
            continue;
        } else {
            $user_feishu_key_data[$item->feishu_union_id] = $item->name;
        }
    }
    return $user_feishu_key_data;
}

/**
 * @return array
 */
function getPreReadingUserData(): array
{
    $media_account_param = (new MediaAccountListParam(['platform' => Platform::TW, 'media_type' => MediaType::FEISHU]));
    $data = (new MediaAccountModel())->getAllByCondition($media_account_param);
    foreach ($data as $datum) {
        $user_list[$datum->creator_id] = $datum->access_token;
    }
    return $user_list ?? [];
}

/**
 * @return array
 */
function getMediaAccountConfig(): array
{
    $media_developer_account_model = new MediaDeveloperAccountModel();
    $zx_app_data = $media_developer_account_model->getDataByPlatformMediaCompany('TW', MediaType::FEISHU, '中旭未来组织');
    if (!$zx_app_data) {
        throw new AppException("feishu-task, zx飞书用户uninon-id运行失败，zx配置为空");
    }

    return [$zx_app_data->app_id, $zx_app_data->app_secret];
}

function memberIdToMemberName(array $member_ids): array
{
    $members_name = [];
    foreach ($member_ids as $member) {
        $name = getUsernameByUnionId($member);
        $members_name[] = $name;
    }

    return $members_name;
}

function getUsernameByUnionId($union_id)
{
    global $app_id, $app_secret;
    $user_feishu_redis_model = new UserFeishuModel();
    if ($user_feishu_redis_model->existUserNameByUnionId($union_id)) {
        $name = $user_feishu_redis_model->getUserNameByUnionId($union_id);
    } else {
        try {
            $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $app_id, $app_secret);
            $user_info = (new OAModel())->getUserInfo($tenant_access_token, $union_id, 'union_id');
        } catch (Exception $exception) {
            Helpers::getLogger('feishu-task')->notice("飞书任务获取用户名出错", [
                'union_id' => $union_id,
                'msg' => $exception->getMessage(),
                'code' => $exception->getCode()
            ]);
        }
        $name = $user_info['data']['user']['name'] ?? 'unknown';
        $user_feishu_redis_model->setUserNameByUnionId($union_id, $name);
    }

    return $name;
}


/**
 * @param $access_token
 * @param array $tasklist_member
 * @param $creator
 * @return array
 */
function getTaskListMembers($access_token, array $tasklist_member, $creator): array
{
    $members_name = [];
    // 俊文要求把清单创建人放在第一位
    $members_name[] = getUsernameByUnionId($creator['id']);
    foreach ($tasklist_member as $member) {
        if ($member['type'] == 'chat') {
            $data = (new ChatsModel())->chatMembers($access_token, $member['id'], 'union_id');
            $member_list = $data['items'];
            foreach ($member_list as $member_items) {
                $members_name[] = $member_items['name'];
            }
        } else {
            $name = getUsernameByUnionId($member['id']);
            $members_name[] = $name;
        }
    }

    return array_unique($members_name);
}

