<?php

/**
 * 0 12 * * *
 * 渠道异常注册记录
 */

use App\Logic\DMS\GameWarningLogic;
use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\Utils\Helpers;
use App\MysqlConnection;
use Illuminate\Database\Query\Builder;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
$logger = Helpers::getLogger('AgentAbnormalReg');

// -d 2021-01-20 d参数接收日期
// 异常点击默认刷前一天的数据
$input = getopt('d:');
// 默认时间和累计时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date('Y-m-d', strtotime('-2 day'));
$date_last_second = $date . ' 23:59:59';
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START:' . $date);
MysqlConnection::setConnection();

$game_uid_reg_model = new V2DWDGameUidRegLogModel();
$user_regs = $game_uid_reg_model->getUserCountGroup($date, $date_last_second);

$user_device_counts = collect();
$system_version_counts = collect();
$ip_counts = collect();
$m_uid_counts = collect();
$ip_segment_counts = collect();
$reg_24_hours = collect();
if ($user_regs->isNotEmpty()) {
    $platform_filters = $user_regs->pluck('platform')->unique();
    $agent_filters = $user_regs->pluck('agent_id')->unique();
    $game_filters = $user_regs->pluck('game_id')->unique();
    $func = function (Builder $query) use ($platform_filters, $agent_filters, $game_filters) {
        $query
            ->whereIn('platform', $platform_filters)
            ->whereIn('agent_id', $agent_filters)
            ->whereIn('game_id', $game_filters);
    };
    $user_device_counts = $game_uid_reg_model->userDeviceCount($date, $date_last_second);
    $system_version_counts = $game_uid_reg_model->systemVersionCount($date, $date_last_second);
    $ip_counts = $game_uid_reg_model->ipRegCount($date, $date_last_second);
    $m_uid_counts = $game_uid_reg_model->mUidRegCount($date, $date_last_second);
    $ip_segment_counts = $game_uid_reg_model->ipSegmentCount($date, $date_last_second);
    $reg_24_hours = $game_uid_reg_model->getMoreThan24HourReg($date, $date_last_second);
}

// 先确定IP是否属于中国
$logic = (new GameWarningLogic());
$chinese_ip_range = $logic->getChinaIPBlock();
$agent_abnormal_model = new V2DWDAgentAbnormalClickModel();
foreach ($user_regs as $user_reg) {
    $reg_err = '';

    if ($user_reg->count >= 30 && $user_reg->count < 56) {
        // 单ip日均注册2次+次数
        $agent_ip_counts = $ip_counts->filter(function ($item) use ($user_reg) {
            return $item->platform == $user_reg->platform &&
                $item->agent_id == $user_reg->agent_id &&
                $item->game_id == $user_reg->game_id;
        });
        $multiple_ip_num = 0;
        foreach ($agent_ip_counts as $agent_ip_count) {
            if ($agent_ip_count->count >= 2) $multiple_ip_num++;
        }
        if ($multiple_ip_num > 10) $reg_err .= "ip日注册次数超过2+次数过高，有{$multiple_ip_num}\r\n";
        $user_reg->multiple_ip_rate = $agent_ip_counts->count() > 0 ? round($multiple_ip_num / $agent_ip_counts->count() * 100, 2) : 0;
        // 单m uid日均注册2次+次数
        $agent_m_uid_counts = $m_uid_counts->filter(function ($item) use ($user_reg) {
            return $item->platform == $user_reg->platform &&
                $item->agent_id == $user_reg->agent_id &&
                $item->game_id == $user_reg->game_id;
        });
        $multiple_m_uid_num = 0;
        foreach ($agent_m_uid_counts as $item) {
            if ($item->count >= 2) $multiple_m_uid_num++;
        }
        if ($multiple_m_uid_num > 8) $reg_err .= "muid日注册次数超过2+次数过高，有{$multiple_m_uid_num}\r\n";
        $user_reg->multiple_m_uid_rate = $agent_m_uid_counts->count() > 0 ? round($multiple_m_uid_num / $agent_m_uid_counts->count() * 100, 2) : 0;
        continue;// 30-56只判断这几项
    }else if ($user_reg->count < 30) {// 日注册小于30不考虑
        goto SKIP;
    }

    // 点击后24小时注册占比
    if (!empty($user_reg->count_click)) {
        $agent_reg_24_hours = $reg_24_hours->filter(function ($item) use ($user_reg) {
            return $item->platform == $user_reg->platform &&
                $item->agent_id == $user_reg->agent_id &&
                $item->game_id == $user_reg->game_id;
        });
        if ($agent_reg_24_hours->isNotEmpty()) {
            $reg24 = $agent_reg_24_hours->first();
            $rate = $reg24->count / $user_reg->count * 100;
            $more_than_24_hours_reg_rate = round($rate, 2);
            $user_reg->more_than_24_hours_reg_rate = $more_than_24_hours_reg_rate;
            if ($more_than_24_hours_reg_rate > 5 && $reg24->count > 7) {
                $reg_err .= "注册日期距离点击日期过长,产生的注册有{$more_than_24_hours_reg_rate}%\r\n";
            }
        }
    }
    // 单用户2设备+注册占比(2设备出现的频次/总注册数)
    $agent_user_multiple_device = $user_device_counts->filter(function ($item) use ($user_reg) {
        return $item->platform == $user_reg->platform &&
            $item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id;
    });
    $multiple_device_num = 0;
    foreach ($agent_user_multiple_device as $item) {
        if ($item->count >= 2) $multiple_device_num++;
    }
    $agent_user_count = $agent_user_multiple_device->count();
    $multiple_device_rate = $agent_user_count > 0 ? $multiple_device_num / $agent_user_count * 100 : 0;
    $multiple_device_rate = round($multiple_device_rate, 2);
    $user_reg->multiple_device_rate = $multiple_device_rate;
    if ($multiple_device_rate > 12.6 && $multiple_device_num > 6) {
        $reg_err .= "多设备用户占比过多有{$multiple_device_rate}%\r\n";
    }
    // 系统版本分布
    $agent_system_version_counts = $system_version_counts->filter(function ($item) use ($user_reg) {
        return $item->platform == $user_reg->platform &&
            $item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id;
    });
    $multiple_system_version_num = 0;
    $agent_system_version_count = $agent_system_version_counts->sum('count');
    foreach ($agent_system_version_counts as $item) {
        $system_version_rate = $agent_system_version_count > 0 ? $item->count / $agent_system_version_count * 100 : 0;
        $system_version_rate = round($system_version_rate, 2);
        if ($system_version_rate > 75) {
            $user_reg->system_version_rate = $system_version_rate;
            $reg_err .= "超过{$system_version_rate}%的用户系统版本一致,版本:{$item->system_version}\r\n";
        }
    }
    // 单ip日均注册2次+比率
    $agent_ip_counts = $ip_counts->filter(function ($item) use ($user_reg) {
        return $item->platform == $user_reg->platform &&
            $item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id;
    });
    $multiple_ip_num = 0;
    $not_china_num = 0;
    foreach ($agent_ip_counts as $item) {
        if ($item->count >= 2) $multiple_ip_num++;
        if (!$logic->isLAN($item->ip)) {
            $index = $logic->binarySearchIP($item->ip, $chinese_ip_range);
            if ($index < 0) $not_china_num++;
        }
    }
    $agent_ip_count = $agent_ip_counts->count();
    $multiple_ip_rate = $agent_ip_count > 0 ? $multiple_ip_num / $agent_ip_count * 100 : 0;
    $user_reg->multiple_ip_rate = round($multiple_ip_rate, 2);
    if ($multiple_ip_rate > 25 && $multiple_ip_num > 6) {
        $once_ip_rate = round(100 - $multiple_ip_rate, 2);
        $reg_err .= "1次注册用户占比过低有{$once_ip_rate}%\r\n";
    }
    // ip段是否异常(国家)
    $not_china_rate = $agent_ip_count > 0 ? $not_china_num / $agent_ip_count * 100 : 0;
    $not_china_rate = round($not_china_rate, 2);
    $user_reg->not_china_rate = $not_china_rate;
    if ($not_china_rate > 5 && $not_china_num > 6) {
        $reg_err .= "非中国的注册ip过多,占比{$not_china_rate}%\r\n";
    }

    // 单m uid日均注册2次+比率
    $agent_m_uid_counts = $m_uid_counts->filter(function ($item) use ($user_reg) {
        return $item->platform == $user_reg->platform &&
            $item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id;
    });
    $multiple_m_uid_num = 0;
    foreach ($agent_m_uid_counts as $item) {
        if ($item->count >= 2) $multiple_m_uid_num++;
    }
    $multiple_m_uid_rate = $agent_m_uid_counts->count() > 0 ? $multiple_m_uid_num / $agent_m_uid_counts->count() * 100 : 0;
    $multiple_m_uid_rate = round($multiple_m_uid_rate, 2);
    $user_reg->multiple_m_uid_rate = $multiple_m_uid_rate;
    if ($multiple_m_uid_rate >= 20 && $multiple_m_uid_num > 6) {
        $reg_err .= "单muid日均注册2次+比率{$multiple_m_uid_rate}%\r\n";
    }
    // 连续ip段出现2次+占比
    $agent_ip_segment_counts = $ip_segment_counts->filter(function ($item) use ($user_reg) {
        return $item->platform == $user_reg->platform &&
            $item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id &&
            $item->ip_segment != '';
    });
    $multiple_ip_segment_num = 0;
    foreach ($agent_ip_segment_counts as $item) {
        if ($item->count >= 2) $multiple_ip_segment_num += $item->count;
    }
    $multiple_ip_segment_rate = $agent_ip_segment_counts->count() > 0 ? $multiple_ip_segment_num / $agent_ip_segment_counts->sum('count') * 100 : 0;
    $multiple_ip_segment_rate = round($multiple_ip_segment_rate, 2);
    $user_reg->multiple_ip_segment_rate = $multiple_ip_segment_rate;
    if ($multiple_ip_segment_rate > 25 &&  $multiple_ip_segment_num > 6) {
        $reg_err .= "同区域用户产生注册比例{$multiple_ip_segment_rate}%\r\n";
    }

    SKIP:
    $update_param = [
        'agent_id' => $user_reg->agent_id,
        'game_id' => $user_reg->game_id,
        'date' => $date,
        'platform' => $user_reg->platform,
        'is_reg_abnormal' => !empty($reg_err),
        'day_reg_count' => $user_reg->count,
        'reg_abnormal_msg' => $reg_err,
        'more_than_24_hours_reg_rate' => $user_reg->more_than_24_hours_reg_rate ?? 0,
        'multiple_device_rate' => $user_reg->multiple_device_rate ?? 0,
        'system_version_rate' => $user_reg->system_version_rate ?? 0,
        'multiple_ip_rate' => $user_reg->multiple_ip_rate ?? 0,
        'multiple_m_uid_rate' => $user_reg->multiple_m_uid_rate ?? 0,
        'multiple_ip_segment_rate' => $user_reg->multiple_ip_segment_rate ?? 0,
        'not_china_rate' => $user_reg->not_china_rate ?? 0
    ];
    if (strlen($update_param['platform']) > 10) continue;
    $agent_abnormal_model->updateOrInsertByPK($update_param);
}

$logger->info('END date:' . $date);



