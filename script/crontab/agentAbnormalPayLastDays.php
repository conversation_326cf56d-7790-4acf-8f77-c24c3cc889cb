<?php
/**
 * 30 5 * * *
 * 渠道异常点击记录
 */

use App\Param\DMS\AbnormalAgentClickParam;
use App\Utils\Helpers;
use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\Model\SqlModel\Tanwan\V2DWDRootGameUIDRegLogModel;
use App\Logic\DMS\GameWarningLogic;
use App\MysqlConnection;
use \Illuminate\Database\Query\Builder;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$logger = Helpers::getLogger('AbnormalPayLastDays');

MysqlConnection::setConnection();

// -d 2021-01-20 d参数接收日期
// -t 1 t参数接收扫描类型目前只接受 7、15分别对应7日15日
$input = getopt('d:t:');
// 默认时间和单日时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date("Y-m-d", strtotime("-6 day"));
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START date:' . $date);
$date_last_second = $date . ' 23:59:59';
$type = isset($input['t']) ? intval($input['t']) : 7;
$start_date = date('Y-m-d', strtotime($date) - ($type - 1) * 86400);
// 首日创角色时间段是15日和30日，对应7日15日
$offset = $type == 7 ? 15 : 30;
$min_num = $offset * 26;
$first_day_user_start_date = date('Y-m-d', strtotime($date) - ($offset - 1) * 86400);

// 多日付费异常
$game_uid_reg_model = new V2DWDGameUidRegLogModel();
$root_game_uid_reg_model = new V2DWDRootGameUIDRegLogModel();
$agent_abnormal_click_model = new V2DWDAgentAbnormalClickModel();
$first_day_users = (new V2DWDRootGameUIDRegLogModel())->getFirstDayCreateUserCount($first_day_user_start_date, $date_last_second, $min_num);
$not_pay_create_user = (new V2DWDRootGameUIDRegLogModel())->getFirstDayCreateUserCount($first_day_user_start_date, $date_last_second, $min_num, true);
foreach ($first_day_users as $item) {
    $find = $not_pay_create_user->where('platform', $item->platform)
        ->where('agent_id', $item->agent_id)
        ->where('game_id', $item->game_id);
    if ($find->isNotEmpty()) {
        $item->not_pay_num = $find->first()->total_num;
    } else {
        $item->not_pay_num = 0;
    }
}
$first_day_users2 = (new V2DWDGameUidRegLogModel())->getFirstDayCreateUserCount($first_day_user_start_date, $date_last_second, $min_num);
$not_pay_create_user2 = (new V2DWDGameUidRegLogModel())->getFirstDayCreateUserCount($first_day_user_start_date, $date_last_second, $min_num, true);
foreach ($first_day_users2 as $item) {
    $find = $not_pay_create_user2->where('platform', $item->platform)
        ->where('agent_id', $item->agent_id)
        ->where('game_id', $item->game_id);
    if ($find->isNotEmpty()) {
        $item->not_pay_num = $find->first()->total_num;
    } else {
        $item->not_pay_num = 0;
    }
}


$last_days_param = new AbnormalAgentClickParam();
$last_days_param->click_date_range = [date('Y-m-d', strtotime($date) - ($type - 1) * 86400), $date];
$abnormal_reg_last_days_records = $agent_abnormal_click_model->getLastDaysAgent($last_days_param, 'reg', $type);

$user_regs = collect();
$game_reg_24_hour_login_times = collect();
if ($abnormal_reg_last_days_records->isNotEmpty()) {
    $platform_filters = $abnormal_reg_last_days_records->pluck('platform')->unique();
    $agent_filters = $abnormal_reg_last_days_records->pluck('agent_id')->unique();
    $game_filters = $abnormal_reg_last_days_records->pluck('game_id')->unique();
    $user_regs = $game_uid_reg_model->getUserCountGroup($start_date, $date_last_second,
        function (Builder $query) use ($platform_filters, $agent_filters, $game_filters) {
            $query
                ->whereIn('platform', $platform_filters)
                ->whereIn('agent_id', $agent_filters)
                ->whereIn('game_id', $game_filters);
        });

    $game_reg_24_hour_login_times = $game_uid_reg_model->getGameReg24HourLoginTimes($start_date, $date_last_second,
        function (Builder $query) use ($platform_filters, $agent_filters, $game_filters) {
            $query
                ->whereIn('platform', $platform_filters)
                ->whereIn('agent_id', $agent_filters)
                ->whereIn('game_id', $game_filters);
        });

}

$logic = (new GameWarningLogic());
foreach ($abnormal_reg_last_days_records as $abnormal_reg_last_days_record) {
    $pay_err = '';
    $agent_not_pay_game_reg_24_hour_login_times = collect();
    $agent_pay_more_than_once_game_reg_24_hour_login_times = collect();
    foreach ($game_reg_24_hour_login_times as $key => $item) {
        if ($item->agent_id == $abnormal_reg_last_days_record->agent_id &&
            $item->game_id == $abnormal_reg_last_days_record->game_id &&
            $item->platform == $abnormal_reg_last_days_record->platform
        ) {
            if ($item->total_pay_times == 0) {
                $agent_not_pay_game_reg_24_hour_login_times->push($item);
            } else {
                $agent_pay_more_than_once_game_reg_24_hour_login_times->push($item);
            }
            unset($game_reg_24_hour_login_times[$key]);
        }
    }

    // 付费次数
    $not_pay_three_fourths = $logic->median($agent_not_pay_game_reg_24_hour_login_times, 0.75, 'game_reg_24_hour_login_times');
    $pay_more_than_once_fifty_percent = $logic->median($agent_pay_more_than_once_game_reg_24_hour_login_times, 0.5, 'game_reg_24_hour_login_times');
    $pay_more_than_once_three_fourths = $logic->median($agent_pay_more_than_once_game_reg_24_hour_login_times, 0.75, 'game_reg_24_hour_login_times');

    if ($not_pay_three_fourths > 0 && $not_pay_three_fourths > $pay_more_than_once_three_fourths) {
        $pay_err .= "付费用户相对于不付费用户不活跃\r\n";
    }
    if ($abnormal_reg_last_days_record->day_pay_count >= 30 && $pay_more_than_once_fifty_percent < 1) {
        $pay_err .= "付费用户首日活跃性较差\r\n";
    }
    // 老付费设备占比
    $agent_user_reg = $user_regs
        ->where('platform', $abnormal_reg_last_days_record->platform)
        ->where('agent_id', $abnormal_reg_last_days_record->agent_id)
        ->where('game_id', $abnormal_reg_last_days_record->game_id);
    if ($agent_user_reg->isNotEmpty()) {
        $agent_user_reg_first = $agent_user_reg->first();
        $total_m_uid = floatval($agent_user_reg_first->count_is_old_pay_muid) + floatval($agent_user_reg_first->count_not_old_pay_muid);
        $old_pay_m_uid_rate = $total_m_uid > 0 ? floatval($agent_user_reg_first->count_is_old_pay_muid) / $total_m_uid * 100 : 0;
        if ($abnormal_reg_last_days_record->day_pay_count >= 30 * $type && $old_pay_m_uid_rate > 50) {
            $old_pay_m_uid_rate = round($old_pay_m_uid_rate, 2);
            $pay_err .= "老设备付费占比超过{$old_pay_m_uid_rate}%\r\n";
        }
    }
    // 首日创角
    $agent_first_day_users = $first_day_users
        ->where('platform', $abnormal_reg_last_days_record->platform)
        ->where('agent_id', $abnormal_reg_last_days_record->agent_id)
        ->where('game_id', $abnormal_reg_last_days_record->game_id);
    if ($agent_first_day_users->isNotEmpty()) {
        $agent_first_day_users_first = $agent_first_day_users->first();
        $first_day_not_pay_rate = $agent_first_day_users_first->not_pay_num / $agent_first_day_users_first->total_num * 100;
        if ($first_day_not_pay_rate > 75) {
            $first_day_not_pay_rate_round = round($first_day_not_pay_rate, 2);
            $pay_err .= "没有付费的用户中有{$first_day_not_pay_rate_round}%的用户都是首日注册创角后就离开的用户 {$offset}日\r\n";
        }
    }
    $agent_first_day_users2 = $first_day_users2
        ->where('platform', $abnormal_reg_last_days_record->platform)
        ->where('agent_id', $abnormal_reg_last_days_record->agent_id)
        ->where('game_id', $abnormal_reg_last_days_record->game_id);
    if ($agent_first_day_users2->isNotEmpty()) {
        $agent_first_day_users2_first = $agent_first_day_users2->first();
        $first_day_not_pay_rate2 = $agent_first_day_users2_first->not_pay_num / $agent_first_day_users2_first->total_num * 100;
        if ($first_day_not_pay_rate2 > 75) {
            $first_day_not_pay_rate2_round = round($first_day_not_pay_rate2, 2);
            $pay_err .= "没有付费的用户中有{$first_day_not_pay_rate2_round}%的用户 都是首日注册创角后就离开的用户 {$offset}日\r\n";
        }
    }

    $update_data = [];
    if (!empty($pay_err)) {
        $filed = 'is_pay_abnormal_' . $type;
        $msg_field = 'pay_abnormal_msg_' . $type;
        $update_data[$filed] = 1;
        $update_data[$msg_field] = $pay_err;
        $param = new AbnormalAgentClickParam($abnormal_reg_last_days_record);
//        echo $param->platform, ',', $param->agent_id, ',', $param->game_id, ',', $pay_err;
        $agent_abnormal_click_model->updateFiledByPK($param, $update_data);
    }
}
$logger->info('END date:' . $date);