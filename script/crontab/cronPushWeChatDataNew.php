<?php

//
// 推送微信数据  这里只推送微信的卡片消息，飞书不在这里操作。飞书直接推图片。微信公众号不支持直接推图片，所以保留这个脚本
// 每分钟推送
// * * * * * php cronPushWeChatDataNew.php
//

use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic as DMSPermissionLogic;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;
use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\MysqlConnection;
use App\Task\NoticeTask;
use App\Utils\Helpers;
use App\Utils\Math;
use Monolog\Logger;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$logger = Helpers::getLogger('wechat_push');

echo "pushing message begin======" . date('Y-m-d H:i:s') . "======================>" . PHP_EOL;

$panel_date = date("Y-m-d");
$last_panel_date = date("Y-m-d", strtotime("-1 days"));
$time = strtotime(date("Y-m-d 00:00:00"));
$cur_hour = intval(date("H"));
$cur_min = intval(date("i"));
$panel_date_time = time();
//
//// TODO 时间改为今天凌晨 再推一次
//$panel_date_time = 1712592012;
//$panel_date = date("Y-m-d");
//$last_panel_date = date("Y-m-d", strtotime("-1 days"));
//$cur_hour = 0;
//$cur_min = 12;

$config_model = new WechatPushConfigNewModel();
$push_log = [];


if ($cur_hour == 0) {
    $now = date("Y年m月d日（统计日期）", strtotime('-1 day'));
    $first = "昨日运营收入";
} else {
    $first = "今日运营收入";
    $now = date("Y年m月d日（统计日期）") . "-" . $cur_hour . ":00";
}
//
////TODO
//$cur_min = 12;
$list = $config_model->getListByTime($cur_min);

//foreach ($list as $item) {
//    $theme = $item->theme;
//    $user_id = $item->user_id;
//    $username = $item->username;
//    $permission = fix_dms_permission($item->config, $theme, $item->account);
//    try {
//        $user_permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
//    } catch (AppException|\Throwable $exception) {
//        echo "获取dms权限报错'uid' => " . $user_id . PHP_EOL;
//        continue;
//    }
//    $data_model = new WechatPushDataDmsModel();
//    $panel_data = $data_model->getPanelData($permission, $panel_date, $cur_hour, $user_permission);
//}
//exit;
//


$process_num = 5;
$size = ceil(count($list) / $process_num);
$chunk_list = $list->chunk($size);

for ($n = 1; $n <= $process_num; $n++) {
    $pid = pcntl_fork();
    if ($pid < 0) {
        echo "创建进程失败" . PHP_EOL;
        exit;
    }
    if ($pid === 0) {
        //
        echo "pushing process==={$n}=========================>\n";
        if (isset($chunk_list[$n - 1])) {
            pushMessage($logger, $chunk_list[$n - 1], $cur_hour, $panel_date, $last_panel_date, $panel_date_time, $first, $now);
        }
        exit;
    }
}
echo "pushing message end======" . date('Y-m-d H:i:s') . "======================>" . PHP_EOL;

function pushMessage($logger, $list, $cur_hour, $panel_date, $last_panel_date, $panel_date_time, $first, $now)
{
    MysqlConnection::setConnection();

    $t1 = microtime(true);
    $notice_task = new NoticeTask();
    $logic = new WechatDataLogic();
    foreach ($list as $item) {
        $theme = $item->theme;
        $user_id = $item->user_id;
        $username = $item->username;

        if ((int)$item->push_hour === 0) {
            //按天0点推送
            if ($cur_hour !== 0) {
                continue;
            }
        } else {
            //按X小时推送
            //当天的先不推送  -- 去除这个逻辑
//            if (date('Y-m-d', $item->create_time) === date('Y-m-d')) {
//                continue;
//            }
            //当前小时数 % $item->push_hour = 0
            if ($cur_hour % $item->push_hour !== 0) {
                continue;
            }
        }

        $key = "$user_id-$theme";
        if (isset($push_log[$key])) {
            continue;
        }
        $push_log[$key] = true;
        /* @var Logger $logger */
        $logger->info($key);
        if ($item->module === 'dms') {
            $permission = fix_dms_permission($item->config, $theme, $item->account);
            try {
                $user_permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
            } catch (AppException|\Throwable $exception) {
                echo "获取dms权限报错'uid' => " . $user_id . PHP_EOL;
                continue;
            }
            $data_model = new WechatPushDataDmsModel();
            $panel_data = $data_model->getPanelData($permission, $panel_date, $cur_hour, $user_permission);
            $last_panel_data = $data_model->getPanelData($permission, $last_panel_date, $cur_hour, $user_permission);
        } else {
            $permission = fix_ly_permission($item->config, $theme, $item->account);
            $data_model = new WechatPushDataLyModel();
            $panel_data = $data_model->getPanelData($permission, $panel_date, $cur_hour);
            $last_panel_data = $data_model->getPanelData($permission, $last_panel_date, $cur_hour);
        }

        $sum_ori_money = $panel_data->sum_ori_money ?? 0;
        $sum_reg_uid_count = $panel_data->sum_reg_uid_count ?? 0;
        $income = $panel_data->sum_total_pay_money ?? 0;
        $open_service = $panel_data->sum_open_service ?? 0;
        $dau = $panel_data->sum_dau ?? 0;
        $reg_cost = Math::div($sum_ori_money, $sum_reg_uid_count);

        $last_sum_ori_money = $last_panel_data->sum_ori_money ?? 0;
        $last_sum_reg_uid_count = $last_panel_data->sum_reg_uid_count ?? 0;
        $last_income = $last_panel_data->sum_total_pay_money ?? 0;
        $last_open_service = $last_panel_data->sum_open_service ?? 0;
        $last_dau = $last_panel_data->sum_dau ?? 0;
        $last_reg_cost = Math::div($last_sum_ori_money, $last_sum_reg_uid_count);

        $reg_cost_circle = Math::circle($reg_cost, $last_reg_cost);
        $reg_cost_circle_str = '';
        $income_circle = Math::circle($income, $last_income);
        $income_circle_str = '';
        $open_service_circle = Math::circle($open_service, $last_open_service);
        $open_service_circle_str = '';
        $dau_circle = Math::circle($dau, $last_dau);
        $dau_circle_str = '';

        $income = Math::decimal($income, 2);
        $push_channel = json_decode($item->push_channel, true);
        foreach ($push_channel as $channel) {
            if ($channel !== 'wechat') {
                continue;
            }

            foreach (['reg_cost_circle', 'income_circle', 'open_service_circle', 'dau_circle'] as $target) {
                if ($$target < 0) {
                    ${$target . '_str'} = '（↓' . abs($$target) . '%）';
                } else {
                    ${$target . '_str'} = '（↑' . $$target . '%）';
                }
            }
            $sum_data = "成本：{$reg_cost} {$reg_cost_circle_str}\n收入：{$income} {$income_circle_str}\n开服数：{$open_service} {$open_service_circle_str}\nDAU：{$dau} {$dau_circle_str}";

            // 泽达情报局
            $wechat_url = $logic->getWechatPushUrl($panel_date_time, $theme, $user_id);
            $notice_task->pushWeChat(
                [
                    'user_id'     => $user_id,
                    'type'        => NoticeMessageModel::TYPE_WECHAT_DATA,
                    'template_id' => TemplateModel::STATISTICS_TEMPLATE,
                    'url'         => $wechat_url,
                    'config_name' => 'qingbaoju',
                    'data'        => [
                        "first"    => ["value" => "{$first}", "color" => "#000000"],
                        "keyword1" => ["value" => "{$username} - {$theme}", "color" => "#000000"],
                        "keyword2" => ["value" => "{$now}", "color" => "#000000"],
                        "keyword3" => ["value" => "{$sum_data}", "color" => "#169115"],
                        "remark"   => ["value" => "点击进入查看详情", "color" => "#000000"],
                    ]
                ]);

            // 中旭情报局
            if ($cur_hour == 0) {
                $now = date("Y-m-d", strtotime('-1 day'));
            } else {
                $now = date("Y-m-d H:i");
            }
            $wechat_url = $logic->getZXWechatPushUrl($panel_date_time, $theme, $user_id);
            $notice_task->pushWeChat(
                [
                    'user_id'     => $user_id,
                    'type'        => NoticeMessageModel::TYPE_WECHAT_DATA,
                    'template_id' => TemplateModel::ZX_STATISTICS_TEMPLATE,
                    'url'         => $wechat_url,
                    'config_name' => 'zx_qbj',
                    'data'        => [
                        "thing6"  => ["value" => "{$username} - {$theme}", "color" => "#000000"],
                        "time7"   => ["value" => "{$now}", "color" => "#000000"],
                        "thing10" => ["value" => "{$sum_data}", "color" => "#169115"],
                    ]
                ]);
        }
    }
    $t2 = microtime(true);
    echo "时间=====>" . round($t2 - $t1, 4) . PHP_EOL;
}

function fix_dms_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);

    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'dms', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $alg_names = [];
            $type = $item['type'];
            $info = $item['info'];
            $is_apportioned = $item['is_apportioned'] ?? 1;

            foreach ($info as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $ag = $data['ag_name'] ?? [];
                $alg = $data['alg_name'] ?? [];
                $alg_names = array_merge($alg_names, $alg);
                $os = $data['os'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['main_game_id'   => $main_game_id,
                                             'agent_group_id' => $ag,
                                             'os'             => $os];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge($type_info[$platform]['main_game_id'], $main_game_id);
                    $type_info[$platform]['agent_group_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $alg_names = array_unique($alg_names);
            $types[$sub_theme][$type] = [
                'game'           => $type_info,
                'alg'            => $alg_names,
                'is_apportioned' => $is_apportioned,
            ];
        }
    }
    return $types;
}

function fix_ly_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);
    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'ly', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $type = $item['type'];
            $info = $item['info'];

            foreach ($info as $data) {
                $platform = $data['platform'];
                $game_id = $data['game_id'];
                $channel_id = $data['channel_id'] ?? [];
                $ag = $data['ag_name'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['game_id'        => $game_id,
                                             'agent_group_id' => $ag,
                                             'channel_id'     => $channel_id];
                } else {
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['game_id'], $game_id);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['channel_id'], $channel_id);
                }
            }

            $types[$sub_theme][$type] = ['game' => $type_info];
        }
    }
    return $types;
}
