<?php

//  更新知识库，5分钟跑一次
// */5 * * * * php cronMessageSummary.php
//  @server 120.55.83.156 zx-dms

use App\ElasticsearchConnection;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\MysqlConnection;
use App\Task\ExportFileTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


try {
    (new \App\Service\GroupAssistant\MessageSummary())->run();
} catch (\Throwable $e) {
    Helpers::getLogger('chat_summary')->error('更新知识库发送异常，任务结束:' . $e->getMessage(), ['s' => $e->getTraceAsString()]);
}


