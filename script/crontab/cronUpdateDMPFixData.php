<?php

/**
 * 临时修复增量缺失的文件
 */

use App\Constant\UserIDType;
use App\Logic\DMS\AudienceLogic;
use App\Logic\ToutiaoLogic;
use App\Model\SqlModel\Tanwan\DmpModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\MysqlConnection;
use App\Service\AudienceService;
use App\Service\DeviceService;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;

require_once dirname(__DIR__) . '/../common/init.php';
require_once dirname(__DIR__) . '/process/utils/common.php';
require_once dirname(__DIR__) . '/process/core/MultiProcess.php';
require_once dirname(__DIR__) . '/process/core/Process.php';


date_default_timezone_set('PRC');

const PROCESS_NUM = 2;
const DAEMONIZE = false;

class CronUpdateDMPFixData extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    /**
     * @throws Exception
     */
    protected function worker($n)
    {
        $redis = RedisCache::getInstance();
        $start_time = time();
        $date = date("Y-m-d");
        $dmp_model = new DmpModel();
        $fix_task_key = "device_task_fix_list";
        $device_model = new DeviceTaskModel();
        while ($data = $redis->rPop($fix_task_key)) {
            $data = unserialize($data);
            $device_info = $data['device_info'];

            $start_date = $data['date'] . ' 00:00:00';
            $end_date   = $data['date'] . ' 23:59:59';

            try {
                if ($device_info->auto_update === DeviceTaskModel::INCREMENT) {
                    echo "start task : {$data['device_id']} {$start_date} --- {$end_date}".PHP_EOL;
                    $file_list = $dmp_model->createAppendMuidFile($device_info, $start_date, $end_date);


                    //移动文件到dmp文件
                    $device_info_from_db = $device_model->getData($data['device_id']);
                    $device_task_name = $device_info_from_db->name;
                    $device_task_file_list = array_column(json_decode($device_info_from_db->file_list, true), NULL, 'data_type');
                    $row = $device_info_from_db->auto_update === DeviceTaskModel::FULL ? 0 : $device_info_from_db->row;
                    $update_file_list = [];
                    $zip = new ZipArchive;
                    $zip->open(DeviceService::getZip($data['device_id']), ZIPARCHIVE::CREATE | ZipArchive::OVERWRITE);
                    foreach ($file_list as $file) {
                        $audience_filename = AudienceService::AUDIENCE_DIR . '/' . $file['name'];
                        if (!file_exists($audience_filename)) {
                            continue;
                        }
                        $filename = DeviceService::getFile($data['device_id'], $file['data_type']);
                        if ($device_info_from_db->auto_update === DeviceTaskModel::FULL) {
                            copy($audience_filename, $filename);
                        } else {
                            file_put_contents($filename, file_get_contents($audience_filename), FILE_APPEND);
                        }
                        $row += $file['row'];
                        $update_file_list[] = [
                            'name' => basename($filename),
                            'row' => $device_info_from_db->auto_update === DeviceTaskModel::FULL ? $file['row'] : ((isset($device_task_file_list[$file['data_type']]) ? $device_task_file_list[$file['data_type']]['row'] : 0) + $file['row']),
                            'size' => filesize($filename),
                            'type' => UserIDType::DEVICE_TYPE_MAP[$file['data_type']],
                            'data_type' => $file['data_type'],
                        ];
                        $zip->addFile($filename, "{$device_task_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']]);
                    }
                    $zip->close();
                    $device_model->success($data['device_id'], $row, json_encode($update_file_list));
                    //移动文件到dmp文件 -end

                } else {
                    $file_list = $dmp_model->createFullMuidFile($device_info);
                }
            } catch (Throwable $e) {
                echo "[device_task_id:$device_info->id] child {$n} pid {$this->pid} error: ", $e->getMessage(), PHP_EOL;
                $redis->lPush($fix_task_key, serialize($data));
                throw $e;
            }
        }
        $spend_time = time() - $start_time;
        echo "[$date] child {$n} pid {$this->pid} spend time {$spend_time}." . PHP_EOL;
    }
}

$device_id = [
    '243','266','268','291','292','297','298','304','307','305','315','311','309','320','337','333','313','342','357',
    '377','379','395','391','146','410','435','456','462','466','475','476','477','483','485','454','498','472','527',
    '499','437','529','532','537','251','646','645','438','764','443','453','455','828'
];
$redis = RedisCache::getInstance();
$yesterday = date('Y-m-d', strtotime('-1 day'));
$script_start_time = time();
echo "昨日[$yesterday]任务开始", PHP_EOL;
$device_list = MysqlConnection::createConnection(EnvConfig::MYSQL['default'])
    ->table('device_task')
    ->whereIn('id', $device_id)
    ->where('state', DeviceTaskModel::SUCCESS)
    ->get();

$start_date = "2021-12-30 00:00:00";
$end_date = "2022-01-06 23:59:59";
$fix_task_key = "device_task_fix_list";

foreach ($device_list as $item) {
    $timeStart = strtotime($start_date);
    $timeEnd = strtotime($end_date);

    while ($timeStart < $timeEnd)
    {
        $task_info = [
            'device_id' => $item->id,
            'date' => date('Y-m-d', $timeStart),
            'device_info' => $item
        ];
        $redis->lPush($fix_task_key, serialize($task_info));

        $timeStart = $timeStart + 86400;
    }
}

$multi_process = new MultiProcess(PROCESS_NUM, DAEMONIZE);
$multi_process->runOnce();
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'CronUpdateDMPFixData');
$max_run_times = 3;
$ret = [];
for ($i = 0; $i <= $max_run_times; $i++) {
    echo "子进程打包开始", PHP_EOL;
    $ret = $multi_process->start();
    if (array_sum($ret) > 0) {
        if ($i < $max_run_times) {
            echo "子进程打包发生错误，120秒后, 将进行第", $i + 1, "次重试", PHP_EOL;
            sleep(120);
        } else {
            echo "子进程打包重试后仍然发生错误，退出本次任务", PHP_EOL;
            Helpers::getLogger('dmp')->error(date("Y-m-d") . "子进程打包重试后仍然发生错误，退出本次任务");
            return;
        }
    } else {
        break;
    }
}

echo "子进程打包完成", PHP_EOL;
