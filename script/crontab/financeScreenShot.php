<?php

use Common\EnvConfig;
use App\MysqlConnection;
use App\Service\FinanceToutiaoService;
use App\Service\FinanceTencentService;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo exec('pkill -9  chromedriver > /dev/null &');
sleep(1);
echo exec('chromedriver --port=4444 > /dev/null &');

sleep(3);
echo 'begin======================'.PHP_EOL;
MysqlConnection::setConnection();
$connection_name = 'default';
$key = EnvConfig::AES_KEY;

//头条
$toutiao_accounts = MysqlConnection::getConnection($connection_name)
                    ->table('media_majordomo_account')
                    ->selectRaw("account_id, account, AES_DECRYPT(unhex(`password`), '{$key}') as password")
                    ->where(['media_type' => 1])
                    ->get();

$toutiao_finance = new FinanceToutiaoService();
foreach ($toutiao_accounts as $value) {
    $account_ids = MysqlConnection::getConnection($connection_name)
        ->table('media_account')
        ->select(['account_id'])
        ->where(['toutiao_majordomo_id' => $value->account_id])
        ->get()
        ->toArray();
//var_dump($account_ids); die('xxxxxxxxxxxxxxx');

    $toutiao_finance->screenShot($value->account, $value->password, $account_ids);
    sleep(rand(5,10));
}
//$account_ids = [
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************,
//    ****************
//];
//$toutiao_finance->screenShot('<EMAIL>','Yudeng123', $account_ids);


//腾讯
$tencent_accounts = MysqlConnection::getConnection($connection_name)
    ->table('media_majordomo_account')
    ->selectRaw("account_id, account, AES_DECRYPT(unhex(`password`), '{$key}') as password")
    ->where(['media_type' => 2])
    ->get();

$data = [];
foreach ($tencent_accounts as $value) {
    if ( isset($data[$value->account]) ){
        $data[$value->account]['account_ids'][] = $value->account_id;
    } else {
        $data[$value->account] = [
            'account' => $value->account,
            'password' => $value->password,
            'account_ids' => [$value->account_id],
        ];
    }
}
//print_r($data);
$tencent_finance = new FinanceTencentService();
foreach ($data as $value) {
    $tencent_finance->screenShot($value['account'], $value['password'], $value['account_ids']);
    sleep(5);
}
//$account_ids = [********];
//$tencent_finance->screenShot('**********','112233zaQ@', $account_ids);
//$account_ids = [********, ********];
//$tencent_finance->screenShot('*********','Qh123456@', $account_ids);


echo exec('pkill -9  chromedriver > /dev/null &');

sleep(3);
echo 'end======================'.PHP_EOL;