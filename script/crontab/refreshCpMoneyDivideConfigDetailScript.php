<?php

/**
 * 刷新研发分成配置到 cp_money_divide_config_detail 表
 * 背景：1.新版研发分成配置因为需要支持根游戏、主游戏、子游戏的多选，所以cp_money_divide_config表的游戏字段都是json格式，为了防止出现多个配置选了同个游戏，
 *      所以需要洗一张cp_money_divide_config_detail表，粒度为game_id，在新增/编辑研发配置的时候根据数据来校验是否重复录入
 *      2.基于第一点，如果是选择了某个根游戏，按道理是从v2_dim_game_id获取所有子游戏，然后洗到detail表。但是高热游戏对应的proxy_type不能完全从is_channel来判断（具体连表获取proxy_type的方法有点复杂，问球哥），
 *        另外一个规范问题，tw-15852这个子游戏在game_pay_money里,既有买量又有发行,这就导致dim_game_id里的is_channel不适用来判断买量发行
 * 处理：改从game_pay_money表获取子游戏和proxy_type的关系，因为game_pay_money是经过清洗sql录入的，所以它的proxy_type关系正确
 * 注：因为数据基本都是运营利润-研发分成刷过来的，以前不需要走背景逻辑，现在需要走了，老配置下的游戏在game_pay_money里可能没有流水记录，所以就获取不到游戏关系，这种情况在脚本这跳过处理，在后台录入就报错不给录入。
 * 0 1,14 * * * php refreshCpMoneyDivideConfigDetailScript.php
 * @server ************ zx-script
 */
use App\Model\SqlModel\Zeda\CpMoneyDivideConfigDetailModel;
use App\Model\SqlModel\Zeda\CpMoneyDivideConfigModel;
use App\Param\DMS\CpDivideConfigParam;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
// 设置MySQL连接对象
MysqlConnection::setConnection();

$model = new CpMoneyDivideConfigModel();
$detail_model = new CpMoneyDivideConfigDetailModel();
$list = $model->getAll();
foreach ($list as $item) {
    $input_data = [
        'id' => $item->id,
        'dimension' => $item->dimension,
        'platform' => $item->platform,
        'root_game_ids' => json_decode($item->root_game_ids, true),
        'main_game_ids' => json_decode($item->main_game_ids, true),
        'game_ids' => json_decode($item->game_ids, true),
        'proxy_type' => $item->proxy_type,
        'ios_divide' => $item->ios_divide,
        'yyb_divide' => $item->yyb_divide,
        'applet_divide' => $item->applet_divide,
        'diy_param' => $item->diy_param,
        'formula' => json_decode($item->formula, true),
        'desc' => $item->desc,
    ];
    try {
        MysqlConnection::getConnection()->beginTransaction();
        $param = new CpDivideConfigParam($input_data);
        $param->checkCpDivideConfig();

        $detail_model->deleteByConfigId($param->id);
        $detail_model->add($param->genDetailInsertData());
        MysqlConnection::getConnection()->commit();
    } catch (Exception $exception) {
        // 抛异常的基本都是“所选游戏参数没有流水”，先记录下暂时不做处理
        MysqlConnection::getConnection()->rollBack();
        echo "研发分成配置详情刷新失败：{$item->id} : " . $exception->getMessage() . PHP_EOL;
    }

}



