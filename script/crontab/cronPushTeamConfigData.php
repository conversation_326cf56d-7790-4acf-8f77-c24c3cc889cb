<?php


// 20 0,14,18,21 * * *
// 收集团队配置推送数据


use App\Logic\DMS\PermissionLogic as DMSPermissionLogic;
use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\Tanwan\DwdWechatTeamPushModel;
use App\MysqlConnection;
use App\Task\NoticeTask;
use App\Logic\TeamDataPushLogic;
use App\Utils\Math;
use App\Model\SqlModel\Zeda\NoticeMessageModel;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';

date_default_timezone_set('PRC');

const SEND_USER = [176,750];
//const SEND_USER = [176, 750, 156, 15, 16, 4];

MysqlConnection::setConnection();

$log_model = new DwdWechatTeamPushModel();
//$log_model = new WechatTeamPushModel();

echo "pushing message begin======" . date('Y-m-d H:i:s') . "======================>" . PHP_EOL;
$cur_hour = intval(date("H"));

if ($cur_hour != 0) {
    $panel_date = date("Y-m-d", strtotime("now"));
    $now = date("Y年m月d日（统计日期）", strtotime('now'));
    $title = "今日运营收入（截止到{$cur_hour}点）";
} else {
    $panel_date = date("Y-m-d", strtotime("-1 days"));
    $now = date("Y年m月d日（统计日期）", strtotime('-1 day'));
    $title = '昨日运营收入';
}
$panel_date_time = time();

foreach (SEND_USER as $user_id) {
    $user_permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
    if (is_array($user_permission['game_permission'])) {
        foreach ($user_permission['game_permission'] as &$game_permission) {
            $game_permission['main_game_id'] = [];
            $game_permission['settle_company_name'] = [];
        }
    }
    $panel_data = $log_model->getPanelData($panel_date, $user_permission, $cur_hour);

    $sum_money = (int)$panel_data->sum_money ?? 0;
    $month_income = (int)$panel_data->sum_month_total_pay_money ?? 0;
    $income = (int)$panel_data->sum_total_pay_money ?? 0;
    $sum_first_day_pay_money = $panel_data->sum_first_day_pay_money ?? 0;
    $sum_total_standard_value = $panel_data->sum_total_standard_value ?? 0;
    $rate = Math::rate($sum_first_day_pay_money, $sum_total_standard_value) . '%';
    $income = Math::decimal($income, 2);
    $sum_data = "消耗：{$sum_money} \n日流水：{$income} \n月流水：{$month_income} \n首日回本达标率：{$rate} ";

    $logic = new TeamDataPushLogic();
    $wechat_url = $logic->getWechatPushUrl($panel_date_time);
    $feishu_url = $logic->getFeishuPushUrl($user_id, $panel_date_time);

    $notice_task = new NoticeTask();
//    $notice_task->pushWeChat([
//        'user_id' => $user_id,
//        'type' => NoticeMessageModel::TYPE_WECHAT_DATA,
//        'template_id' => TemplateModel::STATISTICS_TEMPLATE,
//        'url' => $wechat_url,
//        'config_name' => 'qingbaoju',
//        'data' => [
//            "first" => ["value" => "{$title}", "color" => "#000000"],
//            "keyword1" => ["value" => "团队数据", "color" => "#000000"],
//            "keyword2" => ["value" => "{$now}", "color" => "#000000"],
//            "keyword3" => ["value" => "{$sum_data}", "color" => "#169115"],
//            "remark" => ["value" => "点击进入查看详情", "color" => "#000000"],
//        ]
//    ]);
    $notice_task->pushFeishu([
        'user_id' => $user_id,
        'type' => NoticeMessageModel::TYPE_FEISHU_DATA,
        'config_name' => 'qingbaoju',
        'content' => [
            "elements" => [
                [
                    "tag" => "markdown",
                    "content" => "**团队数据-{$title}**\n",
                    "text_align" => "center"
                ],
                [
                    "tag" => "markdown",
                    "content" => "**{$now}**"
                ],
                [
                    "tag" => "column_set",
                    "flex_mode" => "bisect",
                    "background_style" => "grey",
                    "horizontal_spacing" => "default",
                    "columns" => [
                        [
                            "tag" => "column",
                            "width" => "weighted",
                            "weight" => 1,
                            "elements" => [
                                [
                                    "tag" => "markdown",
                                    "text_align" => "center",
                                    "content" => "消耗\n**{$sum_money}**"
                                ]
                            ]
                        ],
                        [
                            "tag" => "column",
                            "width" => "weighted",
                            "weight" => 1,
                            "elements" => [
                                [
                                    "tag" => "markdown",
                                    "text_align" => "center",
                                    "content" => "日流水\n**{$income}**"
                                ]
                            ]
                        ],
                        [
                            "tag" => "column",
                            "width" => "weighted",
                            "weight" => 1,
                            "elements" => [
                                [
                                    "tag" => "markdown",
                                    "text_align" => "center",
                                    "content" => "月流水\n**{$month_income}**"
                                ]
                            ]
                        ],
                        [
                            "tag" => "column",
                            "width" => "weighted",
                            "weight" => 1,
                            "elements" => [
                                [
                                    "tag" => "markdown",
                                    "text_align" => "center",
                                    "content" => "首日回本达标率\n**{$rate}**"
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    "tag" => "action",
                    "actions" => [
                        [
                            "tag" => "button",
                            "text" => [
                                "tag" => "plain_text",
                                "content" => "点击进入查看详情"
                            ],
                            "type" => "primary",
                            "multi_url" => [
                                "url" => "{$feishu_url}"
                            ]
                        ]
                    ]
                ]
            ],
            "header" => [
                "template" => "blue",
                "title" => [
                    "content" => "统计结果通知",
                    "tag" => "plain_text"
                ]
            ]
        ]
    ]);

}

echo "pushing message end======" . date('Y-m-d H:i:s') . "======================>" . PHP_EOL;
