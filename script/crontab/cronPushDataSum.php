<?php

//
// 推送微信数据
// 在每小时的第20分钟开始，一直到第30分钟，每分钟执行一次指定的命令 （30分的时候应该是数据全跑完了）
// 20-30 * * * * php cronPushDataSum.php

/*
 * 推送逻辑：
 * 1. 首先有个任务会每五分钟跑一次，把数据入表。我们的推送的数据是基于这个表的数据。然后每天0点大概是20分左右会跑完所有数据并入库。
 * 所以，我们的推送任务开始时间是每个小时的第 20分钟到30分钟轮询的跑。留i下一点冗余时间。到30分大概率已经全跑完了。
 *
 * 2. 每个任务会配置自己是按天推送还是按小时推送。
 * 按天推送的话，是 push_hour为 0，按小时推送则为每个小时的数字。
 * 例如某个任务的 push_hour是 0，则在每天的 0 点会推送一次。过了 0 点则不再推送。实现一天推一次的效果。
 * 又例如某个任务的 push_hour是 $hour，当前小时是$current_hour。则表示每$hour个小时推送一次。 实现的判断方式是 【$current_hour % $push_hour = 0】。
 * 注：配置表的push_minute字段已经没用了。
 *
 * 3. 实现细节：
 * 在指定的时间段内，每分钟都去拿所有配置出来。
 * 推完记录一下当前的推送日志。下次不能再推(保证本小时内只推一次)。推送日志的 key应该是 日期+user_id+theme+module+hour
 *
 * 推送的时候需要判断当天的dau是否有数据，有才需要推送。因为 dau这个指标可以判断出数据是否录入完成。
 * 当有数据的时候，执行一下 py截图脚本。然后才执行推送。
 *
 */

use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic as DMSPermissionLogic;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\BotModel;
use App\Model\HttpModel\Feishu\IM\ImagesModel;
use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;
use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\MysqlConnection;
use App\Struct\RedisCache;
use App\Task\NoticeTask;
use App\Utils\Helpers;
use App\Utils\Math;
use Monolog\Logger;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$logger = Helpers::getLogger('data_sum_push');

$logger->info('推送任务开始运行');


$panel_date = date("Y-m-d");
$last_panel_date = date("Y-m-d", strtotime("-1 days"));
$time = strtotime(date("Y-m-d 00:00:00"));
$cur_hour = intval(date("H"));
$panel_date_time = time();
$cur_min = intval(date("i"));

//////// TODO 时间改为今天凌晨 再推一次
//$panel_date_time = 1750176000;
//
//$cur_hour = 0;

$config_model = new WechatPushConfigNewModel();
$push_log = [];


// 获取配置列表
$list = $config_model->getList();


// 初始化进程数量
$process_num = 5;

// 按进程数量分配配置列表
$size = ceil(count($list) / $process_num);
$chunk_list = $list->chunk($size);

//// 子进程的进程id数组
//$pid_list = [];

// 循环创建进程
for ($n = 1; $n <= $process_num; $n++) {
    $pid = pcntl_fork();
    if ($pid < 0) {
        $logger->error('创建进程失败');
        exit;
    } else if ($pid === 0) {
        // 初始化链接，在子进程进行
        MysqlConnection::setConnection();

        // 子进程执行
        $logger->info('子进程执行 :' . $n);


        // 0点的最后一次定时任务,需要一直循环等待任务跑完再推
        if ($cur_hour === 0 && $cur_min === 30) {
            $max_wait_time = 7200; // 设置最大等待时间 两个小时吧
            $start_time = time();

            while (1) {

                try {
                    // 判断收集数据的脚本是否跑完
                    $end_run = RedisCache::getInstance()->get('collect_push_sum_data' . date("Ymd"));

                    // 还要判断 zeda 的脚本有没有跑完, zeda 还要延迟5分钟
                    $zeda_end_run = RedisCache::getInstance()->get('zeda_collect_push_sum_data' . date("Ymd"));

                    if ($end_run && $zeda_end_run && (time() - $zeda_end_run > 300)) {
                        $logger->info('等待结束，退出循环', ['end_run' => $end_run, 'zeda_end_run' => $zeda_end_run, 'time' => time()]);
                        break;
                    }
                    $logger->info('脚本没跑完,等待 1 分钟');
                } catch (RedisException $e) {
                    $logger->error('Redis 操作出现异常: ' . $e->getMessage());
                }

                // 检查是否已达到最大等待时间
                if ((time() - $start_time) > $max_wait_time) {
                    $logger->info('已达最大等待时间，中断等待');
                    break;
                }

                sleep(60); // 等待 60 秒
            }
        }
        try {
            if (isset($chunk_list[$n - 1])) {
                pushMessage($logger, $chunk_list[$n - 1], $cur_hour, $panel_date);
            }
        } catch (\Throwable $throwable) {
            $logger->error('推送任务崩溃，错误信息：' . $throwable->getMessage());
        }

        $logger->info('子进程退出 :' . $n);
        exit;
    }
//    else {
//        // 父进程收集子进程 id
//        $pid_list[] = $pid;
//    }

}

//// 父进程等待所有子进程完成
//$logger->info('父进程等待所有子进程完成');
//$status = 0;
//foreach ($pid_list as $pid) {
//    pcntl_waitpid($pid, $status);
//}
//
//$logger->info('推送任务结束');


/**
 * @param Logger $logger
 * @param $list
 * @param $cur_hour
 * @param $panel_date
 * @throws RedisException
 */
function pushMessage(Logger $logger, $list, $cur_hour, $panel_date)
{
    $notice_task = new NoticeTask();
    $bot_model = new BotModel();
    global $panel_date_time;
    foreach ($list as $item) {
        $theme = $item->theme;
        $user_id = $item->user_id;

        if ((int)$item->push_hour === 0) {
            // 按天0点推送
            if ($cur_hour !== 0) {
                continue;
            }

            // 按天推送还需要判断收集数据的脚本是否跑完
            $end_run = RedisCache::getInstance()->get('collect_push_sum_data' . date("Ymd"));
            if (!$end_run) {
                // 脚本没跑完
                $logger->info('脚本没跑完，先不推');
                continue;
            }
            // 还要判断 zeda 的脚本有没有跑完, zeda 还要延迟5分钟
            $end_run = RedisCache::getInstance()->get('zeda_collect_push_sum_data' . date("Ymd"));
            if (!$end_run || (time() - $end_run < 300)) {
                // 脚本没跑完
                $logger->info('zeda脚本没跑完，先不推');
                continue;
            }

        } else {
            // 按X小时推送
            //当天的先不推送  -- 去除这个逻辑
//            if (date('Y-m-d', $item->create_time) === date('Y-m-d')) {
//                continue;
//            }
            //当前小时数 % $item->push_hour = 0
            if ($cur_hour % $item->push_hour !== 0) {
                continue;
            }
        }

        // 相同用户的相同主题只推一份。不区分 module
        $key = "$user_id-$theme";
        if (isset($push_log[$key])) {
            continue;
        }
        $push_log[$key] = true;


        // Redis 记录的key，用来确保一个小时内只推送一次
        $push_key = getPushKey($panel_date, $user_id, $theme, $item->module, $cur_hour);
        if (RedisCache::getInstance()->get($push_key)) {
            continue;
        }

        // 这里还需要添加一个执行锁，避免多进程访问多次推送的问题。
        if (!lock($push_key, $logger)) {
            continue;
        }

        if ($item->module === 'dms') {
            $permission = fix_dms_permission($item->config, $theme, $item->account);
            try {
                $user_permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
            } catch (AppException|\Throwable $exception) {
                $logger->info("获取dms权限报错。uid => " . $user_id);

                // 解锁
                unlock($push_key, $logger);
                continue;
            }
            $data_model = new WechatPushDataDmsModel();
            $panel_data = $data_model->getDau($permission, $panel_date, $cur_hour, $user_permission);
        } else {
            $permission = fix_ly_permission($item->config, $theme, $item->account);
            $data_model = new WechatPushDataLyModel();
            $panel_data = $data_model->getDau($permission, $panel_date, $cur_hour);
        }
        $dau = $panel_data->sum_dau ?? 0;
        if ($dau <= 0) {
            $logger->info('数据没跑完，先不推', ['push_key' => $push_key]);
            // 解锁
            unlock($push_key, $logger);
            continue;
        }
//
////        // TODO 上线后去掉
//        if ($user_id != 151) {
//            // 解锁
//            unlock($push_key, $logger);
//            continue;
//        }


        $logger->info('执行截图脚本', ['push_key' => $push_key]);

        // 先截图，再推送
        // 执行Python脚本，所需参数：start_time，user_id，theme, code
        $python_path = "/data/www/script.zx.com/script/screenshot.py";
        $secret = '66Nhd&&(';
        $code = md5($panel_date_time . $user_id . $theme . $secret);
        $command = "python3 {$python_path} {$user_id} '{$theme}' {$panel_date_time} {$code}";
        $data = exec($command, $out_array, $result_code);
        $data = json_decode($data, true);
        // 执行异常的任务跳过
        if ($result_code !== 0) {
            $logger->error("推送截图任务命令执行异常, 先不推。", ['push_key' => $push_key, 'out_array' => $out_array]);
            // 解锁
            unlock($push_key, $logger);
            continue;
        }
        if ($data['code'] != 0) {
            $logger->error("推送截图任务执行异常, 先不推。", ['push_key' => $push_key, 'res_data' => $data]);
            // 解锁
            unlock($push_key, $logger);
            continue;
        }
        $logger->info('截图完成，开始推送', ['push_key' => $push_key, 'img_path' => $data['data']['img_path']]);


        $image_path = $data['data']['img_path'];
        try {
            $image_key = uploadImage($image_path);
        } catch (\Throwable $throwable) {
            $logger->error('图片上传失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key]);
            // 解锁
            unlock($push_key, $logger);
            continue;
        }

        $push_channel = json_decode($item->push_channel, true);
        foreach ($push_channel as $channel) {
            if ($channel === 'wechat') {
                // 微信推送不在这里处理，在另一个脚本
                continue;
            }

            if ($channel === 'feishu') {

                try {
                    $notice_task->pushFeishu(
                        [
                            'user_id'     => $user_id,
                            'type'        => NoticeMessageModel::TYPE_FEISHU_DATA,
                            'config_name' => 'qingbaoju',
                            'content'     => ['image_key' => $image_key],
                        ], 'image');
                    $logger->info('单推完成推送', ['push_key' => $push_key]);
                } catch (\Throwable $throwable) {
                    $logger->error('飞书单推推送失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key, 'strace' => $throwable->getTrace()]);
                    continue;
                }

            }

            if ($channel === 'feishu_group') {
                if (isset($item->webhook) && $item->webhook) {
                    try {
                        $content_data = [
                            [
                                ['tag' => 'img', 'image_key' => $image_key]
                            ]
                        ];
                        $bot_model->postMessage($item->webhook, '', $content_data);
                        $logger->info('群推完成推送', ['push_key' => $push_key, 'image_key' => $image_key]);
                    } catch (\Throwable $throwable) {
                        $logger->error('飞书群推推送失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key, 'strace' => $throwable->getTrace()]);
                        continue;
                    }

                }
            }
        }

        // 解锁
        unlock($push_key, $logger);
        // 记录推送日志
        RedisCache::getInstance()->set($push_key, 1, ['ex' => 3600]);
        unlink($data['data']['img_path']);

    }
}

function fix_dms_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);

    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'dms', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $alg_names = [];
            $type = $item['type'];
            $info = $item['info'];
            $is_apportioned = $item['is_apportioned'] ?? 1;

            foreach ($info as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $ag = $data['ag_name'] ?? [];
                $alg = $data['alg_name'] ?? [];
                $alg_names = array_merge($alg_names, $alg);
                $os = $data['os'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['main_game_id'   => $main_game_id,
                                             'agent_group_id' => $ag,
                                             'os'             => $os];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge($type_info[$platform]['main_game_id'], $main_game_id);
                    $type_info[$platform]['agent_group_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $alg_names = array_unique($alg_names);
            $types[$sub_theme][$type] = [
                'game'           => $type_info,
                'alg'            => $alg_names,
                'is_apportioned' => $is_apportioned,
            ];
        }
    }
    return $types;
}

function fix_ly_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);
    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'ly', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $type = $item['type'];
            $info = $item['info'];

            foreach ($info as $data) {
                $platform = $data['platform'];
                $game_id = $data['game_id'];
                $channel_id = $data['channel_id'] ?? [];
                $ag = $data['ag_name'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['game_id'        => $game_id,
                                             'agent_group_id' => $ag,
                                             'channel_id'     => $channel_id];
                } else {
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['game_id'], $game_id);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['channel_id'], $channel_id);
                }
            }

            $types[$sub_theme][$type] = ['game' => $type_info];
        }
    }
    return $types;
}


function getPushKey($date, $user_id, $theme, $module, $hour)
{
    return "{$date}|{$user_id}|{$theme}|{$hour}|{$module}";
}

/**
 * 上传图片到飞书获取image_key
 * @param string $image_path
 * @return mixed|string
 */
function uploadImage(string $image_path)
{
    $model = new ImagesModel();
    // 暂时先写死，到时需要改
    $app_id = 'cli_a490d70d6efb900b';
    $app_secret = 'BrE2l2evxpuRiLxwkcPXYeFOmS0aCxBW';
    $app_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $app_id, $app_secret);
    $data = $model->uploadImages($app_access_token, $image_path);
    return $data['data']['image_key'] ?? '';
}

/**
 * 获取执行锁，用来判断当前任务是否正在执行。
 *
 * @param $push_key
 * @param Logger $logger
 * @return bool  获锁成功返回true 否则返回false
 * @throws RedisException
 */
function lock($push_key, Logger $logger)
{
    $key = 'lock_push_' . $push_key;
    if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 3600])) {
        $logger->info('成功获取执行锁', ['push_key' => $push_key]);
        return true;
    } else {
        $logger->info('获取执行锁失败', ['push_key' => $push_key]);
        return false;
    }
}

/**
 * 解锁
 *
 * @param $push_key
 * @param Logger $logger
 * @throws RedisException
 */
function unlock($push_key, Logger $logger)
{
    $key = 'lock_push_' . $push_key;
    $result = RedisCache::getInstance()->del($key);
    $logger->info('解锁执行锁', ['push_key' => $push_key, 'res' => $result]);
}