<?php
/**
 * 30 /2 * * *
 * 检测素材是否达标，标红
 */

use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo date('Y-m-d H:i:s') . '--Material mark begin' . PHP_EOL;

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';


$material_mark_rule = MysqlConnection::getConnection($connection_name)->table('material_mark_rule')->where(['is_del' => 0])->get();
$material_theme = MysqlConnection::getConnection($connection_name)->table('material_theme')->get();
foreach ($material_mark_rule as $value) {
    $rule_id = $value->id;
    $platform = $value->platform;
    $theme_id = $value->theme_id;
    $theme_ids = $material_theme->filter(function ($value) use($platform,$theme_id) {
            return $value->platform == $platform && $value->theme_pid == $theme_id;
        })
        ->pluck('theme_id')
        ->all();
    //var_dump($theme_ids);


    $builder = MysqlConnection::getConnection($connection_data_media)
        ->table('ods_material_log')
        ->where(['platform' => $platform])
        ->whereIn('theme_id', $theme_ids);
    $list = $builder->get();
    //var_dump($list);

    foreach ($list as $material) {
        $check = [];
        if (!empty($value->calc) && $value->calc != 'null') {
            $calc_array = json_decode($value->calc, true);
            foreach ($calc_array as $calc) {
                if ($calc['name'] === 'cost') {
                    if ($material->cost > 0) {
                        $target_value = $material->cost;
                    }
                } elseif ($calc['name'] === 'first_day_roi') {
                    if ($material->cost > 0 && $material->day_first_day_pay_money > 0) {
                        $target_value = ($material->day_first_day_pay_money / $material->cost) * 100;
                    }
                }
                if (isset($target_value)) {
                    if ($calc['operator'] === 'gt') {
                        $check[] = $target_value > $calc['value'][0];
                    } elseif($calc['operator'] === 'lt') {
                        $check[] = $target_value < $calc['value'][0];
                    } elseif($calc['operator'] === 'in') {
                        $check[] = $target_value >= $calc['value'][0] && $target_value <= $calc['value'][1];
                    }
                    unset($target_value);
                }
            }
        }

        if (count($check) > 0 && !in_array(false, $check)) {
            //符合条件且未标记的标记1
            if ((int) $material->is_under_qualified === 0) {
                echo "marking material=1=={$material->material_id}=={$platform}==rule_id==={$rule_id}===day_first_day_pay_money=={$material->day_first_day_pay_money}====cost=={$material->cost}====>\n";
                MysqlConnection::getConnection($connection_data_media)
                    ->table('ods_material_log')
                    ->where(['platform' => $platform, 'material_id' => $material->material_id])
                    ->update(['is_under_qualified' => 1]);
            }
        } else {
            //不符合条件且已标记1的变回0
            if ((int) $material->is_under_qualified === 1) {
                echo "marking material=0=={$material->material_id}=={$platform}==rule_id==={$rule_id}===day_first_day_pay_money=={$material->day_first_day_pay_money}====cost=={$material->cost}====>\n";
                MysqlConnection::getConnection($connection_data_media)
                    ->table('ods_material_log')
                    ->where(['platform' => $platform, 'material_id' => $material->material_id])
                    ->update(['is_under_qualified' => 0]);
            }
        }
    }
}

echo date('Y-m-d H:i:s') . '--Material mark finish' . PHP_EOL;