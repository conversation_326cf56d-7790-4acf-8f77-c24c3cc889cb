<?php
/**
 * 0 2 3 * * /usr/local/php/bin/php /data/www/script.zx.com/script/crontab/starOrderCancel.php >> /data/www/script.zx.com/logs/starOrderCancel.log
 * 星图订单自动取消 每月3日凌晨两点
 */

use App\Logic\DSP\ToutiaoStarOrderMQLogic;
use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandOrderOperateLogModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

$order_list = (new OdsStarDemandOrderListLogModel())->getWaitCancelOrderData();

if ($order_list->isNotEmpty()) {

    $mq_logic = new ToutiaoStarOrderMQLogic;
    $log_model = new OdsStarDemandOrderOperateLogModel();

    foreach ($order_list as $order) {
        $task_param = [
            "platform" => $order->platform,
            "account_id" => $order->account_id,
            "demand_id" => $order->demand_id,
            "order_id" => $order->order_id,
            "author_id" => $order->author_id,
            "author_name" => $order->author_name,
            "order_create_time" => $order->create_time,
            "operate_type" => OdsStarDemandOrderOperateLogModel::OPERATE_TYPE_ORDER_CANCEL,
            "operate_detail" => '{}',
            "operate_param" => '{}',
            "status" => 0,
            "creator" => '自动取消',
        ];
        $task_id = $log_model->add($task_param);
        $mq_logic->produceTask($task_id);
    }
}