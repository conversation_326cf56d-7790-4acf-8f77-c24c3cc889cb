<?php
/**
 * 每隔30分钟检查一次有无新的所需的素材同步
 */

use App\Logic\DSP\MaterialLogic;
use App\Model\SqlModel\Zeda\MaterialSyncModel;
use App\MysqlConnection;
use App\Param\Material\MaterialSyncParam;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$list = MysqlConnection::getConnection()
    ->table('material_sync')
    ->where(['state_code' => 0])
    ->where('sync_file_type', "!=", 3)
    ->get();

foreach ($list as $key => $info) {
    $param = new MaterialSyncParam([
        'sync_platform' => $info->sync_platform,
        'sync_platform_material_id' => $info->sync_platform_material_id,
        'sync_theme_id' => $info->sync_theme_id,
        'sync_create_mode' => $info->sync_create_mode,
        'sync_material_name' => $info->sync_material_name,
        'sync_file_type' => $info->sync_file_type,
        'sync_file_list' => json_decode($info->sync_file_list, true),
        'sync_extend_size_video' => json_decode($info->sync_extend_size_video, true),
        'ext' => json_decode($info->ext, true)
    ]);
    try {
        (new MaterialLogic())->doSyncMaterialFile($info->id, $param);
    } catch (Throwable $e) {
        $param->state_code = 2;
        $param->error = $e->getMessage();
        (new MaterialSyncModel())->update($info->id, $param->toData());
        var_dump(date('Y-m-d H:i:s') . '-' . $e->getMessage(), '——' . $e->getTraceAsString() . PHP_EOL);
    }
}
