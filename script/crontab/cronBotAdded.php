<?php
//
// 机器人入群事件
// */1 * * * * php cronExportFileTask.php
//

use App\ElasticsearchConnection;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\MysqlConnection;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Struct\RedisCache;
use App\Task\GroupAssistantTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$service = new GroupAssistantService();
$start = time();
$redis = RedisCache::getInstance();
$logger = Helpers::getLogger('group_assistant_bot');
while (time() - $start < 60) {
    $queue_data = [];
    try {
        $queue_data = $redis->rPop(GroupAssistantTask::BOT_ADD_QUEUE_KEY);
    } catch (RedisException $e) {
        // 出队失败
        $logger->error('消息队列出队失败', ['message' => $e->getMessage()]);
    }

    if ($queue_data) {
        $queue_data = json_decode($queue_data, true);
        $chat_id = $queue_data['chat_id'];
        $chat_name = $queue_data['chat_name'];
        $union_id = $queue_data['union_id'];
        $logger->info('机器人进群，队列开始消耗', ['chat_id' => $chat_id]);

        $service->handlerGroupAssistantBotAdded($chat_id, $chat_name, $union_id);
        break;
    }

    sleep(1);
}