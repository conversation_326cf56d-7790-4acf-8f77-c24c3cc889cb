<?php
/**
 * 45 11 * * *
 * 拉取标准预警弹框的数据到本地的临时表
 * 每天刷新一次表数据
 * User: Melody
 * Date: 2020/6/2
 * Time: 11:07
 */

use App\Model\SqlModel\Tanwan\EarlyWarningModel;
use App\Model\SqlModel\Zeda\AlertPayDataModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$alert_data_model = new AlertPayDataModel();
$logger = Helpers::getLogger('pull_alert_payment_data');
$logger->info('任务开始');

// 先删除三个月之前的数据
$del_date = date("Y-m-d", strtotime('-3 month'));
$alert_data_model->deleteByGameRegDate($del_date);
$logger->info('表删除完成');

// 拉取数据 取昨天的数据
$logger->info('开始拉取数据');
$game_reg_date = date("Y-m-d", strtotime('-1 day'));
$list = (new EarlyWarningModel())->getAlertPaymentList($game_reg_date);

if (empty($list)) {
    $logger->info('拉取的数据为空，退出脚本');
    exit;
}
$logger->info('数据拉取完成，开始插入数据');

// 分批次插入 一次1000
$offset = 0;
while (1) {
    $insert_data = [];
    $f_list = array_slice($list, $offset, 1000);
    if (empty($f_list)) {
        $logger->info('任务结束');
        exit;
    }
    foreach ($f_list as $index => $item) {
        if ($item->standard_value <= 0) {
            // 不入库
            continue;
        }
        if ($item->day_cost_money < 100) {
            // 不入库
            continue;
        }
        // 算出回本率
        $first_day_roi = bcdiv($item->day_first_day_pay_money, $item->day_cost_money, 10);
        // 计算差异
        $diff = bcdiv($first_day_roi, $item->standard_value, 4) - 1;
        // 对比每日回本标准，不达标的才入库
        if ($diff >= -0.1) {
            // 已经达标，不入库
            continue;
        }

        $insert_data[] = [
            'platform' => $item->platform,
            'game_reg_date' => $item->game_reg_date,
            'agent_leader' => $item->agent_leader,
            'agent_group_id' => $item->agent_group_id,
            'agent_group_name' => $item->agent_group_name,
            'agent_id' => $item->agent_id,
            'agent_name' => $item->agent_name,
            'os' => $item->os,
            'plat_id' => $item->plat_id,
            'plat_name' => $item->plat_name,
            'root_game_id' => $item->root_game_id,
            'root_game_name' => $item->root_game_name,
            'main_game_id' => $item->main_game_id,
            'main_game_name' => $item->main_game_name,
            'game_id' => $item->game_id,
            'game_name' => $item->game_name,
            'day_pay_money' => $item->day_first_day_pay_money,
            'standard_value' => $item->standard_value,
            'first_standard_value' => Math::decimal($item->standard_value * 100, 2) . '%',
            'day_cost_money' => $item->day_cost_money,
            'first_day_roi' => Math::decimal($first_day_roi * 100, 2) . '%',
            'diff' => ($diff * 100) . '%',
            'day_reg_uid_count' => $item->day_reg_uid_count,
            'mean_first_day_pay_roi' => $item->mean_first_day_pay_roi,
        ];
    }


    try {
        $alert_data_model->addMultiple($insert_data);
        $offset += 1000;
        $logger->info('插入数据成功');
    } catch (\Throwable $exception) {
        $logger->info('数据插入异常，脚本结束');
        $logger->error(substr($exception->getMessage(), 0, 100));
        exit;
    }
}


