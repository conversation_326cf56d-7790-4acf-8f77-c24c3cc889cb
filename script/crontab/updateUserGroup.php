<?php
/**
 * 每天十二点20分跑一次分群更新
 *
 * 20 0 * * *
 * Created by PhpStorm.
 * User: Melody
 * Date: 2022/3/25
 * Time: 14:55
 */

use App\Logic\DMS\UserGroupLogic;
use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\Model\ClickhouseModel\TanwanDatahub\DwdUserGroupLogModel as CKDwdUserGroupLogModel;
use App\Model\SqlModel\Tanwan\V2DWDUserGroupLogModel;
use App\Model\SqlModel\Zeda\UserGroupModel;
use App\MysqlConnection;
use App\Param\DMS\UserGroupParam;
use App\Task\UserGroupTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
// 设置ck数据库链接对象
ClickhouseConnection::setConnection();

$logger = Helpers::getLogger('cron_user_group');
$user_group_model = new UserGroupModel();
$task = new UserGroupTask();

$logger->info('任务开始');
// 获取需要更新的分群列表
$user_group_list = $user_group_model->getUpdateGroupList();
$user_group_logic = new UserGroupLogic();

$logger->info('分群列表获取成功');


foreach ($user_group_list as $param_data) {
    try {
        $logger->info('分群id:' . $param_data->id . ' 开始更新');

        $group_id = $param_data->id;
        $param = new UserGroupParam([
            'group_defined' => json_decode($param_data->group_defined, true),
            'dimension_type' => $param_data->dimension_type,
        ]);
        $param->handleConditionGroupDefined();
        $param->setUserPermission($param_data->user_id);
        $param->setType(2);

        // 获取uid总数
        $all_uid_count_data = $user_group_logic->getAllUidCount($param);
        $all_uid_count = $all_uid_count_data['total_uid_count'];

        $logger->info('获取总数完成');
        // 先删除再添加
        (new V2DWDUserGroupLogModel())->deleteByGroupId($group_id);  // ADB的删除
        (new CKDwdUserGroupLogModel())->deleteByGroupId($group_id);  // ck数据仓库的删除

        $logger->info('数据删除完成');
        // 更新状态
        $update_data = [
            'state' => 0, // 任务跑完后更新状态
            'user_numbers' => $all_uid_count,
            'ck_state' => 0, // 任务跑完后更新状态
        ];
        $user_group_model->updateUserGroup($update_data, $group_id);

        $logger->info('数据状态更新完成');

        $data = [
            'group_id' => $group_id,
            'all_uid_count' => $all_uid_count,
            'user_group_param' => $param
        ];

        $task->addConditionUserGroup($data);
        $logger->info('ADB数据更新完成');

        $task->addCkConditionUserGroup($data);
        $logger->info('CK数据更新完成');
    } catch (\Throwable $exception) {
        $logger->error('任务出错', ['message' => $exception->getMessage(), 'data' => $param_data]);
    }

}
$logger->info('任务结束');
