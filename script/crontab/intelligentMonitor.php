<?php

use App\Logic\DSP\ADIntelligentMonitorLogic;
use App\Model\RabbitModel\ADIntelligentMonitorMQModel;
use App\Model\RedisModel\IntelligentRobotFrequencyModel;
use App\Model\SqlModel\DataMedia\DwdMediaAD4CommonLogModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorBindModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorRobotModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$open_time = date('H:i');

echo date('Y-m-d H:i:s') . ' 智创监控开始生成任务' . PHP_EOL;

$bind_log_model = new ADIntelligentMonitorBindModel();
$bind_log_list = $bind_log_model->getExecList();

echo '-----------本次有' . $bind_log_list->count() . '条任务' . PHP_EOL;

$list_group_by_robot_and_bind_type = $bind_log_list->groupBy(['monitor_robot_id', 'bind_type']);

$logic = new ADIntelligentMonitorLogic();

$mq_model = new ADIntelligentMonitorMQModel();

$robot_sql_model = new ADIntelligentMonitorRobotModel();

$ad4_common_log_model = new DwdMediaAD4CommonLogModel();

$robot_frequency_redis_model = new IntelligentRobotFrequencyModel();

$merge_msg_map = [];
$robot_msg_max_num = 5;

$current_minutes = date('i');
$current_hours = date('H');

foreach ($list_group_by_robot_and_bind_type as $robot_id => $bind_target_list_group_by_bind_type) {

    $robot_info = $robot_sql_model->getDataById($robot_id);
    if (!$robot_info) {
        echo '----------------------机器人-id:' . $robot_id . '找不到具体信息-跳过生成监控任务' . PHP_EOL;
        continue;
    }

    if ($robot_info->exec_mode == 'time' && $robot_info->monitoring_time != $open_time) {
        echo '----------------------机器人-id:' . $robot_id . '指定时间:' . $robot_info->monitoring_time . ' 运行,当前时间:' . $open_time . ',跳过生成监控任务' . PHP_EOL;
        continue;
    }

    if ($robot_info->exec_mode == 'loop') {
        if ($robot_frequency_redis_model->getFrequencyLockForRobot($robot_id)) {
            echo '----------------------机器人-id:' . $robot_id . '-频率被锁-跳过生成监控任务-ttl还有:' . $robot_frequency_redis_model->getFrequencyLockForRobotTTL($robot_id) . PHP_EOL;
            continue;
        } else {
            // 设置机器人频率
            $robot_frequency_redis_model->setFrequencyLockForRobot(
                $robot_id,
                $robot_info->monitoring_frequency
            );
        }
    }

    foreach ($bind_target_list_group_by_bind_type as $bind_type => $bind_target_list) {

        // 范围绑定
        if ($bind_type == 'range') {

            $media_type = $bind_target_list[0]->media_type;

            $bind_list_group_by_bind_target_type_and_target_type = $bind_target_list->groupBy(['bind_target_type', 'target_type']);

            foreach ($bind_list_group_by_bind_target_type_and_target_type as $bind_target_type => $bind_list_group_by_target_type) {

                foreach ($bind_list_group_by_target_type as $target_type => $__bind_target_list) {

                    $bind_list_key_by_bind_target_value = $__bind_target_list->keyBy('bind_target_value');

                    $bind_target_value_list = $__bind_target_list->pluck('bind_target_value')->toArray();

                    $all_range_list = $ad4_common_log_model->getListForRangeBindMonitorForGroup(
                        $media_type,
                        $bind_target_type,
                        $bind_target_value_list,
                        $target_type
                    );

                    $all_range_list_group_by_bind_target_type = $all_range_list->groupBy("{$bind_target_type}_id");

                    foreach ($all_range_list_group_by_bind_target_type as $bind_target_value => $list) {

                        echo '----------------------' . date('Y-m-d H:i:s') . '本次记录' .
                            '-robot_id:' . $robot_id . '-bind_log_id:' . $bind_list_key_by_bind_target_value[$bind_target_value]->id .
                            '-分裂出' . $list->count() . '条任务' . PHP_EOL;

                        $mq_bind_target_list = [];

                        foreach ($list as $index => $target_value) {

                            $bind_target = clone($bind_list_key_by_bind_target_value[$bind_target_value]);

                            $bind_target->target_value = $target_value->{"{$bind_target->target_type}_id"};

                            $mq_bind_target_list[] = $bind_target;
                        }

                        if ($mq_bind_target_list) {
                            $extend_data = [
                                'robot_id' => $robot_id,
                                'round' => $current_hours * 12 + floor($current_minutes / 5) + 1,
                                'target_type' => $target_type,
                                'bind_target_type' => $bind_target_type,
                                'bind_target_value' => $bind_target_value
                            ];

                            $mq_model->produce([
                                'data_list' => $mq_bind_target_list,
                                'extend_data' => $extend_data
                            ]);
//                            (new ADIntelligentMonitorLogic())->bindTargetLogic($mq_bind_target_list, $extend_data);
                        }
                    }

                    foreach ($__bind_target_list as $bind_target) {
                        if ($robot_info->exec_mode == 'once') {
                            $bind_log_model->finish($bind_target->id);
                        }
                    }
                }
            }
        }

        // 精准绑定
        if ($bind_type == 'precise') {
            foreach ($bind_target_list as $bind_target) {
                $mq_model->produce([
                    'data_list' => [$bind_target],
                    'extend_data' => [
                        'robot_id' => $robot_id,
                        'round' => $current_hours * 12 + floor($current_minutes / 5) + 1,
                        'target_type' => $bind_target->target_type,
                        'bind_target_type' => $bind_target->bind_target_type,
                        'bind_target_value' => $bind_target->bind_target_value
                    ],
                ]);

                if ($robot_info->exec_mode == 'once') {
                    $bind_log_model->finish($bind_target->id);
                }
            }
        }
    }


}

echo '-----------' . date('Y-m-d H:i:s') . ' 智创监控结束生成任务' . PHP_EOL;
exit;
