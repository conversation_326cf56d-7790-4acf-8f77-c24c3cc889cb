<?php
/**
 * 10 0 * * *
 * 流水预估 跑未来365天的数据 每天12点10跑一次
 */

use App\Logic\DMS\MarketLogic;
use App\Logic\DMS\OperationProfitLogic;
use App\Model\SqlModel\Tanwan\DataAnalysis\FlowEstimateModel;
use App\Model\SqlModel\Zeda\FlowEstimateConfigModel;
use App\Model\SqlModel\Zeda\FlowEstimateFitModel;
use App\Model\SqlModel\Zeda\FlowEstimateLTVModel;
use App\MysqlConnection;
use App\Param\DimProfitListParam;
use App\Param\DMS\FlowEstimateParam;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use App\Utils\Math;
use App\Utils\MinPack2D;
use Illuminate\Support\Collection;


require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$data_model = new FlowEstimateModel();
$config_model = new FlowEstimateConfigModel();
$flow_estimate_ltv_model = new FlowEstimateLTVModel();
$min_pack = new MinPack2D();
$logger = Helpers::getLogger('flow-log');
$logger->info('任务开始');

// 复原配置，每天更新一次
try {
    $logic = new MarketLogic();
    $logic->recoveryFlowEstimateConfig();
} catch (\Exception $e) {
    $logger->error($e->getMessage());
}

// 三个统计口径
$all_day_ago_60_list = [];
$all_day_ago_7_list = [];
foreach ([1, 2, 3] as $dimension_type) {
    $param = new FlowEstimateParam();
    $param->game_permission = -1;
    $param->dimension_type = $dimension_type;

    // 67天前到7天前的数据
    $param->day_later_67_start = strtotime('-67 day');
    $param->day_later_67_end = strtotime('-7 day');

    $day_ago_60_data = $data_model->getDaysRangPayData($param);
    $all_day_ago_60_list[$dimension_type] = $day_ago_60_data['list'];

    // 最近7天的数据
    $day_ago_7_data = $data_model->getAvgLastDayData($param);
    $all_day_ago_7_list[$dimension_type] = $day_ago_7_data['list'];
}

// 组合三个统计口径，做索引
$day_ago_7_list = [];
foreach ($all_day_ago_7_list as $dimension_type => $item_list) {
    /**@var Collection $item_list */
    $item_list->map(function ($item) use ($dimension_type) {
        $item->dimension_type = $dimension_type;
        return $item;
    });
    $day_ago_7_list = array_merge($day_ago_7_list, DimensionTool::groupByDimension($item_list, ['platform', 'main_game_id', 'dimension_type']));
}
$day_ago_60_list = [];
foreach ($all_day_ago_60_list as $dimension_type => $item_list) {
    /**@var Collection $item_list */
    $item_list->map(function ($item) use ($dimension_type) {
        $item->dimension_type = $dimension_type;
        return $item;
    });
    $day_ago_60_list = array_merge($day_ago_60_list, DimensionTool::groupByDimension($item_list, ['platform', 'main_game_id', 'dimension_type']));
}


// 去配置拿配置 获取每天的平均注册量
$config_list = $config_model->getAllConfig();
// 按维度做个索引
$config_list = DimensionTool::groupByDimension($config_list, ['platform', 'main_game_id', 'dimension_type']);


// 初始化获取运营利润的参数
$where_day = date("d");
if ($where_day > 3) {
    $last_day = date('Y-m-t'); //这个月最后一天
} else {
    $last_day = date('Y-m-t', strtotime('-1 month')); //上个月最后一天
}
$profit_list_param = [
    'dimension'        => ['platform', 'main_game_id'],
    'start_time'       => date("Y-m-01", strtotime($last_day)),// 月的第一天
    'end_time'         => $last_day,
    'aggregation_type' => '按月',
    'game_permission'  => -1,
    'agent_permission' => -1,
];
$profit_list = (new OperationProfitLogic())->getDimList(new DimProfitListParam($profit_list_param));
$profit_list = $profit_list['list'];
$all_profit_list = [];
// 三个统计口径 运营利润没有统计口径 ，这里复制成三份
foreach ([1, 2, 3] as $dimension_type) {
    $profit_list_1 = array_map(function ($item) use ($dimension_type) {
        $item->dimension_type = $dimension_type;
        return $item;
    }, $profit_list);
    $all_profit_list = array_merge($all_profit_list, DimensionTool::groupByDimension($profit_list_1, ['platform', 'main_game_id', 'dimension_type']));
}

// 数据库拿出来的原始x轴的值，固定的。
$all_x = [1, 3, 7, 15, 30, 60, 90, 120, 180, 240, 300, 360];
$x0 = [1, 3, 7, 15, 30];
$x1 = [30, 60, 90, 120, 180, 240, 300, 360];
$insert_data = [];
$now = date("Y-m-d H:i:s");
foreach ($day_ago_60_list as $key => $item_arr) {
    $reg_uid_count = 0;
    $cost = 0;
    $t_pay_list = [];

    // 先算合计
    foreach ($item_arr as $item) {
        // 计算每个维度下面的reg_uid_count合计和tpay_x合计
        $reg_uid_count += $item->reg_uid_count;
        $cost += $item->cost;
        foreach ($all_x as $day) {
            $tpay_key = 'tpay_' . $day;
            // 计算这60天的合计数据
            $t_pay_list['tpay_' . $day] = $t_pay_list['tpay_' . $day] ?? 0;
            $t_pay_list['tpay_' . $day] += $item->$tpay_key;
        }
    }
    // 要分段拟合。30之前是一段 30之后又是一段
    $y0 = [];
    $y1 = [];
    $logger->info('最近60天平均值的拟合开始');
    foreach ($x0 as $day) {
        $y0[] = floatval($t_pay_list['tpay_' . $day]);
    }
    foreach ($x1 as $day) {
        $y1[] = floatval($t_pay_list['tpay_' . $day]);
    }
//    if ($item_arr[0]->platform == 'TW' && $item_arr[0]->main_game_id == 710) {
//        echo $item_arr[0]->platform, $item_arr[0]->main_game_id,PHP_EOL;
//        var_dump($y0);
//        var_dump($y1);
//    }
    try {
        [$A, $B, $C, $D] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x0, $y0);
        $logger->info('拟合结束，开始计算ltv');
    } catch (Throwable $e) {
        $logger->error('拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }

    try {
        [$A2, $B2, $C2, $D2] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x1, $y1);
        $logger->info('拟合结束，开始计算ltv');
    } catch (Throwable $e) {
        $logger->error('第二段拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }

    // 拿出首日付费 小于0直接过滤
    $first_pay_money = $t_pay_list['tpay_1'];
    if ($first_pay_money <= 0) continue;

    // 拿最近7天的平均首日ltv 如果没有 则拿最近67天的数据计算
    if (isset($day_ago_7_list[$key])) {
        $first_ltv = $day_ago_7_list[$key][0]->first_ltv;
        $per_reg_cost = $day_ago_7_list[$key][0]->avg_reg_uid_value;
//        echo "有数据: ". $per_reg_cost,PHP_EOL;
    } else {
        $first_ltv = bcdiv($first_pay_money, $reg_uid_count, 4);
        $per_reg_cost = bcdiv($cost, $reg_uid_count, 2);
//        echo "无数据: ". $per_reg_cost,PHP_EOL;
    }

    // 计算单个主游戏未来的ltv
    $ltv_360 = 0;
    $ltv_720 = 0;
    $ltv_data = [];
    for ($i = 1; $i <= 1200; $i++) {
        $ltv_key = 'ltv_' . $i;
        if ($i === 1) {
            $ltv_data[$ltv_key] = $first_ltv;
        } else {
            if ($i <= 30) {
                // 算出到$i的总付费
                $pay_money = -(($A - $D) / (1.0 + pow(($i / $C), $B)) + $D);
                if ($i === 2) {
                    $pre_pay_money = $first_pay_money;
                } else {
                    // 算出前一天
                    $pre_pay_money = -(($A - $D) / (1.0 + pow((($i - 1) / $C), $B)) + $D);
                }
//                if ($item_arr[0]->platform == 'TW' && $item_arr[0]->main_game_id == 310 && $i == 2) {
//                    echo '第一次拟合参数：', PHP_EOL;
//                    var_dump([$A, $B, $C, $D]);
//                }
            } else {
                // 算出到$i的总付费
                $pay_money = -(($A2 - $D2) / (1.0 + pow(($i / $C2), $B2)) + $D2);
                // 算出前一天
                $pre_pay_money = -(($A2 - $D2) / (1.0 + pow((($i - 1) / $C2), $B2)) + $D2);
//                if ($item_arr[0]->platform == 'TW' && $item_arr[0]->main_game_id == 310 && $i == 360) {
//                    echo '第二次拟合参数：', PHP_EOL;
//                    var_dump([$A2, $B2, $C2, $D2]);
//
//                    echo '360ltv数据：', PHP_EOL;
//                    echo 'tpay360：', $pay_money, PHP_EOL;
//                    echo 'tpay359：', $pre_pay_money, PHP_EOL;
//                    echo '首日ltv：', $first_ltv, PHP_EOL;
//                    echo '首日总付费：', $first_pay_money, PHP_EOL;
//                    echo '360ltv：', bcmul($first_ltv, ($pay_money - $pre_pay_money) / $first_pay_money, 4), PHP_EOL;
//
//                }
            }

            // 算出那天的当日ltv
            $ltv_data[$ltv_key] = bcmul($first_ltv, ($pay_money - $pre_pay_money) / $first_pay_money, 4);
        }

        if ($i <= 360) {
            $ltv_360 += $ltv_data[$ltv_key];
        }
        if ($i <= 720) {
            $ltv_720 += $ltv_data[$ltv_key];
        }
    }


    // 维度赋值,初始化
    $insert_data[$key] = [
        'platform'           => $item_arr[0]->platform,
        'plat_id'            => $item_arr[0]->plat_id,
        'root_game_id'       => $item_arr[0]->root_game_id,
        'root_game_name'     => $item_arr[0]->root_game_name,
        'main_game_id'       => $item_arr[0]->main_game_id,
        'main_game_name'     => $item_arr[0]->main_game_name,
        'dimension_type'     => $item_arr[0]->dimension_type,
        'contract_game_name' => $item_arr[0]->contract_game_name,
        'clique_id'          => $item_arr[0]->clique_id,
        'clique_name'        => $item_arr[0]->clique_name,
        'create_time'        => $now,
        'standard'           => 0,
        'per_reg_cost'       => $per_reg_cost,
    ];
    $insert_data[$key]['ltv_list'] = json_encode($ltv_data);
    $insert_data[$key]['avg_cost'] = $config_list[$key][0]->avg_cost ?? 0;
    $insert_data[$key]['avg_reg_uid_count'] = $config_list[$key][0]->avg_reg_uid_count ?? 0;
    $insert_data[$key]['avg_reg_uid_value'] = $config_list[$key][0]->avg_reg_uid_value ?? 0;

//    if ($item_arr[0]->platform == 'TW' && $item_arr[0]->main_game_id == 710) {
//        echo '找到了';
//        var_dump($insert_data[$key]['avg_cost'], $config_list[$key][0]->avg_cost);
//    }

    /*------------------------- 回本达标参数的计算 ------------------------*/
    if (isset($all_profit_list[$key])) {
        // 注册成本0的也不需要算这个
        if ($insert_data[$key]['avg_reg_uid_value'] == 0) continue;

        // 单独计算一个360天的总ltv  官老爷要特殊处理，要算720
        if (($insert_data[$key]['platform'] == 'TW' && $insert_data[$key]['root_game_id'] == 2)) {
            $ltv_360 = $ltv_720;
        }


        // 获取ios分成，应用宝分成，研发分成
        $ios_divide = Math::div($all_profit_list[$key][0]->ios_divide, $all_profit_list[$key][0]->game_pay_money, 4);
        $yyb_divide = Math::div($all_profit_list[$key][0]->yyb_divide, $all_profit_list[$key][0]->game_pay_money, 4);
        $ys_divide = Math::div($all_profit_list[$key][0]->profit, $all_profit_list[$key][0]->game_pay_money, 4);

        $x = Math::div(1 + $config_list[$key][0]->profit_rate, 1 - $ys_divide - $ios_divide - $yyb_divide, 4);
        $y = Math::div($ltv_360, $insert_data[$key]['avg_reg_uid_value'], 4);
        // 这种情况需要存一个回本达标系数
        $standard = Math::div($x, $y, 4);
        $insert_data[$key]['standard'] = $standard;

//            // TODO  DEBUG
//        if ($item_arr[0]->platform == 'TW' && $item_arr[0]->main_game_id == 310) {
//            echo $insert_data[$key]['platform'], PHP_EOL;
//            echo $insert_data[$key]['main_game_id'], PHP_EOL;
//            echo 'ltv:' . $ltv_360, PHP_EOL;
//            echo 'IOS分成比例：' . $ios_divide, PHP_EOL;
//            echo '应用宝分成比例：' . $yyb_divide, PHP_EOL;
//            echo '研发分成比例：' . $ys_divide, PHP_EOL;
//            echo '研发分成金额：' . $all_profit_list[$key][0]->operation_profit, PHP_EOL;
//            echo '总流水：' . $all_profit_list[$key][0]->game_pay_money, PHP_EOL;
//            echo 'x：' . $x, PHP_EOL;
//            echo 'y：' . $y, PHP_EOL;
//            echo 'standard：' . $standard, PHP_EOL;
//        }
    }


    $logger->info('单游戏ltv计算结束');
}
try {
    MysqlConnection::getConnection('default')->beginTransaction();
} catch (Exception $e) {
    $logger->error('事务开启异常，结束任务。时间区间： ');
    exit;
}


try {
    // 入库前先清空
    $flow_estimate_ltv_model->deleteAll();
    // 入库
    $flow_estimate_ltv_model->addMultiple($insert_data);
    MysqlConnection::getConnection('default')->commit();
    $logger->info('入库成功，LTV任务结束');
} catch (\Exception $e) {
    try {
        $logger->error('入库异常，回滚数据, 异常信息：' . substr($e->getMessage(), 0, 1000));
        MysqlConnection::getConnection('default')->rollBack();
    } catch (\Exception $e) {
        $logger->error('事务回滚异常！');
    }
    exit;
}


/*-----------------  计算所有游戏的最近60天的平均值，用来拟合，最近60天没有数据的 用历史的最后一个月的数据 ------------*/
// 先拿67天前的数据，按月聚合的
$logger->info('开始计算60天的平均值拟合数据');


// 三个统计口径
$all_day_ago_67_list = [];
foreach ([1, 2, 3] as $dimension_type) {
    $param = new FlowEstimateParam();
    $param->game_permission = -1;
    $param->dimension_type = $dimension_type;
    $param->day_ago_67_start = strtotime('2012-01-01');
    $param->day_ago_67_end = strtotime(date('Y-m-d', strtotime('-68 day'))); // 67天前的前一天

    $day_ago_67_data = $data_model->getDaysAgoPayData($param);
    $all_day_ago_67_list[$dimension_type] = $day_ago_67_data['list'];
}
$day_ago_67_list = [];
foreach ($all_day_ago_67_list as $dimension_type => $item_list) {
    /**@var Collection $item_list */
    $item_list->map(function ($item) use ($dimension_type) {
        $item->dimension_type = $dimension_type;
        return $item;
    });
    $day_ago_67_list = array_merge($day_ago_67_list, DimensionTool::groupByDimension($item_list, ['platform', 'main_game_id', 'dimension_type']));
}


$res_list = [];
foreach ($day_ago_67_list as $key => $item_arr) {
    // 维度赋值,初始化
    $res_list[$key] = [
        'platform'           => $item_arr[0]->platform,
        'plat_id'            => $item_arr[0]->plat_id,
        'root_game_id'       => $item_arr[0]->root_game_id,
        'root_game_name'     => $item_arr[0]->root_game_name,
        'main_game_id'       => $item_arr[0]->main_game_id,
        'main_game_name'     => $item_arr[0]->main_game_name,
        'dimension_type'     => $item_arr[0]->dimension_type,
        'contract_game_name' => $item_arr[0]->contract_game_name,
        'clique_id'          => $item_arr[0]->clique_id,
        'clique_name'        => $item_arr[0]->clique_name,
        'create_time'        => $now,
    ];
    // 拟合最后一个月
    $item = $item_arr[count($item_arr) - 1];
    $logger->info('拟合开始', [$item]);
    // 要分段拟合。30之前是一段 30之后又是一段
    $y0 = [];
    $y1 = [];
    $logger->info('最近60天平均值的拟合开始');
    foreach ($x0 as $day) {
        $tpay_key = 'tpay_' . $day;
        $y0[] = floatval($item->$tpay_key);
    }
    foreach ($x1 as $day) {
        $tpay_key = 'tpay_' . $day;
        $y1[] = floatval($item->$tpay_key);
    }

    try {
        [$A, $B, $C, $D] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x0, $y0);
        $logger->info('拟合结束');
    } catch (Throwable $e) {
        $logger->error('拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }

    try {
        [$A2, $B2, $C2, $D2] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x1, $y1);
        $logger->info('拟合结束');
    } catch (Throwable $e) {
        $logger->error('第二段拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }

    // 单个主游戏维度的总注册数和总消耗
    $res_list[$key]['reg_uid_count'] = $item->reg_uid_count;
    $res_list[$key]['cost'] = $item->cost;
    $res_list[$key]['A'] = $A;
    $res_list[$key]['B'] = $B;
    $res_list[$key]['C'] = $C;
    $res_list[$key]['D'] = $D;
    $res_list[$key]['A2'] = $A2;
    $res_list[$key]['B2'] = $B2;
    $res_list[$key]['C2'] = $C2;
    $res_list[$key]['D2'] = $D2;
}


/*-----------------------------  计算67 ~ 7天前的数据  -----------------------------*/
$fit_model = new FlowEstimateFitModel();
foreach ($day_ago_60_list as $key => $item_arr) {
    // 判断是否存在 不存在则初始化
    if (!isset($res_list[$key])) {
        // 维度赋值,初始化
        $res_list[$key] = [
            'platform'           => $item_arr[0]->platform,
            'plat_id'            => $item_arr[0]->plat_id,
            'root_game_id'       => $item_arr[0]->root_game_id,
            'root_game_name'     => $item_arr[0]->root_game_name,
            'main_game_id'       => $item_arr[0]->main_game_id,
            'main_game_name'     => $item_arr[0]->main_game_name,
            'dimension_type'     => $item_arr[0]->dimension_type,
            'contract_game_name' => $item_arr[0]->contract_game_name,
            'clique_id'          => $item_arr[0]->clique_id,
            'clique_name'        => $item_arr[0]->clique_name,
            'create_time'        => $now,
        ];
    }
    $reg_uid_count = 0; // 注册数
    $cost = 0; // 消耗

    $y_list = [];
    foreach ($all_x as $day) {
        $y_list['tpay_' . $day] = 0;
    }

    foreach ($item_arr as $item) {
        $logger->info('60的拟合开始');
        // 拿出每行对应的y0，计算每个维度下面的reg_uid_count合计和tpay_x合计 后面要用
        $reg_uid_count += $item->reg_uid_count;
        $cost += $item->cost;
        foreach ($all_x as $day) {
            $tpay_key = 'tpay_' . $day;
            // 计算这60天的合计数据
            $y_list['tpay_' . $day] += $item->$tpay_key;
        }

    }

    // 首日付费小于等于0的直接过滤，同上对齐数据
    if ($y_list['tpay_1'] <= 0) {
        unset($res_list[$key]);
        continue;
    }


    // 要分段拟合。30之前是一段 30之后又是一段
    $y0 = [];
    $y1 = [];
    $logger->info('最近60天平均值的拟合开始');
    foreach ($x0 as $day) {
        $y0[] = floatval($y_list['tpay_' . $day]);
    }
    foreach ($x1 as $day) {
        $y1[] = floatval($y_list['tpay_' . $day]);
    }

    try {
        [$A, $B, $C, $D] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x0, $y0);
        $logger->info('拟合结束');
    } catch (Throwable $e) {
        $logger->error('拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }
    try {
        [$A2, $B2, $C2, $D2] = $min_pack->curveFitLM(function ($x, $A, $B, $C, $D) {
            $ret = [];
            foreach ($x as $item) {
                $ret[] = -(($A - $D) / (1.0 + pow(($item / $C), $B)) + $D);
            }
            return $ret;
        }, $x1, $y1);
        $logger->info('拟合结束');
    } catch (Throwable $e) {
        $logger->error('第二段拟合出现异常,任务结束', ['message' => $e->getMessage(), 'data' => $item_arr]);
        exit;
    }
    $logger->info('最近60天平均值的拟合结束');


    // 预估需要的数据
    $res_list[$key]['A'] = $A;
    $res_list[$key]['B'] = $B;
    $res_list[$key]['C'] = $C;
    $res_list[$key]['D'] = $D;
    $res_list[$key]['A2'] = $A2;
    $res_list[$key]['B2'] = $B2;
    $res_list[$key]['C2'] = $C2;
    $res_list[$key]['D2'] = $D2;
    $res_list[$key]['reg_uid_count'] = $reg_uid_count;
    $res_list[$key]['cost'] = $cost;
}

// 入表
$fit_model = new FlowEstimateFitModel();
try {
    // 入库前先清空
    $fit_model->deleteAll();

    // 入库
    $fit_model->addMultiple($res_list);
    MysqlConnection::getConnection('default')->commit();
    $logger->info('入库成功，任务结束');
    exit;
} catch (\Exception $e) {
    try {
        $logger->error('入库异常，回滚数据, 异常信息：' . substr($e->getMessage(), 0, 1000));
        MysqlConnection::getConnection('default')->rollBack();
    } catch (\Exception $e) {
        $logger->error('事务回滚异常！');
    }
    exit;
}
