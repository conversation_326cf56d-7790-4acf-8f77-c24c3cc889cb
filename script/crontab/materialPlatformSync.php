<?php
/**
 * 每天23点检查一次有无新的所需的素材同步
 */

use App\Model\HttpModel\Material\SendMaterialSyncModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialPlatformSyncModel;
use App\MysqlConnection;
use App\Param\MaterialPlatformSyncParam;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$sync_task_list = MysqlConnection::getConnection()
    ->table('material_platform_sync')
    ->where(['sync_type' => 'sync'])
    ->where(['state_code' => 0])
    ->get();

foreach ($sync_task_list as $key => $sync_task) {
    $material_list = MysqlConnection::getConnection()
        ->table('material')
        ->where(['platform' => $sync_task->origin_platform])
        ->where(['is_del' => 0])
        ->where('create_time', "<=", strtotime(date('Y-m-d 23:00:00')))
        ->where('create_time', ">=", strtotime(date('Y-m-d 00:00:00')))
        ->get();

    if ($material_list->isEmpty()) {
        continue;
    }

    $origin_material_id_list = $material_list->pluck('material_id')->toArray();

    $material_info_map = $material_list->keyBy('material_id')->toArray();

    $result_list = [];

    foreach ($origin_material_id_list as $origin_material_id) {
        $result_info = [];
        $sql_param = new MaterialPlatformSyncParam([
            'origin_platform' => $sync_task->origin_platform,
            'origin_material_id' => $origin_material_id,
            'origin_theme_id' => $sync_task->origin_theme_id,
            'target_platform' => $sync_task->target_platform,
            'target_material_id' => 0,
            'target_theme_id' => 0,
            'target_author_type' => $sync_task->target_author_type,
            'target_author' => $sync_task->target_author,
            'sync_type' => 'once',
            'creator' => "$sync_task->origin_platform-常驻任务-$sync_task->id",
            'state_code' => 0,
            'error' => '',
        ]);
        $result_info['param'] = $sql_param;
        $result_info['id'] = (new MaterialPlatformSyncModel())->addMaterialPlatformSync($sql_param);

        $result_list[] = $result_info;
    }

    $material_file_map = (new MaterialFileModel())->getListByMaterialIds($origin_material_id_list, $sync_task->origin_platform)->groupBy('material_id')->toArray();

    foreach ($result_list as $result) {
        /** @var MaterialPlatformSyncParam $result_param */
        $result_param = $result['param'];
        try {
            $http_result = (new SendMaterialSyncModel())->pushMaterialPlatformSyncLog([
                'origin_platform' => $result_param->origin_platform,
                'origin_material_id' => $result_param->origin_material_id,
                'origin_theme_id' => $result_param->origin_theme_id,
                'origin_material_name' => $material_info_map[$result_param->origin_material_id]->name,
                'origin_material_file_type' => $material_info_map[$result_param->origin_material_id]->file_type,
                'origin_material_file_list' => $material_file_map[$result_param->origin_material_id],
                'target_platform' => $result_param->target_platform,
                'target_author' => $result_param->target_author,
                'creator' => "$result_param->origin_platform-常驻任务-$sync_task->id"
            ]);
            $http_result = json_decode($http_result, true);
            if ($http_result['code'] == 0) {
                (new MaterialPlatformSyncModel())->updateStateCode($result['id'], 1, ['result_log_id' => $http_result['data']['id']]);
            } else {
                (new MaterialPlatformSyncModel())->updateStateCode($result['id'], 2, ['error' => $http_result['message']]);
            }
        } catch (Throwable $e) {
            (new MaterialPlatformSyncModel())->updateStateCode($result['id'], 2, ['error' => $e->getMessage()]);
        }
    }
}
