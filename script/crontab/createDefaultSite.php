<?php
/**
 * 每3分钟一次
 * 建立默认广告位,建一个休眠一秒
 */

use App\Constant\ConvertToolkit;
use App\Constant\ConvertType;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\SiteConfigParam;
use App\Service\SiteService;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datamedia = 'data_media';

$must_create_ad_platforms = ['ZW'];


$media_agent_builder = MysqlConnection::getConnection($connection_name)
    ->table('media_account')
    ->select('media_account.account_id', 'media_account.media_type', 'media_account.platform')
    ->selectRaw('min(agent.agent_id) as agent_id')
    ->join('agent', function (JoinClause $join) {
        $join->on('agent.account_id', '=', 'media_account.account_id');
        $join->on('agent.platform', '=', 'media_account.platform');
        $join->on('agent.media_type', '=', 'media_account.media_type');
    })
    ->where('media_account.agent_leader', '!=', '')
    ->where('media_account.state', '=', 1)
    ->where('default_site_id', '=', 0)
    ->groupBy('platform', 'media_type', 'account_id');

$agent_builder = MysqlConnection::getConnection($connection_name)
    ->table('agent')
    ->select('agent.*')
    ->joinSub($media_agent_builder, 'media_agent', function (JoinClause $join) {
        $join->on('agent.agent_id', '=', 'media_agent.agent_id');
        $join->on('agent.platform', '=', 'media_agent.platform');
    });


$agent_list = $agent_builder->get();
if ($agent_list->isEmpty()) {
    return;
}

$ad2_account_list = MysqlConnection::getConnection($connection_datamedia)
    ->table('dwd_media_ad2_common_log')
    ->whereInMultiple(
        ["media_type", "account_id"],
        $agent_list
            ->whereIn('platform', $must_create_ad_platforms)
            ->map(function ($item) {
                return ['media_type' => $item->media_type, 'account_id' => $item->account_id];
            })
    )
    ->groupBy(['media_type', 'account_id'])
    ->get(['media_type', 'account_id']);

$media_accounts = [];
foreach ($agent_list as $key => $agent) {
    if (!in_array($agent->platform, $must_create_ad_platforms)) {
        $media_accounts[] = "($agent->media_type,$agent->account_id)";
    } else {
        $exists_ad = $ad2_account_list
            ->where('media_type', $agent->media_type)
            ->where('account_id', $agent->account_id)
            ->isNotEmpty();
        if ($exists_ad) {
            $media_accounts[] = "($agent->media_type,$agent->account_id)";
        } else {
            // 删除不存在的item
            $agent_list->forget($key);
        }
    }
}

$media_account_str = implode(',', $media_accounts);

MysqlConnection::getConnection($connection_name)
    ->table('media_account')
    ->whereRaw("(media_type, account_id) in ($media_account_str)")
    ->update([
        'default_site_id' => 1
    ]);

$site_service = new SiteService();
$media_account_model = new MediaAccountModel();
foreach ($agent_list as $agent) {
    if (!isset(PlatformAbility::DEFAULT_COST_GAME[$agent->platform])) {
        continue;
    }
    $default_site_data = [
        'platform' => $agent->platform,
        'agent_id' => $agent->agent_id,
        'media_type' => $agent->media_type,
        'agent_group' => $agent->agent_group,
        'account_id' => $agent->account_id,
        'user_name' => $agent->user_name,
        'is_concat' => 0,
        'site_suffix_name' => "{$agent->account_id}默认广告位",
        'game_id' => PlatformAbility::DEFAULT_COST_GAME[$agent->platform],
        'plat_id' => PlatId::SY,
        'audit_type' => 0,
        'game_name' => '消耗专用',
        'app_name' => '消耗专用',
        'game_type' => 'IOS',
        'appid' => '消耗专用',
        'package' => '消耗专用',
        'game_pack' => 0,
        'convert_type' => '',
        'convert_source_type' => 'AD_CONVERT_SOURCE_TYPE_APP_DOWNLOAD',
        'convert_toolkit' => $agent->media_type == MediaType::TOUTIAO ? ConvertToolkit::TOUTIAO_CONVERT : '',
        'akey' => '1',
        'ad_turn' => 0,
        'ad_pop_zk' => 0,
        'ad_price' => 0,
        'auto_download' => 0,
        'auto_download_second' => 0,
        'cps_divide_rate' => 0,
        'forbid_tuitan' => 0,
        'template_type' => 'nojump.html',
        'template_address' => '',
        'pay_type' => $agent->pay_type,
        'upt_state' => 0,
        'state' => 1,
    ];

    if (isset(ConvertType::MAP[$agent->media_type])) {
        $default_site_data['convert_type'] = key(ConvertType::MAP[$agent->media_type]);
    }

    if ($agent->platform === 'ZW') {
        $default_site_data['template_type'] = 25;
    }

    try {
        $site_result = $site_service->addSite(new SiteConfigParam($default_site_data), 1, '超管', true);
        echo "{$agent->media_type}-{$agent->account_id}创建{$agent->platform}-{$site_result->site_id}成功", PHP_EOL;
        $media_account_model->updateDefaultSite($agent->media_type, $agent->account_id, $agent->agent_id, $site_result->site_id);

    } catch (Throwable $e) {
        echo "{$agent->media_type}-{$agent->account_id}:", $e->getMessage(), PHP_EOL;
        $media_account_model->updateDefaultSite($agent->media_type, $agent->account_id, 0, 0);
        continue;
    }
    sleep(1);
}


function getSql(Builder $builder)
{
    $bindings = $builder->getBindings();
    return preg_replace_callback('/\?/', function () use (&$bindings) {
        $binding = array_shift($bindings);
        if (is_numeric($binding)) {
            return $binding;
        } else if (is_string($binding)) {
            return empty($binding) ? "''" : "'" . $binding . "'";
        } else {
            return $binding;
        }
    }, $builder->toSql());
}
