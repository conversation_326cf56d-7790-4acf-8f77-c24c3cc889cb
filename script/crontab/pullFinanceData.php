<?php
/**
 * 0 2 * * *
 * 每天两点执行
 * 去ADB拉取最近一年的财务台账数据
 * User: Melody
 * Date: 2020/12/23
 * Time: 11:07
 */

use App\Logic\DMS\FinanceAccountLogic;
use App\MysqlConnection;


require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

// 最近365天
$start_date = date("Y-m-d", strtotime('-366 day'));
$end_date = date("Y-m-d", strtotime('-1 day'));

$logic = new FinanceAccountLogic();

try {
    $logic->updateFinanceData($start_date, $end_date);
} catch (\Exception $exception) {
    var_dump($exception->getMessage());
}
