<?php
/**
 * 每3分钟同步一遍用户权限
 */

use App\MysqlConnection;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$connection_name = 'default';
$connection_datahub_web = 'datahub_web';

$list = MysqlConnection::getConnection($connection_name)
    ->table('user_level as ul')
    ->select(
        'ul.module',
        'user.name as username',
        'ul.platform_id',
        'ul.department_id',
        'ul.department_group_id',
        'ul.department_group_position_id',
        'ul.department_group_position_worker_id'
    )->selectRaw('
        IFNULL(platform.name, "") as platform_name,
        IFNULL(department.name, "") as department_name,
        IFNULL(department_group.name, "") as department_group_name,
        IFNULL(department_group_position.name, "") as department_group_position_name,
        IFNULL(department_group_position_worker.name, "") as department_group_position_worker_name'
    )
    ->leftJoin('platform', 'ul.platform_id', '=', 'platform.id')
    ->leftJoin('department', 'ul.department_id', '=', 'department.id')
    ->leftJoin('department_group', 'ul.department_group_id', '=', 'department_group.id')
    ->leftJoin('department_group_position', 'ul.department_group_position_id', '=', 'department_group_position.id')
    ->leftJoin('department_group_position_worker', 'ul.department_group_position_worker_id', '=', 'department_group_position_worker.id')
    ->leftJoin('user', 'user.id', '=', 'ul.user_id')
    ->get();

$list->chunk(2000)->map(function (Collection $chunk) use ($connection_datahub_web) {
    $data = $chunk->map(function ($item) {
        return (array)$item;
    })->toArray();
    MysqlConnection::getConnection($connection_datahub_web)->table('dim_user_position')->replace($data);
});
