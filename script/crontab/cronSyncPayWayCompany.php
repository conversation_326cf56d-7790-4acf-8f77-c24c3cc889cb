<?php
/**
 * 0 1 * * *
 * 每天定时同步dwd_lie_pay_way_company的数据
 */

use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
// 设置MySQL连接对象
MysqlConnection::setConnection();
$logger = \App\Utils\Helpers::getLogger('syncPayWayCompany');
$logger->info('开始同步');

$list = (new \App\Model\SqlModel\DataMedia\DWDLivePayWayCompanyModel())->getAll();
$logger->info('数据加载完成');


$model = new \App\Model\SqlModel\Zeda\DWDLivePayWayCompanyModel();
$chunk_list = $list->chunk(1000);


// 同步插入
foreach ($chunk_list as $chunk) {
    try {
        $model->replaceInsert($chunk);
        $logger->info("单次数据同步完成");
    } catch (\Throwable $exception) {
        $logger->error($exception->getMessage());
    }

}

$logger->info("同步完成");