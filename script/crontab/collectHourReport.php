<?php

/**
 * 30 04 * * * php collectDailyReport.php
 */

use App\Model\SqlModel\Tanwan\V2DimAgentLeaderGroupModel;
use App\Model\SqlModel\Tanwan\V2DWDADHourCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayRootGameUidLoginLogModel;
use App\Model\SqlModel\Tanwan\V2DWSDayRootGamePayLogTestModel;
use App\Model\SqlModel\Tanwan\V2DWSHourRootGamePayLogModel;
use App\Model\SqlModel\Tanwan\V2DWSHourRootGameRegLogModel;
use App\Model\SqlModel\Zeda\WeChatHourReportModel;
use App\MysqlConnection;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();

$day_time = 24 * 60 * 60;
$start_week = 1;
$start_day = 1;
$time_benchmark = time();


function collect_hour_report($time_benchmark)
{
    global $start_week, $start_day;

    $hour = intval(date('H', $time_benchmark));
    if ($hour === 0) {
        $time_benchmark -= 3600;
        $hour = 24;
    }

    $start_date = date("Y-m-d", $time_benchmark);
    $end_date = date("Y-m-d", $time_benchmark);
    $start_avg_date = date("Y-m-d", strtotime("-15 day", $time_benchmark));
    $end_avg_date = date("Y-m-d", strtotime("-7 day", $time_benchmark));
    $week_no = intval(date('w', $time_benchmark));
    $day_no = intval(date('d', $time_benchmark));

    $hour_cost_model = new V2DWDDayCostLogModel();
    $game_reg_model = new V2DWSHourRootGameRegLogModel();
    $day_pay_model = new V2DWSHourRootGamePayLogModel();
    $day_pay_test_model = new V2DWSDayRootGamePayLogTestModel();
    $day_login_model = new V2DWDDayRootGameUidLoginLogModel();
    $agent_model = new V2DimAgentLeaderGroupModel();
    $wechat_hour_model = new WeChatHourReportModel();

    $dim_list = $agent_model->getAllLeaderAndGame();
    $cost_list = $hour_cost_model->getHourCost($start_date, $end_date, $hour);
    $reg_list = $game_reg_model->getHourRegCount($start_date, $end_date, $hour);
    $income_list = $day_pay_model->getHourIncome($start_date, $end_date, $hour);
    $avg_7day_cost_list = $hour_cost_model->getHourCost($start_avg_date, $end_avg_date, $hour);
    $avg_7day_money_list = $day_pay_test_model->getHourAvg7DayMoney($start_avg_date, $end_avg_date);
    $DAU_list = $day_login_model->getHourDAUCount($start_date, $end_date, $hour);

    if ($week_no === $start_week) {
        $week_start_date = date('Y-m-d', strtotime('-' . (6 + date('w', $time_benchmark)) . ' days', $time_benchmark));
        $week_end_date = date('Y-m-d', strtotime('-' . date('w', $time_benchmark) . ' days', $time_benchmark));
        $week_reg_list = $game_reg_model->getHourRegCount($week_start_date, $week_end_date, $hour);
        $week_DAU_list = $day_login_model->getHourDAUCount($week_start_date, $week_end_date, $hour);
    } else {
        $week_reg_list = Collection::make([]);
        $week_DAU_list = Collection::make([]);
    }

    if ($day_no === $start_day) {
        $month_start_date = date('Y-m-01', strtotime("last day of -1 month", $time_benchmark));
        $month_end_date = date('Y-m-t', strtotime("last day of -1 month", $time_benchmark));
        $month_reg_list = $game_reg_model->getHourRegCount($month_start_date, $month_end_date, $hour);
        $month_DAU_list = $day_login_model->getHourDAUCount($month_start_date, $month_end_date, $hour);
    } else {
        $month_reg_list = Collection::make([]);
        $month_DAU_list = Collection::make([]);
    }

    if ($hour === 24) {
        $hour = 23;
        $now = strtotime($start_date . " $hour:00:00");
    } else {
        $now = strtotime($start_date . " $hour:00:00");
    }
    $insert_data = [];

    foreach ($dim_list as $dim_info) {
        $cost_info = $cost_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $reg_info = $reg_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $income_info = $income_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $avg_7day_money_info = $avg_7day_money_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $DAU_info = $DAU_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $week_reg_info = $week_reg_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $week_DAU_info = $week_DAU_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $month_reg_info = $month_reg_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $month_DAU_info = $month_DAU_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        $avg_7day_cost_info = $avg_7day_cost_list
            ->where('platform', $dim_info->platform)
            ->where('plat_id', $dim_info->plat_id)
            ->where('root_game_id', $dim_info->root_game_id)
            ->where('main_game_id', $dim_info->main_game_id)
            ->where('agent_leader_group_id', $dim_info->agent_leader_group_id)
            ->first();

        if (
            empty($cost_info)
            && empty($reg_info)
            && empty($income_info)
            && empty($DAU_info)
        ) {
            continue;
        }

        $cost = empty($cost_info->money) ? 0 : $cost_info->money;
        $android_reg_uid_count = empty($reg_info->android_reg_uid_count) ? 0 : $reg_info->android_reg_uid_count;
        $ios_reg_uid_count = empty($reg_info->ios_reg_uid_count) ? 0 : $reg_info->ios_reg_uid_count;
        $reg_uid_count = empty($reg_info->reg_uid_count) ? 0 : $reg_info->reg_uid_count;
        $first_day_pay_money = empty($reg_info->first_day_pay_money) ? 0 : $reg_info->first_day_pay_money;
        $second_login_count = empty($reg_info->second_login_count) ? 0 : $reg_info->second_login_count;
        $total_7day_pay_money = empty($avg_7day_money_info->pay_money) ? 0 : $avg_7day_money_info->pay_money;
        $total_7day_cost = empty($avg_7day_cost_info->money) ? 0 : $avg_7day_cost_info->money;
        $create_role_count = empty($reg_info->create_role_count) ? 0 : $reg_info->create_role_count;
        $first_day_pay_count = empty($reg_info->first_day_pay_count) ? 0 : $reg_info->first_day_pay_count;
        $ios_first_day_pay_money = empty($reg_info->ios_first_day_pay_money) ? 0 : $reg_info->ios_first_day_pay_money;
        $android_first_day_pay_money = empty($reg_info->android_first_day_pay_money) ? 0 : $reg_info->android_first_day_pay_money;
        $ios_cost_money = empty($cost_info->ios_cost_money) ? 0 : $cost_info->ios_cost_money;
        $android_cost_money = empty($cost_info->android_cost_money) ? 0 : $cost_info->android_cost_money;

        $income = empty($income_info->pay_money) ? 0 : $income_info->pay_money;
        $DAU = empty($DAU_info->login_uid_count) ? 0 : $DAU_info->login_uid_count;
        $WAU = empty($week_DAU_info->login_uid_count) ? 0 : $week_DAU_info->login_uid_count;
        $MAU = empty($month_DAU_info->login_uid_count) ? 0 : $month_DAU_info->login_uid_count;
        $week_reg_uid_count = empty($week_reg_info->reg_uid_count) ? 0 : $week_reg_info->reg_uid_count;
        $month_reg_uid_count = empty($month_reg_info->reg_uid_count) ? 0 : $month_reg_info->reg_uid_count;
        $day_old_user_count = abs($DAU - $reg_uid_count);
        $week_old_user_count = abs($WAU - $week_reg_uid_count);
        $month_old_user_count = abs($MAU - $month_reg_uid_count);
        $plat_id = ($dim_info->platform == 'TW') ? (($dim_info->plat_id == 1) ? 1 : 2) : 2;

        $insert_data[] = [
            'platform' => $dim_info->platform,
            'plat_id' => $plat_id,
            'root_game_id' => $dim_info->root_game_id,
            'main_game_id' => $dim_info->main_game_id,
            'cost' => $cost,
            'android_reg_uid_count' => $android_reg_uid_count,
            'ios_reg_uid_count' => $ios_reg_uid_count,
            'reg_uid_count' => $reg_uid_count,
            'first_day_pay_money' => $first_day_pay_money,
            'second_login_count' => $second_login_count,
            'income' => $income,
            'day7_pay_money' => $total_7day_pay_money,
            'day7_cost' => $total_7day_cost,
            'day_old_user_count' => $day_old_user_count,
            'week_old_user_count' => $week_old_user_count,
            'month_old_user_count' => $month_old_user_count,
            'DAU' => $DAU,
            'WAU' => $WAU,
            'MAU' => $MAU,
            'create_time' => $now,
            'create_role_count' => $create_role_count,
            'first_day_pay_count' => $first_day_pay_count,
            'agent_leader_group_id' => $dim_info->agent_leader_group_id,
            'agent_leader_group_name' => $dim_info->agent_leader_group_name,
            'ios_first_day_pay_money' => $ios_first_day_pay_money,
            'android_first_day_pay_money' => $android_first_day_pay_money,
            'ios_cost_money' => $ios_cost_money,
            'android_cost_money' => $android_cost_money
        ];
    }

    if ($insert_data) {
        $start_delete_time = $now;
        $end_delete_time = $now;
        $wechat_hour_model->deleteDayRecord($start_delete_time, $end_delete_time);
        $wechat_hour_model->addRecord($insert_data);
    }
}

// $last_time_benchmark = strtotime('-1 day', $time_benchmark);
// collect_hour_report($last_time_benchmark);
collect_hour_report($time_benchmark);
