<?php
/**
 * 每3分钟一次
 * 建立默认广告位,建一个休眠一秒
 */

use App\Constant\ActionTrackType;
use App\Constant\PlatformAbility;
use App\Model\RedisModel\AutoIncrementIdModel;
use App\Model\SqlModel\DataMedia\DimSiteGameIdModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\AgentParam;
use App\Service\AgentService;
use App\Service\OuterService;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$connection_datahub = 'datahub';

$agent_group_list = MysqlConnection::getConnection($connection_name)
    ->table('agent_group')
    ->get();

$site_list = MysqlConnection::getConnection($connection_datahub)
    ->table('v2_dim_site_id_map')
    ->leftJoin('v2_dim_site_id', function (JoinClause $join) {
        $join->on("v2_dim_site_id.platform", '=', "v2_dim_site_id_map.platform");
        $join->on("v2_dim_site_id.site_id", '=', "v2_dim_site_id_map.site_id");
    })
    ->select('v2_dim_site_id_map.*')
    ->whereIn('v2_dim_site_id_map.platform', PlatformAbility::MIGRATE_SITE_MAP)
    ->whereNull('v2_dim_site_id.site_id')
    ->orderBy('v2_dim_site_id_map.create_time')
    ->get();

$agent_model = new AgentModel();
$media_account_model = new MediaAccountModel();
$auto_increment_model = new AutoIncrementIdModel();
$agent_service = new AgentService();
$outer_service = new OuterService();

$account_site_list = $site_list->groupBy(function ($item) {
    return "$item->platform-$item->account_id";
});
/**
 * @var string $platform_account_id
 * @var Collection $site_list
 */
foreach ($account_site_list as $platform_account_id => $site_list) {
    [$platform, $account_id] = explode('-', $platform_account_id);
    $agent_info = $agent_model->getDataByPlatformAccountId($platform, $account_id);
    if (empty($agent_info)) {
        $first_site_info = $site_list->first();
        $agent_group_info = $agent_group_list->where('id', $first_site_info->agent_group_id)->first();

        $default_agent_data = [
            'platform' => $platform,
            'media_type' => $agent_group_info->media_type,
            'account_id' => $account_id,
            'agent_name' => $first_site_info->account_name,
            'agent_group' => $first_site_info->agent_group_id,
            'agent_group_id' => $first_site_info->agent_group_id,
            'agent_group_name' => $agent_group_info->name,
            'agent_leader' => $first_site_info->agent_leader,
            'company' => '',
            'own' => 1,
            'user_name' => $first_site_info->account_name,
            'user_pwd' => '123456',
            'create_time' => 1,
            'for_ad' => 1,
        ];

        try {
            $agent_info = $agent_service->addAgent(new AgentParam($default_agent_data), 1, '超管');
        } catch (Throwable $e) {
            var_dump($default_agent_data);
            echo $e->getMessage(), PHP_EOL;
            continue;
        }

        $media_account_model->updateByAccountId($agent_group_info->media_type, $account_id, ['agent_leader' => $first_site_info->agent_leader]);
        echo "创建{$agent_info->platform}渠道{$agent_info->agent_id}", PHP_EOL;
    }
    $param = new AgentParam((array)$agent_info);
    $site_data = [];
    foreach ($site_list as $site) {
        $site_create_timestamp = strtotime($site->create_time);
        if ($site->agent_leader !== $param->agent_leader && $site_create_timestamp > $param->update_time) {
            $param->agent_leader = $site->agent_leader;
            $param->update_time = $site_create_timestamp;
            $agent_service->editAgent($param, $agent_info, 1, '超管');
            $media_account_model->updateByAccountId($param->media_type, $account_id, ['agent_leader' => $param->agent_leader]);
            $agent_info->agent_leader = $site->agent_leader;
        }

        $site_data[] = [
            'platform' =>  $site->platform,
            'media_type' =>  $agent_info->media_type,
            'agent_id' => $agent_info->agent_id,
            'account_id' => $site->account_id,
            'site_id' => $site->site_id,
            'site_name' => $site->site_name,
            'game_id' => $site->game_id,
            'upt_state' => 0,
            'convert_id' => 0,
            'convert_data_type' => '',
            'create_time' => $site_create_timestamp,
            'game_start_time' => $site_create_timestamp,
            'ad_pop_zk' => 0.00,
            'action_track_type' => ActionTrackType::DEFAULT
        ];
    }

    (new V2DimSiteIdModel())->addMany($site_data, $outer_service->switchSettlementPayType('', $agent_info->pay_type), $agent_info->statistic_caliber);
    (new DimSiteGameIdModel())->replaceMany($site_data);
}
