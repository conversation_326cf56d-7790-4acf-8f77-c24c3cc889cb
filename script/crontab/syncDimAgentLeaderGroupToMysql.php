<?php
/**
 * 全量同步v2_dim_agent_leader_group数据到mysql
 * 0 *\/2 * * * php syncDimAgentLeaderGroupToMysql.php
 * @server 120.55.83.156 zx-dms
 *
 *
 * Created by PhpStorm.
 * User: Lzh
 * Date: 2025/06/04
 * Time: 10:53
 */

use App\Model\SqlModel\Tanwan\V2DimAgentLeaderGroupModel;
use App\Model\SqlModel\Zeda\V2DimAgentLeaderGroupModel as MysqlV2DimAgentLeaderGroupModel;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$model = new V2DimAgentLeaderGroupModel();
$all_data = $model->getList()->toArray();
$insert_data = array_map(function ($item) {
    return (array)$item;
}, $all_data);

try {
    MysqlConnection::getConnection()->beginTransaction();

    $dms_model = new MysqlV2DimAgentLeaderGroupModel();
    $dms_model->delete();
    $dms_model->replace($insert_data);

    MysqlConnection::getConnection()->commit();

} catch (Exception $e) {
    MysqlConnection::getConnection()->rollBack();
    Helpers::getLogger('sync_table')->error("syncDimAgentLeaderGroupToMysql同步脚本出错");
}



