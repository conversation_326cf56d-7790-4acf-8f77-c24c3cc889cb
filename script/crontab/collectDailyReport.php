<?php

/**
 * 30 04 * * * php collectDailyReport.php
 */

use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DimServerOpenTimeModel;
use App\Model\SqlModel\Tanwan\V2DWDADHourCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;
use App\Model\SqlModel\Tanwan\V2DWDDayRootGameUidLoginLogModel;
use App\Model\SqlModel\Tanwan\V2DWSDayRootGamePayLogModel;
use App\Model\SqlModel\Tanwan\V2DWSDayRootGamePayLogTestModel;
use App\Model\SqlModel\Tanwan\V2DWSDayRootGameRegLogModel;
use App\Model\SqlModel\Zeda\WeChatDayReportModel;
use App\MysqlConnection;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();

$day_time = 24 * 60 * 60;
$start_week = 1;
$start_day = 1;

if (count($argv) === 2) {
    $time_benchmark = strtotime($argv[1]);
} else {
    $time_benchmark = time();
}

function collectReport($time_benchmark)
{
    global $start_week, $start_day;

    $start_date = date("Y-m-d", strtotime("-1 day", $time_benchmark));
    $end_date = date("Y-m-d", strtotime("-1 day", $time_benchmark));
    $start_avg_date = date("Y-m-d", strtotime("-15 day", $time_benchmark));
    $end_avg_date = date("Y-m-d", strtotime("-7 day", $time_benchmark));
    $week_no = intval(date('w', $time_benchmark));
    $day_no = intval(date('d', $time_benchmark));

    $hour_cost_model = new V2DWDDayCostLogModel();
    $game_reg_model = new V2DWSDayRootGameRegLogModel();
    $day_pay_model = new V2DWSDayRootGamePayLogModel();
    $day_pay_test_model = new V2DWSDayRootGamePayLogTestModel();
    $day_login_model = new V2DWDDayRootGameUidLoginLogModel();
    $server_open_model = new V2DimServerOpenTimeModel();
    $game_model = new V2DimGameIdModel();

    $main_game_list = $game_model->getMainGameList();
    $cost_list = $hour_cost_model->getTotalCost($start_date, $end_date);
    $reg_list = $game_reg_model->getRegCount($start_date, $end_date);
    $income_list = $day_pay_model->getIncome($start_date, $end_date);
    $avg_7day_cost_list = $hour_cost_model->getTotalCost($start_avg_date, $end_avg_date);
    $avg_7day_money_list = $day_pay_test_model->getAvg7DayMoney($start_avg_date, $end_avg_date);
    $DAU_list = $day_login_model->getDAUCount($start_date, $end_date);
    $server_open_list = $server_open_model->getOpenCount($start_date, $end_date);

    if ($week_no === $start_week) {
        $start_date = date('Y-m-d', strtotime('-' . (6 + date('w', $time_benchmark)) . ' days', $time_benchmark));
        $end_date = date('Y-m-d', strtotime('-' . date('w', $time_benchmark) . ' days', $time_benchmark));
        $week_reg_list = $game_reg_model->getRegCount($start_date, $end_date);
        $week_DAU_list = $day_login_model->getDAUCount($start_date, $end_date);
    } else {
        $week_reg_list = Collection::make([]);
        $week_DAU_list = Collection::make([]);
    }

    if ($day_no === $start_day) {
        $start_date = date('Y-m-01', strtotime("last day of -1 month", $time_benchmark));
        $end_date = date('Y-m-t', strtotime("last day of -1 month", $time_benchmark));
        $month_reg_list = $game_reg_model->getRegCount($start_date, $end_date);
        $month_DAU_list = $day_login_model->getDAUCount($start_date, $end_date);
    } else {
        $month_reg_list = Collection::make([]);
        $month_DAU_list = Collection::make([]);
    }

    $now = $time_benchmark;
    $start_delete_time = strtotime(date("Y-m-d 00:00:00", $time_benchmark));
    $end_delete_time = strtotime(date("Y-m-d 23:59:59", $time_benchmark));
    $insert_data = [];

    foreach ($main_game_list as $game_info) {
        $cost_info = $cost_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $reg_info = $reg_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $income_info = $income_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $avg_7day_money_info = $avg_7day_money_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $DAU_info = $DAU_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $server_open_info = $server_open_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $week_reg_info = $week_reg_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $week_DAU_info = $week_DAU_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $month_reg_info = $month_reg_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $month_DAU_info = $month_DAU_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        $avg_7day_cost_info = $avg_7day_cost_list
            ->where('platform', $game_info->platform)
            ->where('plat_id', $game_info->plat_id)
            ->where('root_game_id', $game_info->root_game_id)
            ->where('main_game_id', $game_info->main_game_id)
            ->first();

        if (
            empty($cost_info)
            && empty($reg_info)
            && empty($income_info)
            && empty($avg_7day_money_info)
            && empty($DAU_info)
            && empty($server_open_info)
        ) {
            continue;
        }

        $cost = empty($cost_info->money) ? 0 : $cost_info->money;
        $android_reg_uid_count = empty($reg_info->android_reg_uid_count) ? 0 : $reg_info->android_reg_uid_count;
        $ios_reg_uid_count = empty($reg_info->ios_reg_uid_count) ? 0 : $reg_info->ios_reg_uid_count;
        $reg_uid_count = empty($reg_info->reg_uid_count) ? 0 : $reg_info->reg_uid_count;
        $first_day_pay_money = empty($reg_info->first_day_pay_money) ? 0 : $reg_info->first_day_pay_money;
        $second_login_count = empty($reg_info->second_login_count) ? 0 : $reg_info->second_login_count;
        $total_7day_pay_money = empty($avg_7day_money_info->pay_money) ? 0 : $avg_7day_money_info->pay_money;
        $total_7day_cost = empty($avg_7day_cost_info->money) ? 0 : $avg_7day_cost_info->money;
        $create_role_count = empty($reg_info->create_role_count) ? 0 : $reg_info->create_role_count;
        $first_day_pay_count = empty($reg_info->first_day_pay_count) ? 0 : $reg_info->first_day_pay_count;

        $income = empty($income_info->pay_money) ? 0 : $income_info->pay_money;
        $DAU = empty($DAU_info->login_uid_count) ? 0 : $DAU_info->login_uid_count;
        $WAU = empty($week_DAU_info->login_uid_count) ? 0 : $week_DAU_info->login_uid_count;
        $MAU = empty($month_DAU_info->login_uid_count) ? 0 : $month_DAU_info->login_uid_count;
        $week_reg_uid_count = empty($week_reg_info->reg_uid_count) ? 0 : $week_reg_info->reg_uid_count;
        $month_reg_uid_count = empty($month_reg_info->reg_uid_count) ? 0 : $month_reg_info->reg_uid_count;
        $day_old_user_count = abs($DAU - $reg_uid_count);
        $week_old_user_count = abs($WAU - $week_reg_uid_count);
        $month_old_user_count = abs($MAU - $month_reg_uid_count);
        $server_open_count = empty($server_open_info->server_open_count) ? 0 : $server_open_info->server_open_count;
        $plat_id = ($game_info->platform == 'TW') ? (($game_info->plat_id == 1) ? 1 : 2) : 2;

        $insert_data[] = [
            'platform' => $game_info->platform,
            'plat_id' => $plat_id,
            'root_game_id' => $game_info->root_game_id,
            'main_game_id' => $game_info->main_game_id,
            'cost' => $cost,
            'android_reg_uid_count' => $android_reg_uid_count,
            'ios_reg_uid_count' => $ios_reg_uid_count,
            'reg_uid_count' => $reg_uid_count,
            'first_day_pay_money' => $first_day_pay_money,
            'second_login_count' => $second_login_count,
            'income' => $income,
            'day7_pay_money' => $total_7day_pay_money,
            'day7_cost' => $total_7day_cost,
            'day_old_user_count' => $day_old_user_count,
            'week_old_user_count' => $week_old_user_count,
            'month_old_user_count' => $month_old_user_count,
            'DAU' => $DAU,
            'WAU' => $WAU,
            'MAU' => $MAU,
            'server_open_count' => $server_open_count,
            'create_time' => $now,
            'create_role_count' => $create_role_count,
            'first_day_pay_count' => $first_day_pay_count
        ];
    }

    if ($insert_data) {
        $wechat_day_report_model = new WeChatDayReportModel();
        $wechat_day_report_model->deleteDayRecord(
            $start_delete_time,
            $end_delete_time
        );
        $wechat_day_report_model->addRecord($insert_data);
    }
}

$last_day_benchmark = strtotime('-1 day', $time_benchmark);
collectReport($last_day_benchmark);
collectReport($time_benchmark);

$week_no = intval(date('w', $time_benchmark));
$day_no = intval(date('d', $time_benchmark));

if ($start_week === $week_no) {
    $benchmark = strtotime('-' . (6 + date('w', $time_benchmark)) . ' days', $time_benchmark);
    for (; $benchmark <= $time_benchmark; $benchmark += $day_time) {
        collectReport($benchmark);
    }
}

if ($start_day === $day_no) {
    $benchmark = strtotime("first day of this month", $time_benchmark);
    for (; $benchmark <= $time_benchmark; $benchmark += $day_time) {
        collectReport($benchmark);
    }
}
