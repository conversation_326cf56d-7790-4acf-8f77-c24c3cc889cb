<?php
/**
 * 10 5 * * *
 * 渠道异常点击记录
 */

use App\Param\DMS\AbnormalAgentClickParam;
use App\Utils\Helpers;
use App\Model\SqlModel\Tanwan\V2DWDADTrackBaseLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$logger = Helpers::getLogger('AbnormalClickLastDays');

MysqlConnection::setConnection();

// -d 2021-01-20 d参数接收日期
// -t 1 t参数接收扫描类型目前只接受 7、15分别对应7日15日
$input = getopt('d:t:');
// 默认时间和单日时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date("Y-m-d", strtotime("-1 day"));
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START date:' . $date);
$date_last_second = $date . ' 23:59:59';
$type = isset($input['t']) ? intval($input['t']) : 7;
$start_date = date('Y-m-d', strtotime($date) - ($type - 1) * 86400);

$agent_abnormal_click_model = new V2DWDAgentAbnormalClickModel();
$ad_track_base_log_model = new V2DWDADTrackBaseLogModel();
// 多日异常点击
$last_days_param = new AbnormalAgentClickParam();
$last_days_param->click_date_range = [date('Y-m-d', strtotime($date) - ($type - 1) * 86400), $date];
$abnormal_click_last_days_records = $agent_abnormal_click_model->getLastDaysAgent($last_days_param, 'click', $type);
$click_date_range = [date('Y-m-d', strtotime($date) - ($type - 1) * 86400), $date_last_second];
$list = collect();
if ($abnormal_click_last_days_records->isNotEmpty()) {
    $platform_filters = $abnormal_click_last_days_records->pluck('platform')->unique();
    $agent_filters = $abnormal_click_last_days_records->pluck('agent_id')->unique();
    $game_filters = $abnormal_click_last_days_records->pluck('game_id')->unique();

    $builder_ip = $ad_track_base_log_model->builder->from('tanwan_datahub.v2_dwd_ad_track_base_log')
        ->select('platform', 'agent_id', 'game_id', 'ip', 'muid')
        ->selectRaw('count(*) AS click_num')
        ->whereIn('platform', $platform_filters)
        ->whereIn('agent_id', $agent_filters)
        ->whereIn('game_id', $game_filters)
        ->whereBetween('click_time', $click_date_range)
        ->where('ip', '<>', '')
        ->groupBy('platform', 'agent_id', 'game_id', 'ip');
    $builder_muid = $ad_track_base_log_model->builder->from('tanwan_datahub.v2_dwd_ad_track_base_log')
        ->select('platform', 'agent_id', 'game_id', 'ip', 'muid')
        ->selectRaw('count(*) AS click_num')
        ->whereIn('platform', $platform_filters)
        ->whereIn('agent_id', $agent_filters)
        ->whereIn('game_id', $game_filters)
        ->whereBetween('click_time', $click_date_range)
        ->where('ip', '')
        ->where('muid', '<>', '')
        ->groupBy('platform', 'agent_id', 'game_id', 'muid');
    $base_builder = $builder_ip->unionAll($builder_muid);

    $list = $ad_track_base_log_model->builder
        ->select('platform', 'agent_id', 'game_id')
        ->selectRaw("count(1) as total")
        ->selectRaw("count(IF(click_num>=2,1,null)) / count(1) as click_egt_2_rate")
        ->selectRaw("count(IF(click_num<=2,1,null)) / count(1) as click_elt_2_rate")
        ->selectRaw("count(IF(click_num = 2,1,null)) / count(IF(click_num>=2,1,null)) as agent_2_ad_clicks_rate")
        ->selectRaw("sum(IF(click_num>=3,click_num,0)) as egt_3_num")
        ->selectRaw("count(IF(click_num>=6 * $type,1,null)) as egt_6_times")
        ->selectRaw("count(IF(click_num>=6 * $type, 1 ,null)) / count(1) as click_egt_6_rate")
        ->selectRaw("sum(IF(click_num>=6 * $type, click_num ,0)) as click_egt_6_num")
        ->selectRaw("count(IF(click_num = 1, 1 ,null)) as ad_1_click")
        ->selectRaw("count(IF(click_num >= 100 * $type and ip != '', 1, null)) as egt_100_clicks_ip")
        ->selectRaw("sum(IF(click_num >= 100 * $type and ip != '', click_num, 0)) / count(IF(click_num >= 100 * $type and ip != '', 1, null)) as ip_click_num")
        ->selectRaw("count(IF(click_num >= 100 * $type and ip = '', 1, null)) as egt_100_clicks_muid")
        ->selectRaw("sum(IF(click_num >= 100 * $type and ip = '', click_num, 0)) / count(IF(click_num >= 100 * $type and ip = '', 1, null)) as muid_click_num")
        ->fromSub($base_builder, 'base')
        ->groupBy('platform', 'agent_id', 'game_id')
        ->get();
}

$ip_list = collect();
$egt_1600_records = $abnormal_click_last_days_records->where('day_click_count', '>=', 1600 * $type);
if ($egt_1600_records->isNotEmpty()) {
    $platform_filters = $abnormal_click_last_days_records->pluck('platform')->unique();
    $agent_filters = $abnormal_click_last_days_records->pluck('agent_id')->unique();
    $game_filters = $abnormal_click_last_days_records->pluck('game_id')->unique();
    // 连续ip段
    $continuous_ip_builder = $ad_track_base_log_model->builder
        ->select('platform', 'agent_id', 'game_id')
        ->selectRaw("SUBSTRING_INDEX(ip, '.', 3) AS ip_segment, count(*) AS ct")
        ->fromSub($ad_track_base_log_model->builder
            ->from('tanwan_datahub.v2_dwd_ad_track_base_log')
            ->selectRaw('DISTINCT platform, agent_id, game_id, ip')
            ->whereIn('platform', $egt_1600_records->pluck('platform')->unique())
            ->whereIn('agent_id', $egt_1600_records->pluck('agent_id')->unique())
            ->whereIn('game_id', $egt_1600_records->pluck('game_id')->unique())
            ->where('ip', '<>', '')
            ->whereBetween('click_time', $click_date_range), 't1')
        ->groupBy('platform', 'agent_id', 'game_id', 'ip_segment');

    $ip_list = $ad_track_base_log_model->builder
        ->select('platform', 'agent_id', 'game_id')
        ->selectRaw("sum(IF(ct >= 20 * $type,ct,0)) / sum(ct) as egt_20_ips_rate")
        ->selectRaw("sum(IF(ct >= 2 * $type,ct,0)) / sum(ct) as egt_2_ips_rate")
        ->fromSub($continuous_ip_builder, 'base')
        ->groupBy('platform', 'agent_id', 'game_id')
        ->get();
}

foreach ($abnormal_click_last_days_records as $abnormal_record) {
    $click_err = '';
    $param = new AbnormalAgentClickParam($abnormal_record);
    $param->click_date_range = $click_date_range;
    $param->date = $date;

    $data = $list
        ->where('agent_id', $abnormal_record->agent_id)
        ->where('game_id', $abnormal_record->game_id)
        ->where('platform', $abnormal_record->platform)
        ->first();

    if (empty($data)) {
        continue;
    }

    if ($abnormal_record->day_click_count < 600 * $type) {// 0-600
        $click_egt_2_rate = $data->click_egt_2_rate * 100;
        if ($click_egt_2_rate > 25) {
            $click_egt_2_rate = round($click_egt_2_rate, 2);
            $click_err .= "超过1次点击ip占比过多有{$click_egt_2_rate}%\r\n";
        }
        if ($type == 15 && 0 < $click_egt_2_rate && $click_egt_2_rate <= 25) {// 15日需更进一步判断
            $agent_2_ad_clicks_rate = $data->agent_2_ad_clicks_rate * 100;
            if (0 < $agent_2_ad_clicks_rate && $agent_2_ad_clicks_rate < 75) {
                $click_egt_3_rate = round((1 - $data->agent_2_ad_clicks_rate) * $click_egt_2_rate, 2);
                $click_err .= "ip3次+(含3)产生超过{$click_egt_3_rate}%的点击\r\n";
            }
        }
    } else if ($abnormal_record->day_click_count >= 600 * $type && $abnormal_record->day_click_count < 1600 * $type) {// 600-1600
        $click_egt_2_rate = $data->click_egt_2_rate * 100;
        if ($click_egt_2_rate > 25) {
            $click_egt_2_rate = round($click_egt_2_rate, 2);
            $click_err .= "超过1次点击ip占比过多有{$click_egt_2_rate}%\r\n";
        } else {
            $click_elt_2_rate = $data->click_elt_2_rate * 100;
            if (0 < $click_elt_2_rate && $click_elt_2_rate < 75) {
                $egt_3_num = $data->egt_3_num;
                $click_err .= "ip点击3次+产生了{$egt_3_num}次\r\n";
            }
        }
        $egt_6_times = $data->egt_6_times;
        if ($egt_6_times >= 3) $click_err .= "频繁点击的ip重复出现{$egt_6_times}次\r\n";
    } else if ($abnormal_record->day_click_count >= 1600 * $type) {
        $click_egt_6_rate = $data->click_egt_6_rate * 100;
        if ($click_egt_6_rate > 4.5) {
            $click_egt_6_num = $data->click_egt_6_num;
            $click_err .= "频繁点击的ip过多有{$click_egt_6_num}次\r\n";
        }

        $eq_1_rate = $data->ad_1_click / $abnormal_record->day_click_count * 100;
        if ($eq_1_rate >= 75) {
            $click_elt_2_rate = $data->click_elt_2_rate * 100;
            if (0 < $click_elt_2_rate && $click_elt_2_rate < 75) {
                $egt_3_num = $data->egt_3_num;
                $click_err .= "ip点击3次+产生了{$egt_3_num}次\r\n";
            }
        } else {
            $gt_1_rate = round(100 - $eq_1_rate, 2);
            $click_err .= "ip2次+点击产生的点击占比是{$gt_1_rate}%\r\n";
        }
        $egt_100_clicks_ip = $data->egt_100_clicks_ip;
        if ($egt_100_clicks_ip > 0) {
            $click_num = intval($data->ip_click_num);
            $click_err .= "单ip日点击超过100 有{$egt_100_clicks_ip}个ip，平均点击{$click_num}次\r\n";
        }
        $egt_100_clicks_muid = $data->egt_100_clicks_muid;
        if ($egt_100_clicks_muid > 0) {
            $click_num = intval($data->muid_click_num);
            $click_err .= "单muid日点击超过100 有{$egt_100_clicks_muid}个muid，平均点击{$click_num}次\r\n";
        }
        $ip_data = $ip_list
            ->where('agent_id', $abnormal_record->agent_id)
            ->where('game_id', $abnormal_record->game_id)
            ->where('platform', $abnormal_record->platform)
            ->first();
        if ($ip_data) {
            // ip段连续出现20次
            $more_than_20_ips_rate = $ip_data->egt_20_ips_rate * 100;
            if ($more_than_20_ips_rate >= 30) {
                $more_than_20_ips_rate = round($more_than_20_ips_rate, 2);
                $click_err .= "同区域点击ip过多,占到了{$more_than_20_ips_rate}%\r\n";
            }
            // ip段连续出现2次
            $more_than_2_ips_rate = $ip_data->egt_2_ips_rate * 100;
            if ($more_than_2_ips_rate > 25) {
                $more_than_2_ips_rate = round($more_than_2_ips_rate, 2);
                $click_err .= "连续ip段2+占比{$more_than_2_ips_rate}%\r\n";
            }
        }
    }
    if (!empty($click_err)) {
        $filed = 'is_click_abnormal_' . $type;
        $msg_field = 'click_abnormal_msg_' . $type;
        $update_data = [
            $filed => 1,
            $msg_field => $click_err
        ];
//        echo $param->platform, ',', $param->agent_id, ',', $param->game_id, ',', $click_err;
        $agent_abnormal_click_model->updateFiledByPK($param, $update_data);
    }
}

$logger->info('END date:' . $date);