<?php


// 5 * * * *
// 收集微信推送数据

use App\Model\SqlModel\DatahubLY\DataCollectionNewModel as DatahubLYDataCollectionModel;
use App\Model\SqlModel\Tanwan\DataCollectionNewModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;

//use App\Model\SqlModel\Zeda\WechatPushDataDmsModel;
//use App\Model\SqlModel\Zeda\WechatPushDataLyModel;
use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';
require dirname(__DIR__) . '/process/utils/common.php';

date_default_timezone_set('PRC');

MysqlConnection::setConnection();
// TODO 弃用

//$logger = Helpers::getLogger('wechat_push');
//
//
//function fix_dms_permission($configs)
//{
//    $game_permission = [];
//    foreach ($configs as $config) {
//        foreach ($config as $sub) {
//            foreach ($sub['sub_config'] as $item) {
//                $info = $item['info'];
//                foreach ($info as $data) {
//                    $platform = $data['platform'];
//                    $main_game_id = $data['main_game_id'];
//
//                    if (!isset($game_permission[$platform])) {
//                        $game_permission[$platform] = [
//                            'main_game_id' => $main_game_id
//                        ];
//                        continue;
//                    }
//                    $game_permission[$platform]['main_game_id'] = array_merge(
//                        $game_permission[$platform]['main_game_id'],
//                        $main_game_id
//                    );
//                }
//            }
//        }
//    }
//    return $game_permission;
//}
//
//function fix_ly_permission($configs)
//{
//    $game_permission = [];
//    $agent_permission = [];
//
//    foreach ($configs as $config) {
//        foreach ($config as $sub) {
//            foreach ($sub['sub_config'] as $item) {
//                $info = $item['info'];
//
//                foreach ($info as $data) {
//                    $platform = $data['platform'];
//                    $game_id = $data['game_id'];
//                    $channel_id = $data['channel_id'];
//                    $ag_name = $data['ag_name'] ?? [];
//
//                    if (!isset($game_permission[$platform])) {
//                        $game_permission[$platform] = [
//                            'game_id' => $game_id
//                        ];
//                    } else {
//                        $game_permission[$platform]['game_id'] = array_merge(
//                            $game_permission[$platform]['game_id'],
//                            $game_id
//                        );
//                    }
//
//                    if (!isset($agent_permission[$platform])) {
//                        $agent_permission[$platform] = [];
//                    }
//                    foreach ($channel_id as $id) {
//                        $agent_permission[$platform]['channel_id'][] = $id;
//                    }
//                    foreach ($ag_name as $agent_group_id) {
//                        $agent_permission[$platform]['agent_group_id'][] = $agent_group_id;
//                    }
//                }
//            }
//        }
//    }
//    return [$game_permission, $agent_permission];
//}
//
//function construct_show_data(
//    $platform,
//    $root_game_id,
//    $root_game_name,
//    $main_game_id,
//    $main_game_name,
//    $agent_group_id,
//    $agent_id,
//    $agent_leader_group_name,
//    $tdate,
//    $money,
//    $ori_money,
//    $reg_uid_count,
//    $dby_second_login_rate,
//    $dby_second_login_count,
//    $dby_reg_uid_count,
//    $reg_uid_new_pay_count,
//    $uid_count,
//    $old_uid_count,
//    $total_pay_money,
//    $first_day_pay_money,
//    $open_service,
//    $mix_id,
//    $month_money,
//    $month_ori_money,
//    $month_total_pay_money,
//    $day_pay_count,
//    $sort,
//    $is_apportioned)
//{
//    return [
//        "platform"                => $platform,
//        "root_game_id"            => $root_game_id,
//        "root_game_name"          => $root_game_name,
//        "main_game_id"            => $main_game_id,
//        "main_game_name"          => $main_game_name,
//        "agent_group_id"          => $agent_group_id,
//        "agent_id"                => $agent_id,
//        "agent_leader_group_name" => $agent_leader_group_name,
//        "tdate"                   => $tdate,
//        "money"                   => $money,
//        "ori_money"               => $ori_money,
//        "reg_uid_count"           => $reg_uid_count,
//        "dby_second_login_rate"   => $dby_second_login_rate,
//        "dby_second_login_count"  => $dby_second_login_count,
//        "dby_reg_uid_count"       => $dby_reg_uid_count,
//        "reg_uid_new_pay_count"   => $reg_uid_new_pay_count,
//        "uid_count"               => $uid_count,
//        "old_uid_count"           => $old_uid_count,
//        "total_pay_money"         => $total_pay_money,
//        "first_day_pay_money"     => $first_day_pay_money,
//        "open_service"            => $open_service,
//        "mix_id"                  => $mix_id,
//        "month_money"             => $month_money,
//        "month_ori_money"         => $month_ori_money,
//        "month_total_pay_money"   => $month_total_pay_money,
//        "day_pay_count"           => $day_pay_count,
//        "sort"                    => $sort,
//        'is_apportioned'          => $is_apportioned,
//
//    ];
//}
//
//function fix_show_data(Collection $list, $date, $is_apportioned = 1)
//{
//    $ret = [];
//
//    foreach ($list as $item) {
//        $d = construct_show_data(
//            $item->platform,
//            $item->root_game_id,
//            $item->root_game_name,
//            $item->main_game_id,
//            $item->main_game_name,
//            $item->agent_group_id,
//            $item->agent_id,
//            $item->agent_leader_group_name,
//            $date,
//            $item->money,
//            $item->ori_money,
//            $item->reg_uid_count,
//            $item->dby_second_login_rate,
//            $item->dby_second_login_count,
//            $item->dby_reg_uid_count,
//            $item->reg_uid_new_pay_count,
//            $item->uid_count,
//            $item->old_uid_count,
//            $item->total_pay_money,
//            $item->first_day_pay_money,
//            $item->open_service,
//            $item->mix_id,
//            $item->month_money,
//            $item->month_ori_money,
//            $item->month_total_pay_money,
//            $item->day_pay_count,
//            0,
//            $is_apportioned,
//        );
//        $ret[] = $d;
//    }
//    return $ret;
//}
//
//function collect_dms_data($configs, $start_date, $end_date)
//{
//    $game_permission = fix_dms_permission($configs);
//    return (new DataCollectionNewModel())->collectWeChatReport($game_permission, $start_date, $end_date);
//}
//
//function construct_show_data_ly(
//    $platform,
//    $game_id,
//    $game_name,
//    $main_game_id,
//    $main_game_name,
//    $agent_group_id,
//    $agent_name,
//    $agent_id,
//    $channel_id,
//    $channel_name,
//    $tdate,
//    $money,
//    $ori_money,
//    $reg_uid_count,
//    $dby_second_login_rate,
//    $dby_second_login_count,
//    $dby_reg_uid_count,
//    $reg_uid_new_pay_count,
//    $uid_count,
//    $old_uid_count,
//    $total_pay_money,
//    $first_day_pay_money,
//    $open_service,
//    $mix_id,
//    $month_money,
//    $month_ori_money,
//    $month_total_pay_money,
//    $day_pay_count,
//    $sort)
//{
//    return [
//        "platform"               => $platform,
//        "game_id"                => $game_id,
//        "game_name"              => $game_name,
//        "main_game_id"           => $main_game_id,
//        "main_game_name"         => $main_game_name,
//        "agent_group_id"         => $agent_group_id,
//        "agent_name"             => $agent_name,
//        "agent_id"               => $agent_id,
//        "channel_id"             => $channel_id,
//        "channel_name"           => $channel_name,
//        "tdate"                  => $tdate,
//        "money"                  => $money,
//        "ori_money"              => $ori_money,
//        "reg_uid_count"          => $reg_uid_count,
//        "dby_second_login_rate"  => $dby_second_login_rate,
//        "dby_second_login_count" => $dby_second_login_count,
//        "dby_reg_uid_count"      => $dby_reg_uid_count,
//        "reg_uid_new_pay_count"  => $reg_uid_new_pay_count,
//        "uid_count"              => $uid_count,
//        "old_uid_count"          => $old_uid_count,
//        "total_pay_money"        => $total_pay_money,
//        "first_day_pay_money"    => $first_day_pay_money,
//        "open_service"           => $open_service,
//        "mix_id"                 => $mix_id,
//        "month_money"            => $month_money,
//        "month_ori_money"        => $month_ori_money,
//        "month_total_pay_money"  => $month_total_pay_money,
//        "day_pay_count"          => $day_pay_count,
//        "sort"                   => $sort
//    ];
//}
//
//function fix_show_data_ly(Collection $list, $date)
//{
//    $ret = [];
//
//    foreach ($list as $item) {
//        $d = construct_show_data_ly(
//            $item->platform,
//            $item->game_id,
//            $item->game_name,
//            $item->main_game_id,
//            $item->main_game_name,
//            $item->agent_group_id,
//            $item->agent_name,
//            $item->agent_id,
//            $item->channel_id,
//            $item->channel_name,
//            $date,
//            $item->money,
//            $item->ori_money,
//            $item->reg_uid_count,
//            $item->dby_second_login_rate,
//            $item->dby_second_login_count,
//            $item->dby_reg_uid_count,
//            $item->reg_uid_new_pay_count,
//            $item->uid_count,
//            $item->old_uid_count,
//            $item->total_pay_money,
//            $item->first_day_pay_money,
//            $item->open_service,
//            $item->mix_id,
//            $item->month_money,
//            $item->month_ori_money,
//            $item->month_total_pay_money,
//            $item->day_pay_count,
//            0
//        );
//        $ret[] = $d;
//    }
//    return $ret;
//}
//
//function collect_ly_data($config, $start_date, $end_date)
//{
//    $model = new DatahubLYDataCollectionModel();
//    [$game_permission, $agent_permission] = fix_ly_permission($config);
//
//    return $model->collectWeChatReport($game_permission, $agent_permission, $start_date, $end_date);
//}
//
//function collect_data($start_date, $end_date, $date, $hour)
//{
//    $dms_configs = $ly_configs = $ret = [];
//    $config_model = new WechatPushConfigNewModel();
//
//    $list = $config_model->getAll();
//
//    foreach ($list as $item) {
//        //非零点的收集只收集开启小时推送的
//        $push_hour = (int)$item->push_hour;
//        if ($hour > 0 && $push_hour === 0) {
//            continue;
//        }
//        $module = $item->module;
//        $str_config = $item->config;
//        if ($module == "dms") {
//            $dms_configs[] = json_decode($str_config, true);
//        } else if ($module == "ly") {
//            $ly_configs[] = json_decode($str_config, true);
//        } else {
//            continue;
//        }
//    }
//
//    $data = collect_dms_data($dms_configs, $start_date, $end_date);
//    $dms_data = fix_show_data($data, $date);
//
//    $data = collect_ly_data($ly_configs, $start_date, $end_date);
//    $ly_data = fix_show_data_ly($data, $date);
//
//    return [$dms_data, $ly_data];
//}
//
//
//$hour = 0;//(int) date('G');
//$date = date("Y-m-d");
//if ($hour == 0) {
//    $pre_start_date = date("Y-m-d", strtotime("-3 days"));
//    $start_date = date("Y-m-d", strtotime("-2 days"));
//    $end_date = date("Y-m-d", strtotime("-1 days"));
//    [$dms_data1, $ly_data1] = collect_data($pre_start_date, $start_date, $end_date, $hour);
//    [$dms_data2, $ly_data2] = collect_data($start_date, $end_date, $date, $hour);
//    $dms_data = array_merge($dms_data1, $dms_data2);
//    $ly_data = array_merge($ly_data1, $ly_data2);
//} else {
//    $start_date = date("Y-m-d", strtotime("-1 days"));
//    $end_date = date("Y-m-d");
//    [$dms_data, $ly_data] = collect_data($start_date, $end_date, $date, $hour);
//}
//
//$log_model = new WechatPushDataDmsModel();
//$ret = [];
//foreach ($dms_data as $item) {
////    if (empty($item['tdate'])) {
////        continue;
////    }
//    $ret[] = $item;
//}
//$data = $ret;
//foreach ($data as &$item) {
//    $item['thour'] = $hour;
//}
//$chunks = array_chunk($data, 100);
//foreach ($chunks as $chunk) {
//    $log_model->batchAdd($chunk);
//}
//
//$log_model = new WechatPushDataLyModel();
//$ret = [];
//foreach ($ly_data as $item) {
////    if (empty($item['tdate'])) {
////        continue;
////    }
//    $ret[] = $item;
//}
//$data = $ret;
//foreach ($data as &$item) {
//    $item['thour'] = $hour;
//}
//$chunks = array_chunk($data, 100);
//foreach ($chunks as $chunk) {
//    $log_model->batchAdd($chunk);
//}
//
////删除30天前的数据
//(new WechatPushDataDmsModel())->delete();
//(new WechatPushDataLyModel())->delete();
