<?php


// 10 0,14,18,21 * * *
// 收集团队配置推送数据


use App\Logic\TeamDataCollectLogic;
use App\Model\SqlModel\Tanwan\DwdWechatTeamPushModel;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__).'/../common/init.php';
require dirname(__DIR__).'/process/utils/common.php';

date_default_timezone_set('PRC');

MysqlConnection::setConnection();

echo "collecting data begin======".date('Y-m-d H:i:s')."======================>".PHP_EOL;
$cur_hour = intval(date("H"));


$logger = Helpers::getLogger('team_config_task');
$logger->info('任务开始');
try {
    // 0点取前一天，下午2点取当天
    if ($cur_hour != 0) {
        $start_date = $end_date = date("Y-m-d", strtotime("now"));
    } else {
        $start_date = $end_date = date("Y-m-d", strtotime("-1 days"));
    }
    $log_model = new DwdWechatTeamPushModel();

    $logic = new TeamDataCollectLogic();
    $logic->startCollectTeamData($log_model, $logger, $start_date, $end_date, $cur_hour);
    if ($cur_hour == 0) {
        $logic->deleteOldTeamData($log_model, $logger);
    }
} catch (\Throwable $exception) {
    $logger->err($exception->getMessage());
}
$logger->info('任务结束');
exit;
