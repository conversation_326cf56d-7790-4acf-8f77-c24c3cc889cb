<?php
/**
 * 30 /1 * * *
 * 删除头条账号里已成功获取智能封面的视频
 */

use App\Model\HttpModel\Toutiao\File\VideoModel;
use App\Model\SqlModel\Zeda\VideoGetCoverTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$connection_name = 'default';

echo date('Y-m-d H:i:s') . '--remove smart cover video begin' . PHP_EOL;

$account_id = ************;
$media_account_model = new MediaAccountModel();
$acc_info = $media_account_model->getDataByAccountId($account_id);

$media_files = MysqlConnection::getConnection($connection_name)->table('video_get_cover_task')
    ->where([['state', '=', VideoGetCoverTaskModel::SUCCESS_FIN], ['create_time', '<', time() - 3600]])
    ->get();
if ($media_files) {
    foreach ($media_files as $media_file) {
        try {
            echo 'deleting=====' . $media_file->media_file_id . ']=======' . PHP_EOL;
            $result = (new VideoModel())->deleteVideos($acc_info->account_id, $acc_info->access_token, [$media_file->media_file_id]);
            MysqlConnection::getConnection($connection_name)->table('video_get_cover_task')
                ->where('id', '=', $media_file->id)
                ->update(['state' => VideoGetCoverTaskModel::SUCCESS_DELETE]);
        }   catch (Throwable $exception) {
            $error_msg = $exception->getMessage();
            var_dump("素材{$media_file->media_file_id}删除出错，{$error_msg}");
        }
    }
}

echo date('Y-m-d H:i:s') . '--remove smart cover video finish' . PHP_EOL;