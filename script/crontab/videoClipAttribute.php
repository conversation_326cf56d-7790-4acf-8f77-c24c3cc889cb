<?php

// 0 * * * *
// 每隔1个小时 运行素材评估任务

use App\Constant\MediaType;
use App\Model\HttpModel\Toutiao\File\MaterialAttributesModel;
use App\Model\SqlModel\DataMedia\OdsVideoClipInefficientLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

try {
    $inefficient_model = new OdsVideoClipInefficientLogModel();
    $list = $inefficient_model->getAll()->groupBy("account_id");
    foreach ($list as $account_id => $item) {
        $account_info = (new MediaAccountModel())->getDataByAccountId($account_id, MediaType::TOUTIAO);
        $tmp_item = collect($item);
        $media_material_ids = $tmp_item->pluck('media_material_id')->map(function ($item) {
            return (int)$item;
        })->toArray();

        // 评估素材
        $page = 1;
        $page_size = 100;
        $material_attributes = [];
        while (true) {
            try {
                $data = (new MaterialAttributesModel())->attribute(
                    $account_info->account_id,
                    $account_info->access_token,
                    "AD",
                    $media_material_ids,
                    $page,
                    $page_size
                );
            } catch (Throwable $e) {
                var_dump("评估素材报错：{$e->getMessage()}");
                break;
            }
            $material_attributes = array_merge($material_attributes, $data['materials']);

            $page_info = $data['page'];
            $total_page = $page_info['total_page'] ?? 0;
            $page++;
            if ($page > $total_page) {
                break;
            }
        }

        // 评估结果还没出来，跳过
        if (empty($material_attributes)) {
            continue;
        }

        foreach ($material_attributes as $material) {
            try {
                $inefficient_model->editByAccountMediaMaterialId($account_id, $material['material_id'], [
                    "is_inefficient" => $material['is_inefficient_material'] ? 1 : 0,
                    "is_ad_low_quality_material" => $material['is_ad_low_quality_material'] ? 1 : 0,
                    "message_ad_low_quality_material" => json_encode($material['ad_low_quality_suggestions'] ?? [], JSON_UNESCAPED_UNICODE),
                    "message_ecp_low_quality_material" => json_encode($material['ecp_low_quality_suggestions'] ?? [], JSON_UNESCAPED_UNICODE),
                    "is_similar_queue_material" => $material['is_similar_queue_material'] ? 1 : 0,
                    "is_similar_expected_queue_material" => $material['is_similar_expected_queue_material'] ? 1 : 0,
                    "is_similar_material" => $material['is_similar_material'] ? 1 : 0,
                    "is_ecp_high_quality" => ($material['is_ecp_high_quality_material'] ?? false) ? 1 : 0,
                    "is_ad_high_quality" => $material['is_ad_high_quality_material'] ? 1 : 0,
                    "is_first_publish_material" => $material['is_first_publish_material'] ? 1 : 0,
                    "is_ecp_low_quality_material" => ($material['is_ecp_low_quality_material'] ?? false) ? 1 : 0,
                    "attributes_modify_time" => $material['attributes_modify_time'] ?? "",
                ]);
            } catch (Throwable $e) {
                var_dump("更新数据库报错：{$e->getMessage()}");
                continue;
            }
        }
    }
} catch (\Throwable $e) {
    var_dump(date('Y-m-d H:i:s') . '-' . $e->getMessage(), '——' . $e->getTraceAsString() . PHP_EOL);
}
