<?php
/**
 * 1 12 * * *
 * 人群包文件归档
 */

use App\Service\AudienceService;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
$archive_path = AudienceService::AUDIENCE_DIR;
$the_date_of_last_month = strtotime('-1 month', strtotime(date('Y-m-d 00:00:00')));
$audience_files = glob(AudienceService::AUDIENCE_DIR . '/task-*.txt');
foreach ($audience_files as $file) {
    $filename = basename($file, ".txt");
    $filename_arr = explode('-', $filename);
    if (count($filename_arr) !== 5) {
        continue;
    }
    $file_time = strtotime($filename_arr[2]);
    if ($file_time >= $the_date_of_last_month) {
        continue;
    }
    var_dump($file);
    $file_yearmonth = date('Ym', strtotime($filename_arr[2]));
    $archive_month_path = implode('/', [$archive_path, $file_yearmonth]);
    if (!is_dir($archive_month_path)) {
        mkdir($archive_month_path, 0755, true);
    }
    rename($file, "{$archive_month_path}/{$filename}.txt");
}