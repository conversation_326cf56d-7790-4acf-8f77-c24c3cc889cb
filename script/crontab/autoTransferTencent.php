<?php
/**
 * 自动转账监控脚本，整点执行，每30分钟执行一次
 */

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\MergeFundTypeFunds\MergeFundTypeFundsModel;
use App\Model\HttpModel\Tencent\MergeFundTypeSubcustomerTransfer\MergeFundTypeSubcustomerTransferModel;
use App\Model\HttpModel\TrinoTask\GDTTaskModel;
use App\Model\RedisModel\AutoTransferRuleRedisModel;
use App\Model\SqlModel\DataMedia\DwdAD2EffectBackDataLogModel;
use App\Model\SqlModel\DataMedia\OdsAutoTransferOperateLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAccountLogModel;
use App\Model\SqlModel\Zeda\AutoTransferRuleModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\ADAnalysisTencentTransferParam;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
const CACHE_EXPIRE_TIME = 300;
/**
 * 调度不同频率的执行频率和次序
 */
function manageExecuteFrequency()
{
    //延迟整点三分钟执行
    sleep(180);
    ob_start();
    echo date('Y-m-d H:i:s') . "开始执行腾讯自动转账匹配" . PHP_EOL;
    ob_flush();
    $auto_transfer_redis_model = new AutoTransferRuleRedisModel();
    $frequency = $auto_transfer_redis_model->hget(AutoTransferRuleRedisModel::HASH_KEY, 'tencent_auto_transfer_last_frequency');
    if (!$frequency) {
        $frequency = 30;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'tencent_auto_transfer_last_frequency', $frequency);
    }
    if (30 === (int)$frequency) {
        //如果上次执行的是30分钟频率的，则本次30分钟和60分钟频率的都执行
        echo "本次执行：30分钟和60分钟频率的自动转账匹配" . PHP_EOL . "开始执行：30分钟频率" . PHP_EOL;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'tencent_auto_transfer_last_frequency', 60);
        ob_flush();
        autoTransferMainHandle(30);
        echo "开始执行：60分钟频率规则" . PHP_EOL;
        ob_flush();
        autoTransferMainHandle(60);
    } elseif (60 === (int)$frequency) {
        //如果上次执行的是60分钟频率的，则本次只执行30分钟频率的
        echo "开始执行：30分钟频率规则" . PHP_EOL;
        $auto_transfer_redis_model->setOrUpdate(AutoTransferRuleRedisModel::HASH_KEY, 'tencent_auto_transfer_last_frequency', 30);
        ob_flush();
        autoTransferMainHandle(30);
    }
    echo "腾讯自动转账匹配结束" . PHP_EOL . PHP_EOL;
    ob_end_flush();
}

function autoTransferMainHandle($frequency)
{
    $auto_transfer_rule_model = new AutoTransferRuleModel();
    $media_account = new MediaAccountModel();
    $redis_model = new AutoTransferRuleRedisModel();
    $all_auto_transfer_rule = $auto_transfer_rule_model->getAllByFrequency($frequency, MediaType::TENCENT);
    if ($all_auto_transfer_rule->isEmpty()) {
        return;
    }
    $update_account_ids = [];
    ob_start();
    foreach ($all_auto_transfer_rule as $one_rule) {
        echo '开始执行腾讯自动转账 规则名：' . $one_rule->name . ' 规则ID：' . $one_rule->id . PHP_EOL;
        ob_flush();
        $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
            'execute_status' => AutoTransferRuleModel::RUNNING_EXECUTE,
            'last_execute_time' => time(),
        ]);
        //检查时间
        if (time() < strtotime($one_rule->start_time) || time() > strtotime($one_rule->end_time)) {
            $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                'last_execute_time' => time(),
            ]);
            continue;
        }
        $use_account_ids = [];
        $reserve_account_ids = [];
        $execution_account = json_decode($one_rule->execution_account, true);
        //聚合所有account id
        foreach ($execution_account as $item) {
            $use_account_ids = array_merge($use_account_ids, $item['use_account_id']);
            $reserve_account_ids[] = $item['reserve_account_id'];
        }
        unset($item);
        $all_account_ids = array_merge($use_account_ids, $reserve_account_ids);
        $all_account_info = getAccountInfoByAccountIds($all_account_ids);
        //检查余额
        if ($balance_condition = json_decode($one_rule->balance_condition, true)) {
            $check_balance = checkBalance($use_account_ids, $reserve_account_ids, $balance_condition, $all_account_info);
            if (false === $check_balance) {
                $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                    'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                    'last_execute_time' => time(),
                ]);
                continue;
            }
            $use_account_ids = $check_balance['match_use_account'];
            $reserve_account_ids = $check_balance['match_reserve_account'];
        }
        //检查数值条件
        if ($calc_condition = json_decode($one_rule->calc_condition, true)) {
            $check_calc = checkCalc($use_account_ids, $calc_condition);
            if (false === $check_calc) {
                $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                    'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                    'last_execute_time' => time(),
                ]);
                continue;
            }
            $use_account_ids = array_diff($use_account_ids, $check_calc);
        }
        if (!$use_account_ids) {
            $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
                'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
                'last_execute_time' => time(),
            ]);
            continue;
        }
        //以上通过即进行转账操作
        $result = executeTransfer($one_rule, $use_account_ids, $reserve_account_ids, $all_account_info);
        $update_account_ids = array_merge($update_account_ids, $result['update_account_ids']);
        //更新media_account表账号当日已转账数额
        if ($result['need_update_daily_transfer_amount']) {
            $all_update_data = $redis_model->hGetAll('tencent_' . AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT);
            if ($all_update_data) {
                $media_account->updateAccountDailyTransferAmount(
                    'today_transfer_in_amount',
                    $all_update_data
                );
            }

        }
        //更新规则执行状态
        $auto_transfer_rule_model->updateRuleExecuteStatus([$one_rule->id], [
            'execute_status' => AutoTransferRuleModel::EXECUTE_COMPLETE,
            'last_execute_time' => time(),
        ]);
    }
    //投递redis更新account
    $update_account_ids = array_flip(array_flip($update_account_ids));
    if ($update_account_ids) {
        (new GDTTaskModel())->account(array_values($update_account_ids));
    }
}

/**
 * 执行转账
 * @param $rule
 * @param $use_account_ids
 * @param $reserve_account_ids
 * @param $all_account_info
 * @return array[]
 */
function executeTransfer($rule, $use_account_ids, $reserve_account_ids, $all_account_info)
{
    $http_model = new MergeFundTypeSubcustomerTransferModel;
    $fund_info_model = new MergeFundTypeFundsModel();
    $redis_model = new AutoTransferRuleRedisModel();
    $operate_log_model = new OdsAutoTransferOperateLogModel();
    $media_account_model = new MediaAccountModel();
    $all_account_ids = array_merge($use_account_ids, $reserve_account_ids);
    //防止SQL超长
    $all_account_ids = collect($all_account_ids)->chunk(2000);
    $access_token_info = [];
    foreach ($all_account_ids as $one_part) {
        $one_part_info = $media_account_model->getAccessTokenInAccountIds($one_part, MediaType::TENCENT);
        $access_token_info = $one_part_info->merge($access_token_info);
    }
    $access_token_info = $access_token_info->keyBy('account_id');
    $update_account_ids = [];
    $daily_transfer_amount = false;
    $execution_account = json_decode($rule->execution_account, true);
    foreach ($execution_account as $item) {
        if (!in_array($item['reserve_account_id'], $reserve_account_ids)) {
            continue;
        }
        $all_record = [];
        foreach ($item['use_account_id'] as $one_account_id) {
            if (!in_array($one_account_id, $use_account_ids)) {
                continue;
            }

            if ('RECHARGE' === $rule->transfer_type) {
                //充值
                $advertiser_id = (int)$item['reserve_account_id'];
                $target_advertiser_id = (int)$one_account_id;
            } else {
                //退款
                $advertiser_id = (int)$one_account_id;
                $target_advertiser_id = (int)$item['reserve_account_id'];
            }
            $record = [];
            $record['platform'] = $access_token_info[$advertiser_id]->platform;
            $record['auto_transfer_rule_id'] = $rule->id;
            $record['auto_transfer_rule_name'] = $rule->name;
            $record['media_type'] = $access_token_info[$advertiser_id]->media_type;
            $record['rule_creator'] = $rule->creator;
            $record['account_id'] = $advertiser_id;
            $record['account_name'] = $access_token_info[$advertiser_id]->account_name;
            $record['target_account_id'] = $target_advertiser_id;
            $record['target_account_name'] = $access_token_info[$target_advertiser_id]->account_name;

            $access_token = $access_token_info[$advertiser_id]->access_token;
            try {
                $transferable_fund = $fund_info_model->info($advertiser_id, $access_token);
                $fund_type_ad_recharge = 0;
                $have_fund_type_ad_recharge = false;
                foreach ($transferable_fund['list'] as $one_fund) {
                    if ('FUND_TYPE_AD_RECHARGE' === $one_fund['fund_type'] && 'FUND_STATUS_NORMAL' === $one_fund['fund_status']) {
                        //分转化为元
                        $fund_type_ad_recharge = Math::decimal($one_fund['balance'] / 100, 2);
                        $have_fund_type_ad_recharge = true;
                    }
                }
                if (false === $have_fund_type_ad_recharge) {
                    throw new AppException('获取余额媒体接口返回数据错误');
                }
                $all_account_info[$advertiser_id]->fund_type_ad_recharge = $fund_type_ad_recharge;
            } catch (AppException $e) {
                $fund_type_ad_recharge = $all_account_info[$advertiser_id]->fund_type_ad_recharge;
            }

            if ('RECHARGE' === $rule->transfer_type) {
                $amount = (float)Math::decimal($item['amount'], 2);
            } else {
                $amount = $fund_type_ad_recharge;
            }

            //验证转账额度
            if ((float)$redis_model->hGet('tencent_' . AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT, $target_advertiser_id) + $amount >
                $access_token_info[$target_advertiser_id]->daily_transfer_in_limit) {
                //达到转账限额
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $amount;
                $record['transfer_type'] = 0;
                $record['transaction_seq'] = 0;
                $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账失败，收款账号达到每日转账限额';
                $record['status'] = 2;
                $all_record[] = $record;
                if ('RECHARGE' === $rule->transfer_type) {
                    //充值，用款账户额度满了，跳出一层
                    continue;
                } else {
                    //退款，备款账户满了，跳出两层
                    $operate_log_model->add($all_record);
                    continue 2;
                }
            }
            //可用总金额不足，停止本次转账
            if (0.0 === (float)$fund_type_ad_recharge || $fund_type_ad_recharge < $amount) {
                //达到转账限额
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $amount;
                $record['transfer_type'] = 0;
                $record['transaction_seq'] = 0;
                $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账失败，总可用余额不足';
                $record['status'] = 2;
                $all_record[] = $record;
                if ('RECHARGE' === $rule->transfer_type) {
                    //充值，备款账户余额不足， 跳出两层
                    $operate_log_model->add($all_record);
                    continue 2;
                } else {
                    //退款，用款账户余额不足，跳出一层
                    continue;
                }
            }

            if ((float)$amount) {
                $record['operate_time'] = date('Y-m-d H:i:s');
                $record['amount'] = $amount;
                $record['transfer_type'] = ADAnalysisTencentTransferParam::TRANSFER_TYPE['FUND_TYPE_AD_RECHARGE'];

                try {
                    $response = $http_model->add(
                        $advertiser_id,
                        intval($amount * 100),
                        $target_advertiser_id,
                        'FUND_TYPE_AD_RECHARGE',
                        $access_token
                    );
                    //如果转账成功
                    $record['transaction_seq'] = $response['external_bill_no'] ?? 0;
                    $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id" . ' 转账成功, 交易时间：' .
                                                date('Y-m-d H:i:s', $response['time']);
                    $record['status'] = 1;

                    $update_account_ids[] = $advertiser_id;
                    $update_account_ids[] = $target_advertiser_id;
                    //如果转账成功，重新计算需要转的现金
//                    $transfer_cash = $amount - $transfer_grant;
                    $daily_transfer_amount = true;
                    //更新当日转账额度
                    $current_amount = $redis_model->hGet('tencent_' . AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT, $target_advertiser_id);
                    $redis_model->hSet('tencent_' . AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT,
                        $target_advertiser_id, $current_amount ? $current_amount + $amount : $amount);

                } catch (AppException $e) {

                    $record['transaction_seq'] = 0;
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $record['operate_detail'] = "账号ID $advertiser_id -> $target_advertiser_id 转账失败，失败原因：" .
                        json_encode($error, JSON_UNESCAPED_UNICODE);
                    $record['status'] = 2;
                }

                $all_record[] = $record;
            }
        }
        $operate_log_model->add($all_record);
    }

    return [
        'update_account_ids' => $update_account_ids,
        'need_update_daily_transfer_amount' => $daily_transfer_amount,
    ];
}

/**
 * 检查数值条件
 * @param $use_account_ids
 * @param $calc_condition
 * @return bool|array
 */
function checkCalc($use_account_ids, $calc_condition)
{
    $account_cost_data = (new DwdAD2EffectBackDataLogModel())
        ->getAccountCalcInAccountIds($use_account_ids, MediaType::TENCENT)
        ->keyBy('account_id');

    $field = ['cost', 'avg_day_cost', 'first_day_roi', 'count_ad2'];
    $not_match_use_account = [];
    $check_gt = false;
    foreach ($calc_condition as $item) {
        //如果没有查到消耗数据，三个消耗指标的条件且要求大于xxx，则必定不符合，用款账户全部不匹配
        if (in_array($item['name'], $field) && 'gt' === $item['operator']) {
            $check_gt = true;
            break;
        }
    }
    if ($account_cost_data->isEmpty()) {
        //如果没有查到消耗数据，三个消耗指标的条件且要求大于xxx，则必定不符合，用款账户全部不匹配
        if (true === $check_gt) {
            return false;
        }
    } else {
        foreach ($use_account_ids as $one_account_id) {
            //没查到账号的消耗数据，且数值条件规则有
            if (!isset($account_cost_data[$one_account_id])) {
                if (true === $check_gt) {
                    $not_match_use_account[] = $one_account_id;
                    continue;
                }
            }

            foreach ($calc_condition as $item) {
                switch ($item['name']) {
                    case 'count_ad2':
                        if (1 === (int)$item['date_type']) {
                            //当天
                            $result = $account_cost_data[$one_account_id]->today_count_ad2 ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            //前三天
                            $result = $account_cost_data[$one_account_id]->three_day_count_ad2 ?? 0;
                        } else {
                            //总
                            $result = $account_cost_data[$one_account_id]->all_count_ad2 ?? 0;
                        }
                        break;
                    //消耗
                    case 'cost':
                        if (1 === (int)$item['date_type']) {
                            //当天
                            $result = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            //前三天
                            $result = $account_cost_data[$one_account_id]->three_day_cost ?? 0;
                        } else {
                            //总
                            $result = $account_cost_data[$one_account_id]->all_cost ?? 0;
                        }

                        break;
                    //平均日消耗
                    case 'avg_day_cost':
                        if (1 === (int)$item['date_type']) {
                            $result = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } else {
                            $result = Math::div($account_cost_data[$one_account_id]->three_day_cost ?? 0, 3);
                        }
                        break;
                    //首日ROI
                    case 'first_day_roi':
                        if (1 === (int)$item['date_type']) {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->today_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->today_cost ?? 0;
                        } elseif (2 === (int)$item['date_type']) {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->three_day_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->three_day_cost ?? 0;
                        } else {
                            $first_day_pay_money = $account_cost_data[$one_account_id]->all_first_day_pay_money ?? 0;
                            $cost = $account_cost_data[$one_account_id]->all_cost ?? 0;
                        }

                        $result = Math::rate($first_day_pay_money, $cost);
                        break;
                    //过去x小时无消耗
                    case 'last_hours_no_cost':
                        $result = strtotime($account_cost_data[$one_account_id]->last_cost_date_hour ?? '1970-01-01 08:00:00');
                        $item['value'] = time() - $item['value'] * 3600;
                        $item['operator'] = 'lt';
                        break;
                    //过去x天无消耗
                    case 'last_days_no_cost':
                        $result = strtotime($account_cost_data[$one_account_id]->last_cost_date_hour ?? '1970-01-01 08:00:00');
                        //模拟组装judgeCondition()函数的判断所需格式， 一起判断
                        $item['value'] = time() - $item['value'] * 86400;
                        $item['operator'] = 'lt';
                        break;
                    //过去x天无转入转出
                    case 'last_days_no_transfer':
                        $result = strtotime($account_cost_data[$one_account_id]->last_transaction_time ?? '1970-01-01 08:00:00');
                        //模拟组装judgeCondition()函数的判断所需格式， 一起判断
                        $item['value'] = time() - $item['value'] * 86400;
                        $item['operator'] = 'lt';
                        break;
                    default:
                        $msg = '数值指标中数据错误';
                        Helpers::getLogger('Auto-transfer')->error($msg);
                        throw new AppException($msg);
                }
                if (false === judgeCondition($result, $item)) {
                    $not_match_use_account[] = $one_account_id;
                    continue 2;
                }
            }

        }

        if ($account_cost_data->count() === count($not_match_use_account)) {
            return false;
        }
    }

    return $not_match_use_account;
}

/**
 * 检查余额条件
 * @param $use_account_ids
 * @param $reserve_account_ids
 * @param $balance_condition
 * @param $account_info
 * @return bool|array
 */
function checkBalance($use_account_ids, $reserve_account_ids, $balance_condition, $account_info)
{
    //循环检查余额和规则是否匹配, 不符合返回不符合的账号ID集合
    $match_use_account = [];
    $match_reserve_account = [];
    foreach ($balance_condition as $item) {
        $judge_account_ids = $item['name'] === 'reserve_account_balance' ? $reserve_account_ids : $use_account_ids;
        foreach ($judge_account_ids as $account_id) {
            if (true === judgeCondition($account_info[$account_id]->fund_type_ad_recharge, $item)) {
                if ($item['name'] === 'reserve_account_balance') {
                    $match_reserve_account[] = $account_id;
                } else {
                    $match_use_account[] = $account_id;
                }
            }
        }
        unset($account_id);
    }
    unset($item);

    //如果通过验证的备款或用款账号列表某一个为空, 直接返回false结束
    if (!$match_use_account || !$match_reserve_account) {
        return false;
    }

    return [
        'match_use_account' => $match_use_account,
        'match_reserve_account' => $match_reserve_account,
    ];
}

/**
 * 根据账号ID获取账号信息
 * @param $all_account_ids
 * @return array
 */
function getAccountInfoByAccountIds($all_account_ids)
{
    //获取账户信息
    $account_model = new OdsTencentAccountLogModel();
    $part_of_account_ids = collect($all_account_ids)->chunk(4000);
    $account_info = [];
    foreach ($part_of_account_ids as $one_set) {
        $one_part_db_balance = $account_model->getAccountTransferInfosInAccountIds($one_set->toArray(), false);
        $account_info = $one_part_db_balance->merge($account_info);
    }

    return $account_info->keyBy('account_id')->toArray();
}

/**
 * 判断数值条件
 * @param $result
 * @param $calc_condition
 * @return bool
 */
function judgeCondition($result, $calc_condition)
{

    switch ($calc_condition['operator']) {
        case 'lt':
            $match = $result < $calc_condition['value'];
            break;
        case 'gt':
            $match = $result > $calc_condition['value'];
            break;
        case 'eq':
            $match = $result == $calc_condition['value'];
            break;
        default:
            return false;
    }

    return $match;
}

manageExecuteFrequency();