<?php


// 5 * * * *
// 收集微信推送数据 在每个小时的第五分钟执行

use App\Model\HttpModel\Feishu\BotModel;
use App\Model\SqlModel\DatahubLY\DataCollectionNewModel as DatahubLYDataCollectionModel;
use App\Model\SqlModel\Tanwan\DataCollectionNewModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;

use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\MysqlConnection;
use App\Struct\RedisCache;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

MysqlConnection::setConnection();

function fix_dms_permission($configs)
{
    $game_permission = [];
    foreach ($configs as $config) {
        foreach ($config as $sub) {
            foreach ($sub['sub_config'] as $item) {
                $info = $item['info'];
                foreach ($info as $data) {
                    $platform = $data['platform'];
                    $main_game_id = $data['main_game_id'];

                    if (!isset($game_permission[$platform])) {
                        $game_permission[$platform] = [
                            'main_game_id' => $main_game_id
                        ];
                        continue;
                    }
                    $game_permission[$platform]['main_game_id'] = array_merge(
                        $game_permission[$platform]['main_game_id'],
                        $main_game_id
                    );
                }
            }
        }
    }
    return $game_permission;
}

function fix_ly_permission($configs)
{
    $game_permission = [];
    $agent_permission = [];

    foreach ($configs as $config) {
        foreach ($config as $sub) {
            foreach ($sub['sub_config'] as $item) {
                $info = $item['info'];

                foreach ($info as $data) {
                    $platform = $data['platform'];
                    $game_id = $data['game_id'];
                    if (!isset($game_permission[$platform])) {
                        $game_permission[$platform] = [
                            'game_id' => $game_id
                        ];
                    } else {
                        $game_permission[$platform]['game_id'] = array_merge(
                            $game_permission[$platform]['game_id'],
                            $game_id
                        );
                    }

                }
            }
        }
    }
    return [$game_permission, $agent_permission];
}

function construct_show_data(
    $platform,
    $root_game_id,
    $root_game_name,
    $main_game_id,
    $main_game_name,
    $os,
    $agent_group_id,
    $agent_id,
    $agent_leader_group_name,
    $tdate,
    $money,
    $ori_money,
    $reg_uid_count,
    $dby_second_login_rate,
    $dby_second_login_count,
    $dby_reg_uid_count,
    $reg_uid_new_pay_count,
    $uid_count,
    $old_uid_count,
    $total_pay_money,
    $first_day_pay_money,
    $open_service,
    $mix_id,
    $month_money,
    $month_ori_money,
    $month_total_pay_money,
    $day_pay_count,
    $month_reg_uid_count,
    $sort,
    $is_apportioned)
{
    return [
        "platform"                => $platform,
        "root_game_id"            => $root_game_id,
        "root_game_name"          => $root_game_name,
        "main_game_id"            => $main_game_id,
        "main_game_name"          => $main_game_name,
        "os"                      => $os,
        "agent_group_id"          => $agent_group_id,
        "agent_id"                => $agent_id,
        "agent_leader_group_name" => $agent_leader_group_name,
        "tdate"                   => $tdate,
        "money"                   => $money,
        "ori_money"               => $ori_money,
        "reg_uid_count"           => $reg_uid_count,
        "dby_second_login_rate"   => $dby_second_login_rate,
        "dby_second_login_count"  => $dby_second_login_count,
        "dby_reg_uid_count"       => $dby_reg_uid_count,
        "reg_uid_new_pay_count"   => $reg_uid_new_pay_count,
        "uid_count"               => $uid_count,
        "old_uid_count"           => $old_uid_count,
        "total_pay_money"         => $total_pay_money,
        "first_day_pay_money"     => $first_day_pay_money,
        "open_service"            => $open_service,
        "mix_id"                  => $mix_id,
        "month_money"             => $month_money,
        "month_ori_money"         => $month_ori_money,
        "month_total_pay_money"   => $month_total_pay_money,
        "day_pay_count"           => $day_pay_count,
        "month_reg_uid_count"     => $month_reg_uid_count,
        "sort"                    => $sort,
        'is_apportioned'          => $is_apportioned,
    ];
}

function fix_show_data(Collection $list, $date, $is_apportioned = 1)
{
    $ret = [];

    foreach ($list as $item) {
        $d = construct_show_data(
            $item->platform,
            $item->root_game_id,
            $item->root_game_name,
            $item->main_game_id,
            $item->main_game_name,
            $item->os,
            $item->agent_group_id,
            $item->agent_id,
            $item->agent_leader_group_name,
            $date,
            $item->money,
            $item->ori_money,
            $item->reg_uid_count,
            $item->dby_second_login_rate,
            $item->dby_second_login_count,
            $item->dby_reg_uid_count,
            $item->reg_uid_new_pay_count,
            $item->uid_count,
            $item->old_uid_count,
            $item->total_pay_money,
            $item->first_day_pay_money,
            $item->open_service,
            $item->mix_id,
            $item->month_money,
            $item->month_ori_money,
            $item->month_total_pay_money,
            $item->day_pay_count,
            $item->month_reg_uid_count,
            0,
            $is_apportioned,
        );
        $ret[] = $d;
    }
    return $ret;
}

function collect_dms_data($configs, $start_date, $end_date)
{
    $game_permission = fix_dms_permission($configs);
    $data = (new DataCollectionNewModel())->collectWeChatReport($game_permission, $start_date, $end_date);
    $official_data = (new DataCollectionNewModel())->collectApportionWeChatReport($game_permission, $start_date, $end_date);
    $data_list = [$data, $official_data ?? Collection::make()];
    $all_dimension = ["platform",
                      "root_game_id",
                      "main_game_id",
                      "os",
                      "agent_group_id",
                      "agent_id",
                      "agent_leader_group_name"];
    $all_target = ["money",
                   "ori_money",
                   "month_money",
                   "month_ori_money",
                   "reg_uid_count",
                   "first_day_pay_money",
                   "reg_uid_new_pay_count",
                   "uid_count",
                   "old_uid_count",
                   "dby_second_login_count",
                   "dby_reg_uid_count",
                   "dby_second_login_rate",
                   "total_pay_money",
                   "day_pay_count",
                   "month_total_pay_money",
                   "open_service",
                   "month_reg_uid_count",
    ];
    $list = mergeDataList($data_list, $all_dimension, $all_target);
    return $list;
}

function collect_dms_data_no_apportion($configs, $start_date, $end_date)
{
    $game_permission = fix_dms_permission($configs);
    return (new DataCollectionNewModel())->collectWeChatReport($game_permission, $start_date, $end_date, 0);
}

/**
 * 合并多个结果集 直接相加
 *
 * @param array $data_list 数据列表合计 常规查询的list要放在第一位 不参与计算的字段包括维度都在第一个列表拿
 * @param array $dimension 维度
 * @param array $target 要计算的列
 *
 * @return Collection
 */
function mergeDataList($data_list, $dimension, $target)
{
    $res_list = [];              // 结果集
    $list_dimension_keys = [];   // 按维度分行后的列表的keys

    // 按维度分行
    foreach ($data_list as &$item) {
        if (!empty($dimension)) {
            $item = DimensionTool::groupByDimension($item, $dimension);
            $list_dimension_keys = array_merge($list_dimension_keys, array_keys($item));
        }
    }

    // 空维度
    if (empty($dimension)) {
        $res_list[0] = $data_list[0][0] ? (array)$data_list[0][0] : []; // 不参与计算的字段默认拿第一个数据列表的值
        foreach ($target as $field) {
            $field_res = 0;
            foreach ($data_list as $list) {
                $field_res += $list[0]->$field ?? 0;
            }
            if (strstr($field, 'money') !== FALSE) {
                $res_list[0][$field] = $field_res;
            } else {
                $res_list[0][$field] = floor($field_res);
            }
        }
    } else {
        // 有维度的要按维度分好行之后按维度去计算 按每一个维度key去循环计算每一个字段值
        $list_dimension_keys = array_unique($list_dimension_keys);
        foreach ($list_dimension_keys as $dimension_key) {
            foreach ($data_list as $list) {
                if (empty($list)) continue; // 空的直接跳过

                // 不参与计算的字段默认拿第一个数据列表的值 拿不到就拿后面的。
                if (!isset($res_list[$dimension_key]) || empty($res_list[$dimension_key])) {
                    $res_list[$dimension_key] = isset($list[$dimension_key][0]) ? (array)$list[$dimension_key][0] : [];
                }

            }
            foreach ($target as $field) {
                $field_res = 0;
                foreach ($data_list as $list) {
                    if (empty($list)) continue; // 空的直接跳过

                    $field_res += $list[$dimension_key][0]->$field ?? 0;
                }

                $res_list[$dimension_key][$field] = $field_res;
            }
        }
    }
    // 转换成stdclass
    foreach ($res_list as $dimension_key => $data) {
        $res_list[$dimension_key] = (object)$data;
    }
    return Collection::make(array_values($res_list));
}

function construct_show_data_ly(
    $platform,
    $game_id,
    $game_name,
    $main_game_id,
    $main_game_name,
    $agent_group_id,
    $agent_name,
    $agent_id,
    $channel_id,
    $channel_name,
    $tdate,
    $money,
    $ori_money,
    $reg_uid_count,
    $dby_second_login_rate,
    $dby_second_login_count,
    $dby_reg_uid_count,
    $reg_uid_new_pay_count,
    $uid_count,
    $old_uid_count,
    $total_pay_money,
    $first_day_pay_money,
    $open_service,
    $mix_id,
    $month_money,
    $month_ori_money,
    $month_total_pay_money,
    $day_pay_count,
    $sort)
{
    return [
        "platform"               => $platform,
        "game_id"                => $game_id,
        "game_name"              => $game_name,
        "main_game_id"           => $main_game_id,
        "main_game_name"         => $main_game_name,
        "agent_group_id"         => $agent_group_id,
        "agent_name"             => $agent_name,
        "agent_id"               => $agent_id,
        "channel_id"             => $channel_id,
        "channel_name"           => $channel_name,
        "tdate"                  => $tdate,
        "money"                  => $money,
        "ori_money"              => $ori_money,
        "reg_uid_count"          => $reg_uid_count,
        "dby_second_login_rate"  => $dby_second_login_rate,
        "dby_second_login_count" => $dby_second_login_count,
        "dby_reg_uid_count"      => $dby_reg_uid_count,
        "reg_uid_new_pay_count"  => $reg_uid_new_pay_count,
        "uid_count"              => $uid_count,
        "old_uid_count"          => $old_uid_count,
        "total_pay_money"        => $total_pay_money,
        "first_day_pay_money"    => $first_day_pay_money,
        "open_service"           => $open_service,
        "mix_id"                 => $mix_id,
        "month_money"            => $month_money,
        "month_ori_money"        => $month_ori_money,
        "month_total_pay_money"  => $month_total_pay_money,
        "day_pay_count"          => $day_pay_count,
        "sort"                   => $sort
    ];
}

function fix_show_data_ly(Collection $list, $date)
{
    $ret = [];

    foreach ($list as $item) {
        $d = construct_show_data_ly(
            $item->platform,
            $item->game_id,
            $item->game_name,
            $item->main_game_id,
            $item->main_game_name,
            $item->agent_group_id,
            $item->agent_name,
            $item->agent_id,
            $item->channel_id,
            $item->channel_name,
            $date,
            $item->money,
            $item->ori_money,
            $item->reg_uid_count,
            $item->dby_second_login_rate,
            $item->dby_second_login_count,
            $item->dby_reg_uid_count,
            $item->reg_uid_new_pay_count,
            $item->uid_count,
            $item->old_uid_count,
            $item->total_pay_money,
            $item->first_day_pay_money,
            $item->open_service,
            $item->mix_id,
            $item->month_money,
            $item->month_ori_money,
            $item->month_total_pay_money,
            $item->day_pay_count,
            0
        );
        $ret[] = $d;
    }
    return $ret;
}

function collect_ly_data($config, $start_date, $end_date)
{
    $model = new DatahubLYDataCollectionModel();
    [$game_permission, $agent_permission] = fix_ly_permission($config);

    return $model->collectWeChatReport($game_permission, $agent_permission, $start_date, $end_date);
}

function collect_data($start_date, $end_date, $date, $hour)
{
    global $logger;
    $dms_configs = $ly_configs = $ret = [];
    $config_model = new WechatPushConfigNewModel();

    $list = $config_model->getAll();

    foreach ($list as $item) {
        //非零点的收集只收集开启小时推送的
        $push_hour = (int)$item->push_hour;
        if ($hour > 0 && $push_hour === 0) {
            continue;
        }
        $module = $item->module;
        $str_config = $item->config;
        if ($module == "dms") {
            $dms_configs[] = json_decode($str_config, true);
        } else if ($module == "ly") {
            $ly_configs[] = json_decode($str_config, true);
        } else {
            continue;
        }
    }

    $logger->info('开始收集dms分摊数据', ['state_date' => $start_date, 'end_date' => $end_date]);
    $data = collect_dms_data($dms_configs, $start_date, $end_date);
    $dms_data = fix_show_data($data, $date, 1);
    $logger->info('dms分摊数据收集完成', ['state_date' => $start_date, 'end_date' => $end_date]);

    // 没有分摊的 dms数据
    $logger->info('开始收集dms无分摊数据', ['state_date' => $start_date, 'end_date' => $end_date]);
    $data_no_apportion = collect_dms_data_no_apportion($dms_configs, $start_date, $end_date);
    $data_no_apportion = fix_show_data($data_no_apportion, $date, 0);
    $dms_data = array_merge($dms_data, $data_no_apportion);
    $logger->info('dms无分摊数据收集完成', ['state_date' => $start_date, 'end_date' => $end_date]);

    $logger->info('开始收集ly分摊数据', ['state_date' => $start_date, 'end_date' => $end_date]);
    $data = collect_ly_data($ly_configs, $start_date, $end_date);
    $logger->info('ly分摊数据收集完成', ['state_date' => $start_date, 'end_date' => $end_date]);
    $ly_data = fix_show_data_ly($data, $date);

    return [$dms_data, $ly_data];
}


function executeTask($logger, $hour, $date)
{
    $logger->info('任务开始', ['hour' => $hour, 'date' => $date]);

    if ($hour == 0) {
        $pre_start_date = date("Y-m-d", strtotime("-3 days", strtotime($date)));
        $start_date = date("Y-m-d", strtotime("-2 days", strtotime($date)));
        $end_date = date("Y-m-d", strtotime("-1 days", strtotime($date)));
        [$dms_data1, $ly_data1] = collect_data($pre_start_date, $start_date, $end_date, $hour);
        [$dms_data2, $ly_data2] = collect_data($start_date, $end_date, $date, $hour);
        $dms_data = array_merge($dms_data1, $dms_data2);
        $ly_data = array_merge($ly_data1, $ly_data2);
    } else {
        $start_date = date("Y-m-d", strtotime("-1 days", strtotime($date)));
        $end_date = date("Y-m-d", strtotime($date));
        [$dms_data, $ly_data] = collect_data($start_date, $end_date, $date, $hour);
    }

    $logger->info('所有数据收集完成，开始入表');
    $log_model = new WechatPushDataDmsModel();
    $ret = [];
    foreach ($dms_data as $item) {
        $ret[] = $item;
    }
    $data = $ret;
    foreach ($data as &$item) {
        $item['thour'] = $hour;
    }
    $chunks = array_chunk($data, 100);

    $logger->info('入表dms数据开始');

    foreach ($chunks as $chunk) {
        $log_model->batchAdd($chunk);
    }
    $logger->info('入表dms数据结束');
    $log_model = new WechatPushDataLyModel();
    $ret = [];
    unset($item);
    foreach ($ly_data as $item) {
        $ret[] = $item;
    }
    $data = $ret;
    foreach ($data as &$item) {
        $item['thour'] = $hour;
    }
    $chunks = array_chunk($data, 100);

    $logger->info('入表ly数据开始');

    foreach ($chunks as $chunk) {
        $log_model->batchAdd($chunk);
    }
    $logger->info('入表ly数据结束');

    $logger->info('所有数据入表完成');
    $logger->info('开始删除数据');
    //删除30天前的数据
    (new WechatPushDataDmsModel())->delete();
    (new WechatPushDataLyModel())->delete();
    $logger->info('数据删除完成');
    $logger->info('任务结束', ['hour' => $hour, 'date' => $date]);

}


$max_retries = 5; // 最大重试次数
$retry_delay = 10; // 重试间隔时间，单位为秒

$attempt = 0;
$logger = Helpers::getLogger('push_data_collect');

$hour = (int)date('G');
$date = date("Y-m-d");
while ($attempt < $max_retries) {
    try {
        executeTask($logger, $hour, $date);

        // 如果是 0 点的数据,标记一下任务已完成
        if ($hour === 0) {
            RedisCache::getInstance()->set('collect_push_sum_data' . date("Ymd"), time(), ['EX' => 86400]);
        }
        break;
    } catch (\Throwable $throwable) {
        $logger->error('任务执行失败,准备重试', ['hour' => $hour, 'error_message' => $throwable->getMessage()]);
        $attempt++;
        sleep($retry_delay); // 等待一段时间后重试
    }

}
if ($attempt == $max_retries) {
    $logger->error("任务重试 {$max_retries} 次后仍然失败");

    $bot_model = new BotModel();
    $webhook = 'https://open.feishu.cn/open-apis/bot/v2/hook/c620d408-971d-4cf5-82ce-5c24d43b42e0';

    $content = "任务重试 {$max_retries} 次后仍然失败。\ntdate: {$date}\nhour: $hour";

    $bot_model->textMessage($webhook, $content);
}

