<?php

/**
 * 0 12 * * * php pushWechatReport.php 7
 * 5 *\/3 * * * php pushWechatReport.php 8
 */

use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Logic\DMS\ReportLogic;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Task\NoticeTask;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$notice_type = intval($argv[1]);
const IGNORE_HOURS = [3, 6];

$hour = intval(date('H'));
if ($notice_type === NoticeMessageModel::TYPE_WECHAT_HOURLY && $hour === 12) {
    return;
}

if (in_array($hour, IGNORE_HOURS)) {
    return;
}

MysqlConnection::setConnection();

$notice_task = new NoticeTask();
$user_model = new UserModel();
$logic = new ReportLogic();

if ($hour === 0) {
    $start_time = strtotime(date("Y-m-d 23:00:00", strtotime('-1 day')));
    $start_search_time = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
    $end_search_time = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
    $last_start_search_time = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
    $last_end_search_time = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));
} else {
    $start_time = strtotime(date("Y-m-d H:00:00"));
    $start_search_time = strtotime(date('Y-m-d 00:00:00'));
    $end_search_time = strtotime(date('Y-m-d 23:59:59'));
    $last_start_search_time = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
    $last_end_search_time = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
}
$url = $logic->getOpenIdUrl($start_time);

$user_list = $user_model->getAllBindWeChatNormal()->toArray();
$super_user_list = $user_model->getAllBindWeChatSuper()->toArray();
$user_list = array_merge($user_list, $super_user_list);
if ($hour === 0) {
    $now = date("Y年m月d日（统计日期）", strtotime('-2 day'));
} else {
    $now = date("Y年m月d日（统计日期）", strtotime('-1 day'));
}

foreach ($user_list as $user_info) {

    $dims = json_decode($user_info->daily_report_dim);
    if (!is_array($dims)) {
        $dims = [$dims];
    }

    if (
        $notice_type === NoticeMessageModel::TYPE_WECHAT_HOURLY
        && !in_array(UserModel::REPORT_AGENT_LEADER_DIM, $dims)
    ) {
        continue;
    }

    $data = $logic->getWeChatPanelInfo(
        $start_search_time,
        $end_search_time,
        $user_info
    );

    $last_data = $logic->getWeChatPanelInfo(
        $last_start_search_time,
        $last_end_search_time,
        $user_info
    );

    if ($data) {
        $money_rate = Math::rate($data->first_day_pay_money, $data->cost);
        $cost = Math::div($data->cost, 10000);
        $income = Math::div($data->income, 10000);
        $reg_count = Math::div($data->reg_uid_count, 10000);
        $DAU = Math::div($data->DAU, 10000);
        $money = $money_rate . '%';

        $last_money_rate = Math::rate($last_data->first_day_pay_money, $last_data->cost);
        $cost_circle = Math::circle($data->cost, $last_data->cost);
        $income_circle = Math::circle($data->income, $last_data->income);
        $reg_uid_count_circle = Math::circle($data->reg_uid_count, $last_data->reg_uid_count);
        $DAU_circle = Math::circle($data->DAU, $last_data->DAU);
        $money_circle = Math::circle(floatval($money_rate), floatval($last_money_rate));

        if ($cost_circle < 0) {
            $cost_circle = '（↓' . abs($cost_circle) . '%）';
        } else {
            $cost_circle = '（↑' . $cost_circle . '%）';
        }

        if ($income_circle < 0) {
            $income_circle = '（↓' . abs($income_circle) . '%）';
        } else {
            $income_circle = '（↑' . $income_circle . '%）';
        }

        if ($reg_uid_count_circle < 0) {
            $reg_uid_count_circle = '（↓' . abs($reg_uid_count_circle) . '%）';
        } else {
            $reg_uid_count_circle = '（↑' . $reg_uid_count_circle . '%）';
        }

        if ($DAU_circle < 0) {
            $DAU_circle = '（↓' . abs($DAU_circle) . '%）';
        } else {
            $DAU_circle = '（↑' . $DAU_circle . '%）';
        }

        if ($money_circle < 0) {
            $money_circle = '（↓' . abs($money_circle) . '%）';
        } else {
            $money_circle = '（↑' . $money_circle . '%）';
        }

        $sum_data = "消耗：{$cost} {$cost_circle}\n收入：{$income} {$income_circle}\n新增：{$reg_count} {$reg_uid_count_circle}\n活跃：{$DAU} {$DAU_circle}\n回本：{$money} {$money_circle}";
    } else {
        $sum_data = '';
    }

    if ($sum_data) {
        // 泽达情报局推送
        $notice_task->pushWeChat([
            'user_id'     => $user_info->id,
            'type'        => $notice_type,
            'template_id' => TemplateModel::STATISTICS_TEMPLATE,
            'url'         => $url,
            'config_name' => 'qingbaoju',
            'data'        => [
                "first"    => ["value" => "昨日运营收入", "color" => "#000000"],
                "keyword1" => ["value" => "{$user_info->name}", "color" => "#000000"],
                "keyword2" => ["value" => "{$now}", "color" => "#000000"],
                "keyword3" => ["value" => "{$sum_data}", "color" => "#169115"],
                "remark"   => ["value" => "点击进入查看详情", "color" => "#000000"],
            ]
        ]);

        // 中旭情报局推送
        if ($hour === 0) {
            $now = date("Y-m-d", strtotime('-2 day'));
        } else {
            $now = date("Y-m-d", strtotime('-1 day'));
        }
        $url = $logic->getZXOpenIdUrl($start_time);
        $notice_task->pushWeChat([
            'user_id'     => $user_info->id,
            'type'        => $notice_type,
            'template_id' => TemplateModel::ZX_STATISTICS_TEMPLATE,
            'url'         => $url,
            'config_name' => 'zx_qbj',
            'data'        => [
                "thing6"  => ["value" => "{$user_info->name}", "color" => "#000000"],
                "time7"   => ["value" => "{$now}", "color" => "#000000"],
                "thing10" => ["value" => "{$sum_data}", "color" => "#169115"],
            ]
        ]);
    }
}
