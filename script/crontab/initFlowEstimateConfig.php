<?php
/**
 * 05 0 * * *
 *
 * 初始化流水预估的配置
 * 每天凌晨3点40分跑一次
 */

use App\Model\SqlModel\Tanwan\DataAnalysis\FlowEstimateModel;
use App\Model\SqlModel\Zeda\FlowEstimateConfigModel;
use App\MysqlConnection;
use App\Param\DMS\FlowEstimateParam;
use App\Utils\DimensionTool;
use App\Utils\Helpers;
use App\Utils\Math;


require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$data_model = new FlowEstimateModel();
$config_model = new FlowEstimateConfigModel();
$logger = Helpers::getLogger('flow-init');

$all_day_ago_list = [];

foreach ([1, 2, 3] as $dimension_type) {
    $param = new FlowEstimateParam();
    $param->game_permission = -1;
    $param->dimension_type = $dimension_type;
    // 获取最近7天的均值数据
    $day_ago_data = $data_model->getAvgLastDayData($param);
    $sql = $day_ago_data['sql'];
    $logger->info('sql日志', [$sql]);
    $day_ago_list = $day_ago_data['list'];
    $now = date("Y-m-d H:i:s");
    $insert_data = [];

    // 组装插入数据
    foreach ($day_ago_list as $key => $item) {
        $item->dimension_type = $dimension_type;
        // 维度赋值,初始化
        $insert_data[$dimension_type][$key] = [
            'platform'           => $item->platform,
            'plat_id'            => $item->plat_id,
            'root_game_id'       => $item->root_game_id,
            'root_game_name'     => $item->root_game_name,
            'main_game_id'       => $item->main_game_id,
            'main_game_name'     => $item->main_game_name,
            'contract_game_name' => $item->contract_game_name,
            'clique_id'          => $item->clique_id,
            'clique_name'        => $item->clique_name,
            'avg_cost'           => Math::decimal($item->avg_reg_uid_count * $item->avg_reg_uid_value, 2),
            'avg_reg_uid_count'  => $item->avg_reg_uid_count,
            'avg_reg_uid_value'  => $item->avg_reg_uid_value,
            'create_time'        => $now,
            'update_time'        => $now,
            'dimension_type'     => $dimension_type,
        ];
    }
    // 组装完存起来
    $all_day_ago_list[$dimension_type] = $day_ago_list;

}
try {
    // 入库，有新数据才需要插入 旧数据忽略
    foreach ($insert_data as $dimension_type => $insert_list) {
        $config_model->addMultiple($insert_list);
    }

    $logger->info('入库成功，开始获取数据');
    // 插入后获取所有配置 更新注册数和成本
    $config_list = $config_model->getAllConfig();
    // 按维度做个索引
    $config_list = DimensionTool::groupByDimension($config_list, ['platform', 'main_game_id', 'dimension_type']);

    // 多个统计口径的列表
    foreach ($all_day_ago_list as $dimension_type => $day_ago_list) {
        $day_ago_list = DimensionTool::groupByDimension($day_ago_list, ['platform', 'main_game_id', 'dimension_type']);
        $logger->info('数据组装成功，开始更新数据');

        $update_data = [];
        foreach ($day_ago_list as $key => $item) {
            // 要从config list里面拿消耗 从day_ago_list拿注册成本 然后去更新注册数
            if (isset($config_list[$key])) {
                // 维度赋值,初始化
                $update_data[] = [
                    'platform'           => $item[0]->platform,
                    'plat_id'            => $item[0]->plat_id,
                    'root_game_id'       => $item[0]->root_game_id,
                    'root_game_name'     => $item[0]->root_game_name,
                    'main_game_id'       => $item[0]->main_game_id,
                    'main_game_name'     => $item[0]->main_game_name,
                    'contract_game_name' => $item[0]->contract_game_name,
                    'clique_id'          => $item[0]->clique_id,
                    'clique_name'        => $item[0]->clique_name,
                    'dimension_type'     => $dimension_type,
                    'avg_cost'           => $config_list[$key][0]->avg_cost,
                    // 如果消耗大于0 就按消耗来算注册数，否则直接取数据库里面的注册数，这里主要是考虑到自然量的问题
                    'avg_reg_uid_count'  => $config_list[$key][0]->avg_cost > 0 ? (Math::div($config_list[$key][0]->avg_cost, $item[0]->avg_reg_uid_value, 0)) : $item[0]->avg_reg_uid_count,
                    'avg_reg_uid_value'  => $item[0]->avg_reg_uid_value,
                    'update_time'        => $now,
                ];
            }
        }
    }
    $config_model->updateMultiple($update_data);
    MysqlConnection::getConnection('default')->commit();
    $logger->info('数据更新成功，任务结束');
    exit;
} catch (\Exception $e) {
    try {
        $logger->error('入库异常，回滚数据, 异常信息：' . substr($e->getMessage(), 0, 1000));
        MysqlConnection::getConnection('default')->rollBack();
        exit;
    } catch (\Exception $e) {
        $logger->error('事务回滚异常！');
        exit;
    }
}
