<?php

/**
 * 获取中旭飞书主体的用户union_id
 * 0 23 * * * php feishuUserUnionId.php
 * @server ************ zx-script
 */

use App\Constant\MediaType;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\OAModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$logger = Helpers::getLogger('feishu');
$logger->info('zx飞书用户union_id任务开始执行');

$media_developer_account_model = new MediaDeveloperAccountModel();
$zx_app_data = $media_developer_account_model->getDataByPlatformMediaCompany('TW', MediaType::FEISHU, '中旭未来组织');
if (!$zx_app_data) {
    $logger->error("zx飞书用户uninon-id运行失败，zx配置为空");
}
$app_id = $zx_app_data->app_id;
$app_secret = $zx_app_data->app_secret;

$user_model = new UserModel();
$auth_model = new AuthModel();
$oa_model = new OAModel();
$tenant_access_token = $auth_model->tenantAccessTokenByAppType($auth_model::INTERNAL, $app_id, $app_secret);

$user_group_list = $user_model->getAllByState(1)
    ->filter(function ($item) {
        return empty($item->feishu_union_id) && !empty($item->mobile);
    })
    ->mapWithKeys(function ($item) {
        return [
            $item->id => $item->mobile,
        ];
    })
    ->chunk(50);

$i = 1;
foreach ($user_group_list as $item_list) {
    $mobiles = $item_list->values()->toArray();
    $resp_data = $oa_model->batchGetId($tenant_access_token, 'union_id', $mobiles);
    if ($resp_data['code'] === 0) {
        foreach ($resp_data['data']['user_list'] as $resp_item) {
            $mobile = $resp_item['mobile'] ?? '';
            $union_id = $resp_item['user_id'] ?? '';
            $user_id = $item_list->search($mobile);
            if ($user_id && $union_id) {
                $feishu_union_id = 'TW-'. $union_id;
//                echo $user_id . "\t" . $mobile . "\t" . $feishu_union_id . "\r\n";
                $user_model->updateFeishuUnionID($user_id, $feishu_union_id);
            }
        }
    }

    // 最大频率是50次/秒
    if ($i % 50 === 0) {
        sleep(1);
    }
    $i++;
}
