<?php
/**
 * 每日0点执行，重置媒体账号表每日转账金额
 */

use App\Model\RedisModel\AutoTransferRuleRedisModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

/**
 * 每天零点刷新当天已转账金额
 */
function flushAccountDailyTransferAmount()
{
    (new MediaAccountModel())->flushAccountDailyTransferAmount('today_transfer_in_amount');
    (new AutoTransferRuleRedisModel())->del(AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT);
    (new AutoTransferRuleRedisModel())->del('tencent_' . AutoTransferRuleRedisModel::AUTO_TRANSFER_TODAY_TRANSFER_IN_AMOUNT);

    ob_start();
    echo date('Y-m-d H:i:s') . "已刷新账户转账限额" . PHP_EOL;
    ob_flush();
}

flushAccountDailyTransferAmount();