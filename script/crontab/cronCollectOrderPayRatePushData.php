<?php


// 收集微信推送数据
// * * * * * php cronCollectOrderPayRatePushData.php

use App\Model\SqlModel\DatahubLY\DataCollectionNewModel as LYDataCollectionModel;
use App\Model\SqlModel\DatahubLY\OrderPayRatePushDataLyModel;
use App\Model\SqlModel\Tanwan\DataCollectionNewModel as DmsDataCollectionModel;
use App\Model\SqlModel\Tanwan\OrderPayRatePushDataDmsModel;
use App\Model\SqlModel\Zeda\OrderPayRatePushConfigModel;
use App\MysqlConnection;
use Illuminate\Support\Collection;

require dirname(__DIR__).'/../common/init.php';
require dirname(__DIR__).'/process/utils/common.php';

date_default_timezone_set('PRC');

MysqlConnection::setConnection();

function fix_dms_permission($configs)
{
    $game_permission = [];
    foreach ($configs as $config) {
        foreach ($config as $sub) {
            foreach ($sub['sub_config'] as $item) {
                $info = $item['info'];
                foreach ($info as $data) {
                    $platform = $data['platform'];
                    $main_game_id = $data['main_game_id'];

                    if (!isset($game_permission[$platform])) {
                        $game_permission[$platform] = [
                            'main_game_id' => $main_game_id,
                        ];
                        continue;
                    }
                    $game_permission[$platform]['main_game_id'] = array_merge(
                        $game_permission[$platform]['main_game_id'],
                        $main_game_id
                    );
                }
            }
        }
    }

    return $game_permission;
}

function fix_ly_permission($configs)
{
    $game_permission = [];

    foreach ($configs as $config) {
        foreach ($config as $sub) {
            foreach ($sub['sub_config'] as $item) {
                $info = $item['info'];

                foreach ($info as $data) {
                    $platform = $data['platform'];
                    $game_id = $data['game_id'];

                    if (!isset($game_permission[$platform])) {
                        $game_permission[$platform] = [
                            'game_id' => $game_id,
                        ];
                    } else {
                        $game_permission[$platform]['game_id'] = array_merge(
                            $game_permission[$platform]['game_id'],
                            $game_id
                        );
                    }
                }
            }
        }
    }

    return $game_permission;
}

function construct_dms_show_data(
    $platform,
    $clique_id,
    $clique_name,
    $root_game_id,
    $root_game_name,
    $main_game_id,
    $main_game_name,
    $os,
    $tdate,
    $order_pay_count,
    $order_count,
    $order_pay_rate
): array {
    return [
        "platform"        => $platform,
        "clique_id"       => $clique_id,
        "clique_name"     => $clique_name,
        "root_game_id"    => $root_game_id,
        "root_game_name"  => $root_game_name,
        "main_game_id"    => $main_game_id,
        "main_game_name"  => $main_game_name,
        "os"              => $os,
        "tdate"           => $tdate,
        "order_pay_count" => $order_pay_count,
        "order_count"     => $order_count,
        "order_pay_rate"  => $order_pay_rate,
    ];
}

function fix_dms_show_data(Collection $list): array
{
    $ret = [];

    foreach ($list as $item) {
        $d = construct_dms_show_data(
            $item->platform,
            $item->clique_id,
            $item->clique_name,
            $item->root_game_id,
            $item->root_game_name,
            $item->main_game_id,
            $item->main_game_name,
            $item->os,
            $item->tdate,
            $item->order_pay_count,
            $item->order_count,
            bcdiv($item->order_pay_count, $item->order_count, 4),
        );
        $ret[] = $d;
    }

    return $ret;
}

function collect_dms_data($configs, $start_date, $end_date)
{
    $game_permission = fix_dms_permission($configs);

    return (new DmsDataCollectionModel())->collectOrderPayRateReport($game_permission, $start_date, $end_date);
}

function construct_ly_show_data(
    $platform,
    $clique_id,
    $clique_name,
    $root_game_id,
    $root_game_name,
    $main_game_id,
    $main_game_name,
    $os,
    $tdate,
    $order_pay_count,
    $order_count,
    $order_pay_rate
): array {
    return [
        "platform"        => $platform,
        "clique_id"       => $clique_id,
        "clique_name"     => $clique_name,
        "root_game_id"    => $root_game_id,
        "root_game_name"  => $root_game_name,
        "main_game_id"    => $main_game_id,
        "main_game_name"  => $main_game_name,
        "os"              => $os,
        "tdate"           => $tdate,
        "order_pay_count" => $order_pay_count,
        "order_count"     => $order_count,
        "order_pay_rate"  => $order_pay_rate,
    ];
}

function fix_ly_show_data(Collection $list): array
{
    $ret = [];

    foreach ($list as $item) {
        $d = construct_ly_show_data(
            $item->platform,
            $item->clique_id,
            $item->clique_name,
            $item->root_game_id,
            $item->root_game_name,
            $item->main_game_id,
            $item->main_game_name,
            $item->os,
            $item->tdata,
            $item->order_pay_count,
            $item->order_count,
            $item->order_pay_rate,
        );
        $ret[] = $d;
    }

    return $ret;
}


function collect_ly_data($config, $start_date, $end_date)
{
    $game_permission = fix_ly_permission($config);

    return (new LYDataCollectionModel())->collectOrderPayRateReport($game_permission, $start_date, $end_date);
}

function collect_data($start_date, $end_date, $hour)
{
    $dms_configs = $ly_configs = [];
    $config_model = new OrderPayRatePushConfigModel();

    $list = $config_model->getAll();

    foreach ($list as $item) {
        //非零点的收集只收集开启小时推送的
        $push_hour = (int)$item->push_hour;
        if ($hour > 0 && $push_hour === 0) {
            continue;
        }
        $module = $item->module;
        $str_config = $item->config;
        switch ($module) {
            case "dms":
                $dms_configs[] = json_decode($str_config, true);
                break;
            case "ly" :
                $ly_configs[] = json_decode($str_config, true);
                break;
            default :
        }
    }

    $data = collect_dms_data($dms_configs, $start_date, $end_date);
    $dms_data = fix_dms_show_data($data);

    $data = collect_ly_data($ly_configs, $start_date, $end_date);
    $ly_data = fix_ly_show_data($data);

    return [$dms_data, $ly_data];
}


$hour = (int)date('G');
$date = date("Y-m-d");
if ($hour == 0) {
    $start_date = date("Y-m-d", strtotime("-1 days"));
} else {
    $start_date = date("Y-m-d",);
}
[$dms_data, $ly_data] = collect_data($start_date, $start_date.' 23:59:59', $hour);

$dms_data_model = new OrderPayRatePushDataDmsModel();
$ret = [];
foreach ($dms_data as $item) {
    $ret[] = $item;
}
$data = $ret;
foreach ($data as &$item) {
    $item['thour'] = $hour;
}
$chunks = array_chunk($data, 100);
foreach ($chunks as $chunk) {
    $dms_data_model->batchAdd($chunk);
}

$ly_data_model = new OrderPayRatePushDataLyModel();
$ret = [];
unset($item);
foreach ($ly_data as $item) {
    $ret[] = $item;
}
$data = $ret;
foreach ($data as &$item) {
    $item['thour'] = $hour;
}
$chunks = array_chunk($data, 100);
foreach ($chunks as $chunk) {
    $ly_data_model->batchAdd($chunk);
}

//删除30天前的数据
$dms_data_model->delete();
$ly_data_model->delete();
