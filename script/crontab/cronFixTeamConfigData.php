<?php

// 0 21 * * *
// 修复团队配置推送数据

use App\Logic\TeamDataCollectLogic;
use App\Model\SqlModel\Tanwan\DwdWechatTeamPushModel;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__).'/../common/init.php';
require dirname(__DIR__).'/process/utils/common.php';

date_default_timezone_set('PRC');

MysqlConnection::setConnection();

$logger = Helpers::getLogger('team_config_task');
$logger->info('任务开始');

$log_model = new DwdWechatTeamPushModel();
$logic = new TeamDataCollectLogic();


echo "fix data begin======".date('Y-m-d H:i:s')."======================>".PHP_EOL;
$cur_hour = 0;
$days = 2;

try {
    for ($i = 1; $i <= $days; $i++) {
        $start_date = $end_date = date("Y-m-d", strtotime("-{$i} days"));
        $logic->startCollectTeamData($log_model, $logger, $start_date, $end_date, $cur_hour);
    }
} catch (\Throwable $exception) {
    $logger->err($exception->getMessage());
}

$logger->info('任务结束');

exit;
