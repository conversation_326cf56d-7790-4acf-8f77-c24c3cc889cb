<?php
/**
 * 每小时更新KPI的值
 */

use App\Model\SqlModel\Zeda\KpiModel;
use App\MysqlConnection;
use App\Logic\DMS\KPILogic;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();


// 一个个处理 先记录日志
Helpers::getLogger('kpi')->info('crontab update current value start', ['message' => '同步开始']);

$kpi_model = new KpiModel();
$kpi_list = $kpi_model->getAllByMonth(strtotime(date('Y-m-01')));

$kpi_service = new KPILogic();
$kpi_service->updateKPIValue($kpi_list);

Helpers::getLogger('kpi')->info('crontab update current value succeed', ['message' => '同步完成']);
