<?php
/**
 * 每天凌晨5点运行
 * 找出可以复用的site_id
 */

use App\MysqlConnection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

echo date('Y-m-d H:i:s') . "开始寻找未被使用的广告位" . PHP_EOL;

// 设置MySQL连接对象
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$connection_datahub = 'datahub';
$start_date = '2021-02-01';
$convert_site_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datahub.v2_dim_site_id')
    ->select(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id', 'v2_dim_site_id.create_time', 'v2_dim_site_id.convert_id', 'v2_dim_site_id.app_android_channel_package_id'])
    ->join('tanwan_datahub.v2_dim_agent_id', function (JoinClause $join) {
        $join->on('v2_dim_agent_id.platform', '=', 'v2_dim_site_id.platform');
        $join->on('v2_dim_agent_id.agent_id', '=', 'v2_dim_site_id.agent_id');
    })
    ->where('media_type_id', '=', 3)
    ->where('create_time', '>=', $start_date)
    ->where('convert_id', '>', 0)
    ->groupBy(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id']);

$toutiao_site_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datahub.v2_dim_site_id')
    ->select(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id', 'v2_dim_site_id.create_time', 'v2_dim_site_id.convert_id', 'v2_dim_site_id.app_android_channel_package_id'])
    ->join('tanwan_datahub.v2_dim_agent_id', function (JoinClause $join) {
        $join->on('v2_dim_agent_id.platform', '=', 'v2_dim_site_id.platform');
        $join->on('v2_dim_agent_id.agent_id', '=', 'v2_dim_site_id.agent_id');
    })
    ->join('tanwan_datamedia.ods_toutiao_convert_log', function (JoinClause $join) {
        $join->on('v2_dim_site_id.convert_id', '=', 'ods_toutiao_convert_log.convert_id');
        $join->where('ods_toutiao_convert_log.status', '=', 'AD_CONVERT_STATUS_ACTIVE');
        $join->where('ods_toutiao_convert_log.opt_status', '=', 'AD_CONVERT_OPT_STATUS_ENABLE');
    })
    ->where('media_type_id', '=', 1)
    ->where('create_time', '>=', $start_date)
    ->where('ods_toutiao_convert_log.is_delete', '!=', 1)
    ->where('v2_dim_site_id.convert_id', '>', 0)
    ->groupBy(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id']);

$tencent_site_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datahub.v2_dim_site_id')
    ->select(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id', 'v2_dim_site_id.create_time', 'v2_dim_site_id.convert_id', 'v2_dim_site_id.app_android_channel_package_id'])
    ->join('tanwan_datahub.v2_dim_agent_id', function (JoinClause $join) {
        $join->on('v2_dim_agent_id.platform', '=', 'v2_dim_site_id.platform');
        $join->on('v2_dim_agent_id.agent_id', '=', 'v2_dim_site_id.agent_id');
    })
    ->join('tanwan_datamedia.ods_tencent_channel_package_log', function (JoinClause $join) {
        $join->on('v2_dim_site_id.app_android_channel_package_id', '=', $join->raw("CONCAT( '0;', ods_tencent_channel_package_log.channel_package_id)"));
        $join->where('ods_tencent_channel_package_log.system_status', '=', 'CHANNEL_PACKAGE_STATUS_PASSED');
    })
    ->whereIn('media_type_id', [2, 5])
    ->where('create_time', '>=', $start_date)
    ->where('app_android_channel_package_id', '!=', '')
    ->where('convert_id', '>', 0)
    ->groupBy(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id']);

$baidu_site_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datahub.v2_dim_site_id')
    ->select(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id', 'v2_dim_site_id.create_time', 'v2_dim_site_id.convert_id', 'v2_dim_site_id.app_android_channel_package_id'])
    ->join('tanwan_datahub.v2_dim_agent_id', function (JoinClause $join) {
        $join->on('v2_dim_agent_id.platform', '=', 'v2_dim_site_id.platform');
        $join->on('v2_dim_agent_id.agent_id', '=', 'v2_dim_site_id.agent_id');
    })
    ->join('tanwan_datamedia.ods_baidu_convert_log', function (JoinClause $join) {
        $join->on('v2_dim_site_id.convert_id', '=', 'ods_baidu_convert_log.convert_id');
        $join->where('ods_baidu_convert_log.trans_status', '=', 1);
    })
    ->where('media_type_id', '=', 4)
    ->where('create_time', '>=', $start_date)
    ->where('v2_dim_site_id.convert_id', '>', 0)
    ->groupBy(['v2_dim_site_id.platform', 'v2_dim_site_id.site_id']);

$site_builder = getSql($convert_site_builder) . ' union ' . getSql($toutiao_site_builder) . ' union ' . getSql($tencent_site_builder) . ' union ' . getSql($baidu_site_builder);

$nowadays = date('Y-m-d');
$days_ago = date('Y-m-d', strtotime('-10 days'));

$hour_data_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datamedia.dwd_media_ad3_common_hour_data_log')
    ->select(['platform', 'site_id'])
    ->where('site_id', '<>', 0)
    ->whereBetween('cost_date', [$days_ago, $nowadays])
    ->groupBy(['platform', 'site_id']);

$overview_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('tanwan_datahub.v2_ads_day_overview_log')
    ->select(['platform', 'site_id'])
    ->where('site_id', '<>', 0)
    ->where('day_reg_uid_count', '>', 0)
    ->whereBetween('log_date', [$days_ago, $nowadays])
    ->groupBy(['platform', 'site_id']);

$ad2_common_builder = MysqlConnection::getConnection($connection_datahub)
    ->table('	tanwan_datamedia.dwd_media_ad2_common_log')
    ->select(['platform', 'site_id'])
    ->where('site_id', '<>', 0)
    ->where('ad2_create_time', '>=', $days_ago)
    ->groupBy(['platform', 'site_id']);

$data_log = getSql($hour_data_builder) . ' union ' . getSql($overview_builder) . ' union ' . getSql($ad2_common_builder);

$builder = MysqlConnection::getConnection($connection_datahub)
    ->table('dim_site')
    ->withExpression('dim_site', $site_builder)
    ->withExpression('data_log', $data_log)
    ->select(['dim_site.platform', 'dim_site.site_id'])
    ->selectRaw('"" as template_address')
    ->leftJoin('data_log', function (JoinClause $join) {
        $join->on('data_log.platform', '=', 'dim_site.platform');
        $join->on('data_log.site_id', '=', 'dim_site.site_id');
    })
    ->whereNull('data_log.site_id');

$pdo = MysqlConnection::getConnection($connection_datahub)->getPdo();
$stmt = $pdo->prepare($builder->toSql());
$stmt->execute($builder->getBindings());
$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

echo "找到", count($data), "条未被使用的广告位", PHP_EOL;

MysqlConnection::getConnection($connection_name)->table('ad_reuse_site')->truncate();

array_map(function ($list) use ($connection_name) {
    MysqlConnection::getConnection($connection_name)->table('ad_reuse_site')->insert($list);
}, array_chunk($data, 5000));


$replace_sql = "REPLACE INTO ad_reuse_site (site_id, platform, media_type, agent_group, account_id, agent_id, game_id, game_type, game_pack, convert_id, convert_type, convert_source_type, convert_data_type, ad_turn, auto_download, auto_download_second, forbid_tuitan, template_type, template_address, download_url, upt_state, upt_rate, pay_discount, reg_discount, is_third, deep_external_action, image_token, app_name, appid, action_track_url, package, app_android_channel_package_id, state) SELECT site.site_id, site.platform, site.media_type, site.agent_group, site.account_id , site.agent_id, site.game_id, site.game_type, site.game_pack, site.convert_id , site.convert_type, site.convert_source_type, site.convert_data_type, site.ad_turn, site.auto_download , site.auto_download_second, site.forbid_tuitan, site.template_type, site.template_address, site.download_url , site.upt_state, site.upt_rate, site.pay_discount, site.reg_discount, site.is_third , site.deep_external_action, site.image_token, site.app_name, site.appid, site.action_track_url , site.package, site.app_android_channel_package_id, site.state FROM site JOIN ad_reuse_site USING (site_id, platform)";

MysqlConnection::getConnection($connection_name)->select($replace_sql);

$delete_sql = "DELETE `ad_reuse_site` FROM `ad_reuse_site` INNER JOIN agent ON agent.platform = ad_reuse_site.platform AND agent.agent_id = ad_reuse_site.agent_id WHERE agent.media_type = 2 AND (agent.company IN ('上海新玩信息技术有限公司', '广州达喆科技有限公司') OR agent.account_id IN (********, ********, ********, ********))";

MysqlConnection::getConnection($connection_name)->delete($delete_sql);

echo "本轮结束", PHP_EOL;

function getSql(Builder $builder)
{
    $bindings = $builder->getBindings();
    return preg_replace_callback('/\?/', function () use (&$bindings) {
        $binding = array_shift($bindings);
        if (is_numeric($binding)) {
            return $binding;
        } else if (is_string($binding)) {
            return empty($binding) ? "''" : "'" . $binding . "'";
        } else {
            return $binding;
        }
    }, $builder->toSql());
}
