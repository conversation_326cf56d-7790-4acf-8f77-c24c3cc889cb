<?php

//
// 推送微信数据
// 在每小时的第20分钟开始，一直到第30分钟，每分钟执行一次指定的命令 （30分的时候应该是数据全跑完了）
// 20-30 * * * * php cronPushDataSum.php

/*
 * 重推某个配置
 *
 */

use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic as DMSPermissionLogic;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\BotModel;
use App\Model\HttpModel\Feishu\IM\ImagesModel;
use App\Model\HttpModel\Wechat\Message\Template\TemplateModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;
use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\MysqlConnection;
use App\Struct\RedisCache;
use App\Task\NoticeTask;
use App\Utils\Helpers;
use App\Utils\Math;
use Monolog\Logger;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$logger = Helpers::getLogger('data_sum_push');

$logger->info('推送任务开始运行');


$panel_date = date("Y-m-d");
$last_panel_date = date("Y-m-d", strtotime("-1 days"));
$time = strtotime(date("Y-m-d 00:00:00"));
$cur_hour = intval(date("H"));
$panel_date_time = time();
$cur_min = intval(date("i"));

////// TODO 时间改为今天凌晨 再推一次
$panel_date_time = 1739809200;
$panel_date = date("Y-m-d");
$last_panel_date = date("Y-m-d", strtotime("-1 days"));
$cur_hour = 0;

$config_model = new WechatPushConfigNewModel();
$push_log = [];


// 获取配置
$list = $config_model->getListByID('野兽领主-日数据推送', 495);
//$list = $config_model->getListByID('野兽领主-日数据推送', 151);

try {
    pushMessage($logger, $list, $cur_hour, $panel_date);
} catch (RedisException $e) {

}


/**
 * @param Logger $logger
 * @param $list
 * @param $cur_hour
 * @param $panel_date
 * @throws RedisException
 */
function pushMessage(Logger $logger, $list, $cur_hour, $panel_date)
{
    $notice_task = new NoticeTask();
    $bot_model = new BotModel();
    global $panel_date_time;
    foreach ($list as $item) {
        $theme = $item->theme;
        $user_id = $item->user_id;

        $push_key = getPushKey($panel_date, $user_id, $theme, $item->module, $cur_hour);


        if ($item->module === 'dms') {
            $permission = fix_dms_permission($item->config, $theme, $item->account);
            try {
                $user_permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
            } catch (AppException|\Throwable $exception) {
                $logger->info("获取dms权限报错。uid => " . $user_id);

                continue;
            }
            $data_model = new WechatPushDataDmsModel();
            $panel_data = $data_model->getDau($permission, $panel_date, $cur_hour, $user_permission);
        } else {
            $permission = fix_ly_permission($item->config, $theme, $item->account);
            $data_model = new WechatPushDataLyModel();
            $panel_data = $data_model->getDau($permission, $panel_date, $cur_hour);
        }
        $dau = $panel_data->sum_dau ?? 0;
        if ($dau <= 0) {
            $logger->info('数据没跑完，先不推', ['push_key' => $push_key]);
            continue;
        }



        $logger->info('执行截图脚本', ['push_key' => $push_key]);

        // 先截图，再推送
        // 执行Python脚本，所需参数：start_time，user_id，theme, code
        $python_path = "/data/www/script.zx.com/script/screenshot.py";
        $secret = '66Nhd&&(';
        $code = md5($panel_date_time . $user_id . $theme . $secret);
        $command = "python3 {$python_path} {$user_id} '{$theme}' {$panel_date_time} {$code}";
        $data = exec($command, $out_array, $result_code);
        $data = json_decode($data, true);
        // 执行异常的任务跳过
        if ($result_code !== 0) {
            $logger->error("推送截图任务命令执行异常, 先不推。", ['push_key' => $push_key, 'out_array' => $out_array]);
            continue;
        }
        if ($data['code'] != 0) {
            $logger->error("推送截图任务执行异常, 先不推。", ['push_key' => $push_key, 'res_data' => $data]);
            continue;
        }
        $logger->info('截图完成，开始推送', ['push_key' => $push_key]);


        $image_path = $data['data']['img_path'];
        try {
            $image_key = uploadImage($image_path);
        } catch (\Throwable $throwable) {
            $logger->error('图片上传失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key]);
            // 解锁
            unlock($push_key, $logger);
            continue;
        }

        $push_channel = json_decode($item->push_channel, true);
        foreach ($push_channel as $channel) {
            if ($channel === 'wechat') {
                // 微信推送不在这里处理，在另一个脚本
                continue;
            }

            if ($channel === 'feishu') {

                try {
                    $notice_task->pushFeishu(
                        [
                            'user_id'     => $user_id,
                            'type'        => NoticeMessageModel::TYPE_FEISHU_DATA,
                            'config_name' => 'qingbaoju',
                            'content'     => ['image_key' => $image_key],
                        ], 'image');
                    $logger->info('单推完成推送', ['push_key' => $push_key]);
                } catch (\Throwable $throwable) {
                    $logger->error('飞书单推推送失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key, 'strace' => $throwable->getTrace()]);
                    continue;
                }

            }

            if ($channel === 'feishu_group') {
                if (isset($item->webhook) && $item->webhook) {
                    try {
                        $content_data = [
                            [
                                ['tag' => 'img', 'image_key' => $image_key]
                            ]
                        ];
                        $bot_model->postMessage($item->webhook, '', $content_data);
                        $logger->info('群推完成推送', ['push_key' => $push_key]);
                    } catch (\Throwable $throwable) {
                        $logger->error('飞书群推推送失败，失败信息：' . $throwable->getMessage(), ['push_key' => $push_key, 'strace' => $throwable->getTrace()]);
                        continue;
                    }

                }
            }
        }

        unlink($data['data']['img_path']);

    }
}

function fix_dms_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);

    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'dms', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $alg_names = [];
            $type = $item['type'];
            $info = $item['info'];
            $is_apportioned = $item['is_apportioned'] ?? 1;

            foreach ($info as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $ag = $data['ag_name'] ?? [];
                $alg = $data['alg_name'] ?? [];
                $alg_names = array_merge($alg_names, $alg);
                $os = $data['os'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['main_game_id'   => $main_game_id,
                                             'agent_group_id' => $ag,
                                             'os'             => $os];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge($type_info[$platform]['main_game_id'], $main_game_id);
                    $type_info[$platform]['agent_group_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $alg_names = array_unique($alg_names);
            $types[$sub_theme][$type] = [
                'game'           => $type_info,
                'alg'            => $alg_names,
                'is_apportioned' => $is_apportioned,
            ];
        }
    }
    return $types;
}

function fix_ly_permission($config, $theme, $account)
{
    $types = [];
    $config = json_decode($config, true);
    // 合并数据
    $config = (new WechatDataLogic())->mergeThemeConfig($config, $theme, 'ly', $account);

    foreach ($config as $sub) {
        $sub_theme = $sub['sub_theme'];
        foreach ($sub['sub_config'] as $item) {
            $type_info = [];
            $type = $item['type'];
            $info = $item['info'];

            foreach ($info as $data) {
                $platform = $data['platform'];
                $game_id = $data['game_id'];
                $channel_id = $data['channel_id'] ?? [];
                $ag = $data['ag_name'] ?? [];

                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['game_id'        => $game_id,
                                             'agent_group_id' => $ag,
                                             'channel_id'     => $channel_id];
                } else {
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['game_id'], $game_id);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['channel_id'], $channel_id);
                }
            }

            $types[$sub_theme][$type] = ['game' => $type_info];
        }
    }
    return $types;
}


function getPushKey($date, $user_id, $theme, $module, $hour)
{
    return "{$date}|{$user_id}|{$theme}|{$hour}|{$module}";
}

/**
 * 上传图片到飞书获取image_key
 * @param string $image_path
 * @return mixed|string
 */
function uploadImage(string $image_path)
{
    $model = new ImagesModel();
    // 暂时先写死，到时需要改
    $app_id = 'cli_a490d70d6efb900b';
    $app_secret = 'BrE2l2evxpuRiLxwkcPXYeFOmS0aCxBW';
    $app_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $app_id, $app_secret);
    $data = $model->uploadImages($app_access_token, $image_path);
    return $data['data']['image_key'] ?? '';
}

/**
 * 获取执行锁，用来判断当前任务是否正在执行。
 *
 * @param $push_key
 * @param Logger $logger
 * @return bool  获锁成功返回true 否则返回false
 * @throws RedisException
 */
function lock($push_key, Logger $logger)
{
    $key = 'lock_push_' . $push_key;
    if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 3600])) {
        $logger->info('成功获取执行锁', ['push_key' => $push_key]);
        return true;
    } else {
        $logger->info('获取执行锁失败', ['push_key' => $push_key]);
        return false;
    }
}

/**
 * 解锁
 *
 * @param $push_key
 * @param Logger $logger
 * @throws RedisException
 */
function unlock($push_key, Logger $logger)
{
    $key = 'lock_push_' . $push_key;
    $result = RedisCache::getInstance()->del($key);
    $logger->info('解锁执行锁', ['push_key' => $push_key, 'res' => $result]);
}