<?php
/**
 * 50 5 * * *
 * 渠道异常点击记录
 */

use App\Param\DMS\AbnormalAgentClickParam;
use App\Utils\Helpers;
use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\Logic\DMS\GameWarningLogic;
use App\MysqlConnection;
use Illuminate\Database\Query\Builder;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$logger = Helpers::getLogger('AbnormalRegLastDays');

MysqlConnection::setConnection();

// -d 2021-01-20 d参数接收日期
// -t 1 t参数接收扫描类型目前只接受 7、15分别对应7日15日
$input = getopt('d:t:');
// 默认时间和单日时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date("Y-m-d", strtotime("-2 day"));
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START date:' . $date);
$date_last_second = $date . ' 23:59:59';
$type = isset($input['t']) ? intval($input['t']) : 7;
$start_date = date('Y-m-d', strtotime($date) - ($type - 1) * 86400);


$logic = (new GameWarningLogic());
// 先确定IP是否属于中国
$chinese_ip_range = $logic->getChinaIPBlock();

$last_days_param = new AbnormalAgentClickParam();
$last_days_param->click_date_range = [date('Y-m-d', strtotime($date) - ($type - 1) * 86400), $date];
$agent_abnormal_click_model = new V2DWDAgentAbnormalClickModel();
$abnormal_reg_last_days_records = $agent_abnormal_click_model->getLastDaysAgent($last_days_param, 'reg', $type);

$user_device_counts = collect();
$system_version_counts = collect();
$ip_counts = collect();
$m_uid_counts = collect();
$ip_segment_counts = collect();
if ($abnormal_reg_last_days_records->isNotEmpty()) {
    // 多日注册异常
    $platform_filters = $abnormal_reg_last_days_records->pluck('platform')->unique();
    $agent_filters = $abnormal_reg_last_days_records->pluck('agent_id')->unique();
    $game_filters = $abnormal_reg_last_days_records->pluck('game_id')->unique();
    $func = function (Builder $query) use ($platform_filters, $agent_filters, $game_filters) {
        $query
            ->whereIn('platform', $platform_filters)
            ->whereIn('agent_id', $agent_filters)
            ->whereIn('game_id', $game_filters);
    };
    $game_uid_reg_model = new V2DWDGameUidRegLogModel();
    $user_device_counts = $game_uid_reg_model->userDeviceCount($start_date, $date_last_second, $func);
    $system_version_counts = $game_uid_reg_model->systemVersionCount($start_date, $date_last_second, $func);
    $ip_counts = $game_uid_reg_model->ipRegCountLastDays($start_date, $date_last_second, $func);
    $m_uid_counts = $game_uid_reg_model->mUidRegCount($start_date, $date_last_second, $func);
    $ip_segment_counts = $game_uid_reg_model->ipSegmentCount($start_date, $date_last_second, $func);
}

foreach ($abnormal_reg_last_days_records as $abnormal_reg_last_days_record) {
    $reg_err = '';

    if ($abnormal_reg_last_days_record->day_reg_count < 30) continue;//

    // 点击后24小时注册占比
    $far_away_from_click_rate = $abnormal_reg_last_days_record->more_than_24_hours_reg_num / $abnormal_reg_last_days_record->day_reg_count * 100;
    if ($far_away_from_click_rate > 5) {
        $far_away_from_click_rate = round($far_away_from_click_rate, 2);
        $reg_err .= "注册日期距离点击日期过长,产生的注册有{$far_away_from_click_rate}%\r\n";
    }

    // 单用户2设备+注册占比(2设备出现的频次/总注册数
    $agent_user_device_count = $user_device_counts->filter(function ($item) use ($abnormal_reg_last_days_record) {
            return $item->platform == $abnormal_reg_last_days_record->platform &&
                $item->agent_id == $abnormal_reg_last_days_record->agent_id &&
                $item->game_id == $abnormal_reg_last_days_record->game_id;
        });
    $agent_user_multiple_device = $agent_user_device_count->where('count', '>=', 2);
    $multiple_device_rate = $agent_user_device_count->count() > 0 ? $agent_user_multiple_device->count() / $agent_user_device_count->count() * 100 : 0;
    if ($multiple_device_rate > 12.6) {
        $multiple_device_rate = round($multiple_device_rate, 2);
        $reg_err .= "多设备用户占比过多有{$multiple_device_rate}%\r\n";
    }

    // 单ip日均注册2次+比率
    $agent_ip_counts = $ip_counts->filter(function ($item) use ($abnormal_reg_last_days_record) {
            return $item->platform == $abnormal_reg_last_days_record->platform &&
                $item->agent_id == $abnormal_reg_last_days_record->agent_id &&
                $item->game_id == $abnormal_reg_last_days_record->game_id;
        });
    $multiple_ip_num = 0;
    $not_china_num = 0;
    foreach ($agent_ip_counts as $item) {
        if ($item->count >= 2) $multiple_ip_num++;
        if (!$logic->isLAN($item->ip)) {
            $index = $logic->binarySearchIP($item->ip, $chinese_ip_range);
            if ($index < 0) $not_china_num++;
        }
    }
    $agent_ip_count = $agent_ip_counts->count();
    $multiple_ip_rate = $agent_ip_count > 0 ? $multiple_ip_num / $agent_ip_count * 100 : 0;
    if ($multiple_ip_rate > 25) {
        $once_ip_rate = round(100 - $multiple_ip_rate, 2);
        $reg_err .= "1次注册用户占比过低有{$once_ip_rate}%\r\n";
    }
    // ip段是否异常(国家)
    $not_china_rate = $agent_ip_count > 0 ? $not_china_num / $agent_ip_count * 100 : 0;
    if ($not_china_rate > 5) {
        $not_china_rate = round($not_china_rate, 2);
        $reg_err .= "非中国的注册ip过多,占比{$not_china_rate}%\r\n";
    }
    // 单muid日均注册2次+比率
    $agent_m_uid_counts = $m_uid_counts->filter(function ($item) use ($abnormal_reg_last_days_record) {
            return $item->platform == $abnormal_reg_last_days_record->platform &&
                $item->agent_id == $abnormal_reg_last_days_record->agent_id &&
                $item->game_id == $abnormal_reg_last_days_record->game_id;
        });
    $multiple_m_uid_num = 0;
    foreach ($agent_m_uid_counts as $item) {
        if ($item->count >= 2) $multiple_m_uid_num++;
    }
    $multiple_m_uid_rate = $agent_m_uid_counts->count() > 0 ? $multiple_m_uid_num / $agent_m_uid_counts->count() * 100 : 0;
    if ($multiple_m_uid_rate >= 20) {
        $multiple_m_uid_rate = round($multiple_m_uid_rate, 2);
        $reg_err .= "单muid日均注册2次+比率{$multiple_m_uid_rate}%\r\n";
    }
    // 连续ip段出现2次+占比
    $agent_ip_segment_counts = $ip_segment_counts->filter(function ($item) use ($abnormal_reg_last_days_record) {
            return $item->platform == $abnormal_reg_last_days_record->platform &&
                $item->agent_id == $abnormal_reg_last_days_record->agent_id &&
                $item->game_id == $abnormal_reg_last_days_record->game_id &&
                $item->ip_segment != '';
        });
    $multiple_ip_segment_num = 0;
    foreach ($agent_ip_segment_counts as $item) {
        if ($item->count >= 2) $multiple_ip_segment_num += $item->count;
    }
    $multiple_ip_segment_rate = $agent_ip_segment_counts->count() > 0 ? $multiple_ip_segment_num / $agent_ip_segment_counts->sum('count') * 100 : 0;
    if ($multiple_ip_segment_rate > 25 && $multiple_ip_segment_num > 6 * $type) {
        $multiple_ip_segment_rate = round($multiple_ip_segment_rate, 2);
        $reg_err .= "同区域用户产生注册比例{$multiple_ip_segment_rate}%\r\n";
    }

    // 同个注册超过2次的ip 超过2日内重复出现
    $agent_multiple_ip_count = $agent_ip_counts->where('count', '>=', 2)->groupBy('ip');
    foreach ($agent_multiple_ip_count as $ip => $group_multiple_ip_count) {
        if (count($group_multiple_ip_count) >= 2) {
            $total_count = 0;
            foreach ($group_multiple_ip_count as $v) {
                $total_count += $v->count;
            }
            $reg_err .= "相同ip多日注册多次,产生了{$total_count}注册\r\n";
        }
    }
    // 同个连续ip段(前3个一样的) 注册大于9次 超过2日重复出现
    $more_than_9_ip_segment_group = $agent_ip_segment_counts->where('count', '>=', 9)->groupBy('ip_segment');
    foreach ($more_than_9_ip_segment_group as $ip => $item) {
        if (count($item) >= 2) $reg_err .= "同区域{$ip}内用户多日注册多次\r\n";
    }

    // 系统版本分布 如果这个渠道只有这1个异常没有别的异常,那这个比例达到85%以上才显示异常
    $agent_system_version_counts = $system_version_counts->filter(function ($item) use ($abnormal_reg_last_days_record) {
            return $item->platform == $abnormal_reg_last_days_record->platform &&
                $item->agent_id == $abnormal_reg_last_days_record->agent_id &&
                $item->game_id == $abnormal_reg_last_days_record->game_id;
        });
    if ($agent_system_version_counts->isNotEmpty()) {
        $threshold = empty($reg_err) ? 85 : 75;
        $agent_system_version_count = $agent_system_version_counts->sum('count');
        foreach ($agent_system_version_counts as $item) {
            $multiple_system_version_rate = $item->count / $agent_system_version_count * 100;
            if ($multiple_system_version_rate > $threshold) {
                $multiple_system_version_rate = round($multiple_system_version_rate, 2);
                $reg_err .= "超过{$multiple_system_version_rate}%的用户系统版本一致,版本:{$item->system_version}\r\n";
            }
        }
        $agent_system_version_counts = collect();// 重置
    }

    $update_data = [];
    if (!empty($reg_err)) {
        $filed = 'is_reg_abnormal_' . $type;
        $msg_field = 'reg_abnormal_msg_' . $type;
        $update_data[$filed] = 1;
        $update_data[$msg_field] = $reg_err;
        $param = new AbnormalAgentClickParam($abnormal_reg_last_days_record);
//        echo $param->platform, ',', $param->agent_id, ',', $param->game_id, ',', $reg_err;
        $agent_abnormal_click_model->updateFiledByPK($param, $update_data);
    }
}
$logger->info('END date:' . $date);
