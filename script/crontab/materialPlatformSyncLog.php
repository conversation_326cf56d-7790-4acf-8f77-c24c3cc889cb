<?php
/**
 * 每隔10分钟检查一次取一条新的同步记录
 */

use App\Logic\DSP\MaterialPlatformSyncLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$log = MysqlConnection::getConnection()
    ->table('material_platform_sync_log')
    ->where(['state_code' => 0])
    ->first();

if (!$log) {
    echo date('Y-m-d H:i:s') . '-没有需要执行的任务' . PHP_EOL;
    exit;
}

try {
    $log = (new MaterialPlatformSyncLogic())->doMaterialPlatformSyncLog($log);
    MysqlConnection::getConnection()
        ->table('material_platform_sync_log')
        ->where(['id' => $log->id])
        ->update([
            'target_material_id' => $log->target_material_id,
            'target_material_file_list' => $log->target_material_file_list,
            'state_code' => 1
        ]);
} catch (Throwable $e) {
    MysqlConnection::getConnection()
        ->table('material_platform_sync_log')
        ->where(['id' => $log->id])
        ->update([
            'error' => $e->getMessage(),
            'state_code' => 2
        ]);
    var_dump(date('Y-m-d H:i:s') . '-' . $e->getMessage(), '——' . $e->getTraceAsString() . PHP_EOL);
}
