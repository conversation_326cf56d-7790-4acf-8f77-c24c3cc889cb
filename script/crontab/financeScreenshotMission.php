<?php

/**
 * 30 * * * *
 * 检测截图任务(0.等待中 1.执行中)是否有变化,更新状态
 */

use App\Exception\AppException;
use App\Model\HttpModel\Tanwan\Spider\Master\MasterModel;
use App\Model\SqlModel\DataMedia\OdsFundScreenshotMissionLogModel;
use App\Utils\Helpers;
use Common\EnvConfig;
use App\MysqlConnection;
use App\Service\FinanceToutiaoService;
use App\Service\FinanceTencentService;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo 'begin======================'.PHP_EOL;
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';

$date = date('Y-m-d H:i:s', strtotime('-30 day'));
$builder = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_fund_screenshot_mission_log')
    ->selectRaw('media_type, platform, majordomo_name, majordomo_account_id, mission_id, year_month, author, status, created_time, updated_time')
    ->whereIn('status', [0, 1, -1, 2, 3])
    ->where('created_time', '>=', $date)
    ->groupBy(['mission_id']);
$list = $builder->get();

foreach ($list as $item) {
    try {
        $re = (new MasterModel())->getList($item->media_type, $item->platform, '', $item->majordomo_name);
        //var_dump($re);
        if ($re['list']) {
            foreach ($re['list'] as $value) {
                if ((int)$value['id'] === (int)$item->mission_id) {
                    //更改任务状态和信息
                    $data = [];
                    if ((int)$value['status'] !== (int)$item->status) {
                        $data['status'] = $value['status'];
                        if (!empty($value['msg'])) {
                            $data['message'] = $value['msg'];
                        }
                    }
                    if ($value['created_time'] !== $item->created_time) {
                        $data['created_time'] = $value['created_time'];
                    }
                    if ($value['updated_time'] !== $item->updated_time) {
                        $data['updated_time'] = $value['updated_time'];
                    }
                    if (count($data) > 0) {
                        echo 'update====mission_id=' . $item->mission_id . '=================' . PHP_EOL;
                        (new OdsFundScreenshotMissionLogModel())->update($item->mission_id, $data);
                    }

                    //更过各个子账号的状态
                    if ($value['account_status']) {
                        $old_list = (new OdsFundScreenshotMissionLogModel())
                            ->getAllList(['mission_id' => $item->mission_id, 'year_month' => $item->year_month])
                            ->pluck('account_status', 'account_id')
                            ->toArray();
                        $account_status = json_decode($value['account_status'], true);
                        $insert_data = [];
                        foreach ($account_status as $account_id => $status) {
                            //在记录里，更新
                            if ( isset($old_list[$account_id]) ) {
                                $status = (int)$status === 0 ? -3 : $status;
                                //新旧状态不一致更新
                                if ((int)$status !== (int)$old_list[$account_id]) {
                                    echo 'update====account_id='.$account_id.'====year_month=='.$item->year_month.'====account_status=='.$status.'========' . PHP_EOL;
                                    (new OdsFundScreenshotMissionLogModel())
                                        ->updateByAccountId($item->mission_id, $item->year_month, $account_id, ['account_status' => $status]);
                                }
                            } else {
                                //不在记录里，直接插入，记录状态为2
                                echo 'insert====account_id=' . $account_id . '====year_month=='.$item->year_month.'====account_status==2========' . PHP_EOL;
                                $insert_data[] = [
                                    'platform' => $item->platform,
                                    'media_type' => $item->media_type,
                                    'mission_id' => $item->mission_id,
                                    'majordomo_name' => $item->majordomo_name,
                                    'majordomo_account_id' => $item->majordomo_account_id,
                                    'account_id' => $account_id,
                                    'year_month' => $item->year_month,
                                    'author' => $item->author,
                                    'status' => $value['status'],
                                    'message' => $value['msg'],
                                    'created_time' => $value['created_time'],
                                    'updated_time' => $value['updated_time'],
                                    'account_status' => 2
                                ];
                            }
                        }
                        if ($insert_data != []) {
                            (new OdsFundScreenshotMissionLogModel())->add($insert_data);
                        }
                    }
                }
            }
        }
    } catch (AppException $exception) {
        Helpers::getLogger('ScreenshotMission')->error("getMissionList fail." . $exception->getMessage(), $item);
    }
}

echo 'end======================'.PHP_EOL;