<?php
//
// 定时给群里发送每日的群聊总结
// 0 19 * * *  每天晚上七点钟

use App\Constant\Environment;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\MysqlConnection;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Struct\RedisCache;
use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');


function handlerPushSummary($chat_list)
{
    $logger = \App\Utils\Helpers::getLogger('crontab_summary');
    $max_message_length = 30000;
    $history_max_message_length = 15000;
    $min_today_message_length = 100;
    $feishu_message_model = new FeiShuMessageModel();
    $group_assistant_service = new GroupAssistantService();
    $feishu_message_summary_model = new FeishuMessageSummary();
    $chat_model = new ChatsModel();
    $auth_model = new AuthModel();


    // 总结的开始时间 今天的开始时间从昨晚的19:00:01计算 也就是大家的下班时间
    $today = date("Y-m-d 19:00:01", strtotime("-1 day"));
    // 总结的消息截止时间 今天的19:00:00
    $end_time = date("Y-m-d 19:00:00");
    // 往前拉的历史记录
    $last_day = date("Y-m-d 00:00:00", strtotime("-4 day"));

//    $today = '2025-03-24 19:00:01';
//    $end_time = '2025-03-25 19:00:00';
////    $last_day = '2025-03-18 19:00:00';
    // 循环发总结消息到群里
    foreach ($chat_list as $item) {
        $chat_id = $item['chat_id'];


        try {
            // 先获取access_token 这里获取是因为防止过期，因为一个循环可能时间比较长
            $tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
            // 获取群信息
            $chat_info = $chat_model->chatInfo($tenant_access_token, $chat_id);
            $chat_name = $chat_info['name'];
            $user_count = $chat_info['user_count'];

            // 获取历史消息记录
            $message_list = $feishu_message_model->getListByChatIdAndTime($chat_id, $last_day, $end_time, 1000);
            if ($message_list->isEmpty()) {
                // 没有消息，不需要总结
                $logger->info('没有消息，不需要总结', $item);
                continue;
            }
            // 判断一下是否有今天的消息。今天的消息从“昨晚”19:00:01开始算
            $today_message_list = $message_list->whereBetween('message_create_time', [$today, $end_time]);
            if ($today_message_list->isEmpty()) {
                // 今天没有消息，不需要总结
                $logger->info('今天没有消息，不需要总结', $item);
                continue;
            }


            // 再判断一下今天的消息内容有没有超过100个字
            $today_str_length = $today_message_list->map(function ($item) use ($chat_id) {

                $format_message = json_decode($item->format_message, true);
                return calculateContentLength($format_message);

            })->sum();
            if ($today_str_length < $min_today_message_length) {
                // 今天没有消息，不需要总结
                $logger->info('今天的消息记录少于100字，不需要总结', $item);
                continue;
            }

            $history_message_list = $message_list->where('message_create_time', '<', $today);
            $history_format_message_list = GroupAssistantService::formatToLLMMessageList($history_message_list, $history_max_message_length);
            $today_format_message_list = GroupAssistantService::formatToLLMMessageList($today_message_list, $max_message_length);


            // 去LLM总结
            $logger->info('开始去LLM总结', ['chat_id' => $chat_id]);
            $raw_summary = $group_assistant_service->summaryMessage($today_format_message_list, $history_format_message_list, $user_count, $today_message_list->count());
            /**-- 替换一下@人的标签 --**/
            $summary = FeiShuService::replaceChatGroupNameToAT($chat_id, $raw_summary, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);


            $logger->info('总结完成', ['chat_name' => $chat_name, 'chat_id' => $chat_id, 'summary' => $raw_summary]);


            $msg_id = 0;
            // 再获取access_token 这里再获取一遍，防止获取中途token过期
            $tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

            if (EnvConfig::ENV !== Environment::PROD) {
                $logger->info('非生产环境，不发消息，本群处理完成', ['chat_id' => $chat_id]);
                continue;
            }
            // 发送消息卡片到指定的群组
            FeiShuService::streamOutput($msg_id, $tenant_access_token, $summary, $chat_id);
            $logger->info('消息发送完成,本群处理完成', ['chat_id' => $chat_id]);

            // 总结内容入库
            $data = [
                'summary'     => $raw_summary,
                'chat_id'     => $chat_id,
                'msg_id_list' => $message_list->pluck('message_id')->all(),
                'start_time'  => $today,
                'end_time'    => $end_time,
            ];
            $feishu_message_summary_model->addOne($data);
            $logger->info('本群处理完成');
        } catch (\Throwable $throwable) {
            $logger->error('循环内发生未知错误，本群不推。错误信息：' . $throwable->getMessage(), ['chat_id' => $chat_id]);
        }


    }

}

function calculateContentLength($data)
{
    if (!isset($data['content'])) {
        return 0;
    }
    // 检查 content 是否为 JSON 格式的字符串
    if (is_array($data['content'])) {
        $nested_contents = $data['content'];
        $total_length = 0;
        foreach ($nested_contents as $nested_content) {
            $total_length += calculateContentLength($nested_content);
        }
        return $total_length;
    } else {
        // 计算当前 content 的字符数
        return mb_strlen($data['content']);
    }
}


$logger = \App\Utils\Helpers::getLogger('crontab_summary');
// 获取群列表数据
$auth_model = new AuthModel();

// 先获取access_token
$tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
    GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
// 获取到群聊天列表
$chat_model = new ChatsModel();

$chat_list = $chat_model->chatList($tenant_access_token);
$logger->info('获取到聊天群列表', $chat_list);

// 关闭链接
unset($chat_model);
unset($auth_model);
RedisCache::destroy();

// 初始化进程处理的列表数量
$chunk_site = 10;

// 按进程数量分配配置列表
$chunks = array_chunk($chat_list, $chunk_site);

// 子进程的进程id数组
$pid_list = [];

// 循环创建进程
foreach ($chunks as $index => $chunk_chat_list) {
    $pid = pcntl_fork();
    if ($pid < 0) {
        $logger->error('创建进程失败');
        exit;
    } else if ($pid === 0) {
        try {
            // 初始化链接，在子进程进行
            MysqlConnection::setConnection();
            // 子进程执行
            $logger->info('子进程执行 :' . $index);
            handlerPushSummary($chunk_chat_list);
        } catch (\Throwable $throwable) {
            $logger->error('发送未知错误：' . $throwable->getMessage(), ['s' => $throwable->getTraceAsString()]);
        }

        $logger->info('子进程退出 :' . $index);
        exit;
    } else {
        // 父进程收集子进程 id
        $pid_list[] = $pid;
    }

}

// 父进程等待所有子进程完成
$logger->info('父进程等待所有子进程完成');
$status = 0;
foreach ($pid_list as $pid) {
    pcntl_waitpid($pid, $status);
}

$logger->info('父进程退出，任务完成');
