<?php
/**
 * # 3号 8号 13号 18号 23号 28号 通知kpi
 * 0 10 3 * *
 * 0 10 8 * *
 * 0 10 13 * *
 * 0 10 18 * *
 * 0 10 23 * *
 * 0 10 28 * *
 * 推送 当月kpi进度达不到100%的
 */

use App\Constant\RouteID;
use App\Model\SqlModel\Zeda\KpiModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Service\KPIIndicator\KPIIndicator;
use App\Service\NoticeService;
use App\Service\UserService;
use App\Utils\Helpers;
use App\Utils\Math;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

Helpers::getLogger('kpi')->info('notice current process rate', ['message' => '通知开始']);
$user_model = new UserModel();
$user_list = $user_model->getAllJoinUserLevel('dms');

// 格式化user list
$user_format_list = [];

foreach ($user_list as $user) {
    switch ($user->level) {
        case UserService::LEVEL_SUPER:
            if (!isset($user_format_list[0])) {
                $user_format_list[0] = [];
            }
            $user_format_list[0][] = $user->id;
            break;
        case UserService::LEVEL_PLATFORM:
            if (!isset($user_format_list[$user->platform_id])) {
                $user_format_list[$user->platform_id] = [];
            }
            $user_format_list[$user->platform_id][] = $user->id;
            break;
        case UserService::LEVEL_DEPARTMENT:
            $key = "{$user->platform_id}-{$user->department_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_DEPARTMENT_GROUP:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}-{$user->department_group_position_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}-{$user->department_group_position_id}-{$user->department_group_position_worker_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_SIX:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}-{$user->department_group_position_id}-{$user->department_group_position_worker_id}-{$user->department_six_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_SEVEN:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}-{$user->department_group_position_id}-{$user->department_group_position_worker_id}-{$user->department_six_id}-{$user->department_seven_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        case UserService::LEVEL_EIGHT:
            $key = "{$user->platform_id}-{$user->department_id}-{$user->department_group_id}-{$user->department_group_position_id}-{$user->department_group_position_worker_id}-{$user->department_six_id}-{$user->department_seven_id}-{$user->department_eight_id}";
            if (!isset($user_format_list[$key])) {
                $user_format_list[$key] = [];
            }
            $user_format_list[$key][] = $user->id;
            break;
        default:
            break;
    }
}

$kpi_model = new KpiModel();
$kpi_list = $kpi_model->getAllByMonth(strtotime(date('Y-m-01')));

$notice_service = new NoticeService();

$yesterday_timestamp = strtotime('-1 day');
$yesterday = date('j', $yesterday_timestamp);
$month = date('t', $yesterday_timestamp);
foreach ($kpi_list as $kpi) {
    $indicator = KPIIndicator::create($kpi);
    $current = $indicator->getCurrentValue();
    $process_rate = Math::rate($current, (($yesterday / $month) * $kpi->target));
    if ($process_rate >= 100) {
        continue;
    }
    $creator = $user_list->where('id', $kpi->user_id)->first();
    $push_user_ids = [$creator->id];
    for ($creator_level = $creator->level - 1; $creator_level >= 0; $creator_level--) {
        switch ($creator_level) {
            case UserService::LEVEL_SUPER:
                $push_user_ids = array_merge($push_user_ids, $user_format_list[0]);
                break;
            case UserService::LEVEL_PLATFORM:
                if (isset($user_format_list[$creator->platform_id])) {
                    $push_user_ids = array_merge($push_user_ids, $user_format_list[$creator->platform_id]);
                }
                break;
            case UserService::LEVEL_DEPARTMENT:
                $key = "{$creator->platform_id}-{$creator->department_id}";
                if (isset($user_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $user_format_list[$key]);
                }
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}";
                if (isset($user_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $user_format_list[$key]);
                }
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}-{$creator->department_group_position_id}";
                if (isset($creator_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $creator_format_list[$key]);
                }
                break;
            case UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}-{$creator->department_group_position_id}-{$creator->department_group_position_worker_id}";
                if (isset($creator_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $creator_format_list[$key]);
                }
                break;
            case UserService::LEVEL_SIX:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}-{$creator->department_group_position_id}-{$creator->department_group_position_worker_id}-{$creator->department_six_id}";
                if (isset($creator_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $creator_format_list[$key]);
                }
                break;
            case UserService::LEVEL_SEVEN:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}-{$creator->department_group_position_id}-{$creator->department_group_position_worker_id}-{$creator->department_six_id}-{$creator->department_seven_id}";
                if (isset($creator_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $creator_format_list[$key]);
                }
                break;
            case UserService::LEVEL_EIGHT:
                $key = "{$creator->platform_id}-{$creator->department_id}-{$creator->department_group_id}-{$creator->department_group_position_id}-{$creator->department_group_position_worker_id}-{$creator->department_six_id}-{$creator->department_seven_id}-{$creator->department_eight_id}";
                if (isset($user_format_list[$key])) {
                    $push_user_ids = array_merge($push_user_ids, $user_format_list[$key]);
                }
                break;
            default:
                break;
        }
    }
    foreach ($push_user_ids as $user_id) {
        $notice_service->addNotice(
            $user_id,
            NoticeService::LEVEL_WARNING,
            "KPI进度",
            NoticeMessageModel::TYPE_KPI,
            [$creator->name, $kpi->name, $process_rate],
            RouteID::INDIVIDUATION_KPI
        );
    }
}

Helpers::getLogger('kpi')->info('notice current process rate', ['message' => '通知结束']);
