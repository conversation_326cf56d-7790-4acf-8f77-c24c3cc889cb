<?php
/**
 * 10 0 * * *
 * 更新人群包
 */


use App\Constant\UserIDType;
use App\Logic\DMS\AudienceLogic;
use App\Logic\ToutiaoLogic;
use App\Model\SqlModel\Tanwan\DmpModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\DeviceTaskModel;
use App\MysqlConnection;
use App\Service\AudienceService;
use App\Service\DeviceService;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;


require_once dirname(__DIR__) . '/../common/init.php';
require_once dirname(__DIR__) . '/process/utils/common.php';
require_once dirname(__DIR__) . '/process/core/MultiProcess.php';
require_once dirname(__DIR__) . '/process/core/Process.php';


date_default_timezone_set('PRC');

const PROCESS_NUM = 2;
const DAEMONIZE = false;

class CronUpdateDMP extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
        switch ($signo) {
            case SIGTERM:
                $this->run = false;
                break;
        }
    }

    /**
     * @throws Exception
     */
    protected function worker($n)
    {
        global $device_task_update_list_key, $device_task_update_files_key;
        $redis = RedisCache::getInstance();
        $start_time = time();
        $date = date("Y-m-d");
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $start_date = $yesterday . ' 00:00:00';
        $end_date = $yesterday . ' 23:59:59';
        $dmp_model = new DmpModel();
        while ($device_info = $redis->rPop($device_task_update_list_key)) {
            $device_info = unserialize($device_info);
            try {
                if ($device_info->auto_update === DeviceTaskModel::INCREMENT) {
                    $file_list = $dmp_model->createAppendMuidFile($device_info, $start_date, $end_date);
                } else {
                    $file_list = $dmp_model->createFullMuidFile($device_info);
                }
            } catch (Throwable $e) {
                echo "[device_task_id:$device_info->id] child {$n} pid {$this->pid} error: ", $e->getMessage(), PHP_EOL;
                $redis->lPush($device_task_update_list_key, serialize($device_info));
                throw $e;
            }

            $redis->hSet($device_task_update_files_key, $device_info->id, serialize($file_list));
        }
        $spend_time = time() - $start_time;
        echo "[$date] child {$n} pid {$this->pid} spend time {$spend_time}." . PHP_EOL;
    }
}

$yesterday = date('Y-m-d', strtotime('-1 day'));
$script_start_time = time();
echo "昨日[$yesterday]任务开始", PHP_EOL;
$device_list = MysqlConnection::createConnection(EnvConfig::MYSQL['default'])
    ->table('device_task')
    ->where('auto_update', '!=', 0)
    ->where('state', DeviceTaskModel::SUCCESS)
    ->whereNotIn('id', DmpModel::SPECIAL_TASK)
    ->get();

$redis = RedisCache::getInstance();
$device_task_update_list_key = "device_task_update_list_$yesterday";
$device_task_update_files_key = "device_task_update_files_$yesterday";

if (!$redis->exists($device_task_update_list_key) && !$redis->exists($device_task_update_files_key)) {
    $redis->lPush($device_task_update_list_key, ...$device_list->map(function ($item) {
        return serialize($item);
    })->toArray());
}
// 销毁父进程的redis, 防止子进程复用。
RedisCache::destroy();

$multi_process = new MultiProcess(PROCESS_NUM, DAEMONIZE);
$multi_process->runOnce();
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'CronUpdateDMP');
$max_run_times = 3;
$ret = [];
for ($i = 0; $i <= $max_run_times; $i++) {
    echo "子进程打包开始", PHP_EOL;
    $ret = $multi_process->start();
    if (array_sum($ret) > 0) {
        if ($i < $max_run_times) {
            echo "子进程打包发生错误，120秒后, 将进行第", $i + 1, "次重试", PHP_EOL;
            sleep(120);
        } else {
            echo "子进程打包重试后仍然发生错误，退出本次任务", PHP_EOL;
            Helpers::getLogger('dmp')->error(date("Y-m-d") . "子进程打包重试后仍然发生错误，退出本次任务");
            return;
        }
    } else {
        break;
    }
}

echo "子进程打包完成", PHP_EOL;

MysqlConnection::setConnection();
$redis = RedisCache::getInstance();

// 自动更新任务开始
$audience_file_list = $redis->hGetAll($device_task_update_files_key);
$audience_file_list = array_map(function ($item) {
    return unserialize($item);
}, $audience_file_list);

$audience_model = new AudienceModel();
$audience_list = $audience_model->getAllSuccessInDeviceTaskId($device_list->pluck(['id']));
echo "追加audience_change_log开始", PHP_EOL;
(new AudienceLogic())->appendDailyChange($yesterday, $device_list, $audience_list, $audience_file_list);
echo "追加audience_change_log结束", PHP_EOL;

// 自动更新头条人群包开始
echo "头条聚合audience_change_log->toutiao_audience_change_log开始", PHP_EOL;
(new ToutiaoLogic())->updateDailyAudience($yesterday);
echo "头条聚合audience_change_log->toutiao_audience_change_log完成", PHP_EOL;

echo '腾讯清除日限制开始.', PHP_EOL;
$audience_model = new AudienceModel();
$audience_model->cancelDayRequestError();

$audience_change_model = new AudienceChangeLogModel();
$audience_change_model->cancelDayRequestError();
echo '腾讯清除日限制完成.', PHP_EOL;

// 备份文件
function backupFile($device_task_id, $file_uuid, $timestamp)
{
    $mv_file_func = function ($filename, $timestamp) {
        if (file_exists($filename)) {
            $backup_name = basename($filename, ".txt") . '-' . date('Ymd', $timestamp);
            $archive_path = AudienceService::AUDIENCE_DIR . '/' . date('Ym', $timestamp);
            if (!is_dir($archive_path)) {
                mkdir($archive_path, 0755, true);
            }
            copy($filename, "{$archive_path}/{$backup_name}.txt");
        }
    };
    $mv_file_func(AudienceService::getFile("{$device_task_id}-{$file_uuid}", UserIDType::IMEI_MD5), $timestamp);
    $mv_file_func(AudienceService::getFile("{$device_task_id}-{$file_uuid}", UserIDType::IDFA_MD5), $timestamp);
    $mv_file_func(AudienceService::getFile("{$device_task_id}-{$file_uuid}", UserIDType::OAID), $timestamp);
    $mv_file_func(AudienceService::getFile("{$device_task_id}-{$file_uuid}", UserIDType::OPEN_ID), $timestamp);
}

echo '备份文件开始.', PHP_EOL;
foreach ($device_list->where('auto_update', DeviceTaskModel::FULL) as $device_info) {
    backupFile($device_info->id, 'all', strtotime($yesterday));
}
echo '备份文件完成.', PHP_EOL;

echo '移动文件到dmp文件开始.', PHP_EOL;
$device_model = new DeviceTaskModel();
foreach ($audience_file_list as $device_task_id => $file_list) {
    $device_info = $device_list->where('id', $device_task_id)->first();
    $device_task_name = $device_info->name;
    $device_task_file_list = array_column(json_decode($device_info->file_list, true), NULL, 'data_type');
    $row = $device_info->auto_update === DeviceTaskModel::FULL ? 0 : $device_info->row;
    $update_file_list = [];
    $zip = new ZipArchive;
    $code = $zip->open(DeviceService::getZip($device_task_id), ZIPARCHIVE::CREATE | ZipArchive::OVERWRITE);
    foreach ($file_list as $file) {
        $audience_filename = AudienceService::AUDIENCE_DIR . '/' . $file['name'];
        if (!file_exists($audience_filename)) {
            continue;
        }
        $filename = DeviceService::getFile($device_task_id, $file['data_type']);
        if ($device_info->auto_update === DeviceTaskModel::FULL) {
            copy($audience_filename, $filename);
        } else {
            file_put_contents($filename, file_get_contents($audience_filename), FILE_APPEND);
        }
        $row += $file['row'];
        $update_file_list[] = [
            'name' => basename($filename),
            'row' => $device_info->auto_update === DeviceTaskModel::FULL ? $file['row'] : ((isset($device_task_file_list[$file['data_type']]) ? $device_task_file_list[$file['data_type']]['row'] : 0) + $file['row']),
            'size' => filesize($filename),
            'type' => UserIDType::DEVICE_TYPE_MAP[$file['data_type']],
            'data_type' => $file['data_type'],
        ];
        $zip->addFile($filename, "{$device_task_name}-" . UserIDType::FILE_NAME_MAP[$file['data_type']]);
    }
    $zip->close();
    $device_model->success($device_task_id, $row, json_encode($update_file_list));
}
echo '移动文件到dmp文件完成.', PHP_EOL;

// 当天凌晨结束过期
$ttl = strtotime(date('Y-m-d',strtotime('+1 day'))) - time();
$redis->expire($device_task_update_files_key, $ttl);

echo "昨日[$yesterday]任务完成，共计", time() - $script_start_time, '秒', PHP_EOL;