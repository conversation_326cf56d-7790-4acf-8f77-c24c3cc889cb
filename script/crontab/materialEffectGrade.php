<?php
/*
 * 0 3 * * *
 * 素材评分等级
 */

use App\Model\SqlModel\DataMedia\OdsMaterialEffectGradeLogModel;
use App\Model\SqlModel\Zeda\MaterialEffectGradeModel;
use App\MysqlConnection;
use Common\EnvConfig;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo date('Y-m-d H:i:s') . '--Material effect grade begin' . PHP_EOL;

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$id = null;
$params = getopt('i:');
if (isset($params['i'])) {
    $id = $params['i'];
}

$material_score_rule_model = MysqlConnection::getConnection($connection_name)->table('material_score_rule')->where(['is_del' => 0, 'pid' => 0]);

if ($id > 0) {
    $material_score_rule = $material_score_rule_model->where('id', $id);
}

$material_score_rule = $material_score_rule_model->get();

foreach ($material_score_rule as $value) {
    $rule_id = $value->id;
    $platform = $value->platform;
    $target = $value->target;
    $theme_ids = json_decode($value->theme_ids);
    $cost_days = $value->cost_days;
    $create_date_start = $value->create_date_start;
    $create_date_end = $value->create_date_end;

    $sub_material_score_rule = MysqlConnection::getConnection($connection_name)
        ->table('material_score_rule')
        ->where(['is_del' => 0, 'pid' => $rule_id])
        ->get();

    $builder = MysqlConnection::getConnection($connection_data_media)
        ->table('dwd_material_best_interval_log as material_interval')
        ->select(['material_interval.*'])
        ->where(['material_interval.platform' => $platform, 'material_interval.cost_days' => $cost_days + 1]);//老后台一直算多了一天,对齐老后台
    if (!empty($create_date_start) && !empty($create_date_end)) {
        $builder
            ->join('ods_material_log as material', function (JoinClause $join) {
                $join->on('material.platform', '=', 'material_interval.platform');
                $join->on('material.material_id', '=', 'material_interval.material_id');
            })
            ->whereBetween('material.insert_time', [$create_date_start . ' 00:00:00', $create_date_end . ' 23:59:59']);
        if (!empty($theme_ids)) {
            $theme_ids = array_map(function ($theme_id) {
                return $theme_id[1];
            }, $theme_ids);
            $builder->whereIn('material.theme_id', $theme_ids);
        }
    }
    $list = $builder->get();
    //var_dump($list);

    $material_effect_grade_model = new MaterialEffectGradeModel();
    $time = time();
    $datas = [];
    foreach ($list as $material) {

        //定档分数
        $grade1 = 0;
        if (!empty($value->sss_grade) && $material->cost >= $value->sss_grade) {
            $grade1 = 7;
        } elseif (!empty($value->ss_grade) && $material->cost >= $value->ss_grade) {
            $grade1 = 6;
        } elseif ($material->cost >= $value->s_grade) {
            $grade1 = 5;
        } elseif ($material->cost >= $value->a_grade) {
            $grade1 = 4;
        } elseif ($material->cost >= $value->b_grade) {
            $grade1 = 3;
        } elseif ($material->cost >= $value->c_grade) {
            $grade1 = 2;
        } elseif ($material->cost >= $value->d_grade) {
            $grade1 = 1;
        }

        //降档分数
        $sub_target_value = [];
        $sub_total = [];
        foreach ($sub_material_score_rule as $sub_rule) {
            $target = $sub_rule->target;
            switch ($target) {
                case 'first_day_roi':
                    if (isset($material->day_first_day_pay_money)) {
                        $material->first_day_roi = ($material->day_first_day_pay_money / $material->cost) * 100;
                    }
                    break;
                case 'rate_day_roi_2':
                    if (isset($material->day_second_day_pay_money)) {
                        $material->rate_day_roi_2 = ($material->day_second_day_pay_money / $material->cost) * 100;
                    }
                    break;
                case 'rate_day_roi_3':
                    if (isset($material->day_third_day_pay_money)) {
                        $material->rate_day_roi_3 = ($material->day_third_day_pay_money / $material->cost) * 100;
                    }
                    break;
                case 'rate_day_roi_7':
                    if (isset($material->day_seventh_day_pay_money)) {
                        $material->rate_day_roi_7 = ($material->day_seventh_day_pay_money / $material->cost) * 100;
                    }
                    break;
                case 'rate_day_roi_15':
                    if (isset($material->day_fifteenth_day_pay_money)) {
                        $material->rate_day_roi_15 = ($material->day_fifteenth_day_pay_money / $material->cost) * 100;
                    }
                    break;
                case 'rate_day_stay_2':
                    if (isset($material->day_second_login_rate)) {
                        $material->rate_day_stay_2 = $material->day_second_login_rate * 100;
                    }
                    break;
                case 'rate_day_stay_3':
                    if (isset($material->day_three_login_rate)) {
                        $material->rate_day_stay_3 = $material->day_three_login_rate * 100;
                    }
                    break;
                case 'rate_day_stay_7':
                    if (isset($material->day_seven_login_rate)) {
                        $material->rate_day_stay_7 = $material->day_seven_login_rate * 100;
                    }
                    break;
                case 'rate_day_stay_15':
                    if (isset($material->day_fifteen_login_rate)) {
                        $material->rate_day_stay_15 = $material->day_fifteen_login_rate * 100;
                    }
                    break;
                default:
                    break;
            }
            if (isset($material->{$target})) {
                $target_value = $material->{$target};
                $grade2 = 5;
                if ($target_value >= $sub_rule->s_grade) {
                    $grade2 = 0;
                } elseif ($target_value >= $sub_rule->a_grade) {
                    $grade2 = 1;
                } elseif ($target_value >= $sub_rule->b_grade) {
                    $grade2 = 2;
                } elseif ($target_value >= $sub_rule->c_grade) {
                    $grade2 = 3;
                } elseif ($target_value >= $sub_rule->d_grade) {
                    $grade2 = 4;
                }
                $sub_total[] = $grade2;
                $sub_target_value[] = ['target' => $target, 'value' => $target_value, 'score' => $grade2];
            }
        }
        $grade2 = array_sum($sub_total);


        $data = [
            'platform' => $platform,
            'material_id' => $material->material_id,
            'rule_id' => $rule_id,
            'grade' => $grade1 - $grade2,
            'grade1' => $grade1,
            'grade2' => $grade2,
            'cost_days' => $cost_days,
            'cost_date_start' => $material->cost_date_start,
            'cost_date_end' => $material->cost_date_end,
            'cost' => !empty($material->cost) ? $material->cost : 0,
            'sub_target_value' => json_encode($sub_target_value),
            'insert_time' => EnvConfig::ENV === 'production' ? date('Y-m-d H:i:s', $time) : $time,
        ];
        //print_r($data);
        echo "scoring material==={$data['material_id']}====rule_id==={$data['rule_id']}=====grade==={$data['grade']}==========>\n";
        $datas[] = $data;
    }
    //print_r(array_reverse($datas));
    if (EnvConfig::ENV === 'production') {
        $material_effect_grade_model = new OdsMaterialEffectGradeLogModel();
    } else {
        $material_effect_grade_model = new MaterialEffectGradeModel();
    }
    $material_effect_grade_model->add($datas);

    echo "update material score=============>\n";

    // 线上由adb更新
    if (EnvConfig::ENV !== 'production') {
        MysqlConnection::getConnection($connection_name)->update("UPDATE material JOIN (
        SELECT
            platform,
            material_id,
            max( IF ( cost_days = 7, grade, 0 ) ) AS effect_grade7,
            max( IF ( cost_days = 30, grade, 0 ) ) AS effect_grade30 
        FROM
            `material_effect_grade` 
        GROUP BY
            `platform`,
            `material_id`
        ) 
        AS `material_score` 
        ON `material_score`.`platform` = `material`.`platform` AND `material_score`.`material_id` = `material`.`material_id` 
        SET material.effect_grade7 = material_score.effect_grade7, material.effect_grade30 = material_score.effect_grade30");
    }

    echo date('Y-m-d H:i:s') . '--Material effect grade finish' . PHP_EOL;
}