<?php
/**
 * 0 4 * * *
 * 拉取结算数据
 * 每天刷新一次表数据
 * User: Melody
 * Date: 2020/6/18
 * Time: 16:07
 */

use App\Logic\DMS\OuterLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
$start_date = date("Y-m-d", strtotime('-2 day'));
$end_date = date("Y-m-d 23:59:59", strtotime('-1 day'));

(new OuterLogic())->flushSettlementData($start_date, $end_date);



