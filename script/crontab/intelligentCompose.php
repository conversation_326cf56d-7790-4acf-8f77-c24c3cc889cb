<?php

use App\Logic\DSP\ADServingLogic;
use App\Model\SqlModel\Zeda\ADIntelligentComposeModel;
use App\MysqlConnection;
use App\Param\ADServing\ADIntelligentComposeParam;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

echo date('Y-m-d H:i:s') . ' 智创组合开始执行' . PHP_EOL;

$intelligent_compose_model = new ADIntelligentComposeModel();
$intelligent_compose_logic = new ADServingLogic();

$intelligent_compose_list = $intelligent_compose_model->getExecIntelligentCompose();

echo date('Y-m-d H:i:s') . " 本轮有" . $intelligent_compose_list->count() . "条任务" . PHP_EOL;

foreach ($intelligent_compose_list as $intelligent_compose) {
    try {
        $intelligent_compose_logic->execIntelligentCompose($intelligent_compose->id, (new ADIntelligentComposeParam())->initBySql($intelligent_compose));
        $intelligent_compose_model->setExecDateTime($intelligent_compose->id, date("Y-m-d H"));
        echo date('Y-m-d H:i:s') . " 成功执行一个智创组合 id-" . $intelligent_compose->id . ",作者-", $intelligent_compose->creator . PHP_EOL;
    } catch (Throwable $exception) {
        echo date('Y-m-d H:i:s') . " 智创组合 id-" . $intelligent_compose->id . ",作者-", $intelligent_compose->creator . '出错:' . $exception->getMessage() . PHP_EOL;
    }
}

echo date('Y-m-d H:i:s') . ' 智创组合结束执行' . PHP_EOL;
