<?php

/**
 * 导出飞书云文件任务
 * /1 * * * * php cronExportFeishuFileTask.php
 * @server 120.55.83.156 zx-dms
 */

use App\Model\ClickhouseModel\Database\ClickhouseConnection;
use App\MysqlConnection;
use App\Task\ExportFeishuFileTask;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
ClickhouseConnection::setConnection();

$task = new ExportFeishuFileTask();
// 定时任务最细时间粒度只能是一分钟一次,加多个循环,一分钟内,执行多次的任务,降低延迟
$start = time();
while (time() - $start < 60) {
    $task->export();
    $task->readTicket();
    sleep(2);
}

