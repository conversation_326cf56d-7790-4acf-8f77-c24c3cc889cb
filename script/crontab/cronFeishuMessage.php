<?php
//
// 消息处理事件
// */1 * * * * php cronExportFileTask.php
//

use App\MysqlConnection;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Task\GroupAssistantTask;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();


$service = new GroupAssistantService();
try {
    (new GroupAssistantTask())->queueConsumer();
} catch (\Throwable $e) {
    \App\Utils\Helpers::getLogger('group_assistant')->error('脚本运行出错' . $e->getMessage());
}
