<?php

// 处理全量更新运营利润的数据
// */1 * * * * php cronSyncAllGamePayData.php
//

use App\MysqlConnection;
use App\Struct\RedisCache;
use App\Task\HistoryPayDataTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$task = new HistoryPayDataTask();
$queue_key = 'q_flush_all_game_data';       // 队列key
$task_status_key = "s_flush_all_game_data"; // 任务状态Key
$user_limit_key = 'flush_all_game_data';    // 24小时限制的key
$lock = 'flush_all_game_data_lock';         // 执行锁的key
$logger = Helpers::getLogger('all-game-history');
$redis = RedisCache::getInstance();
try {
    // 从队列中取出任务
    $task_data = $redis->lPop($queue_key);
    if (!$task_data) {
        $logger->info('获取不到队列信息，退出任务');
        exit;
    }

    // 获取执行锁
    if (!RedisCache::getInstance()->set($lock, '1', ['NX', 'EX' => 86400])) {
        $logger->info('获取不到执行锁，退出任务');
        exit;
    }

    // 到了这一步就可以真正执行任务
    $logger->info('任务开始');

    $task = new HistoryPayDataTask();
    $task->syncAllHistoryGameData($logger);

    // 标记更新完成
    $redis->set($task_status_key, '已完成');

    // 解锁
    RedisCache::getInstance()->del($lock);

    $logger->info('任务结束');
} catch (\Throwable $e) {
    RedisCache::getInstance()->del($lock);
    $logger->error('刷新运营利润报错', ['message' => $e->getMessage()]);
    exit;
}


