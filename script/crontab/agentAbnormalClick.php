<?php

/**
 * 0 4 * * *
 * 渠道异常点击记录
 */

use Illuminate\Support\Collection;

use App\Logic\DMS\GameWarningLogic;
use App\Model\SqlModel\Tanwan\V2DWSDayClickLogModel;
use App\Model\SqlModel\Tanwan\V2DWDADTrackBaseLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\Param\DMS\AbnormalAgentClickParam;
use App\Utils\Helpers;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

$logger = Helpers::getLogger('AgentAbnormalClick');

// -d 2021-01-20 d参数接收日期
// 异常点击默认刷前一天的数据
$input = getopt('d:');
// 默认时间和累计时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date('Y-m-d', strtotime('-1 day'));
$date_last_second = $date . ' 23:59:59';
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START:' . $date );
MysqlConnection::setConnection();

$model = new V2DWDAgentAbnormalClickModel();
$ad_track_base_log_model = new V2DWDADTrackBaseLogModel();
$day_click_model = new V2DWSDayClickLogModel();
$logic = new GameWarningLogic();

$day_click_counts = $day_click_model->getDayClickCountByDate($date);
// $continuous_ips = $ad_track_base_log_model->getContinuousIPByTimeRange($date, $date_last_second);
$insert_data = [];
$abnormal_ip = Collection::make();

foreach ($day_click_counts as $day_click) {
    $param = new AbnormalAgentClickParam($day_click);
    $param->click_date_range = [$date, $date_last_second];
    $click_err = '';
    if ($day_click->day_click_count < 600) {
        // ip单日最大点击
        $ip_click = $ad_track_base_log_model->getIPClickByAgentGameDate($param);
        $abnormal_num = 0;
        foreach ($ip_click as $item) {
            if ($item->click_num > 6) {
                $abnormal_num++;
            }
        }
        if ($abnormal_num >= 10) {
            $click_err .= "ip单日最大点击是{$abnormal_num}次 (日最大应在10内)\r\n频繁点击的ip有{$abnormal_num}个";
        }
        // 连续ip段
        $continuous_ip = $ad_track_base_log_model->getContinuousIP($param);
//        $continuous_ip = $continuous_ips->where('platform', $param->platform)
//            ->where('agent_id', $param->agent_id)
//            ->where('game_id', $param->game_id);
        $abnormal_num = 0;
        foreach ($continuous_ip as $item) {
            if ($item->count >= 10) {
                $abnormal_num++;
                $abnormal_ip->push($item);
            }
        }
        $continuous_ip_rate = $continuous_ip->count() > 0 ? round($abnormal_num / $continuous_ip->count() * 100, 2) : 0;
        $day_click->continuous_ip_rate = '10_' . $continuous_ip_rate;// 连续ip段10次的比例
        if ($abnormal_num >= 6) {
            $click_err .= "连续10个的ip段出现了{$abnormal_num}次(应不超过6次)\r\n";
            foreach ($abnormal_ip as $ip) {
                $click_err .= "同区域{$ip->ip_segment}.* 点击过多,有{$ip->count}个ip\r\n";
            }
        }
    } else if ($day_click->day_click_count >= 600 && $day_click->day_click_count < 1100) {
        // ip单日最大点击
        $ip_click = $ad_track_base_log_model->getIPClickByAgentGameDate($param);
        $ip_click = $ip_click->sortBy('click_num')->values();
        $ip_click_once_count = $ip_click->where('click_num', 1)->count();
        $ip_click_greater_than_20 = $ip_click->where('click_num', '>', 20)->count();
        if ($ip_click_greater_than_20 >= 5) {
            $click_err .= "存在超过10%的超高频次点击, ip日点击20次的ip的总数{$ip_click_greater_than_20}\r\n";
        }
        // ip点击X次中75%是1
        $three_fourths = $logic->threeFourths($ip_click);
        if ($three_fourths > 1) {
            $greater_than_2_rate = round(($day_click->day_click_count - $ip_click_once_count) / $day_click->day_click_count * 100, 2);
            $click_err .= "ip2次+点击产生的点击占比是{$greater_than_2_rate}%\r\n";
        }
        // 连续ip段
        $continuous_ip = $ad_track_base_log_model->getContinuousIP($param);
//        $continuous_ip = $continuous_ips->where('platform', $param->platform)
//            ->where('agent_id', $param->agent_id)
//            ->where('game_id', $param->game_id);
        $abnormal_num = 0;
        foreach ($continuous_ip as $item) {
            if ($item->count >= 10) {
                $abnormal_num++;
                $abnormal_ip->push($item);
            }
        }
        $continuous_ip_rate = $continuous_ip->count() > 0 ? round($abnormal_num / $continuous_ip->count() * 100, 2) : 0;
        $day_click->continuous_ip_rate = '10_' . $continuous_ip_rate;// 连续ip段10次的比例
        if ($abnormal_num >= 12) {
            foreach ($abnormal_ip as $ip) {
                $click_err .= "同区域{$ip->ip_segment}.* 点击过多,有{$ip->count}个ip\r\n";
            }
        }
    } else if ($day_click->day_click_count >= 1100 && $day_click->day_click_count < 1600) {
        // ip点击X次中75%是1
        $ip_click = $ad_track_base_log_model->getIPClickByAgentGameDate($param);
        $ip_click = $ip_click->sortBy('click_num')->values();
        $ip_click_once_count = $ip_click->where('click_num', 1)->count();
        $three_fourths = $logic->threeFourths($ip_click);
        if ($three_fourths > 1) {
            $greater_than_2_rate = round(($day_click->day_click_count - $ip_click_once_count) / $day_click->day_click_count * 100, 2);
            $click_err .= "超过1次点击ip占比过多有{$greater_than_2_rate}%\r\n";
        }
        // 连续ip段
        $continuous_ip = $ad_track_base_log_model->getContinuousIP($param);
//        $continuous_ip = $continuous_ips->where('platform', $param->platform)
//            ->where('agent_id', $param->agent_id)
//            ->where('game_id', $param->game_id);
        $abnormal_num = 0;
        foreach ($continuous_ip as $item) {
            if ($item->count >= 20) {
                $abnormal_num++;
                $abnormal_ip->push($item);
            }
        }
        $continuous_ip_rate = $continuous_ip->count() > 0 ? round($abnormal_num / $continuous_ip->count() * 100, 2) : 0;
        $day_click->continuous_ip_rate = '20_' . $continuous_ip_rate;// 连续ip段20次的比例
        if ($abnormal_num >= 7) {
            foreach ($abnormal_ip as $ip) {
                $click_err .= "同区域{$ip->ip_segment}点击过多,有{$ip->count}个ip\r\n";
            }
        }
    } else if ($day_click->day_click_count >= 1600) {
        // ip点击超过6次+的占比
        $ip_click = $ad_track_base_log_model->getIPClickByAgentGameDate($param);
        $ip_click = $ip_click->sortBy('click_num')->values();
        $ip_click_once_count = $ip_click->where('click_num', 1)->count();
        $count = $ip_click->count();
        $abnormal_num = 0;
        foreach ($ip_click as $item) {
            if ($item->click_num >= 100) { // 如果日点击ip大于等于100 直接判断异常，其他条件则忽略
                $column = $item->ip == '' ? 'muid' : 'ip';
                $click_err .= "单{$column}日点击超过100 有{$item->click_num}次\r\n";
            }
            if ($item->click_num >= 6) {
                $abnormal_num++;
            }
        }
        // 日点击ip大于等于100个数
        $day_click->more_than_100_click_num = $ip_click->where('click_num','>=', 100)->count();
        if ($count > 0 && $abnormal_num / $count > 0.045) {
            $click_err .= "频繁点击的ip过多有{$abnormal_num}次\r\n";
        }
        // ip日点击次数中75%是1
        $three_fourths = $logic->threeFourths($ip_click);
        if ($three_fourths > 1) {
            $greater_than_2_rate = round(($day_click->day_click_count - $ip_click_once_count) / $day_click->day_click_count * 100, 2);
            $click_err .= "ip2次+点击产生的点击占比是{$greater_than_2_rate}%\r\n";
        } else if ($three_fourths == 1) {
            $three_fourths = ($count + 1) * 0.75;
            $last_one_fourths = Collection::make();
            $last_abnormal_ip = Collection::make();
            foreach ($ip_click as $key => $item) {
                if ($key > $three_fourths) {
                    $last_one_fourths->push($item);
                    if ($item->click_num > 2) {
                        $last_abnormal_ip->push($item);
                    }
                }
            }
            $last_three_fourths = $logic->threeFourths($last_one_fourths);
            if ($last_three_fourths > 2) {
                $greater_than_3_rate = round($last_abnormal_ip->sum('click_num') / $count * 100, 2);
                $click_err .= "ip3次+(含3)产生超过{$greater_than_3_rate}%的点击\r\n";
            }
        }
        // 连续ip段
        $continuous_ip = $ad_track_base_log_model->getContinuousIP($param);
//        $continuous_ip = $continuous_ips->where('platform', $param->platform)
//            ->where('agent_id', $param->agent_id)
//            ->where('game_id', $param->game_id);
        $abnormal_num = 0;
        foreach ($continuous_ip as $item) {
            if ($item->count >= 20) {
                $abnormal_num++;
                $abnormal_ip->push($item);
            }
        }
        $continuous_ip_rate = $continuous_ip->count() > 0 ? round($abnormal_num / $continuous_ip->count() * 100, 2) : 0;
        $day_click->continuous_ip_rate = '20_' . $continuous_ip_rate;// 连续ip段20次的比例
        if ($count > 0 && $abnormal_num / $count > 0.005) {
            $round = round($abnormal_num / $count * 100, 2);
            $click_err .= "同区域ip点击过多，占到了{$round}%\r\n";
        }
    }
    $is_click_abnormal = !empty($click_err);
    $insert_data[] = [
        'platform' => $day_click->platform,
        'agent_id' => $day_click->agent_id,
        'game_id' => $day_click->game_id,
        'date' => $day_click->click_date,
        'day_click_count' => $day_click->day_click_count,
        'is_click_abnormal' => $is_click_abnormal,
        'click_abnormal_msg' => $click_err,
        'continuous_ip_rate' => $day_click->continuous_ip_rate ?? 0,
        'more_than_100_click_num' => $day_click->more_than_100_click_num ?? 0,
    ];
    $ad_track_base_log_model->builder->getConnection()->flushQueryLog();// 取消查询日志
}

$logger->info('END date:' . $date);

try {
    MysqlConnection::getConnection('default')->beginTransaction();
} catch (\Throwable $e) {
    $logger->error('事务开启异常，结束任务。');
    return;
}
$chunk_insert_data = array_chunk($insert_data, 10000);
foreach ($chunk_insert_data as $item) {
    (new V2DWDAgentAbnormalClickModel())->addClickInfo($item);
}
MysqlConnection::getConnection('default')->commit();