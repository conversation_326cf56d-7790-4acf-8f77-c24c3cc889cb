<?php

use App\Logic\DSP\DimensionMonitorRobotLogic;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\RedisModel\DimensionMonitorRobotFrequencyModel;
use App\Model\SqlModel\Zeda\DimensionMonitorRobotModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

echo date('Y-m-d H:i:s') . ' 维度监控开始' . PHP_EOL;

$redis_model = (new DimensionMonitorRobotFrequencyModel());

$list = (new DimensionMonitorRobotModel())->getExecList();

/**
 * @param $list
 * @param $sql
 * @return void
 */
function sendFeishuMsg($list, $sql)
{
    $user_info = (new UserModel())->getInfoByName('张中昊');
    $user_id = $user_info['id'];
    if (!$user_info) {
        return;
    }

    if (!$user_info['feishu_union_id']) {
        return;
    }

    $feishu_union = explode('-', $user_info['feishu_union_id']);
    $feishu_info = ['platform' => $feishu_union[0], 'union_id' => $feishu_union[1]];

    $developer_info = (new MediaDeveloperAccountModel())->getFeishuSubscriptList()->keyBy('platform');

    if (
        !isset($developer_info[$feishu_info['platform']]->app_id) ||
        !isset($developer_info[$feishu_info['platform']]->app_secret)
    ) {
        echo date('Y-m-d H:i:s') . '飞书通知发送失败, user_id:' . $user_id . '有问题' . PHP_EOL;
        return;
    }
    $union_id = $feishu_info['union_id'];
    $app_id = $developer_info[$feishu_info['platform']]->app_id;
    $app_secret = $developer_info[$feishu_info['platform']]->app_secret;

    $content = [];
    $content[] = [['tag' => "text", 'text' => "维度机器人查出来数据了"]];

    $content[] = [
        [
            'tag' => "text",
            'text' =>
                'list:' . json_encode($list, JSON_UNESCAPED_UNICODE)
        ]
    ];

    $content[] = [
        [
            'tag' => "text",
            'text' =>
                'sql:' . $sql
        ]
    ];


    $app_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $app_id, $app_secret);
    $content_data = [
        'zh_cn' => [
            'title' => "维度监控机器人执行操作",
            "content" => $content
        ]
    ];

    $model = new MessageModel();
    $content_data = json_encode($content_data);
    $model->message($app_access_token, 'union_id', $union_id, $content_data, 'post');
}

foreach ($list as $robot) {
    if ($redis_model->getFrequencyLockForRobot($robot->id)) {
        continue;
    }

    try {
//        $monitor = MediaIntelligentMonitor::create($robot->media_type, $robot->media_agent_type);

        $robot->condition_compose = json_decode($robot->condition_compose, true);
        $input = (array)$robot;

        $result = (new DimensionMonitorRobotLogic())->previewDimensionMonitorRobot($input);

        sendFeishuMsg($result['list'], $result['sql']);

//        /* @var Collection $list */
//        $exec_list = $result['list'];
//        if ($exec_list->isNotEmpty()) {
//            foreach ($exec_list as $exec_info) {
//
//                $bind = new ADIntelligentMonitorBindParam([
//                    'media_type' => $robot->media_type,
//                    'media_agent_type' => $robot->media_agent_type,
//                    'bind_type' => 'precise',
//                    'bind_target_type' => $robot->exec_target_dim,
//                    'bind_target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
//                    'target_type' => $robot->exec_target_dim,
//                    'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
//                    'unbind_target_value_list' => [],
//                    'finish_target_value_list' => [],
//                    'monitor_robot_id' => $robot->id,
//                    'first_exec_time' => date('Y-m-d H:i:s'),
//                ]);
//                // {"target": "put_status", "target_value": "2"}
//                $action = [
//                    'target' => $robot->action_target,
//                    'target_value' => $robot->action_value,
//                ];
//
//
//                $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}] = new ADIntelligentMonitorRobotLogParam([
//                    'robot_id' => $robot->id,
//                    'creator' => $robot->creator,
//                    'media_type' => $robot->media_type,
//                    'media_agent_type' => $robot->media_agent_type,
//                    'bind_type' => 'precise',
//                    'bind_target_type' => $robot->exec_target_dim,
//                    'bind_target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
//                    'target_type' => $robot->exec_target_dim,
//                    'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
//                ]);
//
//                $monitor->execAction($bind, $action);
//
//                $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->action_result = 'ok';
//                if ($monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective == '') {
//                    $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective = 'effective';
//
//                    (new DimensionMonitorRobotLogModel())->log(new DimensionMonitorRobotLogParam([
//                        'robot_id' => $robot->id,
//                        'media_type' => $robot->media_type,
//                        'media_agent_type' => $robot->media_agent_type,
//                        'target_type' => $robot->exec_target_dim,
//                        'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
//                        'action_target' => $robot->action_target,
//                        'action_value' => $robot->action_value,
//                        'is_effective' => $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective,
//                        'msg' => '',
//                        'sql' => $result['sql'],
//                        'creator' => $robot->creator,
//                    ]));
//                    exit;
//                }
//            }
//        }

        $redis_model->setFrequencyLockForRobot($robot->id, $robot->monitoring_frequency);

    } catch (Throwable $exception) {
        echo date('Y-m-d H:i:s 出错了:') . $exception->getMessage() . PHP_EOL;
        continue;
    }
}

exit;
