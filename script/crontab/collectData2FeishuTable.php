<?php
/**
 * 商务引入游戏数据 by 俊文
 * 业务逻辑：php从飞书表格获取已授权的游戏参数，拿参数去调大数据接口，让大数据跑任务清洗，然后php再到清洗表取数，接着写入多维表格
 * 0 3,8 * * * php collectData2FeishuTable.php
 * @server 120.55.83.156 zx-dms
 */

use App\Constant\Environment;
use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\BiTable\RecordModel;
use App\Model\SqlModel\Tanwan\V2DwdRootGameFlowWaterLogModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

class collectData2FeishuTable
{
    public $app_id;

    public $app_secret;

    public $app_access_token;

    public $auth_table_config;

    public $product_table_config;

    public $current_date_config;

    public $is_run_get_data_by_cli = false;

    public function __construct()
    {
        $this->initConfig();
        $app_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, $this->app_id, $this->app_secret);
        $this->app_access_token = $app_access_token;
    }

    /**
     * @return void
     */
    public function initConfig()
    {
        $this->app_id = EnvConfig::FEISHU['file_assistant']['app_id'];
        $this->app_secret = EnvConfig::FEISHU['file_assistant']['app_secret'];

        if (EnvConfig::ENV == Environment::PROD) {
            $this->auth_table_config = [
                'app_token' => 'CBIDb8r5IadasusAgJMcFkRnnVc',
                'table' => 'tblvNYNdRZoSCMGZ',
                'view_id' => 'vewmpTlb4x',
            ];
            $this->product_table_config = [
                'app_token' => 'IOtDb71FGaYL7BsKzEncGcjPnPg',
                'table' => 'tblwwdj53AJeGVX3',
                'view_id' => 'vewdLiJ7QG',
            ];
            $this->current_date_config = [
                'app_token' => 'IOtDb71FGaYL7BsKzEncGcjPnPg',
                'table' => 'tbl2uhqAAjlRAMMl',
                'view_id' => 'vewwQZWcGo',
            ];
        } else {
            $this->auth_table_config = [
                'app_token' => 'S2RzbhTkTasoTYsVGUEc1q0nnNb',
                'table' => 'tblwjSlyNDHjATLE',
                'view_id' => 'vewmpTlb4x',
            ];
            $this->product_table_config = [
                'app_token' => 'CoDTbheZsaAMZOs33LUcJDSunzh',
                'table' => 'tblHFkVtu25Bip8z',
                'view_id' => 'vewdLiJ7QG',
            ];
            $this->current_date_config = [
                'app_token' => 'CoDTbheZsaAMZOs33LUcJDSunzh',
                'table' => 'tblSwBcc3tKk6pJl',
                'view_id' => 'vewAF8SHCD',
            ];
        }
    }

    /**
     * @return void
     */
    public function main()
    {
        // 运行逻辑：因为调完接口清洗数据需要时间，所以在凌晨的时候去清洗，白天再写数据到表格
        try {
            $run_time = time();
            if ($run_time > strtotime(date('Y-m-d 00:00:00')) && $run_time < strtotime(date('Y-m-d 06:00:00')) || $this->is_run_get_data_by_cli) {
                $this->getGameAndCreateTask();
            } else {
                $this->deleteTableRecord();
                $this->insertTableRecord();
            }
        } catch (Exception $exception) {
            Helpers::getLogger('feishu_bi_table')->error("汇总数据多维表格运行出错", ['msg' => $exception->getMessage(), 'code' => $exception->getCode()]);
            echo $exception->getMessage() . PHP_EOL;
        }

    }

    /**
     * 拉取授权的游戏参数去调接口
     * @return void
     */
    public function getGameAndCreateTask()
    {
        echo "开始 getGameAndCreateTask:" . date('Y-m-d H:i:s') . PHP_EOL;
        // 拉取授权明细数据,拿到平台+根游戏id映射
        $request_data = [
            'view_id' => $this->auth_table_config['view_id'],
            'field_names' => ['平台', '根游戏ID', '授权状态'],
            'filter' => [
                'conjunction' => 'and',
                'conditions' => [
                    [
                        'field_name' => '授权状态',
                        'operator' => 'is',
                        'value' => ['同意'],// 只筛选已同意的游戏
                    ]
                ]
            ]
        ];
        $platform_root_game = [];
        $i = 0;
        $page_token = null;
        $zx_map = array_flip(EnvConfig::PLATFORM_MAP);
        $zeda_map = ['万紫' => 'wanzi', '圣宏' => 'shenghong'];
        while (true) {
            $auth_game_data = (new RecordModel())->search($this->app_access_token, $this->auth_table_config['app_token'], $this->auth_table_config['table'], $request_data, 'union_id', 500, $page_token);
            foreach ($auth_game_data['items'] as $item) {
                $platform_name = $item['fields']['平台'];
                $platform = $zx_map[$platform_name] ?? $zeda_map[$platform_name] ?? '';
                if (empty($platform)) {
                    continue;
                }
                $root_game_id = $item['fields']['根游戏ID'];
                $platform_root_game[] = "{$platform}-{$root_game_id}";
            }

            if (!$auth_game_data['has_more']) {
                break;
            }
            $page_token = $auth_game_data['page_token'];
            // 防止飞书的接口返回分页数据状态有问题，导致死循环，防一手
            $i++;
            if ($i > 100) {
                break;
            }
        }

        // 读取要刷的年月数据
        $request_data = [
            'view_id' => $this->current_date_config['view_id'],
        ];
        $current_date_data = (new RecordModel())->search($this->app_access_token, $this->current_date_config['app_token'], $this->current_date_config['table'], $request_data, 'union_id', 500, $page_token);
        if (!isset($current_date_data['items'][0]['fields']['当年年份'])) {
            throw new AppException("年份 出错");
        }
        if (!isset($current_date_data['items'][0]['fields']['当月月份'])) {
            throw new AppException("月份 出错");
        }
        $current_year = $current_date_data['items'][0]['fields']['当年年份'];
        $current_month = date('Ym', substr($current_date_data['items'][0]['fields']['当月月份'], 0, 10));


        if (!empty($platform_root_game)) {
            // 请求大数据的接口,让大数据那边去跑数据
            $zx_url = "http://zx-exector.zx.com:18202/executor/call/TASK/root_game_flow_water_data";
            $request_data = [
                'bindings' => [
                    "platformRootGameIdArr" => $platform_root_game,
                    "yearMonth" => $current_month,
                    "year" => $current_year,
                ],
            ];
            $result = Helpers::postCurl($zx_url, json_encode($request_data), [
                'header' => [
                    'Content-Type: application/json; charset=utf-8'
                ]
            ]);
            $result = json_decode($result, true);
            if ($result === null || $result['code'] !== 0) {
                Helpers::getLogger('feishu_bi_table')->error("大数据接口返回失败", ["url" => $zx_url, 'request_data' => ['bindings' => ["platformRootGameIdArr" => $platform_root_game]]]);
            }
            echo "调用大数据接口结束" . date('Y-m-d H:i:s') . PHP_EOL;
        }
        echo "结束 getGameAndCreateTask:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    /**
     * 清空《引入产品流水情况》-导出源数据的记录
     * @return void
     */
    public function deleteTableRecord()
    {
        echo "开始 deleteTableData:" . date('Y-m-d H:i:s') . PHP_EOL;
        $i = 0;
        $page_token = null;
        $batch_delete_record = [];
        while (true) {
            $request_data = [
                'view_id' => $this->product_table_config['view_id'],
            ];
            $record_data = (new RecordModel())->search($this->app_access_token, $this->product_table_config['app_token'], $this->product_table_config['table'], $request_data, 'union_id', 500, $page_token);
            foreach ($record_data['items'] as $item) {
                $batch_delete_record[] = $item['record_id'];
            }

            if (!$record_data['has_more']) {
                break;
            }
            $page_token = $record_data['page_token'];
            // 防止飞书的接口返回分页数据状态有问题，导致死循环，防一手
            $i++;
            if ($i > 100) {
                break;
            }
        }
        (new RecordModel())->batchDelete($this->app_access_token, $this->product_table_config['app_token'], $this->product_table_config['table'], $batch_delete_record);
        echo "结束 deleteTableData:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    /**
     * 读取大数据跑完的数据表，写入表格
     * @return void
     */
    public function insertTableRecord()
    {
        echo "开始 写入数据:" . date('Y-m-d H:i:s') . PHP_EOL;
        $insert_data = [];
        $data = (new V2DwdRootGameFlowWaterLogModel())->getFlowWaterLog();
        if ($data->isEmpty()) {
            throw new AppException("v2_dwd_root_game_flow_water_log 数据为空");
        }
        foreach ($data as $item) {
            $fields = [
                'PR' => "{$item->platform}-{$item->root_game_id}",
                '累计消耗' => (float)$item->cost_money,
                '当月新增' => (int)$item->current_ym_reg_count,
                '累计新增' => (int)$item->reg_count,
                '当月流水' => (float)$item->current_ym_pay_money_sum,
                '累计流水' => (float)$item->pay_money_sum,
                '当年累计流水' => (float)$item->current_y_pay_money_sum,
                '预估运营利润' => (float)$item->pre_day_360_profit_exclude_other_cost,
                '预估运营利润（含其他成本）' => (float)$item->pre_day_360_profit,
            ];
            $insert_data[] = ['fields' => $fields];
        }

        $create_result = (new RecordModel())->batchCreate($this->app_access_token, $this->product_table_config['app_token'], $this->product_table_config['table'], $insert_data);
        if (count($insert_data) != count($create_result['records'])) {
            Helpers::getLogger('feishu_bi_table')->error("飞书多维表格个别数据插入失败", ["request_data" => $insert_data, 'result' => $create_result]);
        }
        echo "结束 写入数据:" . date('Y-m-d H:i:s') . PHP_EOL;
    }


}

$is_run_get_data = isset($argv[1]) && $argv[1] == 'get';
$class = new collectData2FeishuTable();
$class->is_run_get_data_by_cli = $is_run_get_data;
$class->main();