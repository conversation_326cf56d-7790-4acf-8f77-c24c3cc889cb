<?php

/**
 * 新版其他成本配置，把配置组合拆分出来，传给沛奇
 * 0 12,14 * * * php collectOtherCostConfigDetail.php
 * @server ************ zx-script
 */

use App\Model\SqlModel\Tanwan\V2DwdRootGameOtherCostLogModel;
use App\Model\SqlModel\Zeda\OtherCostModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

$model = new  OtherCostModel();
$list = $model->getAll();

foreach ($list as $item) {
    $other_cost_config = json_decode($item->other_cost_config, true);
    $sub_other_cost_config = $other_cost_config['other_cost_config'];
    foreach ($sub_other_cost_config as $sub_index => $sub_other_cost_config_item) {
        foreach ($sub_other_cost_config_item['project_teams'] as $project_team) {
            foreach ($sub_other_cost_config_item['root_games'] as $root_game) {
                $insert_data[] = [
                    'id' => $item->id,
                    'sub_id' => $sub_index + 1,
                    'money_type' => $other_cost_config['money_type'],
                    'total_money' => $other_cost_config['money'],
                    'platform' => $sub_other_cost_config_item['platform'],
                    'root_game_id' => $root_game['root_game_id'],
                    'project_team_id' => $project_team['project_team_id'],
                    'proxy_type' => $sub_other_cost_config_item['proxy_type'],
                    'start_date' => $sub_other_cost_config_item['start_month'] . '-01', // Y-m-01
                    'end_date' => $sub_other_cost_config_item['end_month'] . '-01', // Y-m-01
                    'money' => $sub_other_cost_config_item['divide_money'],
                    'agency' => $sub_other_cost_config_item['agency'],
                    'remark' => $sub_other_cost_config_item['remark'],
                    'creator' => $item->creator,
                    'operator' => $item->operator,
                    'create_time' => $item->insert_time,
                    'operation_time' => $item->update_time,
                ];
            }
        }
    }
}

$root_game_other_cost_log_model = new V2DwdRootGameOtherCostLogModel();
$root_game_other_cost_log_model->deleteAll();
$root_game_other_cost_log_model->replace($insert_data ?? []);