<?php
/**
 * 0 12 * * *
 * 渠道异常付费记录
 */

use App\Logic\DMS\GameWarningLogic;
use App\Model\SqlModel\Tanwan\V2DWDGameUidRegLogModel;
use App\Model\SqlModel\Tanwan\V2DWDAgentAbnormalClickModel;
use App\Utils\Helpers;
use App\MysqlConnection;
use Illuminate\Database\Query\Builder;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');
$logger = Helpers::getLogger('AgentAbnormalPay');

$logger = Helpers::getLogger('AgentAbnormalPay');

// -d 2021-01-20 d参数接收日期
// 异常点击默认刷前一天的数据
$input = getopt('d:');
// 默认时间和累计时间要一致
$date = isset($input['d']) ? date('Y-m-d', strtotime($input['d'])) : date('Y-m-d', strtotime('-6 day'));
$date_last_second = $date . ' 23:59:59';
if (strtotime($date) < strtotime('2020-01-01')) {
    $logger->info('日期参数太小，请确认后重试，date：' . $date);
    return;
}
$logger->info('START:' . $date);
MysqlConnection::setConnection();

$game_uid_reg_model = new V2DWDGameUidRegLogModel();
$user_regs = $game_uid_reg_model->getUserCountGroup($date, $date_last_second);

$game_reg_24_hour_login_times = collect();
if ($user_regs->isNotEmpty()) {
    $platform_filters = $user_regs->pluck('platform')->unique();
    $agent_filters = $user_regs->pluck('agent_id')->unique();
    $game_filters = $user_regs->pluck('game_id')->unique();
    $game_reg_24_hour_login_times = $game_uid_reg_model->getGameReg24HourLoginTimes($date, $date_last_second,
        function (Builder $query) use ($platform_filters, $agent_filters, $game_filters) {
            $query
                ->whereIn('platform', $platform_filters)
                ->whereIn('agent_id', $agent_filters)
                ->whereIn('game_id', $game_filters);
        });
}

$logic = new GameWarningLogic();
$agent_abnormal_model = new V2DWDAgentAbnormalClickModel();

foreach ($user_regs as $user_reg) {
    $pay_err = '';
    $agent_not_pay_game_reg_24_hour_login_times = collect();
    $agent_pay_more_than_once_game_reg_24_hour_login_times = collect();
    foreach ($game_reg_24_hour_login_times as $key => $item) {
        if ($item->agent_id == $user_reg->agent_id &&
            $item->game_id == $user_reg->game_id &&
            $item->platform == $user_reg->platform
        ) {
            if ($item->total_pay_times == 0) {
                $agent_not_pay_game_reg_24_hour_login_times->push($item);
            } else {
                $agent_pay_more_than_once_game_reg_24_hour_login_times->push($item);
            }
            unset($game_reg_24_hour_login_times[$key]);
        }
    }

    // 注册24小时内登陆次数
    $not_pay_three_fourths = $logic->median($agent_not_pay_game_reg_24_hour_login_times, 0.75, 'game_reg_24_hour_login_times');
    $pay_more_than_once_fifty_percent = $logic->median($agent_pay_more_than_once_game_reg_24_hour_login_times, 0.5, 'game_reg_24_hour_login_times');
    $pay_more_than_once_three_fourths = $logic->median($agent_pay_more_than_once_game_reg_24_hour_login_times, 0.75, 'game_reg_24_hour_login_times');

    if ($user_reg->pay_user_count >= 30 && $not_pay_three_fourths > $pay_more_than_once_three_fourths) {
        $pay_err .= "付费用户相对于不付费用户不活跃\r\n";
    }

    if ($user_reg->pay_user_count >= 30 && $pay_more_than_once_fifty_percent < 1) {
        $pay_err .= "付费用户首日活跃性较差\r\n";
    }
    // 老付费设备占比
    $total_m_uid = floatval($user_reg->count_is_old_pay_muid) + floatval($user_reg->count_not_old_pay_muid);
    $old_pay_m_uid_rate = $total_m_uid >0 ? floatval($user_reg->count_is_old_pay_muid) / $total_m_uid * 100 : 0;
    $old_pay_m_uid_rate = round($old_pay_m_uid_rate, 2);
    $user_reg->old_pay_m_uid_rate = $old_pay_m_uid_rate;
    if ($user_reg->pay_user_count >= 30 && $old_pay_m_uid_rate > 50) {
        $pay_err .= "老设备付费占比超过{$old_pay_m_uid_rate}%\r\n";
    }
    $user_reg->pay_err = $pay_err;
    $update_param = [
        'agent_id' => $user_reg->agent_id,
        'game_id' => $user_reg->game_id,
        'date' => $date,
        'platform' => $user_reg->platform,
        'is_pay_abnormal' => !empty($pay_err),
        'day_pay_count' => $user_reg->pay_user_count,
        'pay_abnormal_msg' => $pay_err,
        'old_pay_m_uid_rate' => $user_reg->old_pay_m_uid_rate ?? 0,
    ];
    if (strlen($update_param['platform']) > 10) continue;
    $agent_abnormal_model->updateOrInsertByPK($update_param);
}

$logger->info('END date:' . $date);