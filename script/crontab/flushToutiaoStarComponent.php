<?php
/**
 * 刷新星图组件审核状态
 */

// */30 * * * * /usr/local/php/bin/php /data/www/script.zx.com/script/crontab/flushToutiaoStarComponent.php >> /data/www/script.zx.com/logs/flushToutiaoStarComponent.log
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Xingtu\XingtuModel;
use App\Model\SqlModel\Zeda\ToutiaoStarComponentTaskModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$task_model = new ToutiaoStarComponentTaskModel();
$task_data = $task_model->getWaitAuditDataForCrontab();

foreach ($task_data as $task_datum) {

    try {
        $component_list = (new XingtuModel())->setAccount($task_datum->account_id)->getAllComponentList($task_datum->component_id);
        if (!empty($component_list['links'][0])) {
            $update_data = [];
            $update_data['audit_status'] = $component_list['links'][0]['status'] ?? -1;
            $update_data['audit_msg'] = $component_list['links'][0]['ban_reason'] ?? '';
            $task_model->edit($task_datum->id, $update_data);
        }
    } catch (AppException $e) {
        echo date('Y-m-d H:m:s') . '：刷新任务错误|' . $task_datum->id . '|error:' . $e->getMessage() . PHP_EOL;
    }
}



