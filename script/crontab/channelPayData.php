<?php
/**
 * 0 3 * * *
 * 渠道（channel_id）级别的流水
 * 默认同步最近一年数据
 */

use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$logger = Helpers::getLogger('channelPayData');
$pay_order_log = new \App\Model\SqlModel\DatahubLY\V2ODSPayOrderLogModel();
$channel_pay_money = new \App\Model\SqlModel\Zeda\ChannelPayMoneyModel();
$site_id_model = new \App\Model\SqlModel\DatahubLY\ViewV2DimSiteIdModel();
$prime_cost_field_model = new \App\Model\SqlModel\Zeda\PrimeCostFieldModel();
$operate_cost_field_model = new \App\Model\SqlModel\Zeda\OperateCostFieldModel();
$operation_cost_field_model = new \App\Model\SqlModel\Zeda\OperationCostFieldModel();
$service = new \App\Logic\LY\DataHubLogic();

# -----------------------------  开始同步发行游戏支付总额数据  -----------------------------#
$to_date = strtotime(date("Y-m-d", time()));     // 今天凌晨
$start_date = $to_date - 86400 * 365;                  // 最近365天
//$start_date = strtotime('2017-01-01');         // 全量更新的时候 从17年开始
$per_time = 86400 * 10; // 30天一次

$logger->info('开始任务');
try {
    MysqlConnection::getConnection('default')->beginTransaction();
} catch (\Throwable $e) {
    $logger->error('事务开启异常，结束任务。时间区间： ');
    return;
}

$logger->info('开始删除数据');

$channel_pay_money->deleteByDate($start_date, $to_date);

$logger->info('数据删除完成');
while (1) {
    // 终止条件
    if ($start_date > $to_date) break;
    $logger->info('开始同步发行渠道历史数据，时间区间： ' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
    try {
        $total_list = $pay_order_log->getChannelDayData($start_date, $start_date + $per_time);
        $chunk_arr = $total_list->chunk(1000);
        foreach ($chunk_arr as $chunk_list) {
            $insert_data = [];
            foreach ($chunk_list as $item) {
                $insert_data[] = [
                    'platform' => $item->platform ?? '',
                    'root_game_id' => $item->root_game_id ?? 0,
                    'root_game_name' => $item->root_game_name ?? '',
                    'main_game_id' => $item->main_game_id ?? 0,
                    'main_game_name' => $item->main_game_name ?? '',
                    'game_id' => $item->game_id ?? 0,
                    'game_name' => $item->game_name ?? '',
                    'server_id' => $item->server_id ?? 0,
                    'server_name' => $item->server_name ?? '',
                    'channel_name' => $item->channel_name ?? '',
                    'channel_id' => $item->channel_id ?? 0,
                    'game_pay_money' => $item->game_pay_money ?? 0,
                    'pay_date' => $item->pay_date,
                    'plat_id' => $item->plat_id,
                    'plat_name' => $item->plat_name
                ];
            }
            // 入库
            $channel_pay_money->addMultiple($insert_data);
        }

        $logger->info('发行游戏数据同步成功。时间区间： ' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
    } catch (\Throwable $e) {
        $logger->error('发行数据同步失败，回滚数据,时间区间:' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
        $logger->error('发行数据同步失败，回滚数据, 异常信息：' . substr($e->getMessage(), 0, 1000));
        try {
            MysqlConnection::getConnection('default')->rollBack();
        } catch (\Throwable $e) {
            $logger->error('事务回滚异常！');
        }
        exit;
    }

    $add_time = $per_time + 86400;
    $start_date += $add_time;
}

$logger->info('发行游戏数据同步完成');
MysqlConnection::getConnection('default')->commit();

// 确定同步日期，如果是每个月1号 则同步上个月的数据 否则同步当月
$date = date("Y-m-d");
$is_01 = substr($date, 8) === '01';
$cost_month = $is_01 ? date("Y-m", strtotime(' - 1 month')) : date("Y-m");
$start_date = date("Y-m-01", strtotime($cost_month));
$end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));

# -----------------------------  成本、其他成本、运营成本补录  -----------------------------#
$logger->info('开始同步共享的成本数据');

/* 先同步共享的operation_cost_filed */
// 获取需要更新的root_game_id
$need_update_root_game = $operation_cost_field_model->getShareRootByCostMonth($cost_month, 'ly');
$insert_data = [];
foreach ($need_update_root_game as $root_game_item) {
    // 共享的需要同步到根游戏下的所有主游戏
    $main_game_list = (new \App\Model\SqlModel\Tanwan\V2DimGameIdModel())->getLYMainGameByRootGameId($root_game_item->root_game_id, $root_game_item->platform);
    // 获取这个根游戏的子游戏数量，根据根游戏下祝游戏去view_v2_dim_site_id 表查
    $main_game_id = $main_game_list->pluck('main_game_id')->toArray();
    // 获取时间范围内根游戏总流水
    $total_pay_money = $channel_pay_money->getLYRootGameTotalPayMoney($main_game_id, $root_game_item->platform, [$start_date, $end_date]);
    $channel_count = $site_id_model->getChannelCountByMainGame($root_game_item->platform, $main_game_id);

    $per_data = $service->perCostMoney($start_date,
        $end_date,
        $total_pay_money,
        $root_game_item->authorization_money,
        $root_game_item->endorsement_money,
        $root_game_item->server_cost,
        $channel_count);
    $insert_data = [];

    // 共享情况下 mark通过根游戏流水判断
    $mark = $total_pay_money > 0 ? 1 : 0;
    foreach ($main_game_list as $main_game_item) {
        // 补充游戏数据
        $service->makeUpChannelPayMoney($root_game_item->platform, $root_game_item->root_game_id, $main_game_item->main_game_id, $start_date, $end_date);
        $insert_data[] = [
            'platform' => $root_game_item->platform,
            'root_game_id' => $root_game_item->root_game_id,
            'main_game_id' => $main_game_item->main_game_id,
            'per_authorization_money' => $per_data['per_authorization_money'],
            'per_endorsement_money' => $per_data['per_endorsement_money'],
            'per_server_cost' => $per_data['per_server_cost'],
            'per_day_authorization_money' => $per_data['per_day_authorization_money'],
            'per_day_endorsement_money' => $per_data['per_day_endorsement_money'],
            'per_day_server_cost' => $per_data['per_day_server_cost'],
            'cost_month' => $cost_month,
            'authorization_money' => $root_game_item->authorization_money,
            'endorsement_money' => $root_game_item->endorsement_money,
            'server_cost' => $root_game_item->server_cost,
            'mark' => $mark,
            'module' => 'ly'
        ];
    }
    $logger->info('单月根游戏同步完成', [$cost_month, $root_game_item->platform, $root_game_item->root_game_id]);
}
// 入库
$operation_cost_field_model->addMultiple($insert_data);
$logger->info('同步共享的cost_filed完成');

$logger->info('开始同步非共享的cost_filed');
// 获取非共享的主游戏cost_filed
$not_share_main_game = $operation_cost_field_model->getLYNotShareMainGameByCostMonth($cost_month);
foreach ($not_share_main_game as $main_game_item) {
    // 补充游戏数据并获取总充值金额
    $tmp_start_date = date('Y-m-01', strtotime($main_game_item->cost_month));
    $tmp_end_date = date('Y-m-d', strtotime("$tmp_start_date +1 month -1 day"));
    [$mark, $total_pay_money] = $service->makeUpChannelPayMoney($main_game_item->platform, $main_game_item->root_game_id, $main_game_item->main_game_id, $tmp_start_date, $tmp_end_date);
}
//$logger->info('同步非共享的cost_filed完成');

/* 完成同步共享的operation_cost_field */

/* 同步成本prime_cost_field */
$prime_cost_field = $prime_cost_field_model->getAllByDateRange($start_date, $end_date);

foreach ($prime_cost_field as $prime_cost) {
    // 补充游戏数据并获取总充值金额
    [$mark, $total_pay_money] = $service->makeUpChannelPayMoneyOnChannel($prime_cost->platform, $prime_cost->game_id, $prime_cost->channel_id, $prime_cost->cost_date, $prime_cost->cost_date);
}

/* 完成同步成本prime_cost_field */

/* 同步运营成本operate_cost_field */
$operate_cost_field = $operate_cost_field_model->getAllByMonth($cost_month);
foreach ($operate_cost_field as $operate_cost) {
    // 补充游戏数据并获取总充值金额
    $tmp_start_date = date('Y-m-01', strtotime($operate_cost->cost_month));
    $tmp_end_date = date('Y-m-d', strtotime("$tmp_start_date +1 month -1 day"));
    [$mark, $total_pay_money] = $service->makeUpChannelPayMoney($operate_cost->platform, $operate_cost->root_game_id, $operate_cost->main_game_id, $tmp_start_date, $tmp_end_date);
}
/* 完成同步运营成本operate_cost_field */


$logger->info('任务结束');





