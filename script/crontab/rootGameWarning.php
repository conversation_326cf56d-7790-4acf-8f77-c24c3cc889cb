<?php

/**
 * 0 * * * *
 * 根游戏警报
 */

use App\Constant\PlatId;
use App\Constant\RouteID;
use App\Model\SqlModel\Tanwan\ErrorLogModel;
use App\Model\SqlModel\Zeda\GameWarningModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Service\NoticeService;
use App\Struct\RedisCache;
use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
$redis = RedisCache::getInstance();
$date = date('Y-m-d');
$start_datetime = $date . ' 00:00:00';
$end_datetime = $date . ' 23:59:59';
$notice_service = new NoticeService();
$game_warning_model = new GameWarningModel();
$game_warning_list = $game_warning_model->getAll();
$admin_list = (new UserModel())->getSuperUser();
$admin_ids = $admin_list->pluck('id')->toArray();

$error_log_model = new ErrorLogModel();
$error_log_tables = $error_log_model->getErrorLogTables();
// 没有game_id 不需要警报
$no_need_check_table = ['error_v2_dim_server_open_time', 'error_v2_ods_game_server_msg_log', 'error_v2_ods_user_info_log'];
foreach ($error_log_tables as $key => $error_log_table) {
    $error_log_table = (array)$error_log_table;
    $table_name = array_pop($error_log_table);

    if (in_array($table_name, $no_need_check_table)) {
        unset($error_log_tables[$key]);
    }

}
foreach ($game_warning_list as $game_warning) {
    $creator_id = $game_warning->creator_id;
    $platform = $game_warning->platform;
    $plat_id = $game_warning->plat_id;
    $root_game_id = $game_warning->root_game_id;
    $platform_name = EnvConfig::PLATFORM_MAP[$platform];
    $plat_name = PlatId::MAP[$plat_id];
    foreach ($error_log_tables as $key => $error_log_table) {
        $error_log_table = (array)$error_log_table;
        $table_name = array_pop($error_log_table);
        if ($redis->get("game_warning_noticed:{$table_name}:user_id-{$creator_id}")) {
            continue;
        }

        $error_log = $error_log_model->getErrorNumFromTableByRootGameIdBetweenDateTime($table_name, $platform, $plat_id, $root_game_id, $start_datetime, $end_datetime);

        if ($error_log->error_num > 0) {
            $push_admin_ids = array_unique(array_merge($admin_ids, [$creator_id]));
            foreach ($push_admin_ids as $push_admin_id) {
                $notice_service->addBellNotice(
                    $push_admin_id,
                    NoticeService::LEVEL_DANGER,
                    "游戏警报",
                    "【{$platform_name}】平台【{$plat_name}】类型【{$root_game_id}】根游戏存在【{$table_name}】错误日志，请及时修正",
                    RouteID::INDIVIDUATION_GAME_WARNING
                );
            }
            $redis->set("game_warning_noticed:{$table_name}:user_id-{$creator_id}", 1, strtotime($end_datetime) - time());
        }
    }
}

