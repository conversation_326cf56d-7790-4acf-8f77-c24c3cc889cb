<?php

error_reporting(E_ERROR);

use App\MysqlConnection;

require dirname(__DIR__) . '/vendor/autoload.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();


$user_ids = MysqlConnection::getConnection()->select('select id,state,update_time,last_login_time from user WHERE is_internal = 1 and (
    (state = 1) OR 
    (state = 0 AND update_time > 1735660800 AND last_login_time > 1735660800))');
$sql = "SELECT
        t1.*,
        t2.module,
        t3.name as platform_name,
        t4.name as department_name,
        t5.name as department_group_name,
        t6.name as department_group_position_name,
        t7.name as department_group_position_worker_name,
        t8.name as six_name,
        t9.name as seven_name,
        t10.name as e_name
FROM
        `user` t1
        LEFT JOIN user_level t2 ON t1.id = t2.user_id
        LEFT JOIN platform t3 ON t2.platform_id = t3.id
        LEFT JOIN department t4 on t2.department_id = t4.id
        LEFT JOIN department_group t5 on t2.department_group_id = t5.id
        LEFT JOIN department_group_position t6 on t2.department_group_position_id = t6.id
        LEFT JOIN department_group_position_worker t7 on t2.department_group_position_worker_id = t7.id
        LEFT JOIN department_six_level t8 on t2.department_six_id = t8.id
        LEFT JOIN department_seven_level t9 on t2.department_seven_id = t9.id
        LEFT JOIN department_eight_level t10 on t2.department_eight_id = t10.id
WHERE
        t1.id  = ?
        order by state ASC ";

// 这里修改成自己的路径
$file_name = '/Users/<USER>/Desktop/用户角色清单.csv';
$file = fopen($file_name, "w");

// 添加一个 BOM 头
fwrite($file, "\xEF\xBB\xBF");

// 写入表头
//fputcsv($file, ['账号', '姓名', '工号', '状态', '创建时间', '最后修改时间', '最后登录时间', "DMS", "DSP", "LY"]);
fputcsv($file, ['工号', '状态', '创建时间', '最后修改时间', '最后登录时间']);

// 写入每个岗位的数据
$index = 0;
// 用户列表迭代
foreach ($user_ids as $item) {
    $user_id = $item->id;
    $list = MysqlConnection::getConnection()->select($sql, [$user_id]);
    if (!$list) {
        continue;
    }
    $csv_data = [];
    // 岗位的迭代
    foreach ($list as $data_item) {
        $account = $data_item->account;
        $create_time = date("Y-m-d H:i:s", $data_item->create_time);
        $update_time = date("Y-m-d H:i:s", $data_item->update_time);
        $name = maskName($data_item->name);
//        $name = $data_item->name;
        $staff_number = $data_item->staff_number;
        $position_str = $data_item->platform_name;
        $state = $data_item->state == 1 ? '正常' : '禁用';

        $is_super = $data_item->super_manager == 1 ? '是' : '否';
        $creator = $data_item->creator;
        if ($data_item->last_login_time == 0) {
            $last_login_time = '从未登录';
        } else {
            $last_login_time = date("Y-m-d H:i:s", $data_item->last_login_time);
        }
        $data_item->department_name && $position_str .= '-' . $data_item->department_name;
        $data_item->department_group_name && $position_str .= '-' . $data_item->department_group_name;
        $data_item->department_group_position_name && $position_str .= '-' . $data_item->department_group_position_name;
        $data_item->department_group_position_worker_name && $position_str .= '-' . $data_item->department_group_position_worker_name;
        $data_item->six_name && $position_str .= '-' . $data_item->six_name;
        $data_item->seven_name && $position_str .= '-' . $data_item->seven_name;
        $data_item->e_name && $position_str .= '-' . $data_item->e_name;
        if ($state === '禁用') {
            $position_str = '';
        }
        $csv_data[$data_item->module] = $position_str;
    }

//    fputcsv($file, [$account, $name, $staff_number, $state, $create_time, $update_time, $last_login_time, $csv_data["dms"] ?? '', $csv_data["dsp"] ?? '', $csv_data["ly"] ?? '']);
    fputcsv($file, [$staff_number, $state, $create_time, $update_time, $last_login_time]);
    echo $index . ". " . $account . "处理完成", PHP_EOL;
    $index++;

    if ($index === 1 || $index === count($user_ids)) {
        echo $index === 1 ? "第一行数据：" : "最后一行数据：";
        var_dump([
            '工号'         => $staff_number,
            '状态'         => $state,
            '创建时间'     => $create_time,
            '最后登录时间' => $last_login_time,
        ]);
    }

}

function maskName($name)
{
    // 提取汉字部分
    preg_match('/[\p{Han}]+/u', $name, $matches);
    if (!empty($matches)) {
        $chineseName = $matches[0];
        return mb_substr($chineseName, 0, 1) . str_repeat('x', mb_strlen($chineseName) - 1);
    }
    return $name; // 如果没有汉字，返回原始字符串
}

echo "总行数：" . count($user_ids), PHP_EOL;
echo "任务结束", PHP_EOL;


