<?php

use App\Model\HttpModel\Toutiao\Creative\CreativeModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;


class mutiModifyProgramAD3 extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list, $android_info, $ios_info;
        $model = new CreativeModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $item) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            foreach ($item as $one) {
                print_r('ad2_id:' . $one->ad_id . PHP_EOL);
                if ($one->os === 'IOS') {
                    $info = $ios_info;
                } else {
                    $info = $android_info;
                }
                $info['advertiser_id'] = $account_id;
                $info['ad_id'] = $one->ad_id;
                try {
                    $this_info = $model->read($account_id, $access_token, $one->ad_id);
                    $info['inventory_type'] = $this_info['inventory_type'];
                    $info['scene_inventory'] = $this_info['scene_inventory'];
                    $info['smart_inventory'] = $this_info['smart_inventory'];
                    $info['track_url'] = $this_info['track_url'];
                    $info['action_track_url'] = $this_info['action_track_url'];
                    $info['modify_time'] = $this_info['modify_time'];
                    $model->updateV2($info, $access_token);
                } catch (Throwable $e) {
                    print_r($e->getMessage() . PHP_EOL);
                }
            }
        }

        sleep(1);
        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();
$connection_data_media = 'data_media';
$list = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_toutiao_ad_log')
    ->select('ods_toutiao_ad_log.account_id', 'ods_toutiao_ad_log.ad_id', 'v2_dim_game_id.os')
    ->join('dim_site_game_id', function (JoinClause $join) {
        $join->on('ods_toutiao_ad_log.platform', '=', 'dim_site_game_id.platform');
        $join->on('ods_toutiao_ad_log.site_id', '=', 'dim_site_game_id.site_id');
        $join->where('game_end_time', '>', date('Y-m-d'));
    })
    ->join('tanwan_datahub.v2_dim_game_id', function (JoinClause $join) {
        $join->on('v2_dim_game_id.platform', '=', 'dim_site_game_id.platform');
        $join->on('v2_dim_game_id.game_id', '=', 'dim_site_game_id.game_id');
    })
    ->join('ods_toutiao_campaign_log', function (JoinClause $join) {
        $join->on('ods_toutiao_campaign_log.campaign_id', '=', 'ods_toutiao_ad_log.campaign_id');
        $join->where('ods_toutiao_campaign_log.status', '!=', 'CAMPAIGN_STATUS_DELETE');
    })
    ->join('ods_toutiao_creative_log', function (JoinClause $join) {
        $join->on('ods_toutiao_ad_log.ad_id', '=', 'ods_toutiao_creative_log.ad_id');
        $join->where('creative_material_mode', '=', 'STATIC_ASSEMBLE');
        $join->where('ods_toutiao_ad_log.status', '!=', 'AD_STATUS_DELETE');
        $join->where('ods_toutiao_creative_log.status', '!=', 'CREATIVE_STATUS_DELETE');
    })
    ->where(function (Builder $query) {
        $query->where('ods_toutiao_ad_log.platform', '=', '');
        $query->where('root_game_id', '=', '114');
    })
    ->whereIn('ods_toutiao_creative_log.account_id', ['**********'])
    ->groupBy(['ods_toutiao_ad_log.account_id', 'ods_toutiao_ad_log.ad_id'])
    ->get();
$account_ids = $list->pluck('account_id')->unique();
$account_chunk_list = $list->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');

$android_info = (new CreativeModel())->read(****************, '100cceed2d2400aa6f5ef616a8f10298f07690ab', ****************);
$ios_info = (new CreativeModel())->read(****************, '100cceed2d2400aa6f5ef616a8f10298f07690ab', ****************);

$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'mutiModifyProgramAD3');
$multi_process->start();
