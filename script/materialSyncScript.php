<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\Logic\API\AwemeLogic;
use App\Logic\DSP\MaterialLogic;
use App\Model\HttpModel\Material\SendMaterialSyncModel;
use App\Model\HttpModel\Volcengine\PutObject;
use App\MysqlConnection;
use App\Param\Material\MaterialSyncParam;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

//(new AwemeLogic())->digestTask([
//    'id' => 1,
//    'platform' => 'TW',
//    'item_id' => 1,
//    'aweme_play_url' => 'https://www.douyin.com/aweme/v1/play/?video_id=v0200fg10000c7kkjubc77u88b55i6vg&line=0&file_id=99a41fe519714971b097b722617d2846&sign=aade2b004f85f3c154e6e3bf5236e980&is_play_url=1&source=PackSourceEnum_AWEME_DETAIL&aid=6383',
//    'status_code' => 1,
//]);
//exit;

$list = MysqlConnection::getConnection()
    ->table('material_file')
    ->selectRaw(
        'CONCAT(
		"[",
		group_concat(
			JSON_OBJECT(
				"url",
				material_file.url,
				"filename",
				material_file.filename,
				"width",
				material_file.width,
				"height",
				material_file.height,
				"size",
				material_file.size,
				"scale",
				material_file.scale,
				"ratio",
				material_file.scale,
				"type",
				material_file.format,
				"format",
				material_file.format
			)),
		"]"
	) AS list'
    )
    ->selectRaw('material.file_type')
    ->selectRaw('material.theme_id')
    ->selectRaw('material.name')
    ->selectRaw('material.platform')
    ->selectRaw('material.material_id')
    ->selectRaw('material.author')
    ->join('material', function (JoinClause $join) {
        $join->on("material_file.platform", '=', "material.platform");
        $join->on("material_file.material_id", '=', "material.material_id");
    })
    ->whereRaw('material.theme_id IN ( 185001,185002,185005,185003,185004,112001,112002,112003,112004,112005,112006,112007,112008,112009,130001,130002,130003,130004,130005,130006,130007,130008 ) ')
    ->whereNotIn('material_file.file_type', [5, 6, 7])
    ->whereNotIn('material_file.material_id', [198345])
    ->whereRaw('material_file.file_type =  material.file_type',)
    ->whereRaw('material.platform = "TW"')
    ->groupBy('material.material_id')
    ->orderBy('material.material_id', 'desc')
    ->dd();

var_dump($list);
exit;


$i = 1;
foreach ($list as $key => $info) {
    $sync_material_data = [
        'sync_material_name' => $info->name,
        'sync_platform' => $info->platform,
        'sync_platform_material_id' => $info->material_id,
        'sync_create_mode' => 0,
        'sync_theme_id' => $info->theme_id,
        'sync_file_type' => $info->file_type,
        'sync_file_list' => json_decode($info->list),
        'sync_extend_size_video' => [],
        'ext' => [
            'uploader' => $info->author,
            'author' => $info->author,
            'platform' => 'shenghong'
        ]
    ];
    (new SendMaterialSyncModel())->sendSyncInfo($sync_material_data);
    var_dump($info->material_id . '  finish');
    $i++;
    if ($i % 400 == 0) {
        sleep(65);
    }
}

//$list = MysqlConnection::getConnection()
//    ->table('material_sync')
//    ->where(['state_code' => 0])
//    ->get();
//
//foreach ($list as $key => $info) {
//    try {
//        (new MaterialLogic())->doSyncMaterialFile($info->id, new MaterialSyncParam([
//            'sync_platform' => $info->sync_platform,
//            'sync_platform_material_id' => $info->sync_platform_material_id,
//            'sync_theme_id' => $info->sync_theme_id,
//            'sync_create_mode' => $info->sync_create_mode,
//            'sync_material_name' => $info->sync_material_name,
//            'sync_file_type' => $info->sync_file_type,
//            'sync_file_list' => json_decode($info->sync_file_list, true),
//            'sync_extend_size_video' => [],
//        ]));
//    } catch (Throwable $e) {
//        var_dump($e->getMessage(), $e->getTraceAsString());
//    }
//    exit;
//}