<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

//$params = getopt('p:f:');
//if (!isset($params['p'])) {
//    print_r('-p传入需要导入的平台'.PHP_EOL);
//    die;
//}
//$platform = $params['p'];
//if (!isset($params['f'])) {
//    print_r('-f传入需要开始的id'.PHP_EOL);
//    die;
//}
//$from_id = $params['f'];

echo "fix covering material_files============================>\n";

$material_files = [];
$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";
//$times = [1, 2, 3, 4, 5, 6];
//$ffmpeg = FFMpeg::create();

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$db_material_files = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_material_file_log')
    ->where([['filename', 'LIKE', "%_creative_%"], ['filename', 'LIKE', "%(扩展)%"]])
    ->where('file_type',3)
    ->where('width',960)
    ->where('height',540)
    ->whereBetween('insert_time', ['2022-01-11 11:04:00', '2022-01-20 10:50:00'])
    ->orderByDesc('id')
    ->limit(1)
    ->get();
//var_dump($db_material_files);


foreach ($db_material_files as $material_file) {

    $file_path = "$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}";
    if (file_exists($file_path)) {
        $img_info = Image::getImageInfo($file_path);
        if ($img_info['width'] != $material_file->width) {
            $origin_name_array = explode('_', $material_file->filename);
            array_splice($origin_name_array, count($origin_name_array) - 3, 1, "1280x720");
            $from_file = implode("_", $origin_name_array);

            if (file_exists("$upload_path/{$material_file->platform}/{$material_file->material_id}/$from_file")) {
                Image::thumb("$upload_path/{$material_file->platform}/{$material_file->material_id}/$from_file",
                    "$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}", '', 960, 540);

                $signature = md5_file("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
                $size = filesize("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
                MysqlConnection::getConnection($connection_name)->table('material_file')
                    ->where(['id' => $material_file->id])
                    ->update(['signature' => $signature, 'size' => $size]);
                MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')
                    ->where(['id' => $material_file->id])
                    ->update(['signature' => $signature, 'size' => $size]);

                echo "updating file_id = " . $material_file->id . PHP_EOL;

            }
        }
    }
}

echo "end fix covering material_files============================>\n";
