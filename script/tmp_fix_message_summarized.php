<?php

/**
 * 修复message_summarized字段
 */

use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


// 设置MySQL连接对象
MysqlConnection::setConnection();
$logger = \App\Utils\Helpers::getLogger('tmp_fix_message_summarized');

// 先获取所有的summary记录
$summary_model = new \App\Model\SqlModel\Zeda\FeishuMessageSummary();

$summary_list = $summary_model->getAll();
foreach ($summary_list as $summary) {
    $message_id_list = json_decode($summary->msg_id_list, true);
    if ($message_id_list) {
        (new \App\Model\SqlModel\Zeda\FeiShuMessageModel())->updateIsSummarized($message_id_list);
        $logger->info('修复summary记录，id: ' . $summary->id);
    }
}

// 记录日志
$logger->info('修复完成，共修复' . count($summary_list) . '条记录');
