<?php
/**
 * 同步所有的分成比例到ADB
 *
 * Created by PhpStorm.
 * User: Melody
 * Date: 2021/4/16
 * Time: 10:53
 */

use App\Logic\DMS\OperationProfitLogic;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\AppletDivideModel;
use App\Model\SqlModel\Zeda\GameProfitModel;
use App\MysqlConnection;
use App\Task\HistoryPayDataTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

$logic = new OperationProfitLogic();

Helpers::getLogger('sync_divide')->info('开始同步');

try {
    $logic->syncAllDivideToADB();
    Helpers::getLogger('sync_divide')->info('同步完成');
} catch (\Throwable $exception) {
    Helpers::getLogger('sync_divide')->err('sync_divide同步失败，失败详情：' . $exception->getMessage());
}


try {
    Helpers::getLogger('sync_divide')->info('小程序分成比例开始处理');
    (new HistoryPayDataTask())->syncApplet();
    Helpers::getLogger('sync_divide')->info('小程序分成比例处理完成');

} catch (\Throwable $exception) {
    Helpers::getLogger('sync_divide')->err('小程序分成比例同步失败，失败详情：' . $exception->getMessage());
}


