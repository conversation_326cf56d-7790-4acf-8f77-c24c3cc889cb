<?php

use App\MysqlConnection;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Struct\RedisCache;
use App\Task\GroupAssistantTask;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


// 设置MySQL连接对象
MysqlConnection::setConnection();


$service = new GroupAssistantService();
$start = time();
$redis = RedisCache::getInstance();
$logger = Helpers::getLogger('group_assistant_bot');


$chat_id = 'oc_fcf77588b872af5e026ea1ee96fb91c6';
$chat_name = '【私域营销】BI后台-数据报表需求';
$union_id = 'on_ee55ca12cb03c11fc39c54e4aa705965';
$service->handlerGroupAssistantBotAdded($chat_id, $chat_name, $union_id);
exit;

$logic = new \App\Logic\LeaderGroupLogic();


$tag_model = new \App\Model\SqlModel\Zeda\TagOptionModel();
$list = [
    '沈泳楠'        => '沈泳楠@北极星组',
    '龚鼎'          => '龚鼎@北极星组',
    '张智昱'        => '张智昱@北极星组',
    '范柔柔'        => '范柔柔@北极星组',
    '何强'          => '何强@北极星组',
    '陈展鹏'        => '陈展鹏@北极星组',
    '徐俊钦'        => '徐俊钦@北极星组',
    '林冀彦'        => '林冀彦@北极星组',
    '陈冰'          => '陈冰@北极星组',
    '白雪晴'        => '白雪晴@北极星组',
    '林书浩2'       => '林书浩@北极星组',
    '刘雨涵'        => '刘雨涵@原始组',
    '沈星润'        => '沈星润@原始组',
    '黄晓慈'        => '黄晓慈@原始组',
    '麦衡'          => '麦衡@原始组',
    '方若楠'        => '方若楠@原始组',
    '赛藏措'        => '赛藏措@微光组',
    '赵薇2'         => '赵薇@微光组',
    '欧美华'        => '欧美华@微光组',
    '严育晴'        => '严育晴@微光组',
    '李倩茹'        => '李倩茹@微光组',
    '肖灿1'         => '肖灿@微光组',
    '区婉欣'        => '区婉欣@微光组',
    '邓功鹏'        => '邓功鹏@微光组',
    '潘俊彦'        => '潘俊彦@微光组',
    '罗培聪'        => '罗培聪@微光组',
    '谭丽贤'        => '谭丽贤@微光组',
    '黄衍顺'        => '黄衍顺@事业部中台组',
    '叶耿武'        => '叶耿武@事业部中台组',
    '郭沐耘@小程序' => '郭沐耘@龙马组',
    '黄华鹏'        => '黄华鹏@龙马组',
    '吴炳柱'        => '吴炳柱@龙马组',
    '张腾蛟'        => '张腾蛟@皎月组',
    '赵天瑷'        => '赵天瑗@皎月组',
    '黄俊铭'        => '黄俊铭@皎月组',
    '虞媛'          => '虞媛@皎月组',
    '赵娟'          => '赵娟@皎月组',
    '罗素柳'        => '罗素柳@皎月组',
    '郭时杰'        => '郭时杰@皎月组',
    '赖晓雯'        => '赖晓雯@皎月组',
    '梁文俊'        => '梁文俊@皎月组',
    '甘镇文'        => '甘镇文@皎月组',
    '陈林沛'        => '陈林沛@皎月组',
    '郑柳洁'        => '郑柳洁@原始组',
    '陈嘉仪'        => '陈嘉仪@原始组',
    '刘凌云'        => '刘凌云@原始组',
    '雷小怡'        => '雷小怡@浪潮组',
    '韦炳鑫'        => '韦炳鑫@浪潮组',
    '刘超'          => '刘超@浪潮组',
    '王丹1'         => '王丹@浪潮组',
    '杨的'          => '杨的@浪潮组',
    '曾俊辉1'       => '曾俊辉@浪潮组',
    '刘剑华'        => '刘剑华@浪潮组',
    '黄芸1'         => '黄芸@浪潮组',
    '梁健聪'        => '梁健聪@浪潮组',
    '韦天浩'        => '韦天浩@浪潮组',
    '何师凯'        => '何师凯@浪潮组',
    '何海丰'        => '何海丰@浪潮组',
    '朱和蔼'        => '朱和蔼@浪潮组',
    '韦勒'          => '韦勒@微光组',
    '李清林'        => '李清林@微光组',
    '王锦鹏1'       => '王锦鹏@微光组',
    '廖俊亨'        => '廖俊亨@微光组',
    '黄桉琪1'       => '黄桉琪@微光组',
    '陈泽鑫'        => '陈泽鑫@星火组',
    '陆嘉立'        => '陆嘉立@浪潮组',
    '李大军'        => '李大军@事业部中台组',
    '苗艳红'        => '苗艳红@原始组',
    '谢春芳2'       => '谢春芳@原始组',
    '赵克川'        => '赵克川@原始组',
    '吴福江'        => '吴福江@龙马组',
    '杨灏'          => '杨灏@微光组',
    '王帆'          => '王帆@皎月组',
    '许婷婷'        => '许婷婷@事业部中台组',
    '黄海鹏'        => '黄海鹏@事业部中台组',
    '李燕玲'        => '李燕玲@事业部中台组',
    '朱伟天'        => '朱伟天@北极星组',
    '李宜钗'        => '李宜钗@原始组',
    '林子彬'        => '林子彬@原始组',
    '苏榆'          => '苏榆@事业部中台组',
    '吴水强'        => '吴水强@事业部中台组',
    '文思琪'        => '文思琪@事业部中台组',
    '吴烨星'        => '吴烨星@事业部中台组',
    '朱力杰@小程序' => '朱力杰@龙马组',
    '邱子懿'        => '邱子懿@龙马组',
    '冯韵娟'        => '冯韵娟@龙马组',
    '刘宇达@小程序' => '刘宇达@龙马组',
    '艾龙'          => '艾龙@龙马组',
    '廖丽媚'        => '廖丽媚@龙马组',
    '倪时亮'        => '倪时亮@龙马组',
    '刘薇'          => '刘薇@原始组',
    '王城程'        => '王城程@原始组',
    '甄颖桦'        => '甄颖桦@龙马组',
    '李晓君'        => '李晓君@星火组',
    '王胤轲'        => '王胤轲@星火组',
    '范秋仪'        => '范秋仪@星火组',
    '吴汉奇'        => '吴汉奇@星火组',
    '利伟城'        => '利伟城@星火组',
    '赵玥'          => '赵玥@星火组',
    '陈颖瑜'        => '陈颖瑜@星火组',
    '曾锦涛'        => '曾锦涛@星火组',
    '林子瑜'        => '林子瑜@星火组',
    '云昭日'        => '云昭日@星火组',
    '冯佳聪1'       => '冯佳聪@星火组',
    '李祥'          => '李祥@星火组',
    '庄晴源'        => '庄晴源@星火组',
    '叶智梅'        => '叶智梅@星火组',
    '董菲菲'        => '董菲菲@星火组',
    '刘丹阳'        => '刘丹阳@星火组',
    '冯科玮'        => '冯科玮@星火组',
    '郑丽娥'        => '郑丽娥@星火组',
    '刘泽湘'        => '刘泽湘@星火组',
    '曾诗媛'        => '曾诗媛@星火组',
];


$user_model = new \App\Model\SqlModel\Zeda\UserModel();
$index = 1;
foreach ($list as $old_account => $new_account) {
    $old_user = $user_model->getInfoByName($old_account);
    if ($old_user) {
        // 获取新用户
        $new_user = $user_model->getInfoByName($new_account);
        // 获取列表
        $tag_list = $tag_model->getTagByUserID($old_user['id']);
        foreach ($tag_list as $item) {
            $item->user_id = $new_user['id'];
            try {
                $tag_model->addList($item);
            } catch (\Throwable $e) {
                echo "报错，跳过", PHP_EOL;
            }

        }


        echo $index . ": user_id: {$old_user['id']} " . $old_account . "=>new_user_id: {$new_user['id']}  " . $new_account . "处理完成", PHP_EOL;

    } else {
        echo $index . ": " . $old_account . '=>' . $new_account . "没有信息", PHP_EOL;
    }

    $index++;
}

echo "所有任务处理完毕";
