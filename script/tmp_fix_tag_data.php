<?php
/**
 * 修复标签的问题
 */


use App\Model\HttpModel\Aliyun\DashScope\Embeddings\EmbeddingsModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\RoutePermissionModel;
use App\Model\HttpModel\Aliyun\DashVector\Collections\CollectionsModel;
use App\MysqlConnection;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageSummary;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();

echo '任务完成', PHP_EOL;
exit;

// 获取投放总览的指标
$target_list = (new RoutePermissionModel())->getAllByRouteIdCate(5, 3);

// 获取自定义指标
//$target_list = (new \App\Model\SqlModel\Zeda\CustomizedTargetModel())->getTargetByRouteId(5);
$embedding_model = new EmbeddingsModel();
$collection_model = new CollectionsModel();


//$vectors = $embedding_model->textEmbedding(['收入']);
//$embedding = $vectors["output"]["embeddings"][0]["embedding"] ?? [];
//$res = $collection_model->query('data_bot', $embedding, 5, 'overView');
//var_dump($res);
//exit;


// 向量化
foreach ($target_list as $item) {
    $vectors = $embedding_model->textEmbedding([$item->name]);
    $embedding = $vectors["output"]["embeddings"][0]["embedding"] ?? [];
    $ext = json_decode($item->ext, true);
    $docs = [
        [
            'id'     => $item->id,
            'vector' => $embedding,
            'fields' => ['name' => $item->name, 'column' => $ext['column'], 'user_id' => 0],
        ]
    ];
    // 插入
    $res = $collection_model->insertUpdate('data_bot', $docs, 'overView');

    echo $item->name . "处理完成\n";
    if ($res['code'] != 0) {
        echo $item->name . "处理失败\n";
        var_dump($res);
        echo PHP_EOL;
    }

}


echo '所有任务完成';
echo PHP_EOL;

