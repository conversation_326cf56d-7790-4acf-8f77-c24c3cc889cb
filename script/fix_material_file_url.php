<?php
// todo 修复素材文件url
// todo example:
// todo url为 https://dms.zeda.cn/upload/adsimg/material/915/19038/19038_0912-传奇-800-800-4_(6组图)(腾讯投放压缩).jpg
// todo 根据origin_platform origin_material_id 去update

use App\Model\RedisModel\AutoIncrementIdModel;
use App\MysqlConnection;
use Illuminate\Database\Query\Builder;

require dirname(__DIR__) . '/common/init.php';

MysqlConnection::setConnection();

/**
 * @param Builder $builder
 *
 * @return string
 * <AUTHOR>
 */
function getSql(Builder $builder)
{
    $bindings = $builder->getBindings();
    return preg_replace_callback('/\?/', function ($match) use (&$bindings) {
        $binding = array_shift($bindings);
        if (is_numeric($binding)) {
            return $binding;
        } else if (is_string($binding)) {
            return empty($binding) ? "''" : "'" . $binding . "'";
        } else {
            return $binding;
        }
    }, $builder->toSql());
}

$increment_model = new AutoIncrementIdModel();
$list = MysqlConnection::getConnection()
    ->table('material')
    ->select('platform')
    ->selectRaw('max(material_id) as max_material_id')
    ->groupBy('platform')
    ->get();

foreach ($list as $item) {
    if (!$increment_model->hasKey(AutoIncrementIdModel::MATERIAL_ID, $item->platform)) {
        $increment_model->setLastId(AutoIncrementIdModel::MATERIAL_ID, $item->platform, $item->max_material_id);
    }
}

$start_day = '2022-12-13 23:59:59';
$end_day = '2019-07-01 23:59:59';
$platform = 'guangfeng';
$theme_id = [
    20005,20008,20007,20004,20006,20002,20010,20003,20009,20001
];

for (; strtotime($end_day) < strtotime($start_day); $start_day = date('Y-m-d H:i:s', (strtotime($start_day) - (3600 * 24)))) {

    echo "执行-时间{$start_day}的素材" . PHP_EOL;

    $material_builder = MysqlConnection::getConnection('data_media')
        ->table('ods_material_log')
        ->where('platform', '=', $platform)
        ->whereIn('theme_id', $theme_id)
        ->whereBetween('insert_time', [date('Y-m-d H:i:s', (strtotime($start_day) - (3600 * 24))), $start_day])
        ->orderByDesc('material_id');
    echo getSql($material_builder) . PHP_EOL;
    $material_list = $material_builder->get();

    echo "{$start_day}有" . $material_list->count() . '条素材' . PHP_EOL;

    foreach ($material_list as $material) {
        $material_file_exists = MysqlConnection::getConnection('data_media')
            ->table('ods_material_log')
            ->where('platform', '=', 'TW')
            ->where('original_material_id', '=', $material->material_id)
            ->where('original_platform', '=', $material->platform)
            ->first();
        if (!$material_file_exists) {
            echo "执行-时间{$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name}的素材" . PHP_EOL;

            $new_material_id = (new AutoIncrementIdModel())->getNextId(AutoIncrementIdModel::MATERIAL_ID, 'TW');

            echo "执行-时间{$start_day}-平台{$material->platform}新素材id为{$new_material_id}" . PHP_EOL;

            $other_material_files_list = MysqlConnection::getConnection('data_media')
                ->table('ods_material_file_log')
                ->where('material_id', $material->material_id)
                ->where('platform', $material->platform)
                ->where('url', 'like', '%zeda.cn%')
                ->orderByDesc('insert_time')
                ->get();

            echo "时间{$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name}有" . $other_material_files_list->count() . '条素材文件' . PHP_EOL;

            foreach ($other_material_files_list as $material_file) {

                // https://dms.zeda.cn/upload/adsimg/material/TW/99860/37688_沙鳄鱼_800x800.jpg
                echo "{$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename}开始搬迁" . PHP_EOL;

                $url_array = explode('/', $material_file->url);
                $new_url_array = $url_array;
                $new_url_array[2] = 'dms.zx.com';
                $new_url_array[6] = 'TW';
                $new_url_array[7] = $new_material_id;
                $new_url = implode("/", $new_url_array);

                $srv_array = array_slice($url_array, 3);
                $new_srv_array = array_slice($new_url_array, 3);
                $srv = SRV_DIR . '/' . implode("/", $srv_array);
                $new_srv = SRV_DIR . '/' . implode("/", $new_srv_array);

                $new_path = pathinfo($new_srv);

                if (!file_exists($new_path['dirname'])) {
                    mkdir($new_path['dirname'], 0755, true);
                }
                try {
                    if (!file_exists($new_srv)) {
                        $copy_result = copy($srv, $new_srv);
                        if (!$copy_result) {
                            echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 复制失败 退出" . PHP_EOL;
//                        exit;
                        }
                    }
                } catch (Throwable $e) {
                    echo $e->getMessage() . PHP_EOL;
                    echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 复制失败 退出" . PHP_EOL;
                }


                try {
                    $update_material_file_result = MysqlConnection::getConnection('data_media')
                        ->table('ods_material_file_log')
                        ->where('id', $material_file->id)
                        ->update([
                            'material_id' => $new_material_id,
                            'original_material_id' => $material_file->material_id,
                            'platform' => 'TW',
                            'original_platform' => $material_file->platform,
                            'url' => $new_url,
                            'video_hash_10' => '10'
                        ]);
                    if (!$update_material_file_result) {
                        echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 更新adb失败 退出" . PHP_EOL;
                        exit;
                    }
                } catch (Throwable $e) {
                    echo $e->getMessage() . PHP_EOL;
                    echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 更新adb失败 退出" . PHP_EOL;
                    exit;
                }

                try {
                    $insert_material_file_result = MysqlConnection::getConnection()
                        ->table('material_file')
                        ->insertGetId([
                            'id' => $material_file->id,
                            'platform' => 'TW',
                            'media_type' => $material_file->media_type,
                            'material_id' => $new_material_id,
                            'file_type' => $material_file->file_type,
                            'filename' => $material_file->filename,
                            'url' => $new_url,
                            'width' => $material_file->width,
                            'height' => $material_file->height,
                            'scale' => $material_file->scale,
                            'signature' => $material_file->signature,
                            'duration' => $material_file->duration,
                            'bitrate' => $material_file->bitrate,
                            'size' => $material_file->size,
                            'format' => $material_file->format,
                            'uploader' => $material_file->uploader,
                            'is_ext' => $material_file->is_ext,
                            'create_time' => strtotime($material_file->insert_time),
                            'update_time' => strtotime($material_file->update_time),
                            'is_del' => $material_file->is_del,
                            'notify' => $material_file->notify,
                            'video_hash_10' => '10',
                            'source_signature' => $material_file->source_signature,
                        ]);
                    if (!$insert_material_file_result) {
                        echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 插入mysql失败 退出" . PHP_EOL;
                        exit;
                    }
                } catch (Throwable $e) {
                    echo $e->getMessage() . PHP_EOL;
                    echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename} 插入mysql失败 退出" . PHP_EOL;
                    exit;
                }

                echo "{$start_day}-平台{$material->platform}-素材id{$material->material_id}-ID{$material_file->id}-素材文件{$material_file->filename}搬迁完" . PHP_EOL;

            }

            echo "{$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name}素材文件已搬迁完" . PHP_EOL;

            try {
                $update_material_result = MysqlConnection::getConnection('data_media')
                    ->table('ods_material_log')
                    ->insert([
                        'platform' => 'TW',
                        'material_id' => $new_material_id,
                        'name' => $material->name,
                        'media_type' => $material->media_type,
                        'original' => $material->original,
                        'theme_id' => '999999',
                        'file_type' => $material->file_type,
                        'author' => $material->author,
                        'c_author' => $material->c_author,
                        'a_author' => $material->a_author,
                        'm1_author' => $material->m1_author,
                        'm2_author' => $material->m2_author,
                        'm3_author' => $material->m3_author,
                        'm4_author' => $material->m4_author,
                        'actor' => $material->actor,
                        'shoot' => $material->shoot,
                        'insert_time' => $material->insert_time,
                        'update_time' => $material->update_time,
                        'is_del' => $material->is_del,
                        'is_public' => $material->is_public,
                        'm5_author' => $material->m5_author,
                        'cost' => $material->cost,
                        'ori_cost' => $material->ori_cost,
                        'pay_count' => $material->pay_count,
                        'cost_date_hour_count' => $material->cost_date_hour_count,
                        'cost_date_count' => $material->cost_date_count,
                        'show' => $material->show,
                        'convert' => $material->convert,
                        'reg_count' => $material->reg_count,
                        'total_pay_money' => $material->total_pay_money,
                        'day_first_day_pay_money' => $material->day_first_day_pay_money,
                        'day_reg_uid_count' => $material->day_reg_uid_count,
                        'day_first_day_pay_count' => $material->day_first_day_pay_count,
                        'click' => $material->click,
                        'effect_grade7' => $material->effect_grade7,
                        'effect_grade30' => $material->effect_grade30,
                        'approved_rate' => $material->approved_rate,
                        'is_priority' => $material->is_priority,
                        'is_under_qualified' => $material->is_under_qualified,
                        'is_effect_grade7' => $material->is_effect_grade7,
                        'is_effect_grade30' => $material->is_effect_grade30,
                        'original_platform' => $material->platform,
                        'original_material_id' => $material->material_id,
                        'original_theme_id' => $material->theme_id,
                    ]);
                if (!$update_material_result) {
                    echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name} 插入adb失败 退出" . PHP_EOL;
                    exit;
                }
            } catch (Throwable $e) {
                echo $e->getMessage() . PHP_EOL;
                echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name} 插入adb失败 退出" . PHP_EOL;
                exit;
            }

            try {
                $insert_material_result = MysqlConnection::getConnection()
                    ->table('material')
                    ->insert([
                        'platform' => 'TW',
                        'material_id' => $new_material_id,
                        'name' => $material->name,
                        'media_type' => $material->media_type,
                        'original' => $material->original,
                        'theme_id' => '999999',
                        'file_type' => $material->file_type,
                        'is_group' => in_array($material->file_type, [5, 6, 7]) ? 1 : 0,
                        'author' => $material->author,
                        'effect_grade7' => $material->effect_grade7,
                        'effect_grade30' => $material->effect_grade30,
                        'c_author' => $material->c_author,
                        'a_author' => $material->a_author,
                        'is_3d' => $material->is_3d,
                        'm1_author' => $material->m1_author,
                        'm2_author' => $material->m2_author,
                        'm3_author' => $material->m3_author,
                        'm4_author' => $material->m4_author,
                        'm5_author' => $material->m5_author,
                        'is_immortal' => $material->is_immortal,
                        'actor' => $material->actor,
                        'shoot' => $material->shoot,
                        'is_public' => $material->is_public,
                        'create_time' => strtotime($material->insert_time),
                        'update_time' => strtotime($material->update_time),
                        'last_uploaded_time' => strtotime($material->update_time),
                        'is_del' => $material->is_del,
                        'is_priority' => $material->is_priority,
                        'is_under_qualified' => $material->is_under_qualified,
                    ]);
                if (!$insert_material_result) {
                    echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id} 插入mysql失败 退出" . PHP_EOL;
                    exit;
                }
            } catch (Throwable $e) {
                echo $e->getMessage() . PHP_EOL;
                echo "error {$start_day}-平台{$material->platform}-素材id{$material->material_id} 插入mysql失败 退出" . PHP_EOL;
                exit;
            }
        } else {
            echo "执行-时间{$start_day}-平台{$material->platform}-素材id{$material->material_id}-素材名{$material->name}的素材 已经复制过-跳过" . PHP_EOL;
        }
    }

    echo "完成时间为{$start_day}的素材" . PHP_EOL;

    echo "---------------------------------------------------------------------------" . PHP_EOL;
}
