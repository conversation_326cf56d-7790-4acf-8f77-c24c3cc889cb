<?php

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\AdGroup\AdGroupModel;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADLogModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupLogModel;
use App\Model\SqlModel\Zeda\LimitChengduOperateLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;

const TENCENT_CHOSEN_GUANGDONG = 440000;
const TENCENT_GUANGDONG_CITY = [449900, 440400, 445300, 440800, 441200, 442000, 440900, 441400, 441800, 440500,
    441500, 440200, 440300, 441700, 445100, 441900, 440600, 440100, 441600, 441300, 440700, 445200];
const TENCENT_COUNTRY_EXCEPT_GUANGDONG = [156, 110000, 120000, 130000, 140000, 150000, 210000, 220000, 230000, 310000,
    320000, 330000, 340000, 350000, 360000, 370000, 410000, 420000, 430000, 450000, 460000, 500000, 510000,
    520000, 530000, 540000, 610000, 620000, 630000, 640000, 650000, 710000, 810000, 820000];


class multiLimitTencentAD2Region extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $tencent_http_model = new AdGroupModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $ad_info) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            $insert_data = [];
//            $num = 0;

            foreach ($ad_info as $item) {
//                $num += 1;
                $original_data = json_encode($item->regions);
                $request_data = [];
//            $url_data = [];
                $request_data['account_id'] = (int)$item->account_id;
                $request_data['adgroup_id'] = (int)$item->adgroup_id;
//            $url_data['access_token'] = $access_token[$item->account_id];
//            $url_data['timestamp'] = time();
//            $url_data['nonce'] = md5(uniqid('', true));
                if (empty($item->regions) || in_array(1156, $item->regions)) {
                    //不筛选地区
                    $request_data['targeting']['geo_location']['location_types'] = ["LIVE_IN"];
                    $request_data['targeting']['geo_location']['regions'] = TENCENT_COUNTRY_EXCEPT_GUANGDONG;

                } elseif (in_array(TENCENT_CHOSEN_GUANGDONG, $item->regions)) {

                    $position = array_search(TENCENT_CHOSEN_GUANGDONG, $item->regions);
                    unset($item->regions[$position]);
                    foreach ($item->location_types as $key => $value) {
                        $item->location_types[$key] = trim($value, '"');
                    }
                    $request_data['targeting']['geo_location']['location_types'] = $item->location_types;
                    $request_data['targeting']['geo_location']['regions'] = array_values($item->regions);

                } elseif (array_intersect($item->regions, TENCENT_GUANGDONG_CITY)) {
                    foreach ($item->location_types as $key => $value) {
                        $item->location_types[$key] = trim($value, '"');
                    }
                    $request_data['targeting']['geo_location']['location_types'] = $item->location_types;
                    $city = array_diff($item->regions, TENCENT_GUANGDONG_CITY);
                    if (!$city) {
                        $city = TENCENT_COUNTRY_EXCEPT_GUANGDONG;
                    }
                    $request_data['targeting']['geo_location']['regions'] = array_values($city);
                } else {
                    continue;
                }

                $insert = [];
                $insert['account_id'] = $item->account_id;
                $insert['ad2_id'] = $item->adgroup_id;
                $insert['insert_time'] = time();
                $insert['media_type'] = MediaType::TENCENT;
                $insert['old_data'] = $original_data;
                try {
//                    var_dump($request_data);
                    $response = $tencent_http_model->updateForADAnalysisMultiEdit(http_build_query($request_data), $access_token);
                    $insert['info'] = 'success ' . json_encode($response, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 1;
                } catch (AppException $e) {
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $insert['info'] = json_encode($error, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 2;
                }

                $insert_data[] = $insert;
                print_r(date("Y-m-d H:i:s") . "腾讯二级广告ID：" . $item->adgroup_id . "投放地区调整完成" . PHP_EOL);
            }

            if ($insert_data) {
                (new LimitChengduOperateLogModel())->add('tencent_limit_operate_log', $insert_data);
//                var_dump($insert_data);
            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();

$ad_info = (new OdsTencentAdGroupLogModel())->getADInfoByStatusForLimitRegion();
if ($ad_info->isEmpty()) {
    return;
}
foreach ($ad_info as $item) {
    //将地区处理为数组
    $item->regions = trim($item->regions, '[]');
    $item->location_types = trim($item->location_types, '[]');
    if ($item->regions) {
        $item->regions = explode(',', $item->regions);
    } else {
        $item->regions = [];
    }
    if ($item->location_types) {
        $item->location_types = explode(',', $item->location_types);
    } else {
        $item->location_types = [];
    }
}
unset($item);
//$connect2 = MysqlConnection::getConnection($connection_data_media);
//$cost_connect = $connect->table('ods_toutiao_account_log')
//    ->join('ods_toutiao_creative_hour_data_log', 'ods_toutiao_creative_hour_data_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->selectRaw('ods_toutiao_account_log.account_id, SUM(cost)')
//    ->where('cost_date', '2021-01-08');


$account_ids = $ad_info->pluck('account_id')->unique();
$account_chunk_list = $ad_info->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');
if ($media_account_list->isEmpty()) {
    return;
}
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiLimitTencentAD2Region');
$multi_process->start();
