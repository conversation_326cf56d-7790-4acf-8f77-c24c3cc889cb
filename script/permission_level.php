<?php


use App\Logic\DMS\PermissionLogic;
use App\Model\SqlModel\Zeda\DepartmentGroupModel;
use App\Model\SqlModel\Zeda\DepartmentGroupPositionModel;
use App\Model\SqlModel\Zeda\DepartmentGroupPositionWorkerModel;
use App\Model\SqlModel\Zeda\DepartmentModel;
use App\Model\SqlModel\Zeda\PlatformModel;
use App\Model\SqlModel\Zeda\RankPermissionAllModel;
use App\Model\SqlModel\Zeda\RankPlatformModel;
use App\Model\SqlModel\Zeda\RankRouteListModel;
use App\MysqlConnection;
use App\Service\PermissionService;

require dirname(__DIR__) . '/vendor/autoload.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();


// 各种model
$platform_model = new PlatformModel();
$department_model = new DepartmentModel();
$department_group_model = new DepartmentGroupModel();
$department_group_position_model = new DepartmentGroupPositionModel();
$department_group_position_worker_model = new DepartmentGroupPositionWorkerModel();
$rank_platform_model = new RankPlatformModel();
$permission_service = new PermissionService();

// 三个模块
$modules = ['dms', 'ly', 'dsp'];
// 循环三个大模块
foreach ($modules as $module) {
    // 1.建立顶级文件夹
    $module_path = '/Users/<USER>/Desktop/' . $module;
    mkdir($module_path);
    // 找出1级level的所有卡片
    $platform_list = $permission_service->getPlatformList('', $module);
    foreach ($platform_list as $item) {
        $item->name = str_replace('/', '-', $item->name);
        // 建立1级文件夹
        $level_1_path = $module_path . '/' . $item->name;
        mkdir($level_1_path);
        // 获取一级权限
        if ($module === 'dms') {
            $function_name = 'write_dms_csv';
        } elseif ($module === 'dsp') {
            $function_name = 'write_dsp_csv';
        } else {
            $function_name = 'write_ly_csv';
        }

        $function_name($level_1_path, $item->name, 1, $item->id);

        // 写二级权限
        // 找出2级level的所有卡片
        $department_list = $permission_service->getDepartmentList($item->id);
        foreach ($department_list as $department) {
            $department->name = str_replace('/', '-', $department->name);
            // 建立2级文件夹
            $level_2_path = $level_1_path . '/' . $department->name;
            mkdir($level_2_path);
            $function_name($level_2_path, $department->name, 2, $department->id);

            // 写三级权限
            // 找出3级level的所有卡片
            $department_group_list = $permission_service->getDepartmentGroupList($department->id);
            foreach ($department_group_list as $department_group) {
                $department_group->name = str_replace('/', '-', $department_group->name);
                // 建立2级文件夹
                $level_3_path = $level_2_path . '/' . $department_group->name;
                mkdir($level_3_path);
                $function_name($level_3_path, $department_group->name, 3, $department_group->id);

                // 写四级权限
                // 找出4级level的所有卡片
                $department_group_position_list = $permission_service->getDepartmentGroupPositionList($department_group->id);
                foreach ($department_group_list as $department_group_position) {
                    $department_group_position->name = str_replace('/', '-', $department_group_position->name);
                    // 建立2级文件夹
                    $level_4_path = $level_3_path . '/' . $department_group_position->name;
                    mkdir($level_4_path);
                    $function_name($level_4_path, $department_group_position->name, 4, $department_group_position->id);
                    if (!array_diff(scandir($level_4_path), array('..', '.'))) {
                        rmdir($level_4_path);
                        echo '删除空文件夹：', $level_4_path, PHP_EOL;
                    }
                }

                if (!array_diff(scandir($level_3_path), array('..', '.'))) {
                    rmdir($level_3_path);
                    echo '删除空文件夹：', $level_3_path, PHP_EOL;
                }
            }

            if (!array_diff(scandir($level_2_path), array('..', '.'))) {
                rmdir($level_2_path);
                echo '删除空文件夹：', $level_2_path, PHP_EOL;
            }
        }

        if (!array_diff(scandir($level_1_path), array('..', '.'))) {
            rmdir($level_1_path);
            echo '删除空文件夹：', $level_1_path, PHP_EOL;
        }
    }
}


function write_dms_csv($path_pre, $name, $level, $rank_id)
{
    try {
        $permission_service = new PermissionService();
        $permission_logic = new PermissionLogic();
        // 写权限
        $permission_data = $permission_logic->getDataPermissionByRank($rank_id, $level);
        $game_data = $permission_data['game_permission']['TW'] ?? [];
        $agent_data = $permission_data['agent_permission']['TW'] ?? [];
        if (!$game_data || !$agent_data) {
            echo '等级：', $level, ' , 名称：', $name, PHP_EOL;
            return;
        }

        $router_list = $permission_service->getRankRouterCascader($level, $rank_id);
        $line_array = [
            [mb_convert_encoding('岗位名称', 'GB18030', 'utf-8'), mb_convert_encoding('游戏权限', 'GB18030', 'utf-8'), mb_convert_encoding('渠道权限', 'GB18030', 'utf-8'), mb_convert_encoding('菜单权限', 'GB18030', 'utf-8')],
            [mb_convert_encoding($name, 'GB18030', 'utf-8'), json_encode($game_data, JSON_UNESCAPED_UNICODE), json_encode($agent_data, JSON_UNESCAPED_UNICODE), json_encode($router_list, JSON_UNESCAPED_UNICODE)]
        ];

        $file = fopen($path_pre . '/' . $name . '岗位权限.csv', "w");
        foreach ($line_array as $fields) {
            fputcsv($file, $fields);
        }
        // 写人
        $user_list = $permission_service->getUserList($level, $rank_id);

        $file = fopen($path_pre . '/' . $name . '账号列表.csv', "w");
        fputcsv($file, [
            mb_convert_encoding('账号名', 'GB18030', 'utf-8'),
            mb_convert_encoding('姓名', 'GB18030', 'utf-8'),
            mb_convert_encoding('员工号', 'GB18030', 'utf-8'),
            mb_convert_encoding('创建时间', 'GB18030', 'utf-8'),
        ]);
        foreach ($user_list as $item) {
            fputcsv($file, [
                mb_convert_encoding($item->account, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->name, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->staff_number, 'GB18030', 'utf-8'),
                mb_convert_encoding(date("Y-m-d H:i:s", $item->create_time), 'GB18030', 'utf-8'),
            ]);
        }
    } catch (\Throwable $exception) {
        echo $exception->getMessage(), PHP_EOL;
    }
}


function write_dsp_csv($path_pre, $name, $level, $rank_id)
{
    try {
        $permission_service = new PermissionService();
        $permission_logic = new \App\Logic\DSP\PermissionLogic();
        // 写权限
        $permission_data = $permission_logic->getDataPermissionByRank($rank_id, $level);
        $game_data = $permission_data['game_permission'] ?? [];
        $agent_data = $permission_data['agent_permission'] ?? [];
        if (!$game_data || !$agent_data) {
            echo 'dsp等级：', $level, ' , 名称：', $name, PHP_EOL;
            return;
        }

        $router_list = $permission_service->getRankRouterCascader($level, $rank_id);

        $line_array = [
            [mb_convert_encoding('岗位名称', 'GB18030', 'utf-8'), mb_convert_encoding('游戏权限', 'GB18030', 'utf-8'), mb_convert_encoding('渠道权限', 'GB18030', 'utf-8'), mb_convert_encoding('菜单权限', 'GB18030', 'utf-8')],
            [mb_convert_encoding($name, 'GB18030', 'utf-8'), json_encode($game_data, JSON_UNESCAPED_UNICODE), json_encode($agent_data, JSON_UNESCAPED_UNICODE), json_encode($router_list, JSON_UNESCAPED_UNICODE)]
        ];

        $file = fopen($path_pre . '/' . $name . '岗位权限.csv', "w");
        foreach ($line_array as $fields) {
            fputcsv($file, $fields);
        }
        // 写人
        $user_list = $permission_service->getUserList($level, $rank_id);

        $file = fopen($path_pre . '/' . $name . '账号列表.csv', "w");
        fputcsv($file, [
            mb_convert_encoding('账号名', 'GB18030', 'utf-8'),
            mb_convert_encoding('姓名', 'GB18030', 'utf-8'),
            mb_convert_encoding('员工号', 'GB18030', 'utf-8'),
            mb_convert_encoding('创建时间', 'GB18030', 'utf-8'),
        ]);
        foreach ($user_list as $item) {
            fputcsv($file, [
                mb_convert_encoding($item->account, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->name, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->staff_number, 'GB18030', 'utf-8'),
                mb_convert_encoding(date("Y-m-d H:i:s", $item->create_time), 'GB18030', 'utf-8'),
            ]);
        }
    } catch (\Throwable $exception) {
        echo $exception->getMessage(), PHP_EOL;
    }
}

function write_ly_csv($path_pre, $name, $level, $rank_id)
{
    try {


        $permission_service = new PermissionService();
        $permission_logic = new \App\Logic\LY\PermissionLogic();
        // 写权限
        $permission_data = $permission_logic->getDataPermissionByRank($rank_id, $level);
        $game_data = $permission_data['game_permission'] ?? [];
        $agent_data = $permission_data['agent_permission'] ?? [];
        if (!$game_data || !$agent_data) {
            echo 'ly等级：', $level, ' , 名称：', $name, PHP_EOL;
            return;
        }

        $router_list = $permission_service->getRankRouterCascader($level, $rank_id);
        $line_array = [
            [mb_convert_encoding('岗位名称', 'GB18030', 'utf-8'), mb_convert_encoding('游戏权限', 'GB18030', 'utf-8'), mb_convert_encoding('渠道权限', 'GB18030', 'utf-8'), mb_convert_encoding('菜单权限', 'GB18030', 'utf-8')],
            [mb_convert_encoding($name, 'GB18030', 'utf-8'), json_encode($game_data, JSON_UNESCAPED_UNICODE), json_encode($agent_data, JSON_UNESCAPED_UNICODE), json_encode($router_list, JSON_UNESCAPED_UNICODE)]
        ];

        $file = fopen($path_pre . '/' . $name . '岗位权限.csv', "w");
        foreach ($line_array as $fields) {
            fputcsv($file, $fields);
        }
        // 写人 TODO 注意体内外人员
        $user_list = $permission_service->getUserList($level, $rank_id);

        $file = fopen($path_pre . '/' . $name . '账号列表.csv', "w");
        fputcsv($file, [
            mb_convert_encoding('账号名', 'GB18030', 'utf-8'),
            mb_convert_encoding('姓名', 'GB18030', 'utf-8'),
            mb_convert_encoding('员工号', 'GB18030', 'utf-8'),
            mb_convert_encoding('创建时间', 'GB18030', 'utf-8'),
        ]);
        foreach ($user_list as $item) {
            fputcsv($file, [
                mb_convert_encoding($item->account, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->name, 'GB18030', 'utf-8'),
                mb_convert_encoding($item->staff_number, 'GB18030', 'utf-8'),
                mb_convert_encoding(date("Y-m-d H:i:s", $item->create_time), 'GB18030', 'utf-8'),
            ]);
        }
    } catch (\Throwable $exception) {
        echo $exception->getMessage(), PHP_EOL;
    }
}

exit;

