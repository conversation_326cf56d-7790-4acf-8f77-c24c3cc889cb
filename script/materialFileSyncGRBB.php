<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\Exception\AppException;
use App\Logic\DSP\MaterialFileSDTagsTaskMQLogic;
use App\Model\RabbitModel\ADIntelligentMonitorMQModel;
use App\MysqlConnection;
use Common\EnvConfig;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$exist_max_id = 0;

$exist_material_file_for_max_id = MysqlConnection::getConnection('data_media')
    ->table('ods_material_file_log')
    ->where('platform', 'GRBB')
    ->whereBetween('insert_time', ['2025-05-23 00:00:00', '2025-06-22 23:59:59'])
    ->orderBy('id', 'desc')
    ->first();

if ($exist_material_file_for_max_id) {
    $exist_max_id = $exist_material_file_for_max_id->id;
}

$material_file_list = MysqlConnection::getConnection()
    ->table('material_file')
    ->where('platform', 'GRBB')
    ->where('id', '>', $exist_max_id)
    ->where('file_type', '!=', 9)
    ->whereBetween('create_time', [strtotime('2025-05-23 00:00:00'), strtotime('2025-06-22 23:59:59')])
    ->limit(50000)
    ->get();

$user_migrate_map = MysqlConnection::getConnection()
    ->table('user_migrate_map')->get()->keyBy('old_name');

foreach ($material_file_list as $material_file) {
    echo date('Y-m-d H:i:s') . ' 素材文件id:' . $material_file->id . '开始执行' . PHP_EOL;

    $ch = curl_init($material_file->url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $file_data = curl_exec($ch);
    curl_close($ch);
    if (!$file_data) {
        throw new AppException('素材下载链接错误:' . $material_file->url);
    }

    $sub_path = '';
    switch ($material_file->file_type) {
        case 5:
        case 6:
        case 7:
        case 10:
        case 1:
            $sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
            break;
        case 3:
        case 2:
            $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            break;
        case 4:
            $sub_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
            break;
        case 11:
            $sub_path = EnvConfig::MATERIAL_AUDIO_DIR_NAME;
            break;
        default:
            throw new AppException('错误的文件类型:' . $material_file->type);
    }

    $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
    $upload_path = SRV_DIR . "/$access_path/" . 'GRBB/' . $material_file->material_id;
    if (!file_exists($upload_path)) {
        mkdir($upload_path, 0755, true);
    }
    $src = $upload_path . '/' . $material_file->filename;
    $fp = fopen($src, 'a');
    fwrite($fp, $file_data);
    fclose($fp);

    $material_file->uploader = isset($user_migrate_map[$material_file->uploader]) ? $user_migrate_map[$material_file->uploader]->new_name : $material_file->uploader;
    $material_file->url = str_replace('dms.zeda.cn', 'dms.zx.com', $material_file->url);

    MysqlConnection::getConnection()
        ->table('material_file')->where('id', $material_file->id)->update([
            'uploader' => $material_file->uploader,
            'url' => $material_file->url,
        ]);

    $adb_material_file_data = (array)$material_file;
    $adb_material_file_data['insert_time'] = date('Y-m-d H:i:s', $material_file->create_time);
    $adb_material_file_data['update_time'] = date('Y-m-d H:i:s', $material_file->update_time);
    unset($adb_material_file_data['create_time']);


    MysqlConnection::getConnection('data_media')
        ->table('ods_material_file_log')
        ->insert($adb_material_file_data);

}
