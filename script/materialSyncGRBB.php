<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$exist_max_id = 0;

$exist_material_for_max_id = MysqlConnection::getConnection('data_media')
    ->table('ods_material_log')
    ->where('platform', 'GRBB')
    ->where('author', 'null')
    ->whereBetween('insert_time', ['2025-05-23 00:00:00', '2025-06-22 23:59:59'])
    ->orderBy('material_id', 'desc')
    ->first();

if ($exist_material_for_max_id) {
    $exist_max_id = $exist_material_for_max_id->material_id;
}

$user_migrate_map = MysqlConnection::getConnection()
    ->table('user_migrate_map')->get()->keyBy('old_name');

$material_list = MysqlConnection::getConnection()
    ->table('material')
    ->whereBetween('create_time', [strtotime('2025-05-23 00:00:00'), strtotime('2025-06-22 23:59:59')])
    ->where('platform', '=', 'GRBB')
    ->where('material_id', '>', $exist_max_id)
    ->get();

foreach ($material_list as $material) {
    echo date('Y-m-d H:i:s') . ' 素材id:' . $material->material_id . '开始执行' . PHP_EOL;

    $material->author = isset($user_migrate_map[$material->author]) ? $user_migrate_map[$material->author]->new_name : $material->author;

    $c_author = json_decode($material->c_author, true);
    foreach ($c_author as &$c) {
        $c = isset($user_migrate_map[$c]) ? $user_migrate_map[$c]->new_name : $c;
    }
    $material->c_author = json_encode($c_author, JSON_UNESCAPED_UNICODE);

    $a_author = json_decode($material->a_author, true);
    foreach ($a_author as &$a) {
        $a = isset($user_migrate_map[$a]) ? $user_migrate_map[$a]->new_name : $a;
    }
    $material->a_author = json_encode($a_author, JSON_UNESCAPED_UNICODE);

    $m4_author = json_decode($material->m4_author, true);
    foreach ($m4_author as &$m4) {
        $m4 = isset($user_migrate_map[$m4]) ? $user_migrate_map[$m4]->new_name : $m4;
    }
    $material->m4_author = json_encode($m4_author, JSON_UNESCAPED_UNICODE);

    $m5_author = json_decode($material->m5_author, true);
    foreach ($m5_author as &$m5) {
        $m5 = isset($user_migrate_map[$m5]) ? $user_migrate_map[$m5]->new_name : $m5;
    }
    $material->m5_author = json_encode($m5_author, JSON_UNESCAPED_UNICODE);

    MysqlConnection::getConnection()
        ->table('material')
        ->where('platform', $material->platform)
        ->where('material_id', $material->material_id)
        ->update([
            'author' => $material->author,
            'c_author' => $material->c_author,
            'a_author' => $material->a_author,
            'm4_author' => $material->m4_author,
            'm5_author' => $material->m5_author,
        ]);

    $adb_material_data = (array)$material;
    $adb_material_data['insert_time'] = date('Y-m-d H:i:s', $adb_material_data['create_time']);
    $adb_material_data['update_time'] = date('Y-m-d H:i:s', $adb_material_data['update_time']);
    unset($adb_material_data['is_group']);
    unset($adb_material_data['create_time']);
    unset($adb_material_data['last_uploaded_time']);
    unset($adb_material_data['creator_id']);
    unset($adb_material_data['creator']);
    unset($adb_material_data['editor_id']);
    unset($adb_material_data['editor']);

    MysqlConnection::getConnection('data_media')
        ->table('ods_material_log')
//        ->where('material_id', $material->material_id)
//        ->where('platform', 'GRBB')
        ->insert($adb_material_data);
//        ->update(['author' => $material->author]);
}
