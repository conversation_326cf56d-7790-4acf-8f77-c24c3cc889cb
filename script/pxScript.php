<?php

use App\Constant\MediaType;
use App\Model\SqlModel\DataMedia\OdsMaterialUploadMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MonitorADCalcRuleModel;
use App\Model\SqlModel\Zeda\NewOldUserMapModel;
use App\Param\Material\MaterialUploadMediaTaskListParam;
use App\MysqlConnection;
use App\Param\Material\MaterialUploadMediaTaskParam;

require dirname(__DIR__) . '/common/init.php';

// 设置MySQL连接对象
$model = new \NXP\MathExecutor();
$a = $model->execute('"Fruit" == "Fruit" || 5*12+ 1 -33 / 11 + "10" + 1 > 15');
var_dump($a);
//MysqlConnection::setConnection();
//
//$new_old_user_data = (new NewOldUserMapModel())->getAll();
//$uid_map = [];
//$name_map = [];
//$new_name_map = [];
//foreach ($new_old_user_data as $user_data) {
//    $uid_map[$user_data->old_user_id] = $user_data->new_user_id;
//    $name_map[$user_data->old_account] = $user_data->new_account;
//    $new_name_map[] = $user_data->new_account;
//}
//$rule_model = new MonitorADCalcRuleModel();
//$data = $rule_model->getAll();
////var_dump($data);
//$all_new_insert = [];
//$new_insert = [];
//foreach ($data as $row) {
//    if (isset($uid_map[$row->creator_id])) {
//        $new_insert['name'] = $row->name . '-new';
//        $new_insert['suit_media_type'] = $row->suit_media_type;
//        $new_insert['rule_type'] = $row->rule_type;
//        $new_insert['attr_condition'] = $row->attr_condition;
//        $new_insert['calc_condition'] = $row->calc_condition;
//        $new_insert['calc_target'] = $row->calc_target;
//        $new_insert['weighting_target'] = $row->weighting_target;
//        $new_insert['frequency'] = $row->frequency;
//        $new_insert['switch'] = $row->switch;
//        $new_insert['budget'] = $row->budget;
//        $new_insert['budget_operator'] = $row->budget_operator;
//        $new_insert['cpa_bid'] = $row->cpa_bid;
//        $new_insert['cpa_bid_operator'] = $row->cpa_bid_operator;
//        $new_insert['schedule_time'] = $row->schedule_time;
//        $new_insert['schedule_time_operator'] = $row->schedule_time_operator;
//        $new_insert['operate_type'] = $row->operate_type;
//        $new_insert['creator'] = $name_map[$row->creator];
//        $new_insert['creator_id'] = $uid_map[$row->creator_id];
//        $new_insert['editor'] = $name_map[$row->editor] ?? '';
//        $new_insert['editor_id'] = $uid_map[$row->editor_id] ?? 0;
//        $all_new_insert[] = $new_insert;
//    }
//
//}
//$rule_model->batchAdd($all_new_insert);
////var_dump(array_slice($all_new_insert, 0, 3));