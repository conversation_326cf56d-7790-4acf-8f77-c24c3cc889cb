<?php

use App\Container;
use App\Logic\DSP\MediaLogic;
use App\MysqlConnection;
use App\Param\AgentParam;
use App\Struct\Session;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$json = '
{
    "platform": "TW",
    "media_type": 163,
    "account_id": "2976628",
    "agent_name": "原始传奇-效果-IOS-TW-技术测试勿用",
    "agent_group_id": 494,
    "agent_group_name": "哔哩哔哩发行CPS",
    "pay_type": 6,
    "statistic_caliber": 2,
    "permission_list": [
        [
            1,
            2,
            "维度筛选",
            "渠道",
            27
        ],
        [
            1,
            3,
            "维度筛选",
            "渠道",
            36
        ],
        [
            1,
            4,
            "维度筛选",
            "渠道",
            38
        ],
        [
            1,
            5,
            "维度筛选",
            "渠道",
            40
        ],
        [
            1,
            2,
            "维度筛选",
            "渠道",
            28
        ],
        [
            1,
            3,
            "维度筛选",
            "渠道",
            37
        ],
        [
            1,
            4,
            "维度筛选",
            "渠道",
            39
        ],
        [
            1,
            5,
            "维度筛选",
            "渠道",
            41
        ],
        [
            1,
            2,
            "维度选择",
            "市场侧",
            29
        ],
        [
            1,
            2,
            "维度选择",
            "市场侧",
            30
        ],
        [
            1,
            2,
            "指标选择",
            "转化链",
            15
        ],
        [
            1,
            2,
            "指标选择",
            "整体情况",
            18
        ]
    ],
    "user_name": "BILIBILI_CPS-TW_jishuceshi",
    "user_pwd": "123456",
    "route_list": "1,2,3,4,5",
    "route_permission_ids": [
        27,
        36,
        38,
        40,
        28,
        37,
        39,
        41,
        29,
        30,
        15,
        18
    ]
}
';

$json_data = json_decode($json, true);

$account_json = '
[
    {
        "account_id": "3085365",
        "agent_name": "山海经之名剑录-效果-安卓-DT-HN-01",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-01",
        "password": "zhixiang2020 "
    },
    {
        "account_id": "3085366",
        "agent_name": "山海经之名剑录-效果-安卓-DT-HN-02",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-02",
        "password": "zhixiang2021 "
    },
    {
        "account_id": "3085367",
        "agent_name": "山海经之名剑录-效果-安卓-DT-HN-03",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-03",
        "password": "zhixiang2022 "
    },
    {
        "account_id": "3085368",
        "agent_name": "山海经之名剑录-效果-安卓-DT-HN-04",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-04",
        "password": "zhixiang2023 "
    },
    {
        "account_id": "3085369",
        "agent_name": "山海经之名剑录-效果-安卓-DT-HN-05",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-05",
        "password": "zhixiang2024 "
    },
    {
        "account_id": "3085403",
        "agent_name": "山海经之名剑录-效果-iOS-DT-HN-01",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-06",
        "password": "zhixiang2025 "
    },
    {
        "account_id": "3085406",
        "agent_name": "山海经之名剑录-效果-iOS-DT-HN-02",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-07",
        "password": "zhixiang2026 "
    },
    {
        "account_id": "3085409",
        "agent_name": "山海经之名剑录-效果-iOS-DT-HN-03",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-08",
        "password": "zhixiang2027 "
    },
    {
        "account_id": "3085412",
        "agent_name": "山海经之名剑录-效果-iOS-DT-HN-04",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-09",
        "password": "zhixiang2028 "
    },
    {
        "account_id": "3085414",
        "agent_name": "山海经之名剑录-效果-iOS-DT-HN-05",
        "statistic_caliber": "按游戏注册（API上报专用）",
        "user_name": "TW-SH-10",
        "password": "zhixiang2029"
    }
]
';
Container::setSession(new Session("d81b31014208af34eee901e2d93d691e"));

$account_list = json_decode($account_json, true);

echo "----------开始----------\n";
foreach ($account_list as $item) {
    echo "----------{$item['account_id']} 开始----------\n";
    $json_data['agent_group'] = $json_data['agent_group_id'];
    $json_data['account_id'] = $item['account_id'];
    $json_data['agent_name'] = $item['agent_name'];
    $json_data['user_name'] = $item['user_name'];
    $json_data['user_pwd'] = $item['password'];
    $param = new AgentParam($json_data);
    $logic = new MediaLogic();
    $logic->createAgent($param);
    echo "----------{$item['account_id']} 结束----------\n";
}
echo "全部完成\n";
die;
