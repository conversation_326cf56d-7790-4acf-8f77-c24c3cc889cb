<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

//$user_migrate_map = MysqlConnection::getConnection()
//    ->table('user_migrate_map')->get()->keyBy('old_name');

$user_migrate_map = [
    '高热_陈雯' => '陈-雯',
];


$material_list = MysqlConnection::getConnection()
    ->table('material')
    ->where('platform', '=', 'GRBB')
    ->whereIn('author', array_keys($user_migrate_map))
    ->get();


foreach ($material_list as $material) {
    echo date('Y-m-d H:i:s') . ' 素材id:' . $material->material_id . '开始执行fix' . PHP_EOL;

    $material->author = isset($user_migrate_map[$material->author]) ? $user_migrate_map[$material->author] : $material->author;

    $c_author = json_decode($material->c_author, true);
    foreach ($c_author as &$c) {
        $c = isset($user_migrate_map[$c]) ? $user_migrate_map[$c] : $c;
    }
    $material->c_author = json_encode($c_author, JSON_UNESCAPED_UNICODE);

    $a_author = json_decode($material->a_author, true);
    foreach ($a_author as &$a) {
        $a = isset($user_migrate_map[$a]) ? $user_migrate_map[$a] : $a;
    }
    $material->a_author = json_encode($a_author, JSON_UNESCAPED_UNICODE);

    $m4_author = json_decode($material->m4_author, true);
    foreach ($m4_author as &$m4) {
        $m4 = isset($user_migrate_map[$m4]) ? $user_migrate_map[$m4] : $m4;
    }
    $material->m4_author = json_encode($m4_author, JSON_UNESCAPED_UNICODE);

    $m5_author = json_decode($material->m5_author, true);
    foreach ($m5_author as &$m5) {
        $m5 = isset($user_migrate_map[$m5]) ? $user_migrate_map[$m5] : $m5;
    }
    $material->m5_author = json_encode($m5_author, JSON_UNESCAPED_UNICODE);

    MysqlConnection::getConnection()
        ->table('material')
        ->where('platform', $material->platform)
        ->where('material_id', $material->material_id)
        ->update([
            'author' => $material->author,
            'c_author' => $material->c_author,
            'a_author' => $material->a_author,
            'm4_author' => $material->m4_author,
            'm5_author' => $material->m5_author,
        ]);


    MysqlConnection::getConnection()
        ->table('material')
        ->where('platform', '=', $material->platform)
        ->where('material_id', $material->material_id)
        ->update([
            'author' => $material->author,
            'c_author' => $material->c_author,
            'a_author' => $material->a_author,
            'm4_author' => $material->m4_author,
            'm5_author' => $material->m5_author,
        ]);


    MysqlConnection::getConnection('data_media')
        ->table('ods_material_log')
        ->where('platform', '=', $material->platform)
        ->where('material_id', $material->material_id)
        ->update([
            'author' => $material->author,
            'c_author' => $material->c_author,
            'a_author' => $material->a_author,
            'm4_author' => $material->m4_author,
            'm5_author' => $material->m5_author,
        ]);
}

$material_file_list = MysqlConnection::getConnection()
    ->table('material_file')
    ->where('platform', '=', 'GRBB')
    ->whereIn('uploader', array_keys($user_migrate_map))
    ->get();

foreach ($material_file_list as $material_file) {

    echo date('Y-m-d H:i:s') . ' 素材文件id:' . $material_file->id . '开始执行fix' . PHP_EOL;

    $material_file->uploader = isset($user_migrate_map[$material_file->uploader]) ? $user_migrate_map[$material_file->uploader] : $material_file->uploader;

    MysqlConnection::getConnection()
        ->table('material_file')
        ->where('platform', '=', $material_file->platform)
        ->where('id', $material_file->id)
        ->update([
            'uploader' => $material_file->uploader,
        ]);

    MysqlConnection::getConnection('data_media')
        ->table('ods_material_file_log')
        ->where('platform', '=', $material_file->platform)
        ->where('id', $material_file->id)
        ->update([
            'uploader' => $material_file->uploader,
        ]);
}
