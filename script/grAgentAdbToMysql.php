<?php

/**
 * 高热历史渠道导入到mysql
 */

use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Zeda\AgentLeaderChangeLogModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\AgentParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$platform = "GR"; // TODO 需确认
$user_id = 261;   // TODO 需确认
$username = '中旭未来'; // TODO 需确认
$sql = "
    SELECT
        * 
    FROM
        tanwan_datahub.v2_dim_agent_id agent
    WHERE
        agent.platform = '{$platform}' 
        AND agent.is_remote = 0
    order by agent.agent_id, agent_leader_start_time asc
";
$list = MysqlConnection::getConnection('datahub')->select($sql);

$model = new AgentModel();
$adb_model = new V2DimAgentIdModel();
$media_account_model = new MediaAccountModel();
$list = collect($list);
$agent_ids = $list->where('agent_id', '>', 0)->pluck('agent_id')->unique()->toArray();

$mysql_agent = $model->getDataByPlatformAgentIdList($platform, $agent_ids);
$mysql_agent_ids = $mysql_agent->where('agent_id', '>', 0)->pluck('agent_id')->unique()->toArray();

// 找出需要添加的渠道
$need_add_agent_ids = array_diff($agent_ids, $mysql_agent_ids);

echo "----------开始----------\n";
$added_agent_ids = [];
foreach ($list as $item) {
    if (!in_array($item->agent_id, $need_add_agent_ids)) {
        continue;
    }

    $agent_user_name = "{$item->agent_group_name}-自建-{$item->agent_id}"; // TODO 看看要不要改 可自定义

    if (!empty((new AgentModel())->getDataByPlatformUserName($item->platform, $agent_user_name, $item->agent_group_id))) {
        echo "--------- $item->agent_id, $agent_user_name, $item->agent_group_id 渠道已存在----------\n";
        // 已经加过的渠道，只需要加上负责人切换记录
        if (in_array($item->agent_id, $added_agent_ids)) {
            addAgentLeaderChangeData($item);
            echo "--------- $item->agent_id 插入切换记录成功----------\n";
        }
        continue;
    }

    // 获取账号的主体
    $company = '';
    if ($item->account_id > 0) {
        $media_account = $media_account_model->getDataByAccountId($item->account_id, $item->media_type_id);
        $company = $media_account->company ?? '';
    }

    $param = new AgentParam([
        'platform' => $item->platform,
        'media_type' => $item->media_type_id,
        'account_id' => $item->account_id,
        'agent_id' => $item->agent_id,
        'agent_name' => $item->agent_name,
        'agent_group' => $item->agent_group_id,
        'agent_leader' => $item->agent_leader,
        'company' => $company,
        'own' => 1,
        'user_name' => $agent_user_name,
        'user_pwd' => '123456',
        'create_time' => 0,
        'for_ad' => 0, // 不用于批量广告投放
    ]);

    $agent_data = [
        'platform' => $param->platform,
        'media_type' => $param->media_type,
        'account_id' => $param->account_id,
        'agent_id' => $item->agent_id,
        'agent_name' => $param->agent_name,
        'agent_group' => $param->agent_group,
        'agent_group_id' => $param->agent_group,
        'agent_leader' => $param->agent_leader,
        'agent_type' => $param->agent_type,
        'company' => $param->company,
        'own' => $param->own,
        'account_type' => $param->account_type,
        'id_card' => $param->id_card,
        'id_img' => $param->id_img,
        'user_name' => $param->user_name,
        'user_pwd' => $param->user_pwd,
        'bank_holder' => $param->bank_holder,
        'bank_area' => $param->bank_area,
        'bank_name' => $param->bank_name,
        'bank_card_number' => $param->bank_card_number,
        'person' => $param->person,
        'qq' => $param->qq,
        'email' => $param->email,
        'mobile' => $param->mobile,
        'tel' => $param->tel,
        'address' => $param->address,
        'detail_address' => $param->detail_address,
        'protocol_number' => $param->protocol_number,
        'protocol_type' => $param->protocol_type,
        'ad_number' => $param->ad_number,
        'creator_id' => $user_id,
        'creator' => $username,
        'create_time' => strtotime($item->insert_time),
        'update_time' => strtotime($item->update_time),
        'state' => $param->state,
        'pay_type' => $param->pay_type,
        'statistic_caliber' => $param->statistic_caliber,
        'for_ad' => $param->for_ad,
    ];

    (new AgentModel())->add($agent_data);

    addAgentLeaderChangeData($item);

    $added_agent_ids[] = $item->agent_id;

    echo "--------- $item->agent_id 插入成功----------\n";
}
echo "---------完成----------\n";


function addAgentLeaderChangeData($item)
{
    $leader_change_data = [
        'platform' => $item->platform,
        'agent_id' => $item->agent_id,
        'agent_leader' => $item->agent_leader,
        'start_time' => strtotime($item->agent_leader_start_time),
        'end_time' => strtotime($item->agent_leader_end_time)
    ];
    (new AgentLeaderChangeLogModel())->customAdd($leader_change_data);
}
