<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\MysqlConnection;
use Common\EnvConfig;
use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();


$material_file_list = MysqlConnection::getConnection()
    ->table('material_file')
    ->where('platform', 'GRBB')
    ->where('file_type', '=', 2)
    ->whereBetween('create_time', [strtotime('2025-05-23 00:00:00'), strtotime('2025-06-22 23:59:59')])
    ->limit(50000)
    ->orderByDesc('id')
    ->get();


$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/$access_path";

foreach ($material_file_list as $material_file) {
    //给视频加水印
    $ext = explode('.', $material_file->filename);
    $ext = end($ext);
    $base_name = basename($material_file->filename, '.' . $ext);
    $new_name = $base_name . '.wm';

    if (file_exists("$upload_path/$material_file->platform/$material_file->material_id/$new_name")) {
        continue;
    }

    echo date('Y-m-d H:i:s') . ' 素材文件id:' . $material_file->id . '开始执行WM' . PHP_EOL;

    try {
        $ffmpeg = FFMpeg::create();
        $water_open_video = $ffmpeg->open("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename");
        $coordinates = array('x' => '(' . $material_file->width / $material_file->duration . ')*t', 'y' => '(' . $material_file->height / $material_file->duration . ')*t');
        $water_open_video->filters()->watermark("$upload_path/watermark/shuiyin.png", $coordinates);
        $watered_file_name = $material_file->material_id . '_' . $base_name . '_' . $material_file->width . 'x' . $material_file->height . '(watered).' . $ext;
        $format = new X264('libfdk_aac');
        $water_open_video->save($format, "$upload_path/$material_file->platform/$material_file->material_id/$watered_file_name");

        rename("$upload_path/$material_file->platform/$material_file->material_id/$watered_file_name", "$upload_path/$material_file->platform/$material_file->material_id/$new_name");
    } catch (Throwable $e) {
        var_dump($e->getMessage() . PHP_EOL . $e->getTraceAsString());
    }
}
