<?php
/*
 * zzh专用脚本，清洗数据bala bala
 */

use App\MysqlConnection;
use Common\EnvConfig;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

$connection_zeda_name = 'default';
$connection_data_media = 'data_media';


$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

MysqlConnection::setConnection();
$list = MysqlConnection::getConnection($connection_zeda_name)
    ->table('material_file')
    ->where('notify', 1)
    ->where('notify', 1)
    ->where('platform', 'TW')
    ->where('create_time', '>=', 1619676943)
    ->get();

echo $list->count() . PHP_EOL;

foreach ($list as $video) {
    $wm_name = str_replace('.mp4', '.wm', $video->filename);

    file_put_contents("$upload_path/$video->platform/$video->material_id/$wm_name", file_get_contents($video->url));

    $creative1_name = str_ireplace('.mp4', '_creative_1.jpg', $video->filename);
    $cover_name = str_ireplace('.mp4', '.jpg', $video->filename);

    copy(
        "$upload_path/$video->platform/$video->material_id/$creative1_name",
        "$upload_path/$video->platform/$video->material_id/$cover_name"
    );
}

echo 'finish' . PHP_EOL;


