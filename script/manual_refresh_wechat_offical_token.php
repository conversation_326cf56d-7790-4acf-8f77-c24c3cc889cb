<?php

use App\Model\HttpModel\Wechat\Oauth2Model;
use App\Model\RedisModel\WechatAccessTokenModel;
use App\MysqlConnection;
use App\Struct\Generate\MediaTypeGenerator;
use Common\EnvConfig;

require dirname(__DIR__) . '/vendor/autoload.php';
require dirname(__DIR__) . '/common/init.php';

date_default_timezone_set('PRC');
// 设置MySQL连接对象
MysqlConnection::setConnection();

$wechat_access_token_model = new WechatAccessTokenModel();
$oauth2_model = new Oauth2Model();
foreach (EnvConfig::WECHAT as $config_name => $config) {
    $access_token_info = $oauth2_model->token($config['appid'], $config['secret']);
    print_r($access_token_info);
    $wechat_access_token_model->set($config_name, $access_token_info, $access_token_info['expires_in']);
}
