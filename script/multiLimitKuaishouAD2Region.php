<?php

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Kuaishou\ADUnit\ADUnitModel;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADLogModel;
use App\Model\SqlModel\DataMedia\OdsKuaishouUnitLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdLogModel;
use App\Model\SqlModel\Zeda\LimitChengduOperateLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;

const KUAISHOU_CHOSEN_GUANGDONG = 44;
const KUAISHOU_GUANGDONG_CITY = [4401, 4402, 4403, 4404, 4405, 4406, 4407, 4408, 4409, 4412, 4413, 4414, 4415,
    4416, 4417, 4418, 4451, 4452, 4453, 441900, 442000];
const KUAISHOU_COUNTRY_EXCEPT_GUANGDONG = [11, 31, 12, 21, 37, 13, 32, 33, 35, 46, 34, 23, 22, 15, 14, 42, 43, 36,
    45, 41, 61, 64, 62, 63, 51, 52, 53, 50, 65, 54, 810000, 820000, 710000];

class multiLimitKuaishouAD2Region extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $kuaishou_http_model = new ADUnitModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $ad_info) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            $insert_data = [];
//            $num = 0;

            foreach ($ad_info as $item) {
//                $num += 1;
                $original_data = json_encode($item->regions);
                $request_data = $request_intelli_extend_data = [];
                $request_data['advertiser_id'] = $request_intelli_extend_data['advertiser_id'] = (int)$item->account_id;
                $request_data['unit_id'] = $request_intelli_extend_data['unit_id'] = (int)$item->ad_id;
                if (empty($item->regions)) {
                    //不筛选地区
                    $request_data['target']['region'] = KUAISHOU_COUNTRY_EXCEPT_GUANGDONG;

                } elseif (in_array(KUAISHOU_CHOSEN_GUANGDONG, $item->regions)) {

                    $position = array_search(KUAISHOU_CHOSEN_GUANGDONG, $item->regions);
                    unset($item->regions[$position]);
                    $request_data['target']['region'] = array_values($item->regions);

                } elseif (array_intersect($item->regions, KUAISHOU_GUANGDONG_CITY)) {

                    $city = array_diff($item->regions, KUAISHOU_GUANGDONG_CITY);
                    if (!$city) {
                        $city = KUAISHOU_COUNTRY_EXCEPT_GUANGDONG;
                    }
                    $request_data['target']['region'] = array_values($city);
                }

                $intelli_extend = json_decode($item->intelli_extend, true);
                if ($intelli_extend && $intelli_extend['is_open'] == 1) {
                    $request_intelli_extend_data['auto_target'] = false;
//                    $request_intelli_extend_data['target']['intelli_extend']['no_area_break'] = 1;
                }
                $insert = [];
                $insert['account_id'] = $item->account_id;
                $insert['ad2_id'] = $item->ad_id;
                $insert['insert_time'] = time();
                $insert['media_type'] = MediaType::KUAISHOU;
                $insert['old_data'] = $original_data;

                try {
                    var_dump($request_data, $request_intelli_extend_data);
                    $kuaishou_http_model->update($request_intelli_extend_data, $access_token);
                    $response = $kuaishou_http_model->update($request_data, $access_token);
//                    $insert['info'] = 'success ' . json_encode($response, JSON_UNESCAPED_UNICODE);
                    $insert['info'] = 'success ';
                    $insert['status'] = 1;
                } catch (AppException $e) {
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $insert['info'] = json_encode($error, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 2;
                }

                $insert_data[] = $insert;
                print_r(date("Y-m-d H:i:s") . "快手二级广告ID：" . $item->ad_id . "投放地区调整完成" . PHP_EOL);

            }
            if ($insert_data) {
//var_dump($insert_data);
                (new LimitChengduOperateLogModel())->add('kuaishou_limit_operate_log', $insert_data);
            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();

$ad_info = (new OdsKuaishouUnitLogModel())->getADInfoByStatusForLimitRegion();
if ($ad_info->isEmpty()) {
    return;
}

foreach ($ad_info as $item) {
    //将地区处理为数组
    $item->regions = trim($item->regions, '[]');
    if ($item->regions) {
        $item->regions = explode(',', $item->regions);
    } else {
        $item->regions = [];
    }
}
unset($item);
//$connect2 = MysqlConnection::getConnection($connection_data_media);
//$cost_connect = $connect->table('ods_toutiao_account_log')
//    ->join('ods_toutiao_creative_hour_data_log', 'ods_toutiao_creative_hour_data_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->selectRaw('ods_toutiao_account_log.account_id, SUM(cost)')
//    ->where('cost_date', '2021-01-08');


$account_ids = $ad_info->pluck('account_id')->unique();
$account_chunk_list = $ad_info->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');
if ($media_account_list->isEmpty()) {
    return;
}
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiLimitKuaishouAD2Region');
$multi_process->start();
