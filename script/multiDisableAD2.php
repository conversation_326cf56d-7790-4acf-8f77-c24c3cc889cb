<?php

use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;


class multiDisableAD2 extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $ad_model = new App\Model\HttpModel\Toutiao\Ad\AdModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $item) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            foreach ($item as $one) {
                print_r("任务{$n}—ad2_id:{$one->ad_id}" . PHP_EOL);
                try {
                    $ad_model->updateOptStatus(
                        [
                            'advertiser_id' => $account_id,
                            'ad_ids' => [$one->ad_id],
                            'opt_status' => 'disable'
                        ], $access_token
                    );
                } catch (Throwable $e) {
                    print_r("任务{$n}—" . $e->getMessage() . PHP_EOL);
                }
            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();
$connection_data_media = 'data_media';

$sql = "WITH cost AS ( SELECT account_id, sum( cost ) FROM ods_toutiao_account_log INNER JOIN 
ods_toutiao_creative_hour_data_log USING ( account_id ) WHERE cost_date = '2021-01-08' GROUP BY account_id HAVING sum( cost )> 0 ),
ad AS (
	SELECT
		'1' AS media_type,
		a.account_id,
		a.campaign_id,
		ad_id,
		a.platform 
	FROM
		ods_toutiao_campaign_log a
		INNER JOIN ods_toutiao_ad_log b ON a.campaign_id = b.campaign_id 
	WHERE
		a.STATUS = 'CAMPAIGN_STATUS_ENABLE' 
		AND b.STATUS NOT IN ( 'AD_STATUS_DELETE', 'AD_STATUS_AUDIT_DENY' ) 
		AND opt_status = 'AD_STATUS_ENABLE' and b.ad_create_time < '2021-01-08 00:00:00'
	) SELECT
	* 
FROM
	cost
	RIGHT JOIN ad ON ad.account_id = cost.account_id 
WHERE
	cost.account_id IS NULL";

$list = MysqlConnection::getConnection($connection_data_media)->select($sql);
$list = collect($list);
//$connect2 = MysqlConnection::getConnection($connection_data_media);
//$cost_connect = $connect->table('ods_toutiao_account_log')
//    ->join('ods_toutiao_creative_hour_data_log', 'ods_toutiao_creative_hour_data_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->selectRaw('ods_toutiao_account_log.account_id, SUM(cost)')
//    ->where('cost_date', '2021-01-08');


$account_ids = $list->pluck('account_id')->unique();
$account_chunk_list = $list->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiDisableAD2');
$multi_process->start();
