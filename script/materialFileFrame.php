<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\Logic\DSP\MaterialFileSDTagsTaskMQLogic;
use App\MysqlConnection;
use App\Param\Material\MaterialFileAITagTaskParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$material_files = MysqlConnection::getConnection()
    ->table('material_file')
    ->where('create_time', '>=', strtotime('2024-04-01 00:00:00'))
    ->where('file_type', '=', 2)
    ->where('is_ext', '=', 0)
    ->get();

foreach ($material_files as $file) {
    echo $file->id . PHP_EOL;
    $param = new MaterialFileAITagTaskParam([
        'platform' => $file->platform,
        'material_id' => $file->material_id,
        'material_file_id' => $file->id,
        'material_file_name' => $file->filename,
        'signature' => $file->signature,
        'duration' => $file->duration
    ]);
    (new MaterialFileSDTagsTaskMQLogic())->addTask($param);
}
