<?php
// todo 修复素材包，参数包，广告组合——参数，广告组合——素材，广告组合——其他参数的url
// todo example:
// todo url为 https://dms.zeda.cn/upload/adsimg/material/915/19038/19038_0912-传奇-800-800-4_(6组图)(腾讯投放压缩).jpg
// todo 字符串切割 获得平台、素材id、素材文件名字
// todo 根据以上与original_platform original_material_id 名字 去获取新的url，然后update

use App\MysqlConnection;
use App\Constant\MediaType;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

define('ZEDA_DEFULT_SHIQUAN', 'default');
// 需要过滤的平台
define('FILTER_PLAT_FORM_ARR', ['TW', 'hktw', 'ZW', 'GR', 'wanzi', 'GRBB', 'YY', 'buer']);
//todo 选择库 选择数据表
define('DATA_MEDIA_DB_SHIQUAN', 'data_media');
define('MATERIAL_FILE_LOG_SHIQUAN', 'ods_material_file_log');
//todo 测试用
//define('DATA_MEDIA_DB_SHIQUAN', 'default');
//define('MATERIAL_FILE_LOG_SHIQUAN', 'material_file');

$now = date('Y-m-d H:i:s');

echo "任务开始$now", PHP_EOL;

// 设置MySQL连接对象
MysqlConnection::setConnection();

// 初始值
$start = 0;
$end = 0;
////每次跑数
//$num = 20;
//// 最大数量
//$max = 100;
// 素材包表
$ad_material_packet_table = 'ad_material_packet';
$ad_compose_packet_table = 'ad_compose_packet';
$ad_setting_packet_table = 'ad_setting_packet';
// 一天时间戳
$_create_time_days = 86399;

// todo 测试用
$start_date = '2020-02-28 23:59:59';
$end_date = '2022-12-19';

//// 2020年 1月份开始
//$start_date = '2019-12-31 23:59:59';
//// 结束时间
//$end_date = '2022-12-17 23:59:59';
for ($i = strtotime($start_date); $i <= strtotime($end_date); $i = $start + $_create_time_days) {
    $start = $i + 1;
    $end = $start + $_create_time_days;
    echo PHP_EOL;
    echo date('Y-m-d H:i:s', $start) . '---' . date('Y-m-d H:i:s', $end);

    //todo 参数包和素材包走的是 time时间戳 between 数组;
    $start_end_time = [$start, $end];
    // 素材包
    $ad_material_packet = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
        ->table($ad_material_packet_table)
        ->whereBetween('create_time', $start_end_time)
        ->get();
    echo PHP_EOL;
    echo  "-------------------------material_packet---start--素材包开始---------------------------";
    // 素材包数据处理
    if ($ad_material_packet) {
        foreach ($ad_material_packet as $key => $value) {
            echo PHP_EOL;
            echo "素材包id $value->id";
            echo PHP_EOL;

            $video_vertical_list = json_decode($value->video_vertical_list, true);
            $video_list = json_decode($value->video_list, true);
            $group_image_list = json_decode($value->group_image_list, true);
            $group_video_list = json_decode($value->group_video_list, true);
            $small_image_list = json_decode($value->small_image_list, true);
            $large_image_list = json_decode($value->large_image_list, true);
            $large_vertical_image_list = json_decode($value->large_vertical_image_list, true);



            // 竖版视频信息列表
            if ($video_vertical_list) {
                // $video_vertical_list 这是一个多维度数组 & 引用
                foreach ($video_vertical_list as &$video_vertical_list_value) {
                    $param_res = adMaterialFilterOther($video_vertical_list_value);
                    $video_vertical_list_value['url'] = $param_res['url'];
                    $video_vertical_list_value['platform'] = $param_res['platform'];
                    $video_vertical_list_value['material_id'] = $param_res['material_id'];
                    $video_vertical_list_value['cover_list'] = $param_res['cover_list'];
                }
                $value->video_vertical_list = jsonEncode($video_vertical_list);
            }
            // 横版视频信息列表
            if ($video_list) {
                foreach ($video_list as &$video_list_value) {
                    $param_res = adMaterialFilterOther($video_list_value);
                    $video_list_value['url'] = $param_res['url'];
                    $video_list_value['platform'] = $param_res['platform'];
                    $video_list_value['material_id'] = $param_res['material_id'];
                    $video_list_value['cover_list'] = $param_res['cover_list'];
                }
                $value->video_list = jsonEncode($video_list);
            }
            //组图列表
            if ($group_image_list) {
                foreach ($group_image_list as &$group_image_list_value) {
                    $param_res = adMaterialFilterOther($group_image_list_value);
                    $group_image_list_value['url'] = $param_res['url'];
                    $group_image_list_value['platform'] = $param_res['platform'];
                    $group_image_list_value['material_id'] = $param_res['material_id'];
                }
                $value->group_image_list = jsonEncode($group_image_list);
            }
//            //组视频列表
//            if ($group_video_list) {
//                foreach ($group_video_list as &$group_video_list_value) {
//                    $param_res = adMaterialFilterOther($group_video_list_value);
//                    $group_video_list_value['url'] = $param_res['url'];
//                }
//                $value->group_video_list = json_encode($group_video_list);
//            }
            //横版小图信息列表
            if ($small_image_list) {
                foreach ($small_image_list as &$small_image_list_value) {
                    $param_res = adMaterialFilterOther($small_image_list_value);
                    $small_image_list_value['url'] = $param_res['url'];
                    $small_image_list_value['platform'] = $param_res['platform'];
                    $small_image_list_value['material_id'] = $param_res['material_id'];
                }
                $value->small_image_list = jsonEncode($small_image_list);
            }
            //横版大图信息列表
            if ($large_image_list) {
                foreach ($large_image_list as &$large_image_list_value) {
                    $param_res = adMaterialFilterOther($large_image_list_value);
                    $large_image_list_value['url'] = $param_res['url'];
                    $large_image_list_value['platform'] = $param_res['platform'];
                    $large_image_list_value['material_id'] = $param_res['material_id'];
                }
                $value->large_image_list = jsonEncode($large_image_list);
            }
            //竖版大图信息列表
            if ($large_vertical_image_list) {
                foreach ($large_vertical_image_list as &$large_vertical_image_list_value) {
                    $param_res = adMaterialFilterOther($large_vertical_image_list_value);
                    $large_vertical_image_list_value['url'] = $param_res['url'];
                    $large_vertical_image_list_value['platform'] = $param_res['platform'];
                    $large_vertical_image_list_value['material_id'] = $param_res['material_id'];
                }
                $value->large_vertical_image_list = jsonEncode($large_vertical_image_list);
            }

            // 素材包修改参数+1
            $ad_material_packet_data = [
                'video_vertical_list' => $value->video_vertical_list,
                'video_list' => $value->video_list,
                'group_image_list' => $value->group_image_list,
//                'group_video_list' => $value->group_video_list,
                'small_image_list' => $value->small_image_list,
                'large_image_list' => $value->large_image_list,
                'large_vertical_image_list' => $value->large_vertical_image_list,
                'update_time' => time()
            ];
            // update操作
            $result = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
                ->table($ad_material_packet_table)
                ->where(['id' => $value->id])
                ->update($ad_material_packet_data);
            if ($result == 0){
                echo "素材包执行结果：修改失败";
                exit();
            }
            echo "素材包执行结果：$result , 执行的素材包主键id： {$value->id}";
            echo PHP_EOL;
        }
    }
    echo PHP_EOL;
    echo  "-----------------------material_packet----end--素材包执行结束------------------------------";
    echo PHP_EOL;
    echo  "-------------------------setting_packet----start--参数包开始------------------------------";
    // 参数包处理
    $ad_setting_packet = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
        ->table($ad_setting_packet_table)
        ->whereBetween('create_time', $start_end_time)
        ->get();
    if ($ad_setting_packet) {
        foreach ($ad_setting_packet as $ad_setting_packet_key => $ad_setting_packet_value) {
            echo PHP_EOL;
            echo "参数包id $ad_setting_packet_value->id";
            echo PHP_EOL;
            $setting_data = json_decode($ad_setting_packet_value->setting, true);
            // 头条 -- 推广卡片图片
            if (isset($setting_data['product_image_id']) && $setting_data['product_image_id']) {
                $param_res = adSettingAndComposeFilterOther($setting_data['product_image_id']);
                if ($param_res) {
                    $setting_data['product_image_id'] = $param_res;
                }
            }
            // 快手 应用图标
            if (isset($setting_data['site_config_img_url']) && $setting_data['site_config_img_url']) {
                $param_res = adSettingAndComposeFilterOther($setting_data['site_config_img_url']);
                if ($param_res) {
                    $setting_data['site_config_img_url'] = $param_res;
                }
            }
            // 腾讯 -- 卖点图
            if (isset($setting_data['shop_img']) && $setting_data['shop_img']) {
                $param_res = adSettingAndComposeFilterOther($setting_data['shop_img']);
                if ($param_res) {
                    $setting_data['shop_img'] = $param_res;
                }
            }
            // 腾讯 -- 品牌形象
            if (isset($setting_data['brand_img']) && $setting_data['brand_img']) {
                $param_res = adSettingAndComposeFilterOther($setting_data['brand_img']);
                if ($param_res) {
                    $setting_data['brand_img'] = $param_res;
                }
            }
            // 百度信息流 -- icon图标
            if (isset($setting_data['user_portrait']) && $setting_data['user_portrait']) {
                $param_res = adSettingAndComposeFilterOther($setting_data['user_portrait']);
                if ($param_res) {
                    $setting_data['user_portrait'] = $param_res;
                }
            }

            $ad_setting_packet_value->setting = jsonEncode($setting_data);

            //todo update操作 参数包
            $setting_res = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
                ->table($ad_setting_packet_table)
                ->where(['id' => $ad_setting_packet_value->id])
                ->update(['setting' => $ad_setting_packet_value->setting, 'update_time' => time()]);
            if ($setting_res == 0){
                echo "参数包执行结果：修改失败";
                exit();
            }
            echo "参数包执行结果：$setting_res , 执行的参数包主键id： {$ad_setting_packet_value->id}";
            echo PHP_EOL;
        }
    }
    echo PHP_EOL;
    echo  "------------------------setting_packet----end--参数包执行结束------------------------------";
    echo PHP_EOL;
    echo  "-----------------------compose_packet-----start--组合包执行开始----------------------------";
    # todo 组合包走的是 date
    $start_end_date = [date('Y-m-d H:i:s', $start), date('Y-m-d H:i:s', $end)];
    // 广告组合
    $ad_compose_packet = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
        ->table($ad_compose_packet_table)
        ->whereBetween('create_time', $start_end_date)
        ->get();

    if ($ad_compose_packet) {
        foreach ($ad_compose_packet as $ad_compose_packet_key => $ad_compose_packet_value) {
            echo PHP_EOL;
            echo "组合包id $ad_compose_packet_value->id";
            echo PHP_EOL;
            $ad_c_group_image_list = json_decode($ad_compose_packet_value->group_image_list, true);
            $ad_c_small_image_list = json_decode($ad_compose_packet_value->small_image_list, true);
            $ad_c_large_image_list = json_decode($ad_compose_packet_value->large_image_list, true);
            $ad_c_large_vertical_image_list = json_decode($ad_compose_packet_value->large_vertical_image_list, true);
            $ad_c_video_list = json_decode($ad_compose_packet_value->video_list, true);
            $ad_c_video_vertical_list = json_decode($ad_compose_packet_value->video_vertical_list, true);
            $ad_c_cover_list_map = json_decode($ad_compose_packet_value->cover_list_map, true);
            $ad_c_vertical_cover_list_map = json_decode($ad_compose_packet_value->vertical_cover_list_map, true);
            $ad_c_setting = json_decode($ad_compose_packet_value->setting, true);
            $ad_c_other_setting = json_decode($ad_compose_packet_value->other_setting, true);
            //组图列表
            if ($ad_c_group_image_list) {
                foreach ($ad_c_group_image_list as &$ad_c_group_image_list_value) {
                    $param_res = adMaterialFilterOther($ad_c_group_image_list_value);
                    $ad_c_group_image_list_value['url'] = $param_res['url'];
                    $ad_c_group_image_list_value['platform'] = $param_res['platform'];
                    $ad_c_group_image_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->group_image_list = jsonEncode($ad_c_group_image_list);
            }
            //横版小图信息列表
            if ($ad_c_small_image_list) {
                foreach ($ad_c_small_image_list as &$ad_c_small_image_list_value) {
                    $param_res = adMaterialFilterOther($ad_c_small_image_list_value);
                    $ad_c_small_image_list_value['url'] = $param_res['url'];
                    $ad_c_small_image_list_value['platform'] = $param_res['platform'];
                    $ad_c_small_image_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->small_image_list = jsonEncode($ad_c_small_image_list);
            }
            //横版大图信息列表
            if ($ad_c_large_image_list) {
                foreach ($ad_c_large_image_list as &$ad_c_large_image_list_value) {
                    $param_res = adMaterialFilterOther($ad_c_large_image_list_value);
                    $ad_c_large_image_list_value['url'] = $param_res['url'];
                    $ad_c_large_image_list_value['platform'] = $param_res['platform'];
                    $ad_c_large_image_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->large_image_list = jsonEncode($ad_c_large_image_list);
            }

            //竖版大图信息列表
            if ($ad_c_large_vertical_image_list) {
                foreach ($ad_c_large_vertical_image_list as &$ad_c_large_vertical_image_list_value) {
                    $param_res = adMaterialFilterOther($ad_c_large_vertical_image_list_value);
                    $ad_c_large_vertical_image_list_value['url'] = $param_res['url'];
                    $ad_c_large_vertical_image_list_value['platform'] = $param_res['platform'];
                    $ad_c_large_vertical_image_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->large_vertical_image_list = jsonEncode($ad_c_large_vertical_image_list);
            }
            // 横版视频
            if ($ad_c_video_list) {
                foreach ($ad_c_video_list as &$ad_c_video_list_value) {
                    $param_res = adComposeFilterOther($ad_c_video_list_value);
                    $ad_c_video_list_value['url'] = $param_res['url'];
                    $ad_c_video_list_value['cover_list'] = $param_res['cover_list'];
                    $ad_c_video_list_value['platform'] = $param_res['platform'];
                    $ad_c_video_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->video_list = jsonEncode($ad_c_video_list);
            }
            // 竖版视频
            if ($ad_c_video_vertical_list) {
                foreach ($ad_c_video_vertical_list as &$ad_c_video_vertical_list_value) {
                    $param_res = adComposeFilterOther($ad_c_video_vertical_list_value);
                    $ad_c_video_vertical_list_value['url'] = $param_res['url'];
                    $ad_c_video_vertical_list_value['cover_list'] = $param_res['cover_list'];
                    $ad_c_video_vertical_list_value['platform'] = $param_res['platform'];
                    $ad_c_video_vertical_list_value['material_id'] = $param_res['material_id'];
                }
                $ad_compose_packet_value->video_vertical_list = jsonEncode($ad_c_video_vertical_list);
            }
            //视频封面
            if ($ad_c_cover_list_map) {
                foreach ($ad_c_cover_list_map as $ad_c_cover_list_map_key => $ad_c_cover_list_map_value) {
                    $param_res = adComposeFilterOtherTwo($ad_c_cover_list_map_value);
                    $ad_c_cover_list_map[$ad_c_cover_list_map_key] = $param_res;
                }
                $ad_compose_packet_value->cover_list_map = jsonEncode($ad_c_cover_list_map);
            }
            // 竖版视频封面
            if ($ad_c_vertical_cover_list_map) {
                foreach ($ad_c_vertical_cover_list_map as $ad_c_vertical_cover_list_map_key => $ad_c_vertical_cover_list_map_value) {
                    $param_res = adComposeFilterOtherTwo($ad_c_vertical_cover_list_map_value);
                    $ad_c_vertical_cover_list_map[$ad_c_vertical_cover_list_map_key] = $param_res;
                }
                $ad_compose_packet_value->vertical_cover_list_map = jsonEncode($ad_c_vertical_cover_list_map);
            }
            // 参数包
            if ($ad_c_setting) {
                // 头条 -- 推广卡片图片
                if (isset($ad_c_setting['product_image_id']) && $ad_c_setting['product_image_id']) {
                    $param_res = adSettingAndComposeFilterOther($ad_c_setting['product_image_id']);
                    if ($param_res) {
                        $ad_c_setting['product_image_id'] = $param_res;
                    }
                }
                // 快手 -- 应用图标
                if (isset($ad_c_setting['site_config_img_url']) && $ad_c_setting['site_config_img_url']) {
                    $param_res = adSettingAndComposeFilterOther($ad_c_setting['site_config_img_url']);
                    if ($param_res) {
                        $ad_c_setting['site_config_img_url'] = $param_res;
                    }
                }
                // 腾讯 -- 卖点图
                if (isset($ad_c_setting['shop_img']) && $ad_c_setting['shop_img']) {
                    $param_res = adSettingAndComposeFilterOther($ad_c_setting['shop_img']);
                    if ($param_res) {
                        $ad_c_setting['shop_img'] = $param_res;
                    }
                }
                // 腾讯 -- 品牌形象
                if (isset($ad_c_setting['brand_img']) && $ad_c_setting['brand_img']) {
                    $param_res = adSettingAndComposeFilterOther($ad_c_setting['brand_img']);
                    if ($param_res) {
                        $ad_c_setting['brand_img'] = $param_res;
                    }
                }
                // 百度信息流 -- icon图标
                if (isset($ad_c_setting['user_portrait']) && $ad_c_setting['user_portrait']) {
                    $param_res = adSettingAndComposeFilterOther($ad_c_setting['user_portrait']);
                    if ($param_res) {
                        $ad_c_setting['user_portrait'] = $param_res;
                    }
                }
                $ad_compose_packet_value->setting = jsonEncode($ad_c_setting);
            }
            // app_thumbnails 头条2.0 其他参数
            if ($ad_c_other_setting) {
                if (isset($ad_c_other_setting['app_thumbnails']) && $ad_c_other_setting['app_thumbnails']) {
                    foreach ($ad_c_other_setting['app_thumbnails'] as $app_thumbnails_key => $app_thumbnails_value) {
                        if ($app_thumbnails_value) {
                            $param_res = adSettingAndComposeFilterOther($app_thumbnails_value);
                            if ($param_res) {
                                $ad_c_other_setting['app_thumbnails'][$app_thumbnails_key] = $param_res;
                            }
                        }
                    }
                    $ad_c_other_setting['app_thumbnails'] = array_values($ad_c_other_setting['app_thumbnails']);
                }
                $ad_compose_packet_value->other_setting = jsonEncode($ad_c_other_setting);
            }
            // 组合包修改参数+1
            $ad_compose_packet_data = [
                'group_image_list' => $ad_compose_packet_value->group_image_list,
                'small_image_list' => $ad_compose_packet_value->small_image_list,
                'large_image_list' => $ad_compose_packet_value->large_image_list,
                'large_vertical_image_list' => $ad_compose_packet_value->large_vertical_image_list,
                'video_list' => $ad_compose_packet_value->video_list,
                'video_vertical_list' => $ad_compose_packet_value->video_vertical_list,
                'cover_list_map' => $ad_compose_packet_value->cover_list_map,
                'vertical_cover_list_map' => $ad_compose_packet_value->vertical_cover_list_map,
                'setting' => $ad_compose_packet_value->setting,
                'other_setting' => $ad_compose_packet_value->other_setting,
                'update_time' => date('Y-m-d H:i:s')
            ];
            // update操作 组合包
            $compose_packet_res = MysqlConnection::getConnection(ZEDA_DEFULT_SHIQUAN)
                ->table($ad_compose_packet_table)
                ->where(['id' => $ad_compose_packet_value->id])
                ->update($ad_compose_packet_data);
            if ($compose_packet_res == 0){
                echo "组合包执行结果：修改失败";
                exit();
            }
            echo "组合包执行结果：$compose_packet_res , 执行的组合包主键id： {$ad_compose_packet_value->id}";
            echo PHP_EOL;
        }
    }
    echo PHP_EOL;
    echo  "--------------------compose_packet----end----组合包执行开始-------------------------------";
    echo PHP_EOL;
}

/**
 * 素材包处理
 * @param $param
 * @return mixed
 */
function adMaterialFilterOther($param)
{
    if (strpos($param['url'], 'zeda.cn') !== false) {
        $where_data = wherePath($param['url']);

        if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
            $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                ->table(MATERIAL_FILE_LOG_SHIQUAN)
                ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                ->where(['filename' => $where_data['base_name']])
                ->first();
            if ($material_file) {
                $param['url'] = $material_file->url;
                $param['platform'] = $material_file->platform;
                $param['material_id'] = $material_file->material_id;
            } else {
                echo "素材搜索不到: platform --> {$where_data['platform']} ||  material_id --> {$where_data['material_id']} || base_name --> {$where_data['base_name']}";
                echo PHP_EOL;
//                exit();
            }
        }else{
            echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
            echo PHP_EOL;
        }

    }
    if (isset($param['cover_list']) && $param['cover_list']) {
        foreach ($param['cover_list'] as $cover_key => $cover_value) {
            $cover_url = json_decode($cover_value, true);
            if (strpos($cover_url['url'], 'zeda.cn') !== false) {
                $where_data = wherePath($cover_url['url']);
                // 非 'TW', 'hktw', 'ZW', 'GR' 这四个平台的素材才做处理
                if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
                    $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                        ->table(MATERIAL_FILE_LOG_SHIQUAN)
                        ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                        ->where(['filename' => $where_data['base_name']])
                        ->first();

                    if ($material_file) {
                        $cover_url['url'] = $material_file->url;
                        $param['cover_list'][$cover_key] = jsonEncode($cover_url);
                    } else {
                        $param['cover_list'][$cover_key] = $cover_value;
                        echo "素材搜索不到: platform --> {$where_data['platform']} ||  material_id --> {$where_data['material_id']} || base_name --> {$where_data['base_name']}";
                        echo PHP_EOL;
//                        exit();
                    }
                } else {
                    echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
                    echo PHP_EOL;
                    // 复制赋值
                    $param['cover_list'][$cover_key] = $cover_value;
                }
            }
        }
        $param['cover_list'] = array_values($param['cover_list']);
        // 防止其他素材包，乱入，导致数据错误
//        if (strpos($param['cover_list'][0], 'zeda.cn') !== false) {
//
//        }
    }
    return $param;
}


/**
 * 参数包处理
 * @param $url
 * @return mixed|void
 */
function adSettingAndComposeFilterOther($url)
{
    if (strpos($url, 'zeda.cn') !== false) {
        $where_data = wherePath($url);
        if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
            $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                ->table(MATERIAL_FILE_LOG_SHIQUAN)
                ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                ->where(['filename' => $where_data['base_name']])
                ->first();
            if ($material_file) {
                $url = $material_file->url;
            } else {
                echo "素材搜索不到: platform-->{$where_data['platform']} || material_id-->{$where_data['material_id']} || base_name-->{$where_data['base_name']}";
                echo PHP_EOL;
//                exit();
            }
        }else{
            echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
            echo PHP_EOL;
        }
    }
    return $url;
}


/**
 * 组合包处理
 * @param $param
 * @return mixed
 */
function adComposeFilterOther($param)
{
    if (strpos($param['url'], 'zeda.cn') !== false) {
        $where_data = wherePath($param['url']);

        if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
            $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                ->table(MATERIAL_FILE_LOG_SHIQUAN)
                ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                ->where(['filename' => $where_data['base_name']])
                ->first();
            if ($material_file) {
                $param['url'] = $material_file->url;
                $param['platform'] = $material_file->platform;
                $param['material_id'] = $material_file->material_id;
            } else {
                echo "素材搜索不到: platform-->{$where_data['platform']} || material_id-->{$where_data['material_id']} || base_name-->{$where_data['base_name']}";
                echo PHP_EOL;
//                exit();
            }
        }else{
            echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
            echo PHP_EOL;
        }
    }
    if (isset($param['cover_list']) && $param['cover_list']) {
        foreach ($param['cover_list'] as $cover_c_key => $cover_c_value) {
            $cover_url = json_decode($cover_c_value, true);

            if (strpos($cover_url['url'], 'zeda.cn') !== false) {
                $where_data = wherePath($cover_url['url']);
                // 非 'TW', 'hktw', 'ZW', 'GR' 这四个平台的素材才做处理
                if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
                    $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                        ->table(MATERIAL_FILE_LOG_SHIQUAN)
                        ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                        ->where(['filename' => $where_data['base_name']])
                        ->first();

                    if ($material_file) {
                        $cover_url['url'] = $material_file->url;
                        $param['cover_list'][$cover_c_key] = jsonEncode($cover_url);
                    } else {
                        $param['cover_list'][$cover_c_key] = jsonEncode($cover_url);
                        echo "素材搜索不到: platform-->{$where_data['platform']} || material_id-->{$where_data['material_id']} || base_name-->{$where_data['base_name']}";
                        echo PHP_EOL;
//                        exit();
                    }
                } else {
                    echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
                    echo PHP_EOL;
                    // 复制赋值
                    $param['cover_list'][$cover_c_key] = jsonEncode($cover_url);
                }
            }
        }
        $param['cover_list'] = array_values($param['cover_list']);

        // 防止其他素材包，乱入，导致数据错误
//        if (strpos($param['cover_list'][0], 'zeda.cn') !== false) {
//        }
    }
    return $param;
}

/**
 * 组合包素材列表处理
 * @param $param
 * @return array
 */
function adComposeFilterOtherTwo($param)
{
    foreach ($param as $cover_list_map_res_key => $cover_list_map_res_value) {
        $cover_list_map_res = json_decode($cover_list_map_res_value, true);

        if (isset($cover_list_map_res['url']) && $cover_list_map_res['url']) {
            if (strpos($cover_list_map_res['url'], 'zeda.cn') !== false) {
                $where_data = wherePath($cover_list_map_res['url']);
                if (!in_array($where_data['platform'], FILTER_PLAT_FORM_ARR)) {
                    $material_file = MysqlConnection::getConnection(DATA_MEDIA_DB_SHIQUAN)
                        ->table(MATERIAL_FILE_LOG_SHIQUAN)
                        ->where(['original_platform' => $where_data['platform'], 'original_material_id' => $where_data['material_id']])
                        ->where(['filename' => $where_data['base_name']])
                        ->first();
                    if ($material_file) {
                        $cover_list_map_res['url'] = $material_file->url;
                    } else {
                        echo "素材搜索不到: platform-->{$where_data['platform']} || material_id-->{$where_data['material_id']} || base_name-->{$where_data['base_name']}";
                        echo PHP_EOL;
//                        exit();
                    }
                }else{
                    echo "跳过：{$where_data['platform']} 包含在" . implode('-', FILTER_PLAT_FORM_ARR);
                    echo PHP_EOL;
                }
            }
        }
        $param[$cover_list_map_res_key] = jsonEncode($cover_list_map_res);
    }
    return array_values($param);
}

//todo json数据统一处理 69082
function jsonEncode($json_data)
{
    return json_encode($json_data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

//todo 获取basename  platform meterial_id
function wherePath($url)
{
    $plat_form = [
        'TW' => '贪玩',
        'xinxin' => '新新',
        'wanzi' => '万紫',
        '915' => '915',
        '9k' => '千红',
        'DY' => '多娱',
        'buer' => '不二',
        'DYHD' => '多娱互动',
        'wenxue' => '文学',
        'guangfeng' => '广丰',
        'GR' => '八九游',
        'ZW' => '掌玩',
        'twgh' => '公会',
        'haibo' => '海博',
        '43wan' => '43玩',
        'JH' => '嘉皇',
        '1377' => '1377',
        'ceshi' => '测试',
        'flamingo' => '火烈鸟',
        'YY' => '游娱',
        'andou' => '安豆',
        'beyoung' => '千红彼岸',
        'quwan' => '趣玩',
        'hktw' => '香港贪玩',
        'TWSJ' => '贪玩世界',
        'shunwan' => '顺玩',
        'BX' => '冰雪',
        'zhijian' => '指尖',
        '996' => '996',
        'GRBB' => '高热本部',
        'aomu' => '奥目',
        'hkhp' => '欢乐时光',
        'GZTW' => '广州贪玩',
        'overseas-test' => '海外测试',
        '千红' => '千红',
    ];
    $plat_form_two = array_flip($plat_form);
    $plat_form_key = 0;

    $uri_path = parse_url($url);
    $base_name = basename($uri_path['path']);
    $uri_res = explode('/', $uri_path['path']);

    foreach ($uri_res as $uri_res_key => $uri_res_value) {
        if (in_array($uri_res_value, $plat_form_two)) {
            $plat_form_key = $uri_res_key;
        }
    }
    if ($plat_form_key == 0) {
        echo "素材地址解析: 错误无法获取素材对应的平台数据：" . $url;
        exit();
    }
    return ['base_name' => $base_name, 'platform' => $uri_res[$plat_form_key], 'material_id' => $uri_res[$plat_form_key + 1]];
}


$now = date('Y-m-d H:i:s');;

echo "任务完成$now", PHP_EOL;




// 按照id数字
//for ($i = $start; $i <= $max; $i = $start + $num) {
//    $start = $i + 1;
//    $end = $start + $num;
//    echo PHP_EOL;
//    echo $start . '---' . $end;
//    // between 数组;
//    $start_end = [$start, $end];
//    // 素材包
//    $ad_material_packet = MysqlConnection::getConnection($connection_data_media)
//        ->table('ad_material_packet')
//        ->where('create_time', '>=', '')
//        ->whereBetween('id', $start_end)
//        ->get();
//
//    foreach ($ad_material_packet as $key => $value){
//        var_dump($value);die;
//    }
//
////    // 广告组合
////    $ad_compose_packet = MysqlConnection::getConnection($connection_data_media)
////        ->table('ad_compose_packet')
////        ->whereBetween('id', $start_end)
////        ->get();
////    // 参数包
////    $ad_setting_packet = MysqlConnection::getConnection($connection_data_media)
////        ->table('ad_setting_packet')
////        ->whereBetween('id', $start_end)
////        ->get();
//}
