import sys
import json
import os
import matplotlib.pyplot as plt
import textwrap
import pandas as pd
import seaborn as sns
from matplotlib.ticker import ScalarFormatter
import numpy as np

# 解决中文乱码
sns.set(style="whitegrid")
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams["font.family"] = "sans-serif"
# 解决负号无法显示的问题
plt.rcParams['axes.unicode_minus'] = False


def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))


# 处理指标字段，将其转换为数字或保留百分比字符串
def convert_to_number(value):
    if isinstance(value, str):
        if value.endswith('%'):
            return float(value.strip('%'))  # 保留百分比数值
        else:
            return float(value)
    return value

# 禁用科学计数法
def disable_scientific_notation(axis):
    axis.set_major_formatter(ScalarFormatter(useMathText=False))
    axis.get_major_formatter().set_scientific(False)
    axis.get_major_formatter().set_useOffset(False)

def read_json_file(filename_prefix):
    # 这个 filename是一个前缀
    filename = filename_prefix + ".json"
    if not os.path.isfile(filename):
        raise FileNotFoundError(f"File '{filename}' does not exist.")

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
            return data
    except json.JSONDecodeError:
        raise ValueError(f"File '{filename}' is not a valid JSON file.")


# 创建表格
def create_table_and_save_as_png(data, column_labels, filename, title, watermark,max_col_width=2):
    num_rows = len(data)
    num_cols = len(column_labels)

    # 如果数据或列标签为空，则创建一个空图
    if num_rows == 0 or num_cols == 0:
        fig, ax = plt.subplots(figsize=(5, 3))
        ax.text(0.5, 0.5, '您要查询的数据为空列表', ha='center', va='center', fontsize=12)
        ax.axis('off')
        plt.savefig(filename, bbox_inches='tight', dpi=300, pad_inches=0.05)
        plt.close(fig)
        return

    # 计算每列的宽度，限制最大宽度
    col_widths = []
    for col in range(num_cols):
        max_content_length = max(len(str(data[row][col])) for row in range(num_rows))
        max_header_length = len(str(column_labels[col]))
        max_length = max(max_content_length, max_header_length)
        col_width = min(max_length * 0.4, max_col_width)  # 每个字符占用0.4单位宽度
        col_widths.append(col_width)

    # 计算整个图形的大小
    fig_width = sum(col_widths)
    fig_height = (num_rows + 1) * 0.5  # 每行固定高度

    # 创建一个新的图形
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))

    # 设置标题
    if title:
        ax.set_title(title, fontsize=14, fontweight='bold', pad=10)




    # 隐藏坐标轴
    ax.axis('off')
    ax.axis('tight')

    # 自动换行表头
    wrapped_labels = ['\n'.join(textwrap.wrap(label, width=10)) for label in column_labels]

    # 创建表格
    table = ax.table(cellText=data, colLabels=wrapped_labels, loc='center', cellLoc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(12)  # 固定字体大小

    # 设置列宽
    for i, width in enumerate(col_widths):
        table.auto_set_column_width([i])
        for cell in table.get_celld().values():
            if cell.get_text().get_text() in wrapped_labels:
                cell.set_width(width / fig_width)

    # 设置表头背景颜色
    for col in range(num_cols):
        table[(0, col)].set_facecolor('#f0f0f0')
        table[(0, col)].set_text_props(weight='bold')

    # 设置表格线条颜色
    for key, cell in table.get_celld().items():
        cell.set_edgecolor('black')

    # 添加全屏水印（仅当水印不为空时）
    if watermark:
        # 获取表格的边界框
        bbox = table.get_window_extent(fig.canvas.get_renderer())

        # 将边界转换为相对画布坐标
        left, bottom, width, height = bbox.transformed(fig.transFigure.inverted()).bounds

        x_spacing = 0.6
        y_spacing = 0.6
        for i in np.arange(left, left + width, x_spacing / fig_width):
            for j in np.arange(bottom, left+height, y_spacing / fig_height):
                fig.text(i, j, watermark, fontsize=10, color='gray', ha='center', va='center', alpha=0.1, rotation=30)

    # 保存为 PNG 图片
    plt.savefig(filename, bbox_inches='tight', dpi=150, pad_inches=0.05)  # 无额外边距

    plt.close(fig)  # 关闭图形以释放内存


# 折线图
def create_line(data_list, column_list, filename):
    # 将数据转换为 DataFrame
    df = pd.DataFrame(data_list, columns=column_list)

    # 假设第一个字段是维度，第二个字段是指标
    dimension = column_list[0]
    metric = column_list[1]

    # 每一个值应用convert_to_number
    df[metric] = df[metric].apply(convert_to_number)

    # 有可能有重复的维度，聚合一下
    df = df.groupby(dimension, as_index=False).sum()

    # 绘制折线图

    # 动态调整图表的宽度
    fig_width = max(10, len(df) * 1.5)
    plt.figure(figsize=(fig_width, 6))

    # 绘制折线图
    plt.plot(df[dimension], df[metric], linestyle='-', color='b', marker='o', linewidth=2, markersize=8, label=metric)

    # 添加标题和标签
    plt.title(f'{dimension} vs {metric} 折线图')
    plt.xlabel(dimension, fontsize=12)

    # 如果指标是百分比，设置Y轴为百分比格式
    if any(isinstance(i, str) and '%' in i for i in data_list[0]):
        plt.ylabel(metric + "(%)", fontsize=12)
    else:
        plt.ylabel(metric, fontsize=12)

    # 禁用科学计数法
    disable_scientific_notation(plt.gca().yaxis)

    # # 显示图例
    plt.legend(fontsize=12)

    # 设置坐标轴刻度字体大小
    plt.xticks(fontsize=10)
    plt.yticks(fontsize=10)
    # 旋转一下 x 的角度
    # plt.xticks(rotation=45)
    plt.tight_layout()
    # 显示网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 保存为 PNG 图片
    plt.savefig(filename, dpi=300, bbox_inches='tight')


# 饼图
def create_pie(data_list, column_list, filename):
    # 将数据转换为 DataFrame
    df = pd.DataFrame(data_list, columns=column_list)

    # 假设第一个字段是维度，第二个字段是指标
    dimension = column_list[0]
    metric = column_list[1]

    # 每一个值应用convert_to_number
    df[metric] = df[metric].apply(convert_to_number)

    # 设置合并小值的阈值，将小于总和 1% 的维度合并
    threshold = 0.01 * df[metric].sum()

    # 合并小值
    df.loc[df[metric] < threshold, dimension] = 'other'

    # 合并后对分组进行求和，主要是对“其他”分组中的值求一个总和
    df = df.groupby(dimension).sum().reset_index()

    # 计算总和
    total = df[metric].sum()

    # 绘制环形图
    plt.figure(figsize=(8, 8))
    wedges, texts, autotexts = plt.pie(
        df[metric], labels=df[dimension],
        autopct='%1.1f%%'
    )

    # 添加中心的白色圆以形成环形
    centre_circle = plt.Circle((0, 0), 0.70, fc='white')
    fig = plt.gcf()
    fig.gca().add_artist(centre_circle)

    # 确保环形是圆形的
    plt.axis('equal')
    plt.title(f'{dimension} 分布')
    # 保存为 PNG 图片
    plt.savefig(filename, dpi=300, bbox_inches='tight')


def create_bar(data_list, column_list, filename):
    # 将数据转换为 DataFrame
    df = pd.DataFrame(data_list, columns=column_list)

    # 假设第一个字段是维度，第二个字段是指标
    dimension = column_list[0]
    metric = column_list[1]

    # 每一个值应用convert_to_number
    df[metric] = df[metric].apply(convert_to_number)

    # 有可能有重复的维度，聚合一下
    df = df.groupby(dimension, as_index=False).sum()

    # 绘制柱状图
    plt.figure(figsize=(len(df) * 0.5 + 5, 6))
    plt.bar(df[dimension], df[metric], color='skyblue')
    plt.xlabel(dimension)
    plt.ylabel(metric)
    # 禁用科学计数法
    disable_scientific_notation(plt.gca().yaxis)
    plt.title(f'{dimension} vs {metric}')
    plt.xticks(rotation=45)
    # 显示虚线网格
    plt.grid(linestyle='--', alpha=0.7)
    plt.tight_layout()
    # 保存为 PNG 图片
    plt.savefig(filename, dpi=300, bbox_inches='tight')


def create_compose(data_list, column_list, filename):
    # 将数据转换为 DataFrame
    df = pd.DataFrame(data_list, columns=column_list)

    # 第一个字段是维度，第二个字段是折线图指标，第三个是柱状图指标
    dimension = column_list[0]
    line_metric = column_list[1]
    bar_metric = column_list[2]

    # 每一个值应用convert_to_number
    df[line_metric] = df[line_metric].apply(convert_to_number)
    df[bar_metric] = df[bar_metric].apply(convert_to_number)

    # 有可能有重复的维度，聚合一下
    df = df.groupby(dimension, as_index=False).sum()

    # 创建图形和轴
    fig, ax1 = plt.subplots(figsize=(min(len(df) * 0.5 + 5, 15), 6))

    # 绘制柱状图
    ax1.bar(df[dimension], df[bar_metric], color='skyblue', label=bar_metric)
    ax1.set_xlabel(dimension)
    ax1.set_ylabel(bar_metric, color='skyblue')
    ax1.tick_params(axis='y', labelcolor='skyblue')
    # 禁用 ax1 的科学计数法
    disable_scientific_notation(ax1.yaxis)

    # 创建第二个轴用于折线图
    ax2 = ax1.twinx()
    ax2.plot(df[dimension], df[line_metric], color='orange', marker='o', label=f'{line_metric} 趋势')
    ax2.set_ylabel('趋势', color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')
    # 禁用 ax2 的科学计数法
    disable_scientific_notation(ax2.yaxis)

    # 设置标题和布局
    plt.title(f'{dimension} vs {line_metric}、{bar_metric}（组合图）')
    # 设置 x 轴的刻度和标签
    ax1.set_xticks(range(len(df)))
    # 旋转 x 轴标签
    ax1.set_xticklabels(df[dimension], rotation=45, ha='right')

    # 自动换行
    df[dimension] = df[dimension].apply(lambda x: '\n'.join(x[i:i + 5] for i in range(0, len(x), 5)))
    fig.tight_layout()

    # 显示图例
    ax1.legend(loc='upper left')
    ax2.legend(loc='upper right')

    # 显示虚线网格
    ax1.grid(True, linestyle='--', linewidth=0.5, color='gray', which='both')
    ax2.grid(True, linestyle='--', linewidth=0.5, color='gray', which='both')

    # 保存为 PNG 图片
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    # 显示图形
    plt.show()
    print(filename)


if __name__ == '__main__':
    if len(sys.argv) != 2:
        json_return(-1, '错误的传参', {})
        sys.exit(1)

    filename_prefix = sys.argv[1]

    try:

        # 加载 json 数据
        json_data = read_json_file(filename_prefix)

        # 获取画图类型，默认是表格
        draw_type = json_data.get('draw', {}).get('type', '表格')
        title = json_data.get('draw', {}).get('title', '')
        watermark = json_data.get('draw', {}).get('watermark', '')
        # 画图
        if draw_type == '表格':
            create_table_and_save_as_png(json_data['data_list'], json_data['column_list'], filename_prefix + ".png", title,watermark)
        elif draw_type == '折线图':
            create_line(json_data['data_list'], json_data['column_list'], filename_prefix + ".png")
        elif draw_type == '饼图':
            create_pie(json_data['data_list'], json_data['column_list'], filename_prefix + ".png")
        elif draw_type == '柱状图':
            create_bar(json_data['data_list'], json_data['column_list'], filename_prefix + ".png")
        elif draw_type == '组合图':
            create_compose(json_data['data_list'], json_data['column_list'], filename_prefix + ".png")

        else:
            json_return(-1, '不支持的图表类型', {})
            sys.exit(1)

        json_return(0, 'success', {})
    except Exception as e:
        json_return(-1, str(e), {})
