import os
from pandasai import Agent, SmartDataframe
import pandas as pd
from pandasai.llm import AzureOpenAI
import sys
import json
from custom_llm import CustomOpenAI
from custom_llm import CustomAzureOpenAI
import matplotlib.pyplot as plt
from pathlib import Path
import re

user_defined_path = os.path.join(os.path.dirname(__file__), "../../srv/data_bot/pandasai_img")

# 解决中文乱码
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams["font.family"] = "sans-serif"
# 解决负号无法显示的问题
plt.rcParams['axes.unicode_minus'] = False


def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))


def read_json_file(filename):
    if not os.path.isfile(filename):
        raise FileNotFoundError(f"File '{filename}' does not exist.")

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
            return data
    except json.JSONDecodeError:
        raise ValueError(f"File '{filename}' is not a valid JSON file.")


def is_numeric_result(value):
    """判断是否为数值类型结果"""
    if isinstance(value, (int, float, complex)):
        return True
    if isinstance(value, str):
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            pass
    return False


def process_response(response, query=""):
    """
    处理 PandasAI 的返回结果，返回 PHP 兼容的格式

    Args:
        response: PandasAI 返回的结果
        query: 用户查询内容（用于辅助判断）

    Returns:
        dict: PHP 兼容的统一格式 {'type': str, 'value': any}
    """

    # 方法1: 检查返回结果是否为字典格式（PandasAI v2.x 的标准返回格式）
    if isinstance(response, dict):
        response_type = response.get('type', '').lower()
        response_value = response.get('value')

        if response_type == 'plot' or response_type == 'chart':
            return {
                'type': 'plot',
                'value': response_value
            }
        elif response_type == 'number' or response_type == 'numeric':
            return {
                'type': 'string',
                'value': str(response_value)
            }
        elif response_type == 'dataframe' or response_type == 'table':
            # 确保返回 JSON 字符串格式
            if isinstance(response_value, str):
                return {
                    'type': 'dataframe',
                    'value': response_value
                }
            else:
                return {
                    'type': 'dataframe',
                    'value': json.dumps(response_value, ensure_ascii=False) if response_value is not None else "[]"
                }
        elif response_type == 'string' or response_type == 'text':
            return {
                'type': 'string',
                'value': str(response_value) if response_value is not None else ""
            }

    # 方法2: 基于返回值类型判断

    # 检查是否直接返回 DataFrame
    if isinstance(response, pd.DataFrame):
        json_data = response.to_json(orient='records', force_ascii=False)
        return {
            'type': 'dataframe',
            'value': json_data
        }

    # 检查是否为数值类型
    if is_numeric_result(response):
        return {
            'type': 'string',
            'value': str(response)
        }

    # 检查是否为字符串
    if isinstance(response, str):
        # 检查是否为图片文件路径
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg']
        if any(response.lower().endswith(ext) for ext in image_extensions):
            # 验证文件是否存在
            if os.path.exists(response):
                return {
                    'type': 'plot',
                    'value': response
                }

        # 检查是否包含图表相关的提示信息并提取文件路径
        plot_indicators = ['保存', 'saved', '生成', 'generated', 'chart', 'plot', '图表', '图片', '已保存为']
        if any(indicator in response.lower() for indicator in plot_indicators):
            # 尝试从字符串中提取文件路径
            # 匹配常见的文件路径模式（更精确的正则）
            path_patterns = [
                r'([^\s]+\.(?:png|jpg|jpeg|gif|bmp|svg))',  # 匹配完整文件路径
                r'保存为[：:]\s*([^\s\n]+\.(?:png|jpg|jpeg|gif|bmp|svg))',  # 匹配"保存为："后的路径
                r'saved\s+(?:as|to)[:\s]*([^\s\n]+\.(?:png|jpg|jpeg|gif|bmp|svg))',  # 匹配"saved as:"后的路径
            ]

            for pattern in path_patterns:
                matches = re.findall(pattern, response, re.IGNORECASE)
                if matches:
                    file_path = matches[-1] if isinstance(matches[-1], str) else matches[-1]  # 取最后一个匹配项
                    # 验证文件是否存在
                    if os.path.exists(file_path):
                        return {
                            'type': 'plot',
                            'value': file_path
                        }

        # 普通字符串结果
        return {
            'type': 'string',
            'value': response
        }

    # 处理 None 或空值
    if response is None:
        return {
            'type': 'string',
            'value': "查询未返回结果"
        }

    # 处理列表类型
    if isinstance(response, (list, tuple)):
        # 转换为 JSON 字符串
        json_data = json.dumps(list(response), ensure_ascii=False)
        return {
            'type': 'dataframe',
            'value': json_data
        }

    # 其他未知类型，转为字符串
    return {
        'type': 'string',
        'value': f"未知类型结果: {str(response)}"
    }


def check_file_type(filename):
    if filename.endswith('.json'):
        return 'json'
    elif filename.endswith('.csv'):
        return 'csv'
    else:
        return 'unknown'


def auto_convert_dtypes(df):
    """自动推断并转换数据类型，不依赖具体列名"""
    for col in df.columns:
        # 跳过空列
        if df[col].isna().all():
            continue

        # 只处理 object 类型的列
        if df[col].dtype == 'object':
            # 尝试转换为日期类型（检测常见的日期格式）
            sample_values = df[col].dropna().head(5).astype(str)
            date_indicators = ['-', '/', '年', '月', '日', ':', 'T']

            # 检查是否包含日期特征
            has_date_pattern = any(
                any(indicator in str(val) for indicator in date_indicators)
                for val in sample_values
            )

            if has_date_pattern:
                try:
                    converted = pd.to_datetime(df[col], errors='coerce')
                    # 如果转换成功率超过80%，认为是日期列
                    if converted.notna().sum() / len(df) > 0.8:
                        df[col] = converted
                        continue
                except:
                    pass

            # 尝试转换为数值类型
            try:
                converted = pd.to_numeric(df[col], errors='coerce')
                # 如果转换成功率超过80%，认为是数值列
                if converted.notna().sum() / len(df) > 0.8:
                    df[col] = converted
            except:
                pass

    return df


if __name__ == '__main__':
    if len(sys.argv) != 3:
        json_return(-1, '错误的传参', {})
        sys.exit(1)

    data_filename = sys.argv[1]
    query_filename = sys.argv[2]

    try:
        # 加载 json 数据
        file_type = check_file_type(data_filename)
        if file_type == 'csv':
            # 先读取 CSV 文件为 DataFrame
            df = pd.read_csv(data_filename)
        elif file_type == 'json':
            json_data = read_json_file(data_filename)
            # # 将 JSON 数据转换为 DataFrame
            df = pd.DataFrame(data=json_data["data_list"], columns=json_data["column_list"])
        else:
            json_return(-1, '错误的文件类型', {})
            sys.exit(1)

        # 用户查询的数据
        query_data = read_json_file(query_filename)
        query = query_data["content"]
        history_message_list = query_data["history_message_list"]

        # 创建 LLM
        # 模型
        llm = CustomOpenAI(
            api_token="87172cf8-2858-4692-a039-14405818caa1",
            api_base="https://ark.cn-beijing.volces.com/api/v3",
            model="ep-20250806143550-wlqfh",
            history_message_list=history_message_list,
        )
        # 微软的 OpenAI
        # llm = CustomAzureOpenAI(
        #     api_token="69e6ad9453fb4bd2b0f387efd37d940e",
        #     azure_endpoint="http://tw-openai-wus3-azure.zxzt123.com",
        #     api_version="2024-09-01-preview",
        #     deployment_name="gpt-4o"
        # )

        # 如果 DataFrame 的行数大于 1，则去掉第一行合计行
        if len(df) > 1:
            df = df.iloc[1:]

        # 应用自动类型转换
        df = auto_convert_dtypes(df)

        # 确保数据按照原始顺序，重置索引
        df = df.reset_index(drop=True)

        # 确保图片保存目录存在
        os.makedirs(user_defined_path, exist_ok=True)

        sdf = SmartDataframe(df, config={
            "llm": llm,
            "plotting_backend": "matplotlib",  # 使用原生版本
            "save_charts": True,
            "save_charts_path": user_defined_path,
            "open_charts": False,
            "enable_cache": False,
            "security": "none"
        })

        response = sdf.chat(query)

#         print("=== 返回结果 ===")
#         print(response)
#         print(type(response))
        processed_response = process_response(response)

        json_return(0, 'success', processed_response)

    except Exception as e:
        # 返回错误格式，与 PHP 的 error 类型匹配
        error_response = {
            'type': 'error',
            'value': str(e)
        }
        json_return(-1, str(e), error_response)
