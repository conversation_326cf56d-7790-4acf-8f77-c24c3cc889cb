import os
import sys
import zipfile
import pandas as pd
from io import BytesIO
from docx import Document
from docx.shared import Pt
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import re

def remove_watermarks_from_word(input_file, output_file):
    """从Word文档中移除水印并转换嵌入的Excel对象"""
    try:
        doc = Document(input_file)

        # 移除页眉页脚中的水印
        removed_count = 0
        for i, section in enumerate(doc.sections):
            print(f"检查第 {i+1} 节的页眉页脚...")
            removed_count += _clean_header_footer(section.header, f"第{i+1}节页眉")
            removed_count += _clean_header_footer(section.footer, f"第{i+1}节页脚")

        print(f"水印移除成功！共移除 {removed_count} 个水印元素")

        # 转换嵌入的Excel对象
        print("-" * 40)
        print("开始转换嵌入的Excel对象...")
        excel_data_list = extract_embedded_excel_objects(input_file)

        if excel_data_list:
            print(f"发现 {len(excel_data_list)} 个Excel对象，开始转换...")
            replace_embedded_objects(doc, excel_data_list)
            clean_reminder_texts(doc)
            print("Excel对象转换完成")
        else:
            print("未发现嵌入的Excel对象")

        # 保存文档
        doc.save(output_file)
        print(f"新文档已保存为: {output_file}")

    except Exception as e:
        print(f"处理文档时出错: {str(e)}")
        sys.exit(1)

def _clean_header_footer(header_or_footer, location_name):
    """清理页眉或页脚中的水印"""
    removed_count = 0
    for paragraph in header_or_footer.paragraphs:
        if _is_watermark_paragraph(paragraph):
            text = paragraph.text.strip()
            if text:
                print(f"从{location_name}移除水印文本: '{text}'")
            else:
                print(f"从{location_name}移除空白水印段落")
            paragraph.clear()
            removed_count += 1

    if removed_count == 0:
        print(f"{location_name}: 未发现水印")

    return removed_count

def _is_watermark_paragraph(paragraph):
    """判断段落是否可能是水印"""
    text = paragraph.text.strip().lower()
    watermark_keywords = ['watermark', '水印', 'confidential', 'draft', 'sample', 'copy']

    # 检查是否包含水印关键词
    for keyword in watermark_keywords:
        if keyword in text:
            return True

    # 检查是否为空段落
    return len(text) == 0

def extract_embedded_excel_objects(doc_path):
    """提取Word文档中嵌入的Excel对象"""
    excel_data_list = []

    try:
        with zipfile.ZipFile(doc_path, 'r') as zip_ref:
            embed_files = [f for f in zip_ref.namelist() if f.startswith('word/embeddings/')]

            for embed_file in embed_files:
                try:
                    excel_content = zip_ref.read(embed_file)
                    excel_buffer = BytesIO(excel_content)

                    excel_file = pd.ExcelFile(excel_buffer)
                    for sheet_name in excel_file.sheet_names:
                        df = pd.read_excel(excel_buffer, sheet_name=sheet_name, header=None)
                        if not df.empty:
                            excel_data_list.append({
                                'sheet_name': sheet_name,
                                'data': df,
                                'source_file': embed_file
                            })
                except Exception:
                    continue

    except Exception:
        pass

    return excel_data_list

def set_table_borders(table):
    """为表格设置边框"""
    tbl = table._tbl
    for cell in table._cells:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()

        # 设置边框
        tcBorders = OxmlElement('w:tcBorders')

        # 上边框
        top_border = OxmlElement('w:top')
        top_border.set(qn('w:val'), 'single')
        top_border.set(qn('w:sz'), '4')
        top_border.set(qn('w:space'), '0')
        top_border.set(qn('w:color'), '000000')
        tcBorders.append(top_border)

        # 下边框
        bottom_border = OxmlElement('w:bottom')
        bottom_border.set(qn('w:val'), 'single')
        bottom_border.set(qn('w:sz'), '4')
        bottom_border.set(qn('w:space'), '0')
        bottom_border.set(qn('w:color'), '000000')
        tcBorders.append(bottom_border)

        # 左边框
        left_border = OxmlElement('w:left')
        left_border.set(qn('w:val'), 'single')
        left_border.set(qn('w:sz'), '4')
        left_border.set(qn('w:space'), '0')
        left_border.set(qn('w:color'), '000000')
        tcBorders.append(left_border)

        # 右边框
        right_border = OxmlElement('w:right')
        right_border.set(qn('w:val'), 'single')
        right_border.set(qn('w:sz'), '4')
        right_border.set(qn('w:space'), '0')
        right_border.set(qn('w:color'), '000000')
        tcBorders.append(right_border)

        tcPr.append(tcBorders)

def replace_embedded_objects(source_doc, excel_data_list):
    """替换嵌入对象为Word表格"""
    excel_index = 0

    # 从后往前处理，避免索引变化问题
    paragraphs_with_objects = []
    for i, para in enumerate(source_doc.paragraphs):
        has_embedded_object = False
        for run in para.runs:
            if run._element.xpath('.//w:object'):
                has_embedded_object = True
                break

        if has_embedded_object:
            paragraphs_with_objects.append((i, para))

    # 处理每个包含嵌入对象的段落
    for para_index, para in paragraphs_with_objects:
        if excel_index >= len(excel_data_list):
            break

        try:
            excel_data = excel_data_list[excel_index]
            df_clean = excel_data['data'].dropna(how='all').dropna(axis=1, how='all')

            if df_clean.empty or df_clean.shape[0] == 0 or df_clean.shape[1] == 0:
                excel_index += 1
                continue

            # 清空段落，不添加标题
            para.clear()

            # 在当前段落位置直接创建表格
            rows_count = df_clean.shape[0]
            cols_count = df_clean.shape[1]

            # 获取段落的父元素
            parent = para._element.getparent()
            para_pos = list(parent).index(para._element)

            # 创建临时文档来生成表格
            temp_doc = Document()
            temp_table = temp_doc.add_table(rows=rows_count, cols=cols_count)

            # 填充数据到临时表格
            for row_idx in range(rows_count):
                for col_idx in range(cols_count):
                    try:
                        cell_value = df_clean.iloc[row_idx, col_idx]
                        cell = temp_table.cell(row_idx, col_idx)

                        if pd.notna(cell_value):
                            cell_text = str(cell_value).strip()
                            cell.text = cell_text
                        else:
                            cell.text = ""

                        # 设置单元格格式
                        for paragraph in cell.paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            for run in paragraph.runs:
                                run.font.size = Pt(9)
                                run.font.name = '宋体'
                    except Exception:
                        continue

            # 设置表格边框
            set_table_borders(temp_table)

            # 设置表格样式
            try:
                temp_table.alignment = WD_TABLE_ALIGNMENT.CENTER
            except:
                pass

            # 将表格插入到正确位置，替换原段落
            table_element = temp_table._element
            table_element.getparent().remove(table_element)
            parent.insert(para_pos, table_element)

            # 删除原来的段落
            para._element.getparent().remove(para._element)

            excel_index += 1

        except Exception:
            excel_index += 1

def clean_reminder_texts(source_doc):
    """清理提醒文本"""
    paragraphs_to_remove = []

    for para in source_doc.paragraphs:
        para_text = para.text.lower()
        if any(keyword in para_text for keyword in [
            '点击图片可查看完整电子表格', '点击图片可查看完整表格'
        ]):
            paragraphs_to_remove.append(para)

    for para in paragraphs_to_remove:
        try:
            para_element = para._element
            para_element.getparent().remove(para_element)
        except Exception:
            pass

def remove_watermarks_from_excel(input_file, output_file):
    """从Excel文件中移除水印"""
    try:
        print(f"开始处理Excel文件: {input_file}")

        # 直接使用zipfile方式处理，这是最有效的方法
        removed_count = _remove_excel_watermarks_via_zip(input_file, output_file)

        print(f"Excel水印移除完成！共移除 {removed_count} 个水印元素")
        print(f"处理后的文件已保存为: {output_file}")

    except Exception as e:
        print(f"处理Excel文件时出错: {str(e)}")
        sys.exit(1)

def _remove_excel_watermarks_via_zip(input_file, output_file):
    """通过解压zip方式移除Excel深层水印"""
    removed_count = 0

    try:
        with zipfile.ZipFile(input_file, 'r') as zip_read:
            file_list = zip_read.namelist()
            print(f"  Excel文件包含 {len(file_list)} 个内部文件")

            # 分析文件结构
            worksheets = [f for f in file_list if f.startswith('xl/worksheets/')]
            drawings = [f for f in file_list if f.startswith('xl/drawings/')]
            media = [f for f in file_list if f.startswith('xl/media/')]

            print(f"    工作表文件: {len(worksheets)} 个")
            print(f"    绘图文件: {len(drawings)} 个")
            print(f"    媒体文件: {len(media)} 个")

            with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zip_write:
                for item in zip_read.infolist():
                    data = zip_read.read(item.filename)

                    # 处理工作表XML文件
                    if item.filename.startswith('xl/worksheets/') and item.filename.endswith('.xml'):
                        print(f"  处理工作表XML: {item.filename}")
                        modified_data, count = _clean_worksheet_xml_watermarks(data)
                        removed_count += count
                        zip_write.writestr(item, modified_data)

                    # 处理绘图文件 - 直接移除或最小化
                    elif item.filename.startswith('xl/drawings/'):
                        print(f"  处理绘图文件: {item.filename}")
                        modified_data, count = _clean_drawing_xml_watermarks(data)
                        removed_count += count
                        zip_write.writestr(item, modified_data)

                    # 处理媒体文件 - 跳过可疑的水印图片
                    elif item.filename.startswith('xl/media/'):
                        print(f"  检查媒体文件: {item.filename}")
                        file_size = len(data)
                        print(f"    文件大小: {file_size} 字节")

                        if _is_likely_watermark_image(item.filename):
                            print(f"    跳过可能的水印图片: {item.filename}")
                            removed_count += 1
                            continue
                        else:
                            print(f"    保留媒体文件: {item.filename}")
                            zip_write.writestr(item, data)

                    # 其他文件直接复制
                    else:
                        zip_write.writestr(item, data)

        print(f"  ZIP深层清理完成，移除了 {removed_count} 个元素")
        return removed_count

    except Exception as e:
        print(f"通过zip方式处理时出错: {str(e)}")
        return 0

def _clean_worksheet_xml_watermarks(xml_data):
    """清理工作表XML中的水印元素"""
    removed_count = 0
    try:
        xml_str = xml_data.decode('utf-8') if isinstance(xml_data, bytes) else xml_data
        original_length = len(xml_str)

        # 移除背景相关元素
        patterns_to_remove = [
            (r'<backgroundPicture[^>]*>.*?</backgroundPicture>', '背景图片元素'),
            (r'<background[^>]*>.*?</background>', '背景元素'),
            (r'<drawing[^>]*>.*?</drawing>', '绘图元素'),
            (r'<picture[^>]*>.*?</picture>', '图片元素')
        ]

        for pattern, description in patterns_to_remove:
            matches = re.findall(pattern, xml_str, re.DOTALL | re.IGNORECASE)
            if matches:
                print(f"    找到并移除 {len(matches)} 个{description}:")
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    preview = match[:100] + '...' if len(match) > 100 else match
                    print(f"      [{i+1}] {preview}")
                if len(matches) > 3:
                    print(f"      ... 还有 {len(matches) - 3} 个")

                xml_str = re.sub(pattern, '', xml_str, flags=re.DOTALL | re.IGNORECASE)
                removed_count += len(matches)

        if len(xml_str) != original_length:
            print(f"    XML清理: 原长度 {original_length}, 新长度 {len(xml_str)}, 减少 {original_length - len(xml_str)} 字符")

        return xml_str.encode('utf-8') if isinstance(xml_data, bytes) else xml_str, removed_count
    except Exception as e:
        print(f"    XML清理出错: {e}")
        return xml_data, 0

def _clean_drawing_xml_watermarks(xml_data):
    """清理绘图XML中的水印元素"""
    removed_count = 0
    try:
        xml_str = xml_data.decode('utf-8') if isinstance(xml_data, bytes) else xml_data
        original_length = len(xml_str)

        print(f"    原始绘图文件大小: {original_length} 字符")

        # 对于绘图文件，采用最小化策略
        if len(xml_str) < 1000:
            print(f"    绘图文件较小，执行完全清理")
            removed_count = 1
        else:
            # 对于较大的绘图文件，移除可疑的水印元素
            watermark_patterns = [
                (r'<xdr:sp[^>]*>.*?</xdr:sp>', '形状元素'),
                (r'<xdr:pic[^>]*>.*?</xdr:pic>', '图片元素'),
                (r'<a:t[^>]*>.*?</a:t>', '文本元素')
            ]

            for pattern, description in watermark_patterns:
                matches = re.findall(pattern, xml_str, re.DOTALL | re.IGNORECASE)
                if matches:
                    print(f"    找到 {len(matches)} 个{description}")
                    # 检查是否包含可疑的水印内容
                    suspicious_matches = []
                    for match in matches:
                        if any(keyword in match.lower() for keyword in ['watermark', '水印', 'confidential', 'draft']):
                            suspicious_matches.append(match)

                    if suspicious_matches:
                        print(f"    移除 {len(suspicious_matches)} 个可疑的{description}")
                        for match in suspicious_matches:
                            xml_str = xml_str.replace(match, '')
                            removed_count += 1

        # 返回最小的有效绘图XML
        minimal_xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><xdr:wsDr xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"></xdr:wsDr>'
        if removed_count > 0:
            print(f"    绘图文件清理: 原长度 {original_length}, 使用最小XML")
            return minimal_xml.encode('utf-8') if isinstance(xml_data, bytes) else minimal_xml, removed_count

        return xml_str.encode('utf-8') if isinstance(xml_data, bytes) else xml_str, removed_count
    except Exception as e:
        print(f"    绘图文件清理出错: {e}")
        return xml_data, 0

def _is_likely_watermark_image(filename):
    """判断是否可能是水印图片"""
    watermark_keywords = ['watermark', 'logo', 'background', 'draft', 'sample']
    filename_lower = filename.lower()
    return any(keyword in filename_lower for keyword in watermark_keywords)

def _is_watermark_text(text):
    """判断文本是否为水印"""
    if not text:
        return False

    text_lower = text.lower().strip()
    watermark_keywords = ['watermark', '水印', 'confidential', 'draft', 'sample', 'copy']

    return any(keyword in text_lower for keyword in watermark_keywords)

def main():
    """命令行主函数"""
    if len(sys.argv) < 2:
        print("用法: python word_parsing.py <输入文件> [输出文件]")
        print("支持的文件格式: .docx, .xlsx")
        sys.exit(1)

    input_file = sys.argv[1]

    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"找不到输入文件: {input_file}")
        sys.exit(1)

    # 获取文件扩展名
    file_ext = os.path.splitext(input_file)[1].lower()

    # 确定输出文件路径
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    else:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_processed{file_ext}"

    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("-" * 40)

    # 根据文件类型选择处理方法
    if file_ext == '.docx':
        remove_watermarks_from_word(input_file, output_file)
    elif file_ext == '.xlsx':
        remove_watermarks_from_excel(input_file, output_file)
    else:
        print(f"不支持的文件格式: {file_ext}")
        print("支持的格式: .docx, .xlsx")
        sys.exit(1)

if __name__ == "__main__":
    main()