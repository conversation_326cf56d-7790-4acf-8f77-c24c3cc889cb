# pip3 install transformers
# python3 deepseek_tokenizer.py

import os
os.environ["TRANSFORMERS_NO_TORCH"] = "1"  # 禁用 PyTorch
os.environ["TRANSFORMERS_NO_TENSORFLOW"] = "1"  # 禁用 TensorFlow
os.environ["TRANSFORMERS_NO_FLAX"] = "1"  # 禁用 Flax
import sys
import json
import transformers

def main():
    # 默认返回码
    code = 0
    count = 0

    # 检查是否传入了文件路径参数
    if len(sys.argv) < 2:
        code = 1
    else:
        file_path = sys.argv[1]
        chat_tokenizer_dir = os.path.dirname(os.path.abspath(__file__))
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if content:
        # 初始化分词器（预设文件夹）
            tokenizer = transformers.AutoTokenizer.from_pretrained(
                    chat_tokenizer_dir, trust_remote_code=True
            )
            # 计算 tokens
            result = tokenizer.encode(content)
            count = len(result)
        else:
            # 文件为空
            code = 2
            # end if content

    # 构造返回数据并输出
    return_data = {
        'code': code,
        'count': count
    }
    print(json.dumps(return_data, ensure_ascii=False))

if __name__ == '__main__':
    main()
