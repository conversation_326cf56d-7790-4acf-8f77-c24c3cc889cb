from markitdown import MarkItDown
from PIL import Image
from openai import OpenAI
import json
import sys
import os
# client = AzureOpenAI(
#     api_key="69e6ad9453fb4bd2b0f387efd37d940e",
#     api_version="2023-12-01-preview",
#     azure_endpoint="https://tw-openai-wus3-azure.zxzt123.com"
# )

# 压缩的目标图片大小，300KB
target_size = 500 * 1024

def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))

def compress_image(input_path, output_path):
    img = Image.open(input_path)
    current_size = os.path.getsize(input_path)

    # 粗略的估计压缩质量，也可以从常量开始，逐步减小压缩质量，直到文件大小小于目标大小
    image_quality = int(float(target_size / current_size) * 100)
    img.save(output_path, optimize=True, quality=int(float(target_size / current_size) * 100))

    # 如果压缩后文件大小仍然大于目标大小，则继续压缩
    # 压缩质量递减，直到文件大小小于目标大小
    while os.path.getsize(output_path) > target_size:
        img = Image.open(output_path)
        image_quality -= 10
        if image_quality <= 0:
            break
        img.save(output_path, optimize=True, quality=image_quality)
    return image_quality

def main():
    if len(sys.argv) != 2:
        json_return(-1, '错误的传参', {})
        sys.exit(1)

    filename = sys.argv[1]  # 第一个参数是文件名
    # print(f"接收到的文件名: {filename}")

    client = OpenAI(
        api_key="a784bd8d-87f0-4f9b-bcb6-e080deb6a868",
        base_url="https://ark.cn-beijing.volces.com/api/v3"
    )

    md = MarkItDown(llm_client=client, llm_model="doubao-1-5-vision-pro-32k-250115")

    # md = MarkItDown(enable_plugins=False)
    result = md.convert(filename)

    # with open('document.md', 'w', encoding="utf-8", errors='ignore') as f:
    #     f.write(result.text_content)
    # 成功情况下返回转换后的内容
    json_return(0, 'success', str(result.text_content))
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        json_return(-1, str(e), {})