from typing import Dict, List, Any
from pandasai.llm import OpenAI
from pandasai.llm import AzureOpenAI
from pandasai.helpers.openai_info import openai_callback_var
import json

import requests

# 豆包的LLM
class CustomOpenAI(OpenAI):
    """自定义 OpenAI 类，适配兼容 OpenAI 接口的模型"""
    def __init__(
        self,
        api_token: str,
        api_base: str,
        model: str = None,
        history_message_list: List[Dict[str, Any]] = None,
    ):

        # 在调用父类构造函数之前设置属性
        self.__dict__['_is_chat_model'] = True
        # 确保 api_base 被正确设置
        self.api_base = api_base
        # 设置历史记录
        self.history_message_list = history_message_list if history_message_list is not None else []
        # 初始化基类
        super().__init__(
            api_token=api_token,
        )
        # 手动设置 base_url
        if hasattr(self.client, '_client'):
            self.client._client.base_url = api_base

        if model:
            self.model = model

    @property
    def _client_params(self) -> Dict[str, any]:
        params = super()._client_params
        params['base_url'] = self.api_base  # 确保使用自定义的 base_url
        return params

    def chat_completion(self, value: str, memory=None) -> str:
        messages = []

        # 添加历史记录
        if self.history_message_list:
            messages.extend(self.history_message_list)

        # 处理 memory 中的消息
        if memory:
            memory_messages = memory.to_openai_messages()
            for msg in memory_messages:
                messages.append({
                    "role": msg["role"],
                    "content": [{"type": "text", "text": msg["content"]}]
                })

        # 添加当前消息
        messages.append({
            "role": "user",
            "content": [{"type": "text", "text": value}]
        })
        json_data={
                "model": self.model,
                "messages": messages,
                **self._invocation_params
            }
        # print(f"\n=== API 请求详情 ===")
        # print(f"URL: {self.api_base}/chat/completions")
        # print(f"参数: {json.dumps(json_data, ensure_ascii=False, indent=2)}")


        # 直接使用 requests 发送请求
        response = requests.post(
            f"{self.api_base}/chat/completions",
            headers={
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            },
            json={
                "model": self.model,
                "messages": messages,
                **self._invocation_params
            }
        )

        if response.status_code != 200:
            raise Exception(f"API 请求失败: {response.text}")

        result = response.json()

        if openai_handler := openai_callback_var.get():
            openai_handler(result)

        return result['choices'][0]['message']['content']



class CustomAzureOpenAI(AzureOpenAI):
    def __init__(self, history_message_list: List[Dict[str, Any]] = None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.history_message_list = history_message_list if history_message_list is not None else []


    def chat_completion(self, value: str, memory=None) -> str:
        """
        Query the chat completion API

        Args:
            value (str): Prompt

        Returns:
            str: LLM response.

        """
        messages = []

        # 添加历史记录
        if self.history_message_list:
            messages.extend(self.history_message_list)

        memory_messages = memory.to_openai_messages() if memory else []

        if memory_messages:
            messages.extend(memory_messages)

        # adding current prompt as latest query message
        messages.append(
            {
                "role": "user",
                "content": value,
            },
        )

        params = {
            **self._invocation_params,
            "messages": messages,
        }

        if self.stop is not None:
            params["stop"] = [self.stop]

        response = self.client.create(**params)

        if openai_handler := openai_callback_var.get():
            openai_handler(response)

        return response.choices[0].message.content