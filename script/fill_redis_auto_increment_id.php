<?php

/**
 * 填充redis自增key
 */

use App\Model\RedisModel\AutoIncrementIdModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

$params = getopt('t:');
if (!isset($params['t'])) {
    print_r('-t传入需要同步redis.key(rta_index_id|agent_id|site_id|material_id)' . PHP_EOL);
    die;
}

$type = $params['t'];
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$increment_model = new AutoIncrementIdModel();

if ($type === 'rta_index_id') {
    $list = MysqlConnection::getConnection($connection_data_media)
        ->table('ods_tencent_rta_target_log')
        ->select('rta_id')
        ->selectRaw('max(index_id) as max_index_id')
        ->groupBy('rta_id')
        ->get();

    foreach ($list as $item) {
        if (!$increment_model->hasKey(AutoIncrementIdModel::RTA_INDEX_ID, $item->rta_id)) {
            $increment_model->setLastId(AutoIncrementIdModel::RTA_INDEX_ID, $item->rta_id, $item->max_index_id);
        }
    }
}

if ($type === 'material_id') {
    $list = MysqlConnection::getConnection($connection_name)
        ->table('material')
        ->select('platform')
        ->selectRaw('max(material_id) as max_material_id')
        ->groupBy('platform')
        ->get();

    foreach ($list as $item) {
        if (!$increment_model->hasKey(AutoIncrementIdModel::MATERIAL_ID, $item->platform)) {
            $increment_model->setLastId(AutoIncrementIdModel::MATERIAL_ID, $item->platform, $item->max_material_id);
        }
    }
}
