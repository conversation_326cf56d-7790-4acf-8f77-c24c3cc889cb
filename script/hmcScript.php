<?php
/*
 * hmc专用脚本，清洗数据bala bala
 */

/**
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\MysqlConnection;
use App\Model\SqlModel\Tanwan\V2DWDADHourCostLogModel;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

/**
 * 补回直播广告位的直播信息


echo "----------开始----------\n";

$data = (new V2DWDADHourCostLogModel())->getAwemeInfoWithSiteId();

if ($data->isNotEmpty()) {
    $dim_site_model = new V2DimSiteIdModel();
    $site_model = new SiteModel();

    echo "----------开始----------\n";
    foreach ($data as $item) {

        $aweme_account = $item->aweme_account ?: '';
        $aweme_name = $item->aweme_name ?: '';
        $interface_person = $item->interface_person ?: '';

        $dim_site_model->edit($item->platform, $item->site_id, [
            'aweme_account' => $aweme_account,
            'aweme_name' => $aweme_name,
            'interface_person' => $interface_person
        ]);

        $site_model->edit($item->platform, $item->site_id, ['ext->aweme_account' => (string)$aweme_account, 'ext->interface_person' => (string)$interface_person]);

        echo "----------site_id: $item->site_id 已更新----------\n";
    }
}
echo "----------结束----------\n";

 */


/**
 * 补建转化id

use App\MysqlConnection;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Param\SiteConfigParam;
use App\Service\SiteService;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$site_model = new SiteModel();
$site_service = new SiteService();
$sql = "";
$ids = [
    1396207,1396208,1396209,1396210,1396211,1396212,1396213,1396214,1396215,1396216,1396217,1396218,1396219,1396220,
    1396221,1396222,1396223,1396224,1396225,1396226,1396227,1396228,1398056,1398057,1398058,1398059,1398060,1398061,
    1398062,1398063,1399409,1399410,1399411,1399412,1399413,1399414,1399415,1399416,1399417,1399418,1401656,1401662,
    1401663,1401808,1401809,1401810,1401811,1401812,1401813,1401814,1401815,1401816,1401817,1401818,1401819,1401820,
    1401941,1401942,1401943,1401944,1401945,1401946,1402061,1402062,1402063,1402064,1403776,1403777,1403778,1403779,
    1403780,1403781,1403782,1403783,1403784,1403785
];

$list = MysqlConnection::getConnection('default')
    ->table('ad_task')
    ->selectRaw('id, site_id, convert_id, create_time, platform')
    ->whereIn('id', $ids)
//    ->whereBetween('create_time', ['2022-05-13 11:04:00', '2022-05-13 11:45:00'])
    ->where('convert_id', '')
    ->where('state_code', 9)
    ->get();

echo "----------开始----------\n";
foreach ($list as $item) {
        $site_data  = $site_model->getDataByPlatformSiteId($item->platform, $item->site_id);
        $site_param = new SiteConfigParam((array)$site_data);
        $site_param = $site_service->addConvert($site_param);
        echo "----------site_id: $site_param->site_id convert_id: $site_param->convert_id 已更新----------\n";
//        break;
}

echo "----------结束----------\n";
 */

/**
 * 抖音小手柄创建广告位脚本


use App\Constant\AgentGroup;
use App\Constant\MediaType;
use App\Model\SqlModel\Zeda\SiteModel;
use App\MysqlConnection;
use App\Service\SiteService;
use App\Param\SiteConfigParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$list = MysqlConnection::getConnection('datahub')
    ->select("
SELECT * FROM tanwan_datahub.v2_dim_shoubing_aweme_site_id
    INNER JOIN tanwan_datahub.v2_dim_agent_site_id using(platform,site_id)
WHERE agent_leader_end_time > CURRENT_DATE and agent_group_name = '手柄粉丝群'
	");

$new_agent_ids = [
    '170326' => '178130',
    '189168' => '178102',
    '186591' => '178130',
    '189166' => '178121',
    '189165' => '178101',
];
$site_service = new SiteService();
echo "---------- 开始 ----------\n";
foreach ($list as $item) {
    try {
        $site_data = (new SiteModel())->getDataByPlatformSiteId($item->platform, $item->site_id);
        $ext = json_decode($site_data->ext, true);
        $ext['skip_interface_person'] = true;
        $param = new SiteConfigParam([
            'platform' => $item->platform,
            'agent_id' => $new_agent_ids[$item->agent_id],
            'media_type' => MediaType::TOUTIAO_APP,
            'agent_group' => AgentGroup::DOUYIN_UOP,
            'account_id' => $item->account_id,
            'is_concat' => 0,
            'site_suffix_name' => $site_data->site_name,
            'game_id' => $item->game_id,
            'game_type' => $site_data->game_type,
            'appid' => $site_data->appid,
            'game_pack' => $item->site_id == ******** ? 1 : 0,
            'convert_type' => $site_data->convert_type,
            'convert_source_type' => $site_data->convert_source_type,
            'convert_toolkit' => $site_data->convert_toolkit,
            'akey' => $site_data->akey,
            'ad_turn' => $site_data->ad_turn,
            'ad_pop_zk' => $site_data->ad_pop_zk,
            'ad_price' => $site_data->ad_price,
            'auto_download' => $site_data->auto_download,
            'auto_download_second' => $site_data->auto_download_second,
            'cps_divide_rate' => $site_data->cps_divide_rate,
            'forbid_tuitan' => $site_data->forbid_tuitan,
            'template_type' => $site_data->template_type,
            'template_address' => $site_data->template_address,
            'pay_type' => $site_data->pay_type,
            'upt_state' => $site_data->upt_state,
            'state' => 1,
            'ext' => $ext
        ]);

        $site_result = $site_service->addSite($param, $site_data->creator_id, $site_data->creator);
        echo "----------platform: $item->platform site_id: $item->site_id 创建 {$item->platform}-{$site_result->site_id} 成功 ----------\n";
    } catch (Throwable $e) {
        echo "----------platform: $item->platform site_id: $item->site_id 失败原因：{$e->getMessage()} ----------\n";
    }
}
echo "---------- 完成 ----------\n";
 */

/**
 * 修正头条对账余额


use App\Logic\DSP\FinanceLogic;
use App\Logic\DSP\MaterialToolLogic;
use App\Logic\DSP\VideoClipAttributeMQLogic;
use App\MysqlConnection;
use App\Param\FundBalanceLogParam;
use App\Struct\RedisCache;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

//$token_list = RedisCache::getInstance()->hGetAll('session_token_bind');
//foreach ($token_list as $account => $token) {
//    if ($account == "songhan") {
//        var_dump($token);
//        die;
//        RedisCache::getInstance()->del($token_list[$account]);
//    }
//}
//
//die;
//$now = date('Y-m-d H:i:s');
//echo "任务开始$now", PHP_EOL;
//$json = '{"agency_type": 1, "end_date": "2024-04-30", "platform": ["TW"], "fund_type": "", "account_id": "", "begin_date": "2024-04-01", "media_type": "1"}';
//
//try {
//    $param = new FundBalanceLogParam(json_decode($json, true));
//    $data = (new FinanceLogic())->calculateToutiaoBalance($param);
//} catch (\Throwable $exception) {
//    Helpers::getLogger('finance-check')->error('任务出错',
//        [
//            'error_message' => substr($exception->getMessage(), 0, 255),
//            '任务详情' => $json
//        ]);
//    var_dump($json);
//}
//echo "任务结束" . date('Y-m-d H:i:s'), PHP_EOL;


(new MaterialToolLogic())->validateClipVideo(188, "野兽领主：新世界", 2);
die;
$task_ids = [
    94,
    92,
    91,
    90,
    89,
    88,
    87,
    86,
    85,
    84,
    83,
    82,
    80,
    79,
    78,
    77,
    76,
    75,
    74,
    73,
    72,
    71,
    70,
    68,
    67,
    66,
    65,
    64,
    63,
    62,
    61,
    60,
    59,
];
$mq_logic = new VideoClipAttributeMQLogic();
foreach ($task_ids as $task_id) {
    $mq_logic->produceTask($task_id);
}

 */


/**
 * 批量触发编辑广告位

use App\Container;
use App\Logic\DSP\SiteLogic;
use App\MysqlConnection;
use App\Param\SiteConfigParam;
use App\Struct\Session;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

Container::setSession(new Session("7953b6c3a42dd4ed6680de43615a08e1"));

$sql = "SELECT * from site where platform = 'TW' and site_id in (28181937,28728079,28181946,28181875,28181967,40016571,28383265,28509587,28181955,28728089,28181950,28181882,28181969,28783433,28383276,28509593,28578243,28688365,28755977)";

$site_list = MysqlConnection::getConnection('default')->select($sql);

$site_logic = new SiteLogic();
echo "----------开始----------\n";
foreach ($site_list as $item) {
    echo "----------$item->site_id 开始----------\n";

    try {
        $site_param = new SiteConfigParam((array)$item);
        $site_logic->editSite($site_param);
    } catch (Throwable $e) {
        echo "$item->site_id 更新失败：{$e->getMessage()} \n";
    }
    echo "$item->site_id 更新完成\n";
}
echo "全部完成\n";
die;

**/

/**
 * 更新返款历史游点相关金额


use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\MysqlConnection;
use App\Param\ADLiveCostReportFilterParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$order_list = MysqlConnection::getConnection('datahub')
    ->select("
        select * from tanwan_datamedia.ods_star_demand_qrcode_receive_log where pay_way = 0
    ");

//$order_ids = array_column($list, 'order_id');

$model = new OdsStarDemandQrcodeReceiveLogModel();
echo "---------- 开始 ----------\n";
foreach (array_chunk($order_list, 100) as $chunk) {
    try {
        $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
            "dimension" => [
                "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                "live_demand_name", "live_author_id", "live_author_name"
            ],
            "filter" => [
                "live_order_id" => [
                    "column" => "live_order_id",
                    "value" => array_column($chunk, 'order_id')
                ],
                "anchor_log_id" => [
                    "column" => "anchor_log_id",
                    "value" => array_column($chunk, 'anchor_log_id')
                ]
            ],
            "target" => [
                "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
                "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type", "should_pay_money",
                "author_mcn", "tw_pay_yd", "cloud_account_fee", "yd_out_tax", "yd_company_fee",
                "cloud_account_tax_author_cover_rate", "com_is_extra", "is_extra", "anchor_order_extra",
                "author_type", "anchor_order_tax", "pay_way", "company_tax"
            ]
        ]));

        foreach ($list['list'] as $item) {
            // anchor_log_id 为主键 需要先删除原来数据再 replace
            foreach ($chunk as $trade_info) {
                $trade_info = (array)$trade_info;
                if ($item->live_order_id == $trade_info['order_id'] && $item->anchor_log_id == $trade_info['anchor_log_id']) {

                    $is_extra = $item->is_extra;
                    if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
                        $is_extra = 0;
                    }

                    $trade_info['author_mcn'] = $item->author_mcn;
                    $trade_info['pay_way'] = $item->pay_way;
                    $trade_info['cloud_account_tax_author_cover_rate'] = $item->cloud_account_tax_author_cover_rate;
                    $trade_info['com_is_extra'] = $item->com_is_extra;
                    $trade_info['company_tax'] = $item->company_tax;
                    $trade_info['anchor_order_extra'] = $item->anchor_order_extra ?? 0;
                    $trade_info['is_extra'] = $is_extra;
                    $trade_info['author_type'] = $item->author_type;
                    $model->replace($trade_info);
                    echo "---------- 标识：{$trade_info['trade_no']} 订单: {$item->live_order_id} 主播ID：{$item->anchor_log_id} 更新成功 ----------\n";
                }
            }
//                $model->updateByOrderId($item->live_order_id, [
//                    "anchor_log_id" => $item->anchor_log_id,
//                ]);
        }

        echo "---------- 成功100条 ----------\n";
    } catch (Throwable $e) {
        $log_order_ids = json_encode($chunk);
        echo "---------- $log_order_ids 失败原因：{$e->getMessage()} ----------\n";
    }
}
echo "---------- 完成 ----------\n";
die;
*/



/**
 * 更新返款历史结算类型


use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$model = new OdsStarDemandQrcodeReceiveLogModel();


$sql = '
    SELECT
       *
    FROM
        (
        SELECT
                trade_no,
                sum( amount ) - sum( pay_amount ) AS diff,
        IF(author_mcn = "游点文化" or author_type = 1, 2, IF
                ( sum( amount ) > sum( pay_amount ), 1, 2 )) AS trade_type,
                trade_type AS ori_trade_type
        FROM
                tanwan_datamedia.ods_star_demand_qrcode_receive_log
        GROUP BY
                trade_no
        )
    WHERE
        trade_type != ori_trade_type and ori_trade_type = 1
    order by trade_no
';

$trade_list = MysqlConnection::getConnection('datahub')->select($sql);

foreach ($trade_list as $trade_info) {
    $model->updateByTradeNo($trade_info->trade_no, [
        'trade_type' => $trade_info->trade_type
    ]);
    echo "---------- {$trade_info->trade_no} 成功 ----------\n";
}

echo "---------- 完成 ----------\n";
*/


/**
 * 重新计算历史结算补款单税后应补款


use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\MysqlConnection;
use App\Param\ADLiveCostReportFilterParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$model = new OdsStarDemandQrcodeReceiveLogModel();

$order_list = $model->builder
//    ->where('after_tax_pay_amount', 0)
    ->where('trade_type', $model::TRADE_TYPE_PAY)
    ->where('status', '!=', $model::STATUS_CANCEL)
//    ->where('trade_no', '202504021036151743561375213410')
    ->get();

$trade_list = $order_list->groupBy('trade_no');

echo "---------- 开始 ----------\n";
foreach ($trade_list as $trade_no => $detail_list) {
    try {
        $settlement_amount = 0;
        $special_date = "2025-03-01";
        $cloud_account_tax = 0;
        $pay_way = 0;
        $cloud_account_tax_author_cover_rate = 0;
        $com_is_extra = 0;
        $company_tax = 0;
        foreach ($detail_list as $item) {
            if (strtotime($item->first_live_time) < strtotime($special_date)) {
                $cloud_account_tax = 1.063;
            } else {
                $cloud_account_tax = 1.064;
            }

            // 是否承担星图提现手续费
            $is_extra = $item->is_extra;
            if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
                $is_extra = 0;
            }

            if ($is_extra) {
                $amount = $item->anchor_order_fee - $item->anchor_cost + $item->anchor_order_extra;
            } else {
                $amount = $item->anchor_order_fee - $item->anchor_cost;
            }

            // 贪玩蓝V 主播不能提现，所以没有提现金额，直接就是主播工资
            if ($item->author_type == 2) {
                $amount = - $item->anchor_cost;
            }

            $settlement_amount += $amount;
            $pay_way = $item->pay_way;
            $cloud_account_tax_author_cover_rate = $item->cloud_account_tax_author_cover_rate;
            $com_is_extra = $item->com_is_extra;
            $company_tax = $item->company_tax;
        }

        if ($settlement_amount < 0) {
            $tmp_settlement_amount = abs($settlement_amount);
            // 对公 + 是承担开票税
            if ($pay_way == 1 && $com_is_extra) {
                $tmp_settlement_amount = $tmp_settlement_amount - ($tmp_settlement_amount / 1.06 * 0.06 * 1.12 - ($tmp_settlement_amount / (1 + $company_tax) * ($company_tax * 1.12)));
            } elseif ($pay_way == 2) {
                $tmp_settlement_amount = $tmp_settlement_amount * ($cloud_account_tax - $cloud_account_tax_author_cover_rate);
            }

            $settlement_amount = - $tmp_settlement_amount;
        }

        $model->updateByTradeNo($trade_no, [
            "after_tax_pay_amount" => abs($settlement_amount)
        ]);

        echo "---------- 标识：{$trade_no} 税后金额：{$settlement_amount} 更新成功 ----------\n";
    } catch (Throwable $e) {
        echo "---------- 标识：{$trade_no} 失败原因：{$e->getMessage()} ----------\n";
    }
}
echo "---------- 完成 ----------\n";

die;

*/



/**
 * 更新返款历史游点相关金额


use App\Logic\DSP\ADLiveCostReportLogic;
use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\MysqlConnection;
use App\Param\ADLiveCostReportFilterParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$model = new OdsStarDemandQrcodeReceiveLogModel();

$order_list = $model->builder
//    ->where('trade_type', $model::TRADE_TYPE_RETURN)
//    ->where('status', '!=', $model::STATUS_CANCEL)
//    ->where('insert_time', '<', '2025-04-15 00:00:00')
//    ->where('trade_no', '202504091734241744191264247573')
    ->whereIn('trade_no', [
        '202504251753021745574782780343'
    ])
    ->get();

$trade_list = $order_list->groupBy('trade_no');

$logic = new ADLiveCostReportLogic();

$model = new OdsStarDemandQrcodeReceiveLogModel();
echo "---------- 开始 ----------\n";
foreach ($trade_list as $trade_no => $detail_list) {
    try {
        $anchor_order_unique_key_list = [];
        $order_ids = [];
        $anchor_log_ids = [];
        foreach ($detail_list as $detail_info) {
            $anchor_order_unique_key_list[] = "{$detail_info->order_id}-{$detail_info->anchor_log_id}";
            $order_ids[] = $detail_info->order_id;
            $anchor_log_ids[] = $detail_info->anchor_log_id;
        }

        $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
            "dimension" => [
                "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                "live_demand_name", "live_author_id", "live_author_name", "live_anchor_name",
                "live_media_type", "dsp_order_type"
            ],
            "filter" => [
                "live_order_id" => [
                    "column" => "live_order_id",
                    "value" => $order_ids
                ],
                "anchor_log_id" => [
                    "column" => "anchor_log_id",
                    "value" => $anchor_log_ids
                ]
            ],
            "target" => [
                "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
                "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type", "should_pay_money",
                "author_mcn", "tw_pay_yd", "cloud_account_fee", "yd_out_tax", "yd_company_fee",
                "cloud_account_tax_author_cover_rate", "com_is_extra", "is_extra", "anchor_order_extra",
                "author_type", "anchor_order_tax", "pay_way", "company_tax", "pre_should_pay_money", "universal_order_status",
                "price_type"
            ]
        ]));

        $list['list'] = $list['list']->filter(function ($value) use ($anchor_order_unique_key_list) {
            return in_array("{$value->live_order_id}-{$value->anchor_log_id}", $anchor_order_unique_key_list);
        });
//
        // 先计算出抵扣后的金额 判断返款或补款
        $special_date = "2025-03-01";
        $settlement_amount = 0;
        $cloud_account_tax = $pay_way = $cloud_account_tax_author_cover_rate = $com_is_extra = $company_tax = 0;
        foreach ($list["list"] as $item) {
            if (strtotime($item->first_live_time) < strtotime($special_date)) {
                $cloud_account_tax = 1.063;
            } else {
                $cloud_account_tax = 1.064;
            }

            $settlement_amount += ($item->pre_should_pay_money - $item->should_return_money);

            $pay_way = $item->pay_way;
            $cloud_account_tax_author_cover_rate = $item->cloud_account_tax_author_cover_rate;
            $com_is_extra = $item->com_is_extra;
            $company_tax = $item->company_tax;
        }

        // $settlement_amount 正数或等于0是应补款 负数就是应返款

        // 补款特殊计算
        if ($settlement_amount >= 0) {
            $settlement_amount = $logic->settleCalcAfterTaxAmount(
                $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $settlement_amount
            );
        }

        // 用结算表的数据计算应补款 才取消注释使用
//        $settlement_amount = 0;
//        foreach ($detail_list as $trade_info) {
//            $trade_info = (array)$trade_info;
//            $settlement_amount += ($trade_info['pay_amount'] - $trade_info['amount']);
//        }

        foreach ($list['list'] as $item) {
            foreach ($detail_list as $trade_info) {
                $trade_info = (array)$trade_info;
                if ($item->live_order_id == $trade_info['order_id'] && $item->anchor_log_id == $trade_info['anchor_log_id']) {
                    $should_return_money = $item->should_return_money;
                    $pre_should_pay_money = $item->pre_should_pay_money;

                    $is_extra = $item->is_extra;
                    if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
                        $is_extra = 0;
                    }

                    // 当游点的特殊情况，应返应补都应该为0
                    $is_youdian_special = false;
                    if ($item->author_mcn === '游点文化' || $item->author_type == 1) {
                        $is_youdian_special = true;
                    }

                    $amount = $pre_should_pay_money - $should_return_money;
                    $amount = $trade_info['pay_amount'] - $trade_info['amount'];

                    $after_tax_deduction_amount = $amount;
                    // 抵扣完是补款，需要计算每一笔的税后应补款或税后应返款
                    if ($settlement_amount >= 0) {
                        $after_tax_deduction_amount = $logic->settleCalcAfterTaxAmount(
                            $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $after_tax_deduction_amount
                        );
                    }

                    if ($is_youdian_special) {
                        $after_tax_deduction_amount = 0;
                        $settlement_amount = 0;
                    }

                    $trade_info["company_name"] = $item->anchor_company ?? "";
                    $trade_info['tw_pay_yd'] = $item->tw_pay_yd;
                    $trade_info['cloud_account_fee'] = 0;
                    $trade_info['yd_out_tax'] = $item->yd_out_tax;
                    $trade_info['yd_company_fee'] = $item->yd_company_fee;
                    $trade_info['amount'] = $is_youdian_special ? 0 : $should_return_money;
                    $trade_info['pay_amount'] = $is_youdian_special ? 0 : $pre_should_pay_money;
                    $trade_info['author_mcn'] = $item->author_mcn;
                    $trade_info['pay_way'] = $item->pay_way;
                    $trade_info['cloud_account_tax_author_cover_rate'] = $item->cloud_account_tax_author_cover_rate;
                    $trade_info['com_is_extra'] = $item->com_is_extra;
                    $trade_info['company_tax'] = $item->company_tax;
                    $trade_info['anchor_order_extra'] = $item->anchor_order_extra ?? 0;
                    $trade_info['is_extra'] = $is_extra;
                    $trade_info['author_type'] = $item->author_type;
                    $trade_info['after_tax_pay_amount'] = $settlement_amount;
                    $trade_info['after_tax_deduction_amount'] = $after_tax_deduction_amount;
//                    $trade_info['price_type'] = $item->price_type;
//                    $trade_info['live_media_type'] = $item->live_media_type;
//                    $trade_info['dsp_order_type'] = $item->dsp_order_type;
                    $trade_info['update_time'] = '2025-05-21 15:00:00';
                    $model->replace($trade_info);
//                    echo "---------- 标识：{$trade_info['trade_no']} 订单: {$item->live_order_id} 主播ID：{$item->anchor_log_id} 更新成功 ----------\n";
                    echo "---------- 标识：{$trade_info['trade_no']} 订单: {$trade_info['order_id']} 主播ID：{$trade_info['anchor_log_id']} 更新成功 ----------\n";
                }
            }
        }

        echo "---------- {$trade_no} 成功 ----------\n";
    } catch (Throwable $e) {
        echo "---------- {$trade_no} 失败原因：{$e->getMessage()} {$e->getTraceAsString()} ----------\n";
    }
}
echo "---------- 完成 ----------\n";
die;

*/


/**
 * 批量插入虚拟上报


use App\Container;
use App\Logic\DSP\VirtualCallBackStrategyLogic;
use App\Model\SqlModel\Tanwan\V2DimVirtualCallbackStrategyLogModel;
use App\MysqlConnection;
use App\Struct\Input;
use App\Struct\Session;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$site_list = [
    40000498,28188753,40000491,40000493,40000494,40000478,40000485,28188744,40000484,28188768,28188777,28188818,
    40000469,40000467,40000480,28188781,28188756,40000481,28188723,28188796,28188754,40000466,28188730,28188762,28188747,
    40000468,40000474,40000473,40000465,28188742,28188786,28188732,28188712,40000486,40000479,40000471,40000472,40000476,
    40000489,28188778,40000475,28188720,40000487,40000483,40000488,40000477,28188807,40000496,40000482,40000470,40000464,
    28188700,28188766,28188820,28188772
];

$virtual_logic = new VirtualCallBackStrategyLogic();

echo "---------- 开始 ----------\n";
$register_rule = [
    'platform' => 'TW',
    'virtual_rule_type' => V2DimVirtualCallbackStrategyLogModel::RULE_TYPE_TIMES,
    'virtual_action_type' => V2DimVirtualCallbackStrategyLogModel::ACTION_TYPE_REGISTER,
    'virtual_rule_num' => 10,
    'virtual_money' => 0,
];

$pay_rule = [
    'platform' => 'TW',
    'virtual_rule_type' => V2DimVirtualCallbackStrategyLogModel::RULE_TYPE_TIMES,
    'virtual_action_type' => V2DimVirtualCallbackStrategyLogModel::ACTION_TYPE_PAY,
    'virtual_rule_num' => 10,
    'virtual_money' => 6,
];

Container::setSession(new Session("885afc2a61e6855bf863a787ed4ad302"));

foreach ($site_list as $site_id) {
    try {
        $register_rule['site_id'] = $site_id;
        $input = new Input();
        foreach ($register_rule as $key => $value) {
            $input->setDataField($key, $value);
        }

        $virtual_logic->addVirtualCallback($input);

        echo "---------- {$site_id} 上报注册成功 ----------\n";
    } catch (Throwable $e) {
        echo "---------- {$site_id} 上报注册失败原因：{$e->getMessage()} {$e->getTraceAsString()} ----------\n";
    }

    try {
        $pay_rule['site_id'] = $site_id;
        $input = new Input();
        foreach ($pay_rule as $key => $value) {
            $input->setDataField($key, $value);
        }

        $virtual_logic->addVirtualCallback($input);
        echo "---------- {$site_id} 上报付费成功 ----------\n";
    } catch (Throwable $e) {
        echo "---------- {$site_id} 上报付费失败原因：{$e->getMessage()} {$e->getTraceAsString()} ----------\n";
    }
}
echo "---------- 完成 ----------\n";
die;

 */


/**
 * 迁移GRBB用户


use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\UserInternalModel;
use App\Model\SqlModel\Zeda\UserLevelModel;
use App\Model\SqlModel\Zeda\UserMigrateMapModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Param\UserParam;
use App\Service\UserService;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$user_list_json = '
[
    {
        "zeda_user": "3876",
        "zeda_name": "郑泽彬",
        "zeda_account": "zhengzebin",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_郑泽彬",
        "new_account": "gr_zhengzebin",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4387",
        "zeda_name": "何国印",
        "zeda_account": "heguoyin",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_何国印",
        "new_account": "gr_heguoyin",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4388",
        "zeda_name": "林泰来",
        "zeda_account": "lintailai",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_林泰来",
        "new_account": "gr_lintailai",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4389",
        "zeda_name": "叶嘉旭",
        "zeda_account": "yejiaxu",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_叶嘉旭",
        "new_account": "gr_yejiaxu",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4440",
        "zeda_name": "何嘉倩",
        "zeda_account": "hejiaqian",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_何嘉倩",
        "new_account": "gr_hejiaqian",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4468",
        "zeda_name": "胡阳",
        "zeda_account": "huyang",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_胡阳",
        "new_account": "gr_huyang",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4483",
        "zeda_name": "胡展宏",
        "zeda_account": "huzhanhong",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_胡展宏",
        "new_account": "gr_huzhanhong",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4486",
        "zeda_name": "马佳帅",
        "zeda_account": "majiashuai",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_马佳帅",
        "new_account": "gr_majiashuai",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4880",
        "zeda_name": "肖泳恩",
        "zeda_account": "xiaoyongen",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_肖泳恩",
        "new_account": "gr_xiaoyongen",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4881",
        "zeda_name": "李育昇",
        "zeda_account": "liyusheng",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_李育昇",
        "new_account": "gr_liyusheng",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "4882",
        "zeda_name": "林华亨",
        "zeda_account": "linhuaheng",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_林华亨",
        "new_account": "gr_linhuaheng",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "5085",
        "zeda_name": "甘顺安",
        "zeda_account": "ganshunan",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_甘顺安",
        "new_account": "gr_ganshunan",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "5592",
        "zeda_name": "郑锦华",
        "zeda_account": "zhengjinhua",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_郑锦华",
        "new_account": "gr_zhengjinhua",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "5732",
        "zeda_name": "陈平党",
        "zeda_account": "chenpingdang",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_陈平党",
        "new_account": "gr_chenpingdang",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "5815",
        "zeda_name": "金文填",
        "zeda_account": "jinwentian",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_金文填",
        "new_account": "gr_jinwentian",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "6504",
        "zeda_name": "王剑杰",
        "zeda_account": "wangjianjie",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_王剑杰",
        "new_account": "gr_wangjianjie",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "6576",
        "zeda_name": "曾晓纯",
        "zeda_account": "zengxiaochun",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_曾晓纯",
        "new_account": "gr_zengxiaochun",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "6810",
        "zeda_name": "吴婷婷",
        "zeda_account": "wutingting1",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_吴婷婷",
        "new_account": "gr_wutingting1",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "6848",
        "zeda_name": "梁维霖",
        "zeda_account": "liangweilin",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_梁维霖",
        "new_account": "gr_liangweilin",
        "new_is_internal": "1",
        "is_internal": "1"
    },
    {
        "zeda_user": "6849",
        "zeda_name": "秦立鹏",
        "zeda_account": "qinlipeng",
        "old_is_internal": "0",
        "new_user_id": "",
        "new_name": "高热_秦立鹏",
        "new_account": "gr_qinlipeng",
        "new_is_internal": "1",
        "is_internal": "1"
    }
]
';

function getUserParam($account, $name)
{
    return [
        "id" => 0,
        "avatar" => "",
        "account" => $account,
        "name" => $name,
        "mobile" => "",
        "password" => "Aa123456",
        "leader" => 0,
        "staff_number" => "",
        "level" => 1,
        "platform_id" => "114",
        "department_id" => 0,
        "department_group_id" => 0,
        "department_group_position_id" => 0,
        "department_group_position_worker_id" => 0,
        "department_six_id" => 0,
        "department_seven_id" => 0,
        "department_eight_id" => 0,
        "creator" => '中旭未来',
        "module" => 'dsp'
    ];
}

function addNewUser(UserParam $param, $is_internal)
{
    $err_msg = '';
    try {
        addUser($param, $is_internal);
    } catch (Throwable $e) {
        $err_msg = $e->getMessage();
    }
    return $err_msg;
}

function addUser(UserParam $user_param, $is_internal)
{
    $user_model = new UserModel();

    // 判断账号是否存在
    $user_info = $user_model->getDataByAccount($user_param->account);
    if ($user_info) {
        throw new AppException('账号已存在, 请勿重复添加');
    }

    // 判断姓名是否存在
    $user_info = $user_model->getDataByName($user_param->name);
    if ($user_info) {
        throw new AppException('姓名已存在, 请勿重复添加');
    }

    $user_param->password = UserService::genHashPwd($user_param->password);

    // 开始事务
    MysqlConnection::getConnection('default')->beginTransaction();
    try {
        // 加入用户表
        $user_id = add($user_param, $is_internal);
        $user_param->id = $user_id;
        $user_level_model = new UserLevelModel();

        // 写死所有人都是 leader
        $user_param->leader = 1;

        // 添加部门信息
        $user_level_model->addUserLevel($user_param);
        MysqlConnection::getConnection('default')->commit();
    } catch (\Exception $e) {
        MysqlConnection::getConnection('default')->rollBack();
        throw new AppException($e->getMessage(), ResponseCode::FAILURE);
    }
}

function add(UserParam $user_param, $is_internal)
{
    $insert_data = [
        'account'      => $user_param->account,
        'name'         => $user_param->name,
        'password'     => $user_param->password,
        'avatar'       => $user_param->avatar,
        'creator'      => $user_param->creator,
        'staff_number' => $user_param->staff_number,
        'create_time'  => request_time(),
        'update_time'  => request_time(),
        'expire'       => request_time() + ********, // 有效期180天
        'state'        => 1,
        'is_internal' => $is_internal
    ];

    $user_id = (new UserModel())->builder->insertGetId($insert_data);

    if ($is_internal) {
        (new UserInternalModel())->add($user_param, $user_id);
    }

    (new \App\Model\SqlModel\XLXH\UserModel())->add($user_param);

    return $user_id;
}

$user_list = json_decode($user_list_json, true);
$user_migrate_map_model = new UserMigrateMapModel();
$user_model = new UserModel();

echo "---------- 开始 ----------\n";
foreach ($user_list as $item) {
    // 添加账号
    if (!empty($item['new_name'])) {
        $name = $item['new_name'];
    } else {
        $name = $item['zeda_name'];
    }

    if (!empty($item['new_account'])) {
        $account = $item['new_account'];
    } else {
        $account = $item['zeda_account'];
    }

    $param = new UserParam(getUserParam($account, $name));

    // 如果有这个参数 则是特殊指定体制内的
    if (!isset($item['new_is_internal'])) {
        if ($item['is_internal']) {
            $account_prefix = 'bjy_';
            $name_prefix = '八九游_';
        } else {
            $account_prefix = 'gr_';
            $name_prefix = '高热_';
        }

        $param->account = strpos($param->account, $account_prefix) !== false ? $param->account : $account_prefix . $param->account;
        $param->name = strpos($param->name, $name_prefix) !== false ? $param->name : $name_prefix . $param->name;
    }

    // 添加用户账号
    $err_msg = addNewUser($param, $item['is_internal']);
    if (strpos($err_msg, "账号已存在, 请勿重复添加") !== false) {
//        $param->account = strpos($param->account, $account_prefix) !== false ? $param->account : $account_prefix . $param->account;
//        $param->name = strpos($param->name, $name_prefix) !== false ? $param->name : $name_prefix . $param->name;
//        $err_msg = addNewUser($param, $item['is_internal']);
        if ($err_msg !== '') {
            echo "---------- 账号：{$param->account} 名字: {$param->name} 账号重复重新新建失败：{$err_msg} ----------\n";
            continue;
        }
    } elseif (strpos($err_msg, "姓名已存在, 请勿重复添加") !== false) {
//        $param->account = strpos($param->account, $account_prefix) !== false ? $param->account : $account_prefix . $param->account;
//        $param->name = strpos($param->name, $name_prefix) !== false ? $param->name : $name_prefix . $param->name;
//        $err_msg = addNewUser($param, $item['is_internal']);
        if ($err_msg !== '') {
            echo "---------- 账号：{$param->account} 名字: {$param->name} 姓名重复重新新建失败：{$err_msg} ----------\n";
            continue;
        }
    } elseif ($err_msg !== '') {
        echo "---------- 账号：{$param->account} 名字: {$param->name} 新建失败：{$err_msg} ----------\n";
        continue;
    }

    echo "---------- 账号：{$param->account} 名字: {$param->name} 新建成功 ----------\n";

    $user_info = $user_model->getInfoByName($param->name);
    if (empty($user_info)) {
        echo "---------- 账号：{$param->account} 名字: {$param->name} 找不到用户 ----------\n";
        continue;
    }

    if (!empty($item['zeda_account'])) {
        $migrate_data = [
            'old_user_id' => $item['zeda_user'],
            'old_account' => $item['zeda_account'],
            'old_name' => $item['zeda_name'],
            'old_is_internal' => $item['is_internal'],
            'new_user_id' => $user_info['id'],
            'new_account' => $user_info['account'],
            'new_name' => $user_info['name'],
            'new_is_internal' => $user_info['is_internal'],
            'create_time' => time(),
            'update_time' => time(),
            'creator' => '中旭未来',
        ];

        $user_migrate_map_model->replace($migrate_data);

        echo "---------- 账号：{$param->account} 名字: {$param->name} 新建映射成功 ----------\n";
    }
}
echo "---------- 完成 ----------\n";
die;
 */


/**
 * 迁移用户岗位

use App\Exception\AppException;
use App\Logic\UserLogic;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Param\UserParam;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

$level_rank_json = '
[
    {
        "user_id": "3251",
        "module": "dsp",
        "level": "1",
        "platform_id": "116",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "3250",
        "module": "dsp",
        "level": "1",
        "platform_id": "116",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8004",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "528",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7898",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "542",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7899",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "542",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7904",
        "module": "dsp",
        "level": "2",
        "platform_id": "56",
        "department_id": "228",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7900",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "542",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7901",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "542",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7963",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "542",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7962",
        "module": "dsp",
        "level": "1",
        "platform_id": "116",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7891",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7906",
        "module": "dsp",
        "level": "2",
        "platform_id": "56",
        "department_id": "252",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7953",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7954",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7955",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7956",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7968",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7892",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7957",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7172",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "2062",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6888",
        "module": "dsp",
        "level": "3",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1624",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7894",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7895",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7952",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4510",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7896",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7958",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7959",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6841",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7960",
        "module": "dsp",
        "level": "3",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7838",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7961",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7897",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "5700",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4484",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1625",
        "department_group_position_id": "2063",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7861",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7951",
        "module": "dsp",
        "level": "3",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "3581",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7974",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6911",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "3174",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6581",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6311",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6912",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6449",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4363",
        "module": "dsp",
        "level": "4",
        "platform_id": "116",
        "department_id": "531",
        "department_group_id": "1626",
        "department_group_position_id": "2064",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4087",
        "module": "dsp",
        "level": "1",
        "platform_id": "116",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7882",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7950",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7883",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7884",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4068",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7966",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7967",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7885",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8002",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8003",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7996",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7886",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7887",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7888",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7825",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7187",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6857",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "6361",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7905",
        "module": "dsp",
        "level": "2",
        "platform_id": "56",
        "department_id": "538",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7970",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "5937",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7965",
        "module": "dsp",
        "level": "2",
        "platform_id": "56",
        "department_id": "538",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4774",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "5712",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7931",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7932",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7936",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2066",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7934",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7935",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7903",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7938",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2066",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7937",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8025",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2066",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7939",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7985",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2066",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7975",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2066",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7941",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7945",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2067",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7942",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7943",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7946",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2067",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8014",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2067",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7976",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2067",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7907",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1627",
        "department_group_position_id": "2073",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7909",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1627",
        "department_group_position_id": "2073",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7911",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1627",
        "department_group_position_id": "2073",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7914",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1627",
        "department_group_position_id": "2073",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7981",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1627",
        "department_group_position_id": "2073",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7947",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7933",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7940",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8023",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7948",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8018",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8017",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8024",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7944",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8013",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8012",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8010",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8022",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8008",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8020",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7949",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8007",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8009",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "2070",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4382",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1633",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8011",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7972",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8006",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8016",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7908",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8015",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2065",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7902",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7910",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8021",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "2069",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7912",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7913",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7984",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7915",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7916",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7917",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7918",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7919",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "2074",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "7920",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7921",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7922",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "2074",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "7889",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7923",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7924",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "2074",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "7925",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7926",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1637",
        "department_group_position_id": "2075",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7927",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7928",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "2074",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "7929",
        "module": "dsp",
        "level": "4",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "1628",
        "department_group_position_id": "2074",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "8027",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7930",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "534",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4841",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7890",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8019",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "537",
        "department_group_id": "1632",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7997",
        "module": "dsp",
        "level": "2",
        "platform_id": "117",
        "department_id": "535",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7998",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "535",
        "department_group_id": "1631",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7999",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "535",
        "department_group_id": "1631",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8000",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "535",
        "department_group_id": "1631",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8001",
        "module": "dsp",
        "level": "3",
        "platform_id": "117",
        "department_id": "535",
        "department_group_id": "1631",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7971",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7853",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "530",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7969",
        "module": "dsp",
        "level": "2",
        "platform_id": "116",
        "department_id": "533",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    }
]
';

$dms_level_rank_json = '
[
    {
        "user_id": "3251",
        "module": "dms",
        "level": "1",
        "platform_id": "115",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "3250",
        "module": "dms",
        "level": "1",
        "platform_id": "115",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8004",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "532",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7906",
        "module": "dms",
        "level": "2",
        "platform_id": "33",
        "department_id": "231",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7968",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4087",
        "module": "dms",
        "level": "1",
        "platform_id": "115",
        "department_id": "0",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7882",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7950",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7883",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7884",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "4068",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7966",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7967",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7885",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7931",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7936",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1638",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7934",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7935",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7903",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7938",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1638",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7937",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8025",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1638",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7985",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1638",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7975",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1638",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7945",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1639",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7946",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1639",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8014",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1639",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7976",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1639",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7948",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8011",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7972",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8006",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7902",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8021",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "1640",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7913",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7984",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7917",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7920",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7921",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7923",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7925",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8027",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": ""
    },
    {
        "user_id": "7930",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "540",
        "department_group_id": "1642",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8019",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "539",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7997",
        "module": "dms",
        "level": "2",
        "platform_id": "118",
        "department_id": "541",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7998",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "541",
        "department_group_id": "1643",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7999",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "541",
        "department_group_id": "1643",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8000",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "541",
        "department_group_id": "1643",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "8001",
        "module": "dms",
        "level": "3",
        "platform_id": "118",
        "department_id": "541",
        "department_group_id": "1643",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    },
    {
        "user_id": "7969",
        "module": "dms",
        "level": "2",
        "platform_id": "115",
        "department_id": "543",
        "department_group_id": "0",
        "department_group_position_id": "0",
        "department_group_position_worker_id": "0",
        "department_seven_id": "0",
        "department_eight_id": "0",
        "department_six_id": "0"
    }
]
';

$dsp_user_list = json_decode($level_rank_json, true);
$dms_user_list = json_decode($dms_level_rank_json, true);
$user_list = array_merge($dsp_user_list, $dms_user_list);

$user_model = new UserModel();
$user_logic = new UserLogic();

echo "---------- 开始 ----------\n";
foreach ($user_list as $item) {
    try {
        $user_info = $user_model->getData($item['user_id']);
        if (empty($user_info)) {
            throw new AppException("找不到该用户");
        }

        $param = new UserParam([
            'id' => $item['user_id'],
            'module' => $item['module'],
            'account' => $user_info->account,
            'name' => $user_info->name,
            'mobile' => $user_info->mobile,
            'leader' => 1,
            'level' => $item['level'],
            'platform_id' => $item['platform_id'],
            'department_id' => $item['department_id'],
            'department_group_id' => $item['department_group_id'],
            'department_group_position_id' => $item['department_group_position_id'],
            'department_group_position_worker_id' => $item['department_group_position_worker_id'],
            'department_six_id' => $item['department_six_id'],
            'department_seven_id' => $item['department_seven_id'],
            'department_eight_id' => $item['department_eight_id'],
        ]);

        $can_link = $user_logic->canLink($user_info->account, $item['module']);

        if ($can_link['can_link']) {
            $user_logic->linkUser($param);
        } else {
            $user_logic->editUser($param);
        }

        echo "---------- user_id：{$item['user_id']} 编辑岗位成功 ----------\n";
    } catch (Throwable $e) {
        echo "---------- user_id：{$item['user_id']} 出错：{$e->getMessage()} ----------\n";
        continue;
    }
}

echo "---------- 完成 ----------\n";
*/

/**
 * 订阅RDS


use App\Constant\MediaType;
use App\Model\HttpModel\Toutiao\Subscribe\SubscribeModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

echo "---------- 开始 ----------\n";
// 按照开发者app_id和access_token聚合
$media_account_model = new MediaAccountModel();
$list = $media_account_model->getNeedSubscribeRDSList(MediaType::TOUTIAO);

$developer_list = $media_account_model->getListByMediaType(MediaType::TOUTIAO_DEVELOPER)
    ->keyBy('account_id')
    ->toArray();

$group_list = [];
foreach ($list as $item) {
////     TODO delete
//    if ($item->toutiao_majordomo_id != ****************) {
//        continue;
//    }

    $group_key = "{$item->agent}-{$item->access_token}";
    if (!isset($group_list[$group_key])) {
        $group_list[$group_key] = [
            'app_id' => $item->agent,
            'app_access_token' => $developer_list[$item->agent]->access_token ?? '',
            'access_token' => $item->access_token,
            'account_ids' => []
        ];
    }

    $group_list[$group_key]['account_ids'][] = (int)$item->account_id;
}

$task_list = [
    [
        "app_name" => "adsGroup",
        "app_id" => ****************,
        "description" => "adsGroup_2.0广告小时报表",
        "id" => ****************,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup",
        "app_id" => ****************,
        "description" => "adsGroup_2.0素材小时报表",
        "id" => ****************,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup01",
        "app_id" => ****************,
        "description" => "adsGroup01_2.0广告小时报表",
        "id" => ****************,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup01",
        "app_id" => ****************,
        "description" => "adsGroup01_2.0素材小时报表",
        "id" => ****************,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup02",
        "app_id" => ****************,
        "description" => "adsGroup02_2.0广告小时报表",
        "id" => 1836415200905731,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup02",
        "app_id" => ****************,
        "description" => "adsGroup02_2.0素材小时报表",
        "id" => 1836414698990601,
        "subscribe_id" => ****************
    ],
    [
        "app_name" => "adsGroup03",
        "app_id" => 1694460456435715,
        "description" => "adsGroup03_2.0广告小时报表",
        "id" => 1836415228466247,
        "subscribe_id" => 1694460456435715
    ],
    [
        "app_name" => "adsGroup03",
        "app_id" => 1694460456435715,
        "description" => "adsGroup03_2.0素材小时报表",
        "id" => 1836414623983753,
        "subscribe_id" => 1694460456435715
    ],
    [
        "app_name" => "adstanwan",
        "app_id" => 1598615457838094,
        "description" => "adstanwan_2.0广告小时报表",
        "id" => 1836415036630683,
        "subscribe_id" => 1598615457838094
    ],
    [
        "app_name" => "adstanwan",
        "app_id" => 1598615457838094,
        "description" => "adstanwan_2.0素材小时报表",
        "id" => 1836414956492427,
        "subscribe_id" => 1598615457838094
    ],
    [
        "app_name" => "adstanwan2",
        "app_id" => 1626046377857053,
        "description" => "adstanwan2_2.0广告小时报表",
        "id" => 1836415069958602,
        "subscribe_id" => 1626046377857053
    ],
    [
        "app_name" => "adstanwan2",
        "app_id" => 1626046377857053,
        "description" => "adstanwan2_2.0素材小时报表",
        "id" => 1836414925417924,
        "subscribe_id" => 1626046377857053
    ]
];

$auth_user_model = new \App\Model\HttpModel\Toutiao\User\UserModel();
$subscribe_model = new SubscribeModel();
foreach ($group_list as $item) {
    try {
        $auth_user_info = $auth_user_model->info($item['access_token']);
    } catch (Exception $e) {
        echo "---------- 获取授权User信息错误 {$e->getMessage()} {$item['access_token']}----------\n";
//        Helpers::getLogger('toutiao')->error('获取授权User信息错误', [
//            'msg' => $e->getMessage(),
//            'access_token' => $item['access_token']
//        ]);
        continue;
    }

    // 接口一次最多500个账号
    $account_chunk = array_chunk($item['account_ids'], 500);
    foreach ($account_chunk as $chunk) {
        $update_account_ids = $chunk;
        foreach ($task_list as $task) {
            if ($task['app_id'] == $item['app_id']) {
                $data = [
                    'app_id' => (int)$item['app_id'],
                    'subscribe_task_id' => (int)$task['id'],
                    'core_user_id' => (int)$auth_user_info['id'] ?? 0,
                    'advertiser_ids' => $chunk
                ];

                try {
                    $result = $subscribe_model->subscribeRDS($developer_list[$item['app_id']]->access_token ?? '', $data);
                    if (isset($result['failed_advertiser_ids'])) {
                        $update_account_ids = array_diff($update_account_ids, $result['failed_advertiser_ids']);
                    }
                } catch (Exception $e) {
                    echo "---------- 订阅RDS失败 {$e->getMessage()}----------\n";
//                    Helpers::getLogger('toutiao')->error('订阅RDS失败', [
//                        'msg' => $e->getMessage(),
//                        'data' => $data
//                    ]);
                    continue 2;
                }
            }
        }

        $media_account_model->updateInAccountIds(MediaType::TOUTIAO, $update_account_ids, ['wechat_account_name' => 'subscribe_rds']);
    }
    echo "---------- 订阅RDS成功 {$item['app_id']}, {$item['access_token']}----------\n";
}

echo "---------- 完成 ----------\n";
*/


/**
 * 重跑星图同步到BPM任务
 */

use App\Logic\API\StarDemandLogic;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

echo "---------- 开始 ----------\n";

$start_time = strtotime("2025-07-30 12:20:00");
$end_time = strtotime("2025-07-30 14:15:00");

$current_timestamp = $start_time;

while ($current_timestamp <= $end_time) {
    $run_start_time = date("Y-m-d H:i:s", $current_timestamp);

    // 每次循环后增加 5 分钟（5 * 60 秒）
    $current_timestamp += 5 * 60;

    $run_end_time = date("Y-m-d H:i:s", $current_timestamp);

    echo "---------- 开始 {$run_start_time}----------\n";

    // 跑同步星图订单到BPM
    $live_order_result = (new StarDemandLogic())->updateAnchorCooperateLiveOrder($run_start_time, $run_end_time, []);

    // 跑同步结算订单到BPM
    $condition = [
        'update_time' => [$run_start_time, $run_end_time],
        'page' => 1,
        'rows' => 100000
    ];
    $settle_result = (new StarDemandLogic())->updateAnchorCooperateSettlement($condition);

    // 跑同步主播费用订单到BPM
    $special_fund_result = (new StarDemandLogic())->updateAnchorCooperateSpecialFund([
        'update_time' => [$run_start_time, $run_end_time]
    ]);

    if (!empty($live_order_result['error_list'] ?? '')) {
        $error_list = json_encode($live_order_result['error_list'], JSON_UNESCAPED_UNICODE);
        echo "---------- {$run_start_time} 跑同步星图订单到BPM有错误的 {$error_list}----------\n";
    }
    if (!empty($settle_result['error_list'] ?? '')) {
        $error_list = json_encode($settle_result['error_list'], JSON_UNESCAPED_UNICODE);
        echo "---------- {$run_start_time} 跑同步结算订单到BPM有错误的 {$error_list}----------\n";
    }
    if (!empty($special_fund_result['error_list'] ?? '')) {
        $error_list = json_encode($special_fund_result['error_list'], JSON_UNESCAPED_UNICODE);
        echo "---------- {$run_start_time} 跑同步主播费用订单到BPM有错误的 {$error_list}----------\n";
    }

    echo "---------- 完成 {$run_start_time}----------\n";
}

echo "---------- 完成 ----------\n";
