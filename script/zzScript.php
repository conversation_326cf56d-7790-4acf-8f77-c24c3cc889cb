<?php
/*
 * zz专用脚本，清洗数据bala bala
 */

use App\Constant\MediaType;
use App\Constant\UserIDType;
use App\Model\HttpModel\Baidu\ImageManageService\ImageManageServiceModel;
use App\Model\HttpModel\BaiduSearch\AppCenterJobService\AppCenterJobModel;
use App\Model\HttpModel\BaiduSearch\AppCenterPackageService\AppCenterPackageModel;
use App\Model\HttpModel\BaiduSearch\ExtAudienceService\ExtAudienceModel;
use App\Model\HttpModel\BaiduSearch\MarketingFileService\MarketingFileModel;
use App\Model\HttpModel\BaiduSearch\ShareService\ShareModel;
use App\Model\HttpModel\Kuaishou\DMP\PopulationModel;
use App\Model\HttpModel\OPPO\Communal\FinanceModel;
use App\Model\HttpModel\OPPO\Data\AdModel;
use App\Model\HttpModel\Tencent\CustomAudiences\CustomAudiencesModel;
use App\Model\HttpModel\Tencent\Insights\HourlyInsightsModel;
use App\Model\HttpModel\Toutiao\DMP\CustomAudienceModel;
use App\Model\HttpModel\UC\Account\AccountModel;
use App\Model\HttpModel\UC\DMP\AudienceModel;
use App\Model\HttpModel\UC\Report\DownloadFileModel;
use App\Model\SqlModel\AbstractSqlModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\ChannelPackageParam;
use App\Param\MediaAccountInfoParam;
use App\Service\AudienceService;
use App\Service\DeviceService;
use App\Service\KuaishouService;
use App\Service\MediaAD\MediaAD;
use App\Service\MediaAD\MediaBaidu;
use App\Struct\Generate\MediaTypeGenerator;
use App\Struct\RedisCache;
use App\Utils\FileComparator;
use App\Utils\Helpers;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

function removeEmoji($text)
{
    $pattern = '/[\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}\x{1F300}-\x{1F5FF}\x{1F600}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{1F900}-\x{1F9FF}\x{1F1E0}-\x{1F1FF}\x{1F191}-\x{1F251}\x{2600}-\x{26FF}\x{2700}-\x{27BF}]/u';
    return preg_replace($pattern, '', $text);
}

// 示例用法
$inputString = "xiaosi66668-王小四✨（景涛）-01-01_震慑了员工_13696_xxlh_无锚";
$outputString = removeEmoji($inputString);

echo $outputString . PHP_EOL;
exit;
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$params = getopt('f:');
if (!isset($params['f'])) {
    print_r('-f 文件绝对路径' . PHP_EOL);
    die;
}
$file = file_get_contents($params['f']);
// 这里需要是数组
$images = [['content' => chunk_split(base64_encode($file))]];
$media_account = new MediaAccountModel();
$media_account_info = $media_account->getDataByAccountId(********);
$username = $media_account_info->company;
$password = $media_account_info->access_token;
$target = $media_account_info->account_name;
$token = $media_account_info->refresh_token;
$image_model = new ImageManageServiceModel();
$result = $image_model->upload(
    $username,
    $password,
    $token,
    $target,
    $images
);

var_dump($result);
die;
//$game_sdk_info = (new OdsMediaSDKModel())->getDataByGame('TW', MediaType::BAIDU, 2607);
//$ext = json_decode($game_sdk_info->ext, true);
//$channel_package_param = new ChannelPackageParam([
//    'media_account' => new MediaAccountInfoParam((array)$media_account),
//    'site_id' => 12345,
//    'game_id' => 2607,
//    'suffix_name' => trim("test", '-'),
//    'app_id' => $game_sdk_info->appid,
//    'download_url' => 'https://download2.aaymx.com/tw_2607_lyzzbhbb/tw_2607_lyzzbhbb_2357839.apk',
//    'sdk_ext' => $ext,
//]);
//
//$data = (new MediaAD(MediaType::BAIDU))->createChannelPackage($channel_package_param);
//

//$list = MysqlConnection::getConnection($connection_name)
//    ->table('audience')
//    ->select('audience.*')
//    ->leftJoin('device_task', 'device_task_id', '=', 'device_task.id')
//    ->where('audience.media_type', 3)
//    ->where('auto_update', 2)
//    ->where('audience.create_time', '>', **********)
//    ->where('audience.state', 2)
//    ->orderBy('audience.id')
//    ->get();
//
//$data_type_map = [
//    '安卓' => UserIDType::IMEI_MD5,
//    'IOS' => UserIDType::IDFA_MD5,
//    'OAID' => UserIDType::OAID,
//];
//$redis = RedisCache::getInstance();
//$population_model = new PopulationModel();
//$media_account = new MediaAccountModel();
//$audience_upload_file_model =  new AudienceUploadFileModel();
//$audience_change_log_model = new AudienceChangeLogModel();
//foreach ($list as $item) {
//    $explode_audience_name = explode('-', $item->audience_name);
//    $create_time = $item->create_time;
//    $subpath = date("Ym", $create_time);
//    $date = date("Ymd", $create_time);
//    $os = array_pop($explode_audience_name);
//    $filename = "task-$item->device_task_id-all-$os-%s.txt";
//    $file = AudienceService::AUDIENCE_DIR . '/' . $subpath . '/' . sprintf($filename, $date);
//    if (file_exists($file)) {
//        echo $file, PHP_EOL;
//        $file2 = AudienceService::AUDIENCE_DIR . '/' . '202104' . '/' . sprintf($filename, '********');
//        $kuaishou_audience_id = $item->audience_id;
//        $item->audience_id = $item->id;
//        $item->file_data_type = $data_type_map[$os];
//        $zip_paths = getUploadFile($item, $file, $file2);
//        $zip = array_shift($zip_paths);
//        $zip_name = basename($zip);
//        $operation_type = (int)$zip_name[0];
//        $type = $item->file_data_type === UserIDType::OAID ? UserIDType::OAID_MD5 : $item->file_data_type;
//        $media_account_info = $media_account->getDataByAccountId($item->account_id);
//        $upload_file_name = "task-$item->device_task_id-all-$os.txt";
//        $audience_upload_file_info = $audience_upload_file_model->getLastRecordByName($upload_file_name, **********);
//        var_dump($audience_upload_file_info);
//        $population_model->update($item->account_id, $media_account_info->access_token, UserIDType::KUAISHOU_TYPE_MAP[$type], $kuaishou_audience_id, $operation_type, $zip);
//        $redis->rPush("kuaishou_orientation_file:$kuaishou_audience_id", ...$zip_paths);
//        $audience_change_log_model->add(1, '超管', $item->id, $kuaishou_audience_id, $audience_upload_file_info->id, 3, 3, **********, 1);
//        exit;
//    } else {
//        echo "找不到id:{$item->id}的文件 ", $file, PHP_EOL;
//    }
//}
//
//function getUploadFile($record, $file1, $file2)
//{
//    $comparator = new FileComparator();
//    $kuaishou_service = new KuaishouService();
//    $upload_files = [];
//    $audience_id = $record->audience_id;
//    $file_data_type = $record->file_data_type;
//
//    $result = $comparator->compare($file1, $file2, [FileComparator::OP_FLAG_FILE1_DIFF, FileComparator::OP_FLAG_FILE2_DIFF]);
//    $reduce_file = $result[0];
//    $append_file = $result[1];
//    if (filesize($reduce_file)) {
//        // prefix第一个字符串用来充当operation_type
//        $zip_prefix = "3-delete-{$audience_id}";
//        $zip_paths = $kuaishou_service->createDataSourceFile($zip_prefix, [
//            'name' => $reduce_file,
//            'data_type' => $file_data_type,
//            'need_md5' => $file_data_type === UserIDType::OAID,
//        ]);
//        $upload_files = array_merge($upload_files, $zip_paths);
//    }
//    if (filesize($append_file)) {
//        // prefix第一个字符串用来充当operation_type
//        $zip_prefix = "1-append-{$audience_id}";
//        $zip_paths = $kuaishou_service->createDataSourceFile($zip_prefix, [
//            'name' => $append_file,
//            'data_type' => $file_data_type,
//            'need_md5' => $file_data_type === UserIDType::OAID,
//        ]);
//        $upload_files = array_merge($upload_files, $zip_paths);
//    }
//
//    $comparator->clean();
//    return $upload_files;
//}

//$data = (new AppCenterJobModel())->getDetail(
//    '原生-贪玩管家2户',
//    'Tw2021',
//    '92b82cad293c6474555c48081ab0879b',
//    '原生-贪玩1156-B21KA00373', 1, 1,
//    239742
//);

//
//$data = (new AppCenterPackageModel())->get(
//    '原生-贪玩管家2户',
//    'Tw2021',
//    '92b82cad293c6474555c48081ab0879b',
//    '原生-贪玩853-B21KA00373', 1, 1,
//    ***********
//);


//$data = (new \App\Model\HttpModel\OPPO\Communal\AdModel())->getList(**********, 'e52ed4a3ead149dbbf543be3d1bc3563', 'c5135a3b7efc42b7b4464f43f32e25c8', 1, 1);

//$data = (new \App\Model\HttpModel\Tencent\RTA\TargetModel())->getList(1,10);


//$list = MysqlConnection::getConnection($connection_data_media)
//    ->table('ods_toutiao_custom_audience_log')
//    ->join('ods_toutiao_account_log', 'ods_toutiao_custom_audience_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->select('ods_toutiao_custom_audience_log.platform', 'ods_toutiao_custom_audience_log.account_id', 'ods_toutiao_custom_audience_log.custom_audience_id')
//    ->where('ods_toutiao_custom_audience_log.platform', '')
//    ->whereIn('ods_toutiao_custom_audience_log.custom_audience_id', [*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,300382305,300952036,309621763,301748070,300046715,308530233,301748058,300928413,302437521,300388758,300132059,302617985,302159871,312611484,300563214,305238128,300391864,300421860,313643249,304093997,302471814,300887582,300763932,300951992,300350119,300237261,300750619,304094232,302048252,300508726,308296841,303332192,302159913,301444638,300928332,300147214,300027192,302737616,300900217,300325396,300278478,301444649,300952026,300290222,301209863,300498306,300059021,309785739,308768982,312018520,300549402,300970302,308185547,300345795,303224858,300411087,300940886,303223787,300391796,309621684,308868343,308142616,312635902,302574179,300589343,300147120,309144692,301748303,300540377,300573219,301729767,300310560,301542025,302782251,309381947,301517841,300579614,304094243,300928410,300619523,300947049,300750690,300059006,300046702,300531501,301748122,304217237,300352605,311417454,300549426,300366601,300147125,302574206,300753503,309963413,300951374,300301590,304094151,300027194,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********])
//    ->where('ods_toutiao_account_log.company', '江西玉登网络科技有限公司')
//    ->where('ods_toutiao_custom_audience_log.account_id', '!=', ************)
//    ->where('is_del', 0)
//    ->groupBy(['ods_toutiao_custom_audience_log.custom_audience_id'])
//    ->get();
//
//$custom_audience_model = new CustomAudienceModel();
//$media_account = new MediaAccountModel();
//foreach ($list as $toutiao_audience) {
//    $media_account_info = $media_account->getDataByAccountId($toutiao_audience->account_id);
//    print_r($toutiao_audience->account_id.PHP_EOL);
//    print_r($toutiao_audience->custom_audience_id.PHP_EOL);
//    try {
//        $data = $custom_audience_model->push($toutiao_audience->account_id, $media_account_info->access_token, $toutiao_audience->custom_audience_id, [****************]);
//    } catch (\Exception $e) {
//        print_r($e->getMessage().PHP_EOL);
//    }
//}


//print_r($data);
$media_account = (new MediaAccountModel())->getDataByAccountId(********, MediaType::BAIDU);

$game_sdk_info = (new OdsMediaSDKModel())->getDataByGame('', MediaType::BAIDU, 14);
$channel_package_param = new ChannelPackageParam([
    'media_account' => new MediaAccountInfoParam((array)$media_account),
    'site_id' => 101322,
    'game_id' => 14,
    'suffix_name' => "技术测试",
    'app_id' => 13249,
    'download_url' => "https://packages.695723.com/shouyou/baidu_14_zpqx/baidu_14_zpqx_101322.apk",
    'sdk_ext' => json_decode($game_sdk_info->ext, true),
]);

$data = (new MediaAD(MediaType::BAIDU))->createChannelPackage($channel_package_param);

var_dump($data);
die;

//$data = (new MarketingFileModel())->upload(
//    $username,
//    $password,
//    $token,
//    $target,
//    DeviceService::DMP_DIR . '/task-267-IOS.txt',
//    [
//        'fileName' => 'task-267-IOS.txt',
//        'fileType' => 'txt',
//        'storeType' => 'temp',
//        'bizFlag' => 'audience'
//    ],
//);

//
//$data = (new ExtAudienceModel())->createEmptyGroup(
//    $username,
//    $password,
//    $token,
//    $target,
//    ********,
//    '技术测试',
//    'id_upload',
//    true
//);

//$data = (new ExtAudienceModel())->updateIdUpload4Pour(
//    $username,
//    $password,
//    $token,
//    $target,
//    *********,
//    'idfa_md5',
//    'add',
//    371331
//);
//

//$data = (new ExtAudienceModel())->updateAudienceDsp(
//    $username,
//    $password,
//    $token,
//    $target,
//    ********,
//    *********,
//    'feedcpc,fc,feedgd,jinnang,pinzhuan',
//);
$username = $media_account->company;
$password = $media_account->access_token;
$target = $media_account->account_name;
$token = $media_account->refresh_token;
$data = (new ExtAudienceModel())->getAudienceList(
    $username,
    $password,
    $token,
    $target,
    [*********]
);

print_r($data);

//$data = (new ShareModel())->saveSharingBatchDr(
//    $username,
//    $password,
//    $token,
//    $target,
//    [*********],
//    [********]
//);

//$data = (new AudienceModel())->queryPackage(
//    $username,
//    $password,
//    $token,
//    $target,
//    [
//        'query' =>1823697,
//        'pageNo' => 1,
//        'pageSize' => 1
//    ]);

//$data = (new AudienceModel())->uploadNumberFile(
//    $username,
//    $password,
//    $token,
//    $target,
//    5,
//    DeviceService::DMP_DIR . '/task-468-安卓.zip'
//);


//$data = (new AudienceModel())->addPackage(
//    $username,
//    $password,
//    $token,
//    $target,
//    '技术测试-改名1',
//    5,
//    '210677906_1617780318942.zip',
//    'task-468-安卓.zip'
//);

//$data = (new AudienceModel())->queryPackage(
//    $username,
//    $password,
//    $token,
//    $target,
//    [
//        'query' =>1843452,
//        'pageNo' => 1,
//        'pageSize' => 1
//    ]);

//$data = (new AudienceModel())->pushPackage(
//    $username,
//    $password,
//    $token,
//    $target,
//    [1843452]);

//$data = (new PopulationModel())->delete(9994400, 'c99292ec4ffecf0fc441db91bcb927bc', 100963018);
//print_r($data);