<?php
// todo 修复素材文件url
// todo example:
// todo url为 https://dms.zeda.cn/upload/adsimg/material/915/19038/19038_0912-传奇-800-800-4_(6组图)(腾讯投放压缩).jpg
// todo 根据origin_platform origin_material_id 去update

use App\Model\RedisModel\AutoIncrementIdModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';

MysqlConnection::setConnection();


$list = MysqlConnection::getConnection('data_media')
    ->table('ods_material_file_log')
    ->whereIn('original_platform', ['xinxin'])
    ->where('file_type', '=', 2)
    ->orderByDesc('id')
    ->get();

foreach ($list as $video) {
    // https://dms.zeda.cn/upload/adsimg/material/TW/99860/37688_沙鳄鱼_800x800.jpg
    $srv_array = explode('/', $video->url);
    $new_srv_array = $srv_array;

    $srv_array[6] = $video->original_platform;
    $srv_array[7] = $video->original_material_id;
    $srv_array[8] = str_replace('.mp4', '.wm', $srv_array[8]);
    $srv_array = array_slice($srv_array, 3);
    $srv = SRV_DIR . '/' . implode("/", $srv_array);

    $new_srv_array[8] = str_replace('.mp4', '.wm', $new_srv_array[8]);
    $new_srv_array = array_slice($new_srv_array, 3);
    $new_srv = SRV_DIR . '/' . implode("/", $new_srv_array);

    $new_path = pathinfo($new_srv);

    if (!file_exists($new_path['dirname'])) {
        mkdir($new_path['dirname'], 0755, true);
    }
    try {
        if (!file_exists($new_srv)) {
            $copy_result = copy($srv, $new_srv);
            if (!$copy_result) {
                echo "error $srv $new_srv 复制失败 退出" . PHP_EOL;
            } else {
                echo "error $srv $new_srv 复制成功" . PHP_EOL;
            }
        } else {
            echo "error $srv $new_srv 已存在 跳过" . PHP_EOL;
        }
    } catch (Throwable $e) {
        echo $e->getMessage() . PHP_EOL;
        echo "error $srv $new_srv 复制失败 退出" . PHP_EOL;
    }
}
