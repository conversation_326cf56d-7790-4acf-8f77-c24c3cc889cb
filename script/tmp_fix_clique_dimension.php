<?php

/**
 * 如果账号有根游戏维度权限，就添加集团游戏维度权限
 */

use App\Model\SqlModel\Zeda\RankRoutePermissionModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


// 设置MySQL连接对象
MysqlConnection::setConnection();


// 包含根游戏、集团游戏的维度筛选或维度的指标
$sql = "SELECT route.title,route.uri,route.module,route_permission.* FROM route left join `route_permission` on route.id=route_permission.route_id 
where route.state=1 and route_permission.state=1 and route_permission.cate in(1,2) and route_permission.name in('根游戏','集团游戏') and route.module='dms'
order by route_permission.route_id, route_permission.name";
$tmp_list = MysqlConnection::getConnection()->select($sql);

// 根游戏、集团游戏指标分组
$root_game_list = $clique_list = [];
foreach ($tmp_list as $item) {
    if ($item->name === '根游戏') {
        $root_game_list[$item->route_id . '|' . $item->cate] = $item;
    } else {
        $clique_list[$item->route_id . '|' . $item->cate] = $item;
    }
}

// 同时存在根游戏、集团游戏的指标，需要添加集团游戏指标权限
$root_game_route_permission_id = $clique_route_permission_id = $map = [];
foreach ($root_game_list as $key => $item) {
    if (!isset($clique_list[$key])) {
        continue;
    }
    $root_game_route_permission_id[] = $item->id;
    $clique_route_permission_id[] = $clique_list[$key]->id;
    $map[$item->id] = $clique_list[$key]; // 根游戏route_permission_id 指向 集团游戏route_permission数据
}

//print_r($map);

// 已开通集团游戏权限的 rank 和 level
$clique_permission_map = [];
$clique_route_permission_ids = implode(',', $clique_route_permission_id);
$sql = "SELECT * from rank_route_permission where route_permission_id in($clique_route_permission_ids) order by rank_id,level";
$clique_permission_list = MysqlConnection::getConnection()->select($sql);
foreach ($clique_permission_list as $key => $item) {
    $tmp_key = $item->rank_id . '|' . $item->level . '|' . $item->route_permission_id;
    $clique_permission_map[$tmp_key] = $item;
}

// 已开通根游戏权限的 rank 和 level
$root_game_route_permission_ids = implode(',', $root_game_route_permission_id);
$sql = "SELECT * from rank_route_permission where route_permission_id in($root_game_route_permission_ids) order by rank_id,level";
$root_game_permission_list = MysqlConnection::getConnection()->select($sql);

//print_r($clique_permission_map);
//print_r($root_game_permission_list);

echo "获取数据成功。开始处理", PHP_EOL;
$model = new RankRoutePermissionModel();
$now = time();
$insert_data = [];
foreach ($root_game_permission_list as $item) {
    $rank_id = $item->rank_id;
    $level = $item->level;
    $root_game_route_permission_id = $item->route_permission_id; // 根游戏指标id
    $route_permission_id = $map[$root_game_route_permission_id]->id; // 集团游戏指标id

    $tmp_key = $rank_id . '|' . $level . '|' . $route_permission_id;
    if (isset($clique_permission_map[$tmp_key])) {
        continue;
    }

    $insert_data[] = [
        'level' => $level,
        'rank_id' => $rank_id,
        'route_permission_id' => $route_permission_id,
        'create_time' => $now,
        'update_time' => $now,
    ];

    echo "rank_id: $rank_id level: $level route_permission_id: $route_permission_id <= $root_game_route_permission_id ", PHP_EOL;
}
if (!empty($insert_data)) {
    $model->replace($insert_data);
}

echo '处理完成', PHP_EOL;


