<?php

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADLogModel;
use App\Model\SqlModel\DataMedia\OdsToutiaoAdLogModel;
use App\Model\SqlModel\Zeda\LimitChengduOperateLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;
/**
 * 头条
 */
//头条选择广东全省代号
const TOUTIAO_CHOSEN_GUANGDONG = 44;
//头条广东所有城市代号
const TOUTIAO_GUANGDONG_CITY = [440100, 440200, 440300, 440400, 440500, 440600, 440700, 440800, 440900, 441200,
    441300, 441400, 441500, 441600, 441700, 441800, 441900, 442000, 445100, 445200, 445300];
//头条全国只排除广东的代号
const TOUTIAO_COUNTRY_EXCEPT_GUANGDONG = [64, 11, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 37, 41, 42, 43, 45, 46, 50, 51, 53, 54, 52,
    61, 62, 63, 12, 36];

class multiLimitToutiaoAD2Region extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $toutiao_http_model = new App\Model\HttpModel\Toutiao\Ad\AdModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $ad_info) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            $insert_data = [];
//            $num = 0;
            foreach ($ad_info as $item) {
//                $num += 1;
                $get_modify_time = [];
                $request_data = [];
                $get_modify_time['advertiser_id'] = (int)$item->account_id;
                $get_modify_time['filtering'] = ['ids' => [(int)$item->ad_id]];
                try {
                    $modify_time_info = $toutiao_http_model->getAD2Info($get_modify_time, $access_token);
                } catch (AppException $e) {
                    $modify_time_info = "获取modify_time失败，" . $e->getMessage();
                }

                $insert = [];
                $insert['account_id'] = $item->account_id;
                $insert['ad2_id'] = $item->ad_id;
                $insert['insert_time'] = time();
                $insert['media_type'] = MediaType::TOUTIAO;
                $insert['old_data'] = json_encode($item->region);
                if (is_array($modify_time_info)) {
                    $request_data['advertiser_id'] = (int)$item->account_id;
                    $request_data['ad_id'] = (int)$item->ad_id;
                    $request_data['modify_time'] = $modify_time_info['list'][0]['modify_time'];
                    if (empty($item->region)) {
                        $request_data['district'] = 'CITY';
                        $request_data['city'] = TOUTIAO_COUNTRY_EXCEPT_GUANGDONG;

                    } elseif (in_array(TOUTIAO_CHOSEN_GUANGDONG, $item->region)) {
                        $position = array_search(TOUTIAO_CHOSEN_GUANGDONG, $item->region);
                        unset($item->region[$position]);
                        $request_data['district'] = 'CITY';
                        $request_data['city'] = array_values($item->region);

                    } elseif (array_intersect($item->region, TOUTIAO_GUANGDONG_CITY)) {
                        $request_data['district'] = 'CITY';
                        $city = array_diff($item->region, TOUTIAO_GUANGDONG_CITY);

                        if (!$city) {
                            $city = TOUTIAO_COUNTRY_EXCEPT_GUANGDONG;
                        }
                        $request_data['city'] = array_values($city);
                    } else {
                        continue;
                    }

                    try {
                        $response = $toutiao_http_model->update($request_data, $access_token);
                        $insert['info'] = 'success ' . json_encode($response, JSON_UNESCAPED_UNICODE);
                        $insert['status'] = 1;
                    } catch (AppException $e) {
                        $error['message'] = $e->getMessage();
                        $error['code'] = $e->getCode();
                        $insert['info'] = json_encode($error, JSON_UNESCAPED_UNICODE);
                        $insert['status'] = 2;
                        print_r("任务{$n}—" . $e->getMessage() . PHP_EOL);
                    }

                } else {
                    $insert['info'] = $modify_time_info;
                    $insert['status'] = 2;
                }
                $insert_data[] = $insert;

                print_r(date("Y-m-d H:i:s") . "头条二级广告ID：" . $item->ad_id . "投放地区调整完成" . PHP_EOL);
            }
            unset($item);

            if ($insert_data) {
                (new LimitChengduOperateLogModel())->add('toutiao_limit_operate_log', $insert_data);
            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();

$ad_info = (new OdsToutiaoAdLogModel())->getADInfoByStatusForLimitRegion();
if ($ad_info->isEmpty()) {
    return;
}
foreach ($ad_info as $item) {
    //将地区处理为数组
    $item->region = trim($item->region, '[]');
    if ($item->region) {
        $item->region = explode(',', $item->region);
    } else {
        $item->region = [];
    }
}
unset($item);
//$connect2 = MysqlConnection::getConnection($connection_data_media);
//$cost_connect = $connect->table('ods_toutiao_account_log')
//    ->join('ods_toutiao_creative_hour_data_log', 'ods_toutiao_creative_hour_data_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->selectRaw('ods_toutiao_account_log.account_id, SUM(cost)')
//    ->where('cost_date', '2021-01-08');


$account_ids = $ad_info->pluck('account_id')->unique();
$account_chunk_list = $ad_info->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');
if ($media_account_list->isEmpty()) {
    return;
}
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiLimitToutiaoAD2Region');
$multi_process->start();
