<?php

use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

$contents = $argv[1];
$log_pattern     = '/\[(?P<date>.*?)\] \[(?P<wrole>.*?)\] \[(?P<uuid>.*?)\] (?P<logger>.*?)\.(?P<level>\w+): (?P<message>.*)/';
$message_pattern = '/\"code\":(?P<code>.*?),/';
preg_match($log_pattern, $contents, $monolog);

$date    = $monolog['date'];
$logger  = $monolog['logger'];
$level   = $monolog['level'];
$uuid    = $monolog['uuid'];
$message = $monolog['message'];
preg_match($message_pattern, $message, $data);

if (isset($data['code'])) {
    $code = $data['code'];
    $redis = RedisCache::getInstance();
    $key = "logParser_{$logger}:{$code}";
    if ($redis->get($key)) return '';
    $redis->set($key, '1', 3600);
}
$message_ellipsis = getEllipsisStr($message);

// 过滤一下不需要的错误信息
$filter = false;
if($logger === 'app') {
    $substrings = [
        'Update failed for dictionary tanwan_datahub.dim_site_id_dict',
        'DB::Exception: Dictionary tanwan_datahub.dim_site_id_dict',
        'DB::Exception: Dictionary tanwan_datahub.dim_site_id_full_dict',
        'DB::Exception: Too many simultaneous queries',
        'DB::Exception: Memory limit',
        'Out of Memory Pool size pre cal',
        'The cluster is out of memory',
        'Query exceeded maximum time limit of',
    ];

    foreach ($substrings as $substring) {
        if (strpos($message_ellipsis, $substring) !== false) {
            $filter = true;
            break;
        }
    }
}


//只查询日志发生时间前后的10秒，提高查询效率。 es查询的时区问题（差8小时），需要将时间回退且按下方的格式
$log_time_range_start = date('Y-m-d\TH:i:s.000\Z', strtotime($date) - (3600 * 8) - 1);
$log_time_range_end   = date('Y-m-d\TH:i:s.000\Z', strtotime($date) - (3600 * 8) + 10);
$link                 = EnvConfig::ELASTICSEARCH['default']['kibana_domain'] . "/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:'{$log_time_range_start}',to:'{$log_time_range_end}'))&_a=(columns:!(),filters:!(),index:d48c1340-dd3f-11eb-98a7-a92700c7e6de,interval:auto,query:(language:kuery,query:%22{$uuid}%22),sort:!(!('@timestamp',desc)))";
$a                    = Helpers::postCurl(
    EnvConfig::ELASTICSEARCH['default']['error_notify']['webhook'],
    json_encode([
        'msg_type' => 'post',
        'content' => [
            'post' => [
                'zh_cn' => [
                    'title' => "$logger.$level",
                    'content' => [
                        array_merge(
                            [
                                ['tag' => 'text', 'text' => "$date"],
                                ['tag' => 'a', 'text' => "点我ES直达\n", 'href' => $link],
                                ['tag' => 'text', 'text' => "$message_ellipsis"],
                            ],
                            $logger === 'app' && $filter === false ? [['tag' => 'at', 'user_id' => EnvConfig::ELASTICSEARCH['default']['error_notify']['at_id']]] : []
                        )
                    ],
                ]
            ],
        ]
    ]),
    [
        'header' => [
            'Content-Type: application/json',
        ]
    ]
);


function getEllipsisStr($str)
{
    $str_len = mb_strlen($str);
    if ($str_len < 400) {
        return $str;
    }
    $subStr1 = mb_substr($str, 0, 220);
    $subStr2 = mb_substr($str, $str_len - 150);
    return $subStr1 . '.....(超长省略)......' . $subStr2;
}
