<?php

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Baidu\AdGroupFeed\AdGroupFeedModel;
use App\Model\HttpModel\Toutiao\Ad\AdModel;
use App\Model\SqlModel\DataMedia\OdsBaiduADLogModel;
use App\Model\SqlModel\Zeda\LimitChengduOperateLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = true;

//百度选择广东全省代号
const BAIDU_CHOSEN_GUANGDONG = 4000;
//百度四川省除广东外所有代号
const BAIDU_GUANGDONG_CITY = [4085,4116,4090,4084,4115,4117,4082,4083,4086,4088,4089,4091,4092,4094,4093,4109,
    4111,4110,4114,4112,4113];
//百度全国只排除广东的代号
const BAIDU_COUNTRY_EXCEPT_GUANGDONG = [1000, 2000, 3000, 5000, 8000, 9000, 10000, 11000, 12000, 13000, 14000,
    15000, 16000, 17000, 18000, 19000, 20000, 21000, 22000, 23000, 24000, 25000, 26000, 27000, 29000, 30000, 31000,
    32000, 33000, 28000];

class multiLimitBaiduAD2Region extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $baidu_http_model = new AdGroupFeedModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $ad_info) {
            var_dump($account_id);
            $insert_data = [];
//            $num = 0;

            foreach ($ad_info as $item) {
//                $num += 1;
                $original_data = json_encode($item->region);
                $baidu_header = [];
                $baidu_header['target'] = $media_account_list[$account_id]->account_name;
                $baidu_header['password'] = $media_account_list[$account_id]->access_token;
                $baidu_header['username'] = $media_account_list[$account_id]->company;
                $baidu_header['token'] = $media_account_list[$account_id]->refresh_token;

                $baidu_body = [];
                $baidu_body['adgroupFeedId'] = (int)$item->ad_id;
                $baidu_body['campaignFeedId'] = (int)$item->campaign_id;

                if (in_array('9999999', $item->region)) {
                    //不限制地区
                    $baidu_body['audience']['region'] = implode(',', BAIDU_COUNTRY_EXCEPT_GUANGDONG);
                } elseif (in_array(BAIDU_CHOSEN_GUANGDONG, $item->region)) {
                    //选择了全省
                    $position = array_search(BAIDU_CHOSEN_GUANGDONG, $item->region);
                    unset($item->region[$position]);
                    $baidu_body['audience']['region'] = implode(',', array_values($item->region));
                } elseif (array_intersect($item->region, BAIDU_GUANGDONG_CITY)) {
                    $city = array_diff($item->region, BAIDU_GUANGDONG_CITY);
                    if (!$city) {
                        $city = BAIDU_COUNTRY_EXCEPT_GUANGDONG;
                    }
                    $baidu_body['audience']['region'] = implode(',', array_values($city));
                }

                $insert = [];
                $insert['account_id'] = $item->account_id;
                $insert['ad2_id'] = $item->ad_id;
                $insert['insert_time'] = time();
                $insert['media_type'] = MediaType::BAIDU;
                $insert['old_data'] = $original_data;
                try {
                    $response = $baidu_http_model->updateFeed($baidu_header, [$baidu_body]);
                    $insert['info'] = 'success ' . json_encode($response, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 1;
                } catch (AppException $e) {
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $insert['info'] = json_encode($error, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 2;
                }
//
                $insert_data[] = $insert;
                print_r(date("Y-m-d H:i:s") . "百度二级广告ID：" . $item->ad_id . "投放地区调整完成" . PHP_EOL);
            }
            if ($insert_data) {
                (new LimitChengduOperateLogModel())->add('baidu_limit_operate_log', $insert_data);
            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();

$ad_info = (new OdsBaiduADLogModel())->getADInfoByStatusForLimitRegion();
if ($ad_info->isEmpty()) {
    return;
}
foreach ($ad_info as $item) {
    //将地区处理为数组
    if ($item->region) {
        $item->region = explode(',', $item->region);
    } else {
        $item->region = [];
    }
}
unset($item);
//$connect2 = MysqlConnection::getConnection($connection_data_media);
//$cost_connect = $connect->table('ods_toutiao_account_log')
//    ->join('ods_toutiao_creative_hour_data_log', 'ods_toutiao_creative_hour_data_log.account_id', '=', 'ods_toutiao_account_log.account_id')
//    ->selectRaw('ods_toutiao_account_log.account_id, SUM(cost)')
//    ->where('cost_date', '2021-01-08');


$account_ids = $ad_info->pluck('account_id')->unique();
$account_chunk_list = $ad_info->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');
if ($media_account_list->isEmpty()) {
    return;
}
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiLimitBaiduAD2Region');
$multi_process->start();
