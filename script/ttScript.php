<?php
/*
 * tt专用脚本，清洗数据bala bala
 */

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\V2\Material\MaterialModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Service\MediaIntelligentMonitor\MediaIntelligentMonitor;
use Common\EnvConfig;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();


$access_token_info = (new MediaAccountModel())->getDataByAccountId(****************, MediaType::TOUTIAO);

try {
    var_dump((new MaterialModel())->updateMaterialStatus(
        ****************,
        7525784562504106003,
        [['material_id' => 7462287404026708031, 'opt_status' => 'ENABLE']],
        $access_token_info->access_token
    ));
} catch (Throwable $e) {
    var_dump($e->getMessage());

}
exit;

// 使用示例

$material_file_list = MysqlConnection::getConnection('data_media')
    ->table('ods_material_file_log')
    ->where('filename', 'like', '%腾讯投放压缩%')
    ->where('update_time', '>', '2025-06-10 00:00:00')
    ->whereNotIn('file_type', [10, 5, 6, 7])
    ->get();


foreach ($material_file_list as $key => $material_file) {
    try {
        switch ($material_file->file_type) {
            case 5:
            case 6:
            case 7:
            case 10:
            case 1:
                $sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
                break;
            case 3:
            case 2:
                $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
                break;
            case 4:
                $sub_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
                break;
            case 11:
                $sub_path = EnvConfig::MATERIAL_AUDIO_DIR_NAME;
                break;
            default:
                throw new AppException('错误的文件类型:' . $material_file->type);
        }

        $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
        $upload_path = SRV_DIR . "/$access_path";

        $new_md5 = md5_file("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename");

        if ($new_md5 == $material_file->signature) {
            continue;
        }

        echo date('Y-m-d H:i:s') . $material_file->id . '开始更换,剩下' . ($material_file_list->count() - $key) . PHP_EOL;

        MysqlConnection::getConnection()
            ->table('material_file')
            ->where('id', '=', $material_file->id)
            ->update([
                'signature' => $new_md5,
            ]);

        MysqlConnection::getConnection('data_media')
            ->table('ods_material_file_log')
            ->where('id', '=', $material_file->id)
            ->update([
                'signature' => $new_md5,
            ]);
    } catch (Throwable $e) {
        var_dump($e->getMessage());
    }
}

exit;

$list = [
    '7477484362184032294',
    '7477484293859508262',
    '7477484191896584255',
    '7477484168140013578',
    '7477484153501089801',
    '7477484082483413033',
    '7477484046852980772',
    '7477484031744016438',
    '7477483959641161778',
    '7477483928606474249',
    '7477483853714030642',
    '7477483786931978267',
    '7477483757449904154',
    '7477483649451507724',
    '7477483655200866345',
    '7477483547995078693',
    '7477483544430149683',
    '7477483495997358134',
    '7477483454714036275',
    '7477483368892989452',
    '7477483359101288474',
    '7477483194477297673',
    '7477483205266243594',
    '7477483182953922611',
    '7476390566209617930',
    '7476390379920965682',
    '7476390067469058085',
];

$i_s = MediaIntelligentMonitor::create(1, 2);

foreach ($list as $value) {
    $i_s->bind('ad1', $value, 4, '郭沐耘@龙马组');
}
exit;
$a = "1";
$b = $a + 0;
echo $b;
exit;

$game_id_list = [
    12978, 13010, 13574, 14172, 12504, 12462, 13008, 12498, 12288, 12488, 12646, 12470, 12640, 12492, 13030, 12486, 12994, 12618,
    12418, 13212, 12582, 13060, 12706, 12472, 12392, 12574, 15136, 12634, 12802, 12600, 12546, 12946, 12780, 12566, 12484, 12704,
    13054, 12982, 13084, 12674, 12972, 12968, 12476, 13000, 12636, 12628, 12494, 13048, 12614, 12666, 13394, 13590, 12522, 14160,
    12268, 13566, 13046, 12638, 13216, 12980, 13588, 12506, 12502, 13244, 12572, 13618, 12568, 12174, 13050, 12594, 12342, 12580,
    12450, 13012, 12578, 13506, 12796, 13172, 13014, 13090, 13006, 13004, 13188, 12998, 13002, 14018, 14014, 14020, 14022, 14006,
    15348, 15542, 15230, 15362, 14974, 15346, 15000, 15560, 15342, 15350, 15222, 15544, 15336, 15476, 15220, 15226, 15540, 15358,
    15500, 15470, 15256, 15352, 15340, 15176, 15562, 15510, 14220, 15788, 15792, 15764, 15700, 15778, 15782, 15714, 15784, 15740,
    15722, 15770, 15744, 15712, 15732, 15748, 15724, 15730, 15762, 15736, 15734, 15756, 15754, 15772, 15704, 15728, 15698, 15694,
    15786, 15718, 15750, 15776, 15766, 15710, 15738, 15696, 15702, 15774, 15746, 15706, 15768, 15780, 15708, 15716, 15752, 15790,
    15742, 15760, 15690, 15726, 15636, 15566, 15298, 15572, 15622, 15548, 15616, 15518, 15520, 15620, 15628, 15380, 15630, 15564,
    15584, 15378, 15618, 15656, 15634, 15638, 15524, 15632, 15538, 15586, 15522, 15646, 15506, 13492, 13490, 13982, 14130, 9080,
    13984,
];

$game_into_list = MysqlConnection::getConnection('datahub')
    ->table('v2_dim_game_id')
    ->whereIn('v2_dim_game_id.game_id', $game_id_list)
    ->get();

$game_id_key_map = $game_into_list->keyBy('game_id');

$task_list = MysqlConnection::getConnection('default')
    ->table('ad_task')
    ->whereRaw('site_config ->> "$.game_id" in (' . implode(',', $game_id_list) . ')')
    ->where('create_time', '<=', '2023-04-02 00:29:18')
    ->get();

echo $task_list->count();
exit;

foreach ($task_list as $task) {
    $site_config = json_decode($task->site_config, true);
    $game_id = $site_config['game_id'];
    if ($game_id_key_map[$game_id] ?? '') {
        $origin_game_name = $site_config['game_name'];
        //改site
        $site_config['full_site_suffix_name'] = str_replace($origin_game_name, $game_id_key_map[$game_id]->game_name, $site_config['full_site_suffix_name']);
        $site_config['app_name'] = $game_id_key_map[$game_id]->app_name;
        $site_config['game_name'] = $game_id_key_map[$game_id]->game_name;
        $task->site_config = json_encode($site_config, JSON_UNESCAPED_UNICODE);
        //改setting
        $task->setting = str_replace($origin_game_name, $game_id_key_map[$game_id]->game_name, $task->setting);
        //改other_setting
        $task->other_setting = str_replace($origin_game_name, $game_id_key_map[$game_id]->game_name, $task->other_setting);
    }
}
