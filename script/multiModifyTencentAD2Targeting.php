<?php

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Tencent\AdGroup\AdGroupModel;
use App\Model\SqlModel\DataMedia\OdsTencentAdGroupHisLogModel;
use App\Model\SqlModel\Zeda\LimitChengduOperateLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\Tencent\AudiencePackageCreateParam;

require dirname(__DIR__) . '/common/init.php';
require dirname(__DIR__) . '/script/process/utils/common.php';
require dirname(__DIR__) . '/script/process/core/MultiProcess.php';
require dirname(__DIR__) . '/script/process/core/Process.php';

date_default_timezone_set('PRC');

const PROCESS_NUM = 1;
const DAEMONIZE = false;

const TENCENT_CHOSEN_GUANGDONG = 440000;
const TENCENT_GUANGDONG_CITY = [449900, 440400, 445300, 440800, 441200, 442000, 440900, 441400, 441800, 440500,
    441500, 440200, 440300, 441700, 445100, 441900, 440600, 440100, 441600, 441300, 440700, 445200];
const TENCENT_COUNTRY_EXCEPT_GUANGDONG = [156, 110000, 120000, 130000, 140000, 150000, 210000, 220000, 230000, 310000,
    320000, 330000, 340000, 350000, 360000, 370000, 410000, 420000, 430000, 450000, 460000, 500000, 510000,
    520000, 530000, 540000, 610000, 620000, 630000, 640000, 650000, 710000, 810000, 820000];


class multiModifyTencentAD2Targeting extends Process
{
    public function init()
    {
        $this->enableSignal(SIGTERM);
        MysqlConnection::setConnection();
    }

    public function handleSignal($signo)
    {
        echo "child {$this->pid} accept signal {$signo}" . PHP_EOL;
    }

    public function worker($n)
    {
        global $account_chunk_list, $media_account_list;
        $tencent_http_model = new AdGroupModel();
        $account_group_list = $account_chunk_list[$n - 1]->groupBy('account_id');
        foreach ($account_group_list as $account_id => $ad_info) {
            $access_token = $media_account_list[$account_id]->access_token;
            var_dump($account_id);
            $insert_data = [];
//            $num = 0;
            foreach ($ad_info as $item) {
                $original_data = json_encode($item->regions);
                $request_data = [];
                $request_data['account_id'] = (int)$item->account_id;
                $request_data['adgroup_id'] = (int)$item->adgroup_id;

                $item_array = (array)$item;
                $data['age'] = [
                    'min' => $item_array['min_age'],
                    'max' => $item_array['max_age']
                ];
                $data['gender'] = $item_array['gender'];
                $data['education'] = json_decode($item_array['education'], true);
                $data['marital_status'] = json_decode($item_array['marital_status'], true);
                $data['working_status'] = json_decode($item_array['working_status'], true);
                $data['user_os'] = json_decode($item_array['user_os'], true);
                $data['new_device'] = json_decode($item_array['new_device'], true);
                $data['device_price'] = json_decode($item_array['device_price'], true);
                $data['device_brand_model'] = [];
                $data['dressing_index'] = [];
                $data['uv_index'] = [];
                $data['makeup_index'] = [];
                $data['climate'] = [];
                $data['temperature'] = [];
                $data['air_quality_index'] = [];
                $data['network_type'] = json_decode($item_array['network_type'], true);
                $data['network_operator'] = json_decode($item_array['network_operator'], true);
                $data['network_scene'] = json_decode($item_array['network_scene'], true);
//                var_dump($item_array['app_install_status'], json_decode($item_array['app_install_status'], true));
                $data['app_install_status'] = json_decode($item_array['app_install_status'], true)[0];
                $data['consumption_status'] = json_decode($item_array['consumption_status'], true);
                $data['game_consumption_level'] = ['HIGH'];
                $data['consumption_type'] = json_decode($item_array['consumption_type'], true);
                $data['behavior_or_interest'] = json_decode($item_array['behavior_or_interest'], true);
                unset($data['behavior_or_interest']['interest']['targeting_tags']);
                unset($data['behavior_or_interest']['behavior']['targeting_tags']);
                $data['custom_audience'] = json_decode($item_array['custom_audience'], true);
                $data['excluded_custom_audience'] = json_decode($item_array['excluded_custom_audience'], true);
                ($item_array['location_types']) && ($item_array['regions']) && $data['geo_location'] = [
                    'regions' => $item_array['regions'],
                    'location_types' => json_decode($item_array['location_types'], true)
                ];

                $media_targeting_param = new AudiencePackageCreateParam($data);
                $request_data['targeting'] = $media_targeting_param->toBody();

                var_dump($request_data);
//                return;
                if (empty($item->regions) || in_array(1156, $item->regions)) {
                    //不筛选地区
                    $request_data['targeting']['geo_location']['location_types'] = ["LIVE_IN"];
                    $request_data['targeting']['geo_location']['regions'] = TENCENT_COUNTRY_EXCEPT_GUANGDONG;

                } elseif (in_array(TENCENT_CHOSEN_GUANGDONG, $item->regions)) {

                    $position = array_search(TENCENT_CHOSEN_GUANGDONG, $item->regions);
                    unset($item->regions[$position]);
                    foreach ($item->location_types as $key => $value) {
                        $item->location_types[$key] = trim($value, '"');
                    }
                    $request_data['targeting']['geo_location']['location_types'] = $item->location_types;
                    $request_data['targeting']['geo_location']['regions'] = array_values($item->regions);

                } elseif (array_intersect($item->regions, TENCENT_GUANGDONG_CITY)) {
                    foreach ($item->location_types as $key => $value) {
                        $item->location_types[$key] = trim($value, '"');
                    }
                    $request_data['targeting']['geo_location']['location_types'] = $item->location_types;
                    $city = array_diff($item->regions, TENCENT_GUANGDONG_CITY);
                    if (!$city) {
                        $city = TENCENT_COUNTRY_EXCEPT_GUANGDONG;
                    }
                    $request_data['targeting']['geo_location']['regions'] = array_values($city);
                } else {
                    continue;
                }

                $insert = [];
                $insert['account_id'] = $item->account_id;
                $insert['ad2_id'] = $item->adgroup_id;
                $insert['insert_time'] = time();
                $insert['media_type'] = MediaType::TENCENT;
                $insert['old_data'] = $original_data;
                try {

                    $response = $tencent_http_model->updateForADAnalysisMultiEdit(http_build_query($request_data), $access_token);
                    $insert['info'] = 'success ' . json_encode($response, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 1;
                } catch (AppException $e) {
                    $error['message'] = $e->getMessage();
                    $error['code'] = $e->getCode();
                    $insert['info'] = json_encode($error, JSON_UNESCAPED_UNICODE);
                    $insert['status'] = 2;
                }

                $insert_data[] = $insert;
                print_r(date("Y-m-d H:i:s") . "腾讯二级广告ID：" . $item->adgroup_id . "投放地区调整完成" . PHP_EOL);
            }

//            if ($insert_data) {
//                (new LimitChengduOperateLogModel())->add('tencent_limit_operate_log', $insert_data);
//            }
        }

        $ppid = posix_getppid();
        posix_kill($ppid, SIGTERM);
        sleep(5);
    }
}

MysqlConnection::setConnection();

$ad_ids = (new LimitChengduOperateLogModel())->getModifyAD2Ids('tencent_limit_operate_log');

$ad_ids = $ad_ids->chunk(5000);

$all_ad_info = [];
foreach ($ad_ids as $ad_id_set) {
    $ad_info = (new OdsTencentAdGroupHisLogModel())->getADTargetingInfoByAD2Id($ad_id_set);
    $all_ad_info = $ad_info->merge($all_ad_info);
}

if ($all_ad_info->isEmpty()) {
    return;
}
foreach ($all_ad_info as $item) {
    //将地区处理为数组
    $item->regions = trim($item->regions, '[]');
    $item->location_types = trim($item->location_types, '[]');
    if ($item->regions) {
        $item->regions = explode(',', $item->regions);
    } else {
        $item->regions = [];
    }
    if ($item->location_types) {
        $item->location_types = explode(',', $item->location_types);
    } else {
        $item->location_types = [];
    }
}
unset($item);

$account_ids = $all_ad_info->pluck('account_id')->unique();
$account_chunk_list = $all_ad_info->chunk(1000);
$media_account_list = (new MediaAccountModel())
    ->getAccessTokenInAccountIds($account_ids)
    ->keyBy('account_id');

MysqlConnection::getManager()->purge('default');
$multi_process = new MultiProcess($account_chunk_list->count(), DAEMONIZE);
$multi_process->on(MultiProcess::EVT_WORKER_HANDLER, 'multiModifyTencentAD2Targeting');
$multi_process->start();
