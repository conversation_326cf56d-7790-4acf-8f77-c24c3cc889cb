<?php

use App\Constant\AgentGroup;
use App\Constant\ConvertSourceType;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\MediaLogic;
use App\Model\HttpModel\Bilibili\Creative\CreativeModel;
use App\Model\SqlModel\DataMedia\DwdMediaAd2SiteChangeLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Param\AgentParam;
use App\Param\SiteConfigParam;
use App\Service\SiteService;
use App\Struct\Session;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();


$json = '
{
    "platform": "TW",
    "media_type": 163,
    "account_id": "2976628",
    "agent_name": "原始传奇-效果-IOS-TW-技术测试勿用",
    "agent_group_id": 494,
    "agent_group_name": "哔哩哔哩发行CPS",
    "pay_type": 6,
    "statistic_caliber": 3,
    "permission_list": [
        [
            1,
            2,
            "维度筛选",
            "渠道",
            27
        ],
        [
            1,
            3,
            "维度筛选",
            "渠道",
            36
        ],
        [
            1,
            4,
            "维度筛选",
            "渠道",
            38
        ],
        [
            1,
            5,
            "维度筛选",
            "渠道",
            40
        ],
        [
            1,
            2,
            "维度筛选",
            "渠道",
            28
        ],
        [
            1,
            3,
            "维度筛选",
            "渠道",
            37
        ],
        [
            1,
            4,
            "维度筛选",
            "渠道",
            39
        ],
        [
            1,
            5,
            "维度筛选",
            "渠道",
            41
        ],
        [
            1,
            2,
            "维度选择",
            "市场侧",
            29
        ],
        [
            1,
            2,
            "维度选择",
            "市场侧",
            30
        ],
        [
            1,
            2,
            "指标选择",
            "转化链",
            15
        ],
        [
            1,
            2,
            "指标选择",
            "整体情况",
            18
        ]
    ],
    "user_name": "BILIBILI_CPS-TW_jishuceshi",
    "user_pwd": "123456",
    "route_list": "1,2,3,4,5",
    "route_permission_ids": [
        27,
        36,
        38,
        40,
        28,
        37,
        39,
        41,
        29,
        30,
        15,
        18
    ]
}
';

// 设置token获取用户登录信息
Container::setSession(new Session("403147d062f2919f92d2a10e9ffe730e"));

$cps_data = json_decode($json, true);

//$sql = "
//select
//	ad3.platform,
//	ad3.account_id,
//	ag.agent_group_id,
//	ag.agent_group_name,
//	ad2.site_id as ad2_site,
//	sg.game_id,
//	ad2.unit_id,
//	ad3.creative_id,
//	ad3.programmed_type,
//	cost_date
//from
//    (select platform, creative_id,sum(cost) as cost, min(cost_date) as cost_date from ods_bilibili_creative_hour_data_log group by platform, creative_id) c
//    left join ods_bilibili_creative_log ad3  ON ad3.platform = c.platform and ad3.creative_id = c.creative_id
//    LEFT JOIN ods_bilibili_unit_log ad2 on ad3.platform = ad2.platform and ad3.unit_id = ad2.unit_id
//    LEFT JOIN tanwan_datahub.v2_dim_agent_site_id ag on ad2.platform = ag.platform and ad2.site_id = ag.site_id and CURRENT_TIME() between ag.agent_leader_start_time and ag.agent_leader_end_time
//    LEFT JOIN tanwan_datamedia.dim_site_game_id sg on ad2.platform = sg.platform and ad2.site_id = sg.site_id and CURRENT_TIME() BETWEEN sg.game_start_time and sg.game_end_time
//WHERE
//    ad3.account_id in
//    (2976628,2976629,2976630,2976631,2976632,2976633,2976634,2976635,2976636,2976637,2976638,2976639,2976640,2976641,2976642,2976643,2976644,2976645,2976646,2976647,2976607,2976608,2976609,2976610,2976611,2976612,2976613,2976614,2976615,2976616,2976617,2976618,2976619,2976621,2976622,2976623,2976624,2976625,2976626,2976627,2981741,2981742,2981743,2981744,2981745,2981746,2981747,2981748,2981749,2981750,2983892,2983893,2983894,2983895,2983896,2975385,2975387,2975388,2975390,2975391,2975393,2975395,2975397,2975398,2975400,2975401,2975402,2975403,2975404,2975405,2975406,2975408,2975410,2975412,2975414,2975562,2975564,2975566,2975568,2975571,2975573,2975575,2975577,2975579,2975581,2975583,2975585,2975587,2975588,2975590,2975592,2975593,2975594,2975595,2975597,2974784,3081045,3081046,3081047,3081048,3081049,3081872,3081873,3081874,3081875,3081876,2983930,2983931,2983932,2983933,2983934,2983935,2983936,2983937,2983938,2983939,2983940,2983941,2983942,2983943,2983944,2983945,2983946,2983947,2983948,2983949,2983953,2983954,2983955,2983956,2983957,2983958,2983959,2983960,2983961,2983962,2983963,2983964,2983965,2983966,2983967,2983968,2983969,2983970,2983971,2983972,2983916,3081019,3081020,3081021,3081022,3081023,3081033,3081034,3081035,3081036,3081037,2983623,2983624,2983625,2983626,2983627,2983628,2983629,2983630,2983631,2983632,2983633,2983634,2983635,2983636,2983637,2983639,2983640,2983642,2983644,2983646,2983638,2983641,2983643,2983645,2983647,2983648,2983649,2983650,2983651,2983652,2983653,2983654,2983655,2983656,2983657,2983658,2983659,2983661,2983662,2983663,3041092,3041093,3041094,3041095,3041096,3041097,3041098,3041099,3041100,3041101,3041119,3041120,3041121,3041122,3041123,3041124,3041125,3041126,3041127,3041128,3081001,3081002,3081003,3081004,3081005,3081012,3081013,3081014,3081015,3081016,3041084,2983556,2983557,2983558,2983559,2983560,2983561,2983562,2983563,2983564,2983565,2983566,2983567,2983568,2983569,2983570,2983571,2983572,2983573,2983574,2983575,2983535,2983536,2983537,2983539,2983540,2983541,2983542,2983543,2983544,2983545,2983546,2983547,2983548,2983549,2983550,2983551,2983552,2983553,2983554,2983555,2983898,2983899,2983900,2983901,2983902,2983903,2983904,2983905,2983906,2983907,3085365,3085366,3085367,3085368,3085369,3085403,3085406,3085409,3085412,3085414,3104801,3104803,3104804,3104805,3104806,3104809,3104810,3104811,3104812,3104813)
//    and cost > 0 and c.platform = 'TW' and ag.account_id != ad3.account_id and ag.agent_group_id in (494)
//group by ad3.account_id, sg.game_id, ad2.unit_id
//order by c.cost desc
//";

$now = date('Y-m-d H:i:s');
echo "----------{$now} 开始----------\n";

//$list = MysqlConnection::getConnection('data_media')->select($sql);

$account_json = '
[
    {
        "account_id": "2976634",
        "game_id": "2609"
    },
    {
        "account_id": "2976635",
        "game_id": "2609"
    },
    {
        "account_id": "2976636",
        "game_id": "2609"
    },
    {
        "account_id": "2976637",
        "game_id": "2609"
    },
    {
        "account_id": "2976638",
        "game_id": "2609"
    },
    {
        "account_id": "2976639",
        "game_id": "2609"
    },
    {
        "account_id": "2976640",
        "game_id": "2609"
    },
    {
        "account_id": "2976641",
        "game_id": "2609"
    },
    {
        "account_id": "2976642",
        "game_id": "2609"
    },
    {
        "account_id": "2976643",
        "game_id": "2609"
    },
    {
        "account_id": "2976644",
        "game_id": "2609"
    },
    {
        "account_id": "2976645",
        "game_id": "2609"
    },
    {
        "account_id": "2976646",
        "game_id": "2609"
    },
    {
        "account_id": "2976647",
        "game_id": "2609"
    },
    {
        "account_id": "2976613",
        "game_id": "17722"
    },
    {
        "account_id": "2976614",
        "game_id": "17722"
    },
    {
        "account_id": "2976615",
        "game_id": "17722"
    },
    {
        "account_id": "2976616",
        "game_id": "17722"
    },
    {
        "account_id": "2976617",
        "game_id": "17722"
    },
    {
        "account_id": "2976618",
        "game_id": "17722"
    },
    {
        "account_id": "2976619",
        "game_id": "17722"
    },
    {
        "account_id": "2976621",
        "game_id": "17722"
    },
    {
        "account_id": "2976622",
        "game_id": "17722"
    },
    {
        "account_id": "2976623",
        "game_id": "17722"
    },
    {
        "account_id": "2976624",
        "game_id": "17722"
    },
    {
        "account_id": "2976625",
        "game_id": "17722"
    },
    {
        "account_id": "2976626",
        "game_id": "17722"
    },
    {
        "account_id": "2976627",
        "game_id": "17722"
    },
    {
        "account_id": "2981742",
        "game_id": "2609"
    },
    {
        "account_id": "2981743",
        "game_id": "2609"
    },
    {
        "account_id": "2981744",
        "game_id": "2609"
    },
    {
        "account_id": "2981745",
        "game_id": "2609"
    },
    {
        "account_id": "2981746",
        "game_id": "2609"
    },
    {
        "account_id": "2981747",
        "game_id": "2609"
    },
    {
        "account_id": "2981748",
        "game_id": "2609"
    },
    {
        "account_id": "2981749",
        "game_id": "2609"
    },
    {
        "account_id": "2981750",
        "game_id": "2609"
    },
    {
        "account_id": "2983893",
        "game_id": "17722"
    },
    {
        "account_id": "2983894",
        "game_id": "17722"
    },
    {
        "account_id": "2983895",
        "game_id": "17722"
    },
    {
        "account_id": "2983896",
        "game_id": "17722"
    },
    {
        "account_id": "2975388",
        "game_id": "2609"
    },
    {
        "account_id": "2975390",
        "game_id": "2609"
    },
    {
        "account_id": "2975391",
        "game_id": "2609"
    },
    {
        "account_id": "2975398",
        "game_id": "2609"
    },
    {
        "account_id": "2975400",
        "game_id": "2609"
    },
    {
        "account_id": "2975401",
        "game_id": "2609"
    },
    {
        "account_id": "2975402",
        "game_id": "2609"
    },
    {
        "account_id": "2975403",
        "game_id": "2609"
    },
    {
        "account_id": "2975404",
        "game_id": "2609"
    },
    {
        "account_id": "2975405",
        "game_id": "2609"
    },
    {
        "account_id": "2975406",
        "game_id": "2609"
    },
    {
        "account_id": "2975408",
        "game_id": "2609"
    },
    {
        "account_id": "2975410",
        "game_id": "2609"
    },
    {
        "account_id": "2975412",
        "game_id": "2609"
    },
    {
        "account_id": "2975414",
        "game_id": "2609"
    },
    {
        "account_id": "2975566",
        "game_id": "17722"
    },
    {
        "account_id": "2975568",
        "game_id": "17722"
    },
    {
        "account_id": "2975571",
        "game_id": "17722"
    },
    {
        "account_id": "2975585",
        "game_id": "17722"
    },
    {
        "account_id": "2975587",
        "game_id": "17722"
    },
    {
        "account_id": "2975588",
        "game_id": "17722"
    },
    {
        "account_id": "2975590",
        "game_id": "17722"
    },
    {
        "account_id": "2975592",
        "game_id": "17722"
    },
    {
        "account_id": "2975593",
        "game_id": "17722"
    },
    {
        "account_id": "2975594",
        "game_id": "17722"
    },
    {
        "account_id": "2975595",
        "game_id": "17722"
    },
    {
        "account_id": "2975597",
        "game_id": "17722"
    },
    {
        "account_id": "3081047",
        "game_id": "2609"
    },
    {
        "account_id": "3081048",
        "game_id": "2609"
    },
    {
        "account_id": "3081049",
        "game_id": "2609"
    },
    {
        "account_id": "3081874",
        "game_id": "17722"
    },
    {
        "account_id": "3081875",
        "game_id": "17722"
    },
    {
        "account_id": "3081876",
        "game_id": "17722"
    },
    {
        "account_id": "2983934",
        "game_id": "13528"
    },
    {
        "account_id": "2983935",
        "game_id": "13528"
    },
    {
        "account_id": "2983936",
        "game_id": "13528"
    },
    {
        "account_id": "2983937",
        "game_id": "13528"
    },
    {
        "account_id": "2983938",
        "game_id": "13528"
    },
    {
        "account_id": "2983939",
        "game_id": "13528"
    },
    {
        "account_id": "2983940",
        "game_id": "13528"
    },
    {
        "account_id": "2983941",
        "game_id": "13528"
    },
    {
        "account_id": "2983942",
        "game_id": "13528"
    },
    {
        "account_id": "2983943",
        "game_id": "13528"
    },
    {
        "account_id": "2983944",
        "game_id": "13528"
    },
    {
        "account_id": "2983945",
        "game_id": "13528"
    },
    {
        "account_id": "2983946",
        "game_id": "13528"
    },
    {
        "account_id": "2983947",
        "game_id": "13528"
    },
    {
        "account_id": "2983948",
        "game_id": "13528"
    },
    {
        "account_id": "2983949",
        "game_id": "13528"
    },
    {
        "account_id": "2983959",
        "game_id": "18090"
    },
    {
        "account_id": "2983960",
        "game_id": "18090"
    },
    {
        "account_id": "2983961",
        "game_id": "18090"
    },
    {
        "account_id": "2983962",
        "game_id": "18090"
    },
    {
        "account_id": "2983963",
        "game_id": "18090"
    },
    {
        "account_id": "2983964",
        "game_id": "18090"
    },
    {
        "account_id": "2983965",
        "game_id": "18090"
    },
    {
        "account_id": "2983966",
        "game_id": "18090"
    },
    {
        "account_id": "2983967",
        "game_id": "18090"
    },
    {
        "account_id": "2983968",
        "game_id": "18090"
    },
    {
        "account_id": "2983969",
        "game_id": "18090"
    },
    {
        "account_id": "2983970",
        "game_id": "18090"
    },
    {
        "account_id": "2983971",
        "game_id": "18090"
    },
    {
        "account_id": "2983972",
        "game_id": "18090"
    },
    {
        "account_id": "3081021",
        "game_id": "13528"
    },
    {
        "account_id": "3081022",
        "game_id": "13528"
    },
    {
        "account_id": "3081023",
        "game_id": "13528"
    },
    {
        "account_id": "3081033",
        "game_id": "18090"
    },
    {
        "account_id": "3081034",
        "game_id": "18090"
    },
    {
        "account_id": "3081037",
        "game_id": "18090"
    },
    {
        "account_id": "2983626",
        "game_id": "17110"
    },
    {
        "account_id": "2983627",
        "game_id": "17110"
    },
    {
        "account_id": "2983628",
        "game_id": "17110"
    },
    {
        "account_id": "2983629",
        "game_id": "17110"
    },
    {
        "account_id": "2983630",
        "game_id": "17110"
    },
    {
        "account_id": "2983631",
        "game_id": "17110"
    },
    {
        "account_id": "2983632",
        "game_id": "17110"
    },
    {
        "account_id": "2983633",
        "game_id": "17110"
    },
    {
        "account_id": "2983634",
        "game_id": "17110"
    },
    {
        "account_id": "2983635",
        "game_id": "17110"
    },
    {
        "account_id": "2983636",
        "game_id": "17110"
    },
    {
        "account_id": "2983637",
        "game_id": "17110"
    },
    {
        "account_id": "2983639",
        "game_id": "17110"
    },
    {
        "account_id": "2983640",
        "game_id": "17110"
    },
    {
        "account_id": "2983642",
        "game_id": "17110"
    },
    {
        "account_id": "2983644",
        "game_id": "17110"
    },
    {
        "account_id": "2983646",
        "game_id": "17110"
    },
    {
        "account_id": "2983643",
        "game_id": "17348"
    },
    {
        "account_id": "2983645",
        "game_id": "17348"
    },
    {
        "account_id": "2983647",
        "game_id": "17348"
    },
    {
        "account_id": "2983648",
        "game_id": "17348"
    },
    {
        "account_id": "2983649",
        "game_id": "17348"
    },
    {
        "account_id": "2983650",
        "game_id": "17348"
    },
    {
        "account_id": "2983651",
        "game_id": "17348"
    },
    {
        "account_id": "2983652",
        "game_id": "17348"
    },
    {
        "account_id": "2983653",
        "game_id": "17348"
    },
    {
        "account_id": "2983654",
        "game_id": "17348"
    },
    {
        "account_id": "2983655",
        "game_id": "17348"
    },
    {
        "account_id": "2983656",
        "game_id": "17348"
    },
    {
        "account_id": "2983657",
        "game_id": "17348"
    },
    {
        "account_id": "2983658",
        "game_id": "17348"
    },
    {
        "account_id": "2983659",
        "game_id": "17348"
    },
    {
        "account_id": "2983661",
        "game_id": "17348"
    },
    {
        "account_id": "2983662",
        "game_id": "17348"
    },
    {
        "account_id": "2983663",
        "game_id": "17348"
    },
    {
        "account_id": "3041094",
        "game_id": "17110"
    },
    {
        "account_id": "3041095",
        "game_id": "17110"
    },
    {
        "account_id": "3041096",
        "game_id": "17110"
    },
    {
        "account_id": "3041097",
        "game_id": "17110"
    },
    {
        "account_id": "3041098",
        "game_id": "17110"
    },
    {
        "account_id": "3041099",
        "game_id": "17110"
    },
    {
        "account_id": "3041100",
        "game_id": "17110"
    },
    {
        "account_id": "3041101",
        "game_id": "17110"
    },
    {
        "account_id": "3041123",
        "game_id": "17348"
    },
    {
        "account_id": "3041124",
        "game_id": "17348"
    },
    {
        "account_id": "3041125",
        "game_id": "17348"
    },
    {
        "account_id": "3041126",
        "game_id": "17348"
    },
    {
        "account_id": "3041127",
        "game_id": "17348"
    },
    {
        "account_id": "3041128",
        "game_id": "17348"
    },
    {
        "account_id": "3081003",
        "game_id": "17110"
    },
    {
        "account_id": "3081004",
        "game_id": "17110"
    },
    {
        "account_id": "3081005",
        "game_id": "17110"
    },
    {
        "account_id": "3081014",
        "game_id": "17348"
    },
    {
        "account_id": "3081015",
        "game_id": "17348"
    },
    {
        "account_id": "3081016",
        "game_id": "17348"
    },
    {
        "account_id": "2983559",
        "game_id": "12548"
    },
    {
        "account_id": "2983560",
        "game_id": "12548"
    },
    {
        "account_id": "2983561",
        "game_id": "12548"
    },
    {
        "account_id": "2983562",
        "game_id": "12548"
    },
    {
        "account_id": "2983563",
        "game_id": "12548"
    },
    {
        "account_id": "2983564",
        "game_id": "12548"
    },
    {
        "account_id": "2983565",
        "game_id": "12548"
    },
    {
        "account_id": "2983566",
        "game_id": "12548"
    },
    {
        "account_id": "2983567",
        "game_id": "12548"
    },
    {
        "account_id": "2983568",
        "game_id": "12548"
    },
    {
        "account_id": "2983569",
        "game_id": "12548"
    },
    {
        "account_id": "2983570",
        "game_id": "12548"
    },
    {
        "account_id": "2983571",
        "game_id": "12548"
    },
    {
        "account_id": "2983572",
        "game_id": "12548"
    },
    {
        "account_id": "2983573",
        "game_id": "12548"
    },
    {
        "account_id": "2983574",
        "game_id": "12548"
    },
    {
        "account_id": "2983575",
        "game_id": "12548"
    },
    {
        "account_id": "2983540",
        "game_id": "17718"
    },
    {
        "account_id": "2983541",
        "game_id": "17718"
    },
    {
        "account_id": "2983542",
        "game_id": "17718"
    },
    {
        "account_id": "2983543",
        "game_id": "17718"
    },
    {
        "account_id": "2983544",
        "game_id": "17718"
    },
    {
        "account_id": "2983545",
        "game_id": "17718"
    },
    {
        "account_id": "2983546",
        "game_id": "17718"
    },
    {
        "account_id": "2983547",
        "game_id": "17718"
    },
    {
        "account_id": "2983548",
        "game_id": "17718"
    },
    {
        "account_id": "2983549",
        "game_id": "17718"
    },
    {
        "account_id": "2983550",
        "game_id": "17718"
    },
    {
        "account_id": "2983551",
        "game_id": "17718"
    },
    {
        "account_id": "2983552",
        "game_id": "17718"
    },
    {
        "account_id": "2983553",
        "game_id": "17718"
    },
    {
        "account_id": "2983554",
        "game_id": "17718"
    },
    {
        "account_id": "2983555",
        "game_id": "17718"
    },
    {
        "account_id": "2983900",
        "game_id": "12548"
    },
    {
        "account_id": "2983901",
        "game_id": "12548"
    },
    {
        "account_id": "2983902",
        "game_id": "12548"
    },
    {
        "account_id": "2983906",
        "game_id": "17718"
    },
    {
        "account_id": "2983907",
        "game_id": "17718"
    },
    {
        "account_id": "3085365",
        "game_id": "17718"
    },
    {
        "account_id": "3085412",
        "game_id": "12548"
    },
    {
        "account_id": "3085414",
        "game_id": "12548"
    }
]
'; // 账号JSON
$list = json_decode($account_json, true);

$list = collect($list);

$account_game_list = $list->groupBy(function ($item) {
    return $item['account_id'] . '-' . $item['game_id'];
})->toArray();

$media_logic = new MediaLogic();
$agent_model = new AgentModel();
$site_service = new SiteService();
$user_model = new UserModel();
$site_model = new SiteModel();
$media_account_model = new MediaAccountModel();
$creative_model = new CreativeModel();
$game_model = new V2DimGameIdModel();
$ad2_site_change_model = new DwdMediaAd2SiteChangeLogModel();
$user_list = [];
$site_list = [];
$media_account_list = [];
$game_list = [];
$platform = 'TW';
$site_change_list = [];

echo "----------开始----------\n";

foreach ($account_game_list as $key => $value) {
    try {
        $tmp_item = (array)$value[0];
        echo "----------{$key} 开始----------\n";

        // 创建渠道
//        if ($tmp_item['agent_group_id'] == AgentGroup::BILIBILI_LY_CPS) {
            $agent_data = $cps_data;
            $agent_data['platform'] = $platform;
            $agent_data['agent_group'] = AgentGroup::BILIBILI_LY_CPS;
            $agent_data['account_id'] = $tmp_item['account_id'];
            $agent_data['agent_name'] = '哔哩哔哩发行CPS' . '-特殊-' . $tmp_item['account_id'];
            $agent_data['user_name'] = '哔哩哔哩发行CPS' . '-特殊-' . $tmp_item['account_id'];
            $agent_data['user_pwd'] = 123456;
//        } else {
//            $agent_data = [
//                'platform' => $platform,
//                'media_type' => MediaType::BILIBILI,
//                'account_id' => $tmp_item['account_id'],
//                'agent_name' => $tmp_item['agent_group_name'] . '-特殊-' . $tmp_item['account_id'],
//                'agent_group' => $tmp_item['agent_group_id'],
//                'agent_group_id' => $tmp_item['agent_group_id'],
//                'pay_type' => 5,
//                'statistic_caliber' => 3,
//                'permission_list' => [],
//                'user_name' => '',
//                'user_pwd' => '',
//            ];
//        }

        try {
            $param = new AgentParam($agent_data);
            $media_logic->createAgent($param);
        } catch (Throwable $e) {
            if (strpos($e->getMessage(), '账户已存在') === false) {
                throw new AppException($e->getMessage());
            }
        }

        // 查询最新的渠道ID
        $agent_info = $agent_model->getSpecialDataByPlatformAccountId('TW', $tmp_item['account_id']);
        if (empty($agent_info)) {
            throw new AppException('查询渠道失败');
        }

        // 查询负责人用户信息
        if (!isset($user_list[$agent_info->agent_leader])) {
            $user_list[$agent_info->agent_leader] = $user_model->getDataByName($agent_info->agent_leader);
        }

        $creator_id = 261;
        $creator = '中旭未来';
        if (!empty($user_list[$agent_info->agent_leader])) {
            $creator_id = $user_list[$agent_info->agent_leader]['id'];
            $creator = $agent_info->agent_leader;
        }

        // 查询历史广告位的信息
//        if (!isset($site_list[$tmp_item['ad2_site']])) {
//            $site_list[$tmp_item['ad2_site']] = $site_model->getDataByPlatformSiteId($platform, $tmp_item['ad2_site']);
//        }

        if (!isset($game_list[$tmp_item['game_id']])) {
            $game_list[$tmp_item['game_id']] = $game_model->getDataByGameId($platform, $tmp_item['game_id']);
        }

        $convert_source_type = $game_list[$tmp_item['game_id']]->plat_id == PlatId::MINI ? ConvertSourceType::H5_API :
            ($game_list[$tmp_item['game_id']]->os == '安卓' ? ConvertSourceType::SDK : ConvertSourceType::API);

        $game_pack = $game_list[$tmp_item['game_id']]->os == '安卓' ? 1 : 0;

        // 创建广告位
        $site_data = [
            'creator_id' => $creator_id,
            'creator' => $creator,
            'platform' => $platform,
            'media_type' => MediaType::BILIBILI,
            'account_id' => $tmp_item['account_id'],
            'agent_group_id' => AgentGroup::BILIBILI_LY_CPS,
            'game_id' => $tmp_item['game_id'],
            'game_pack' => $game_pack, // 0-不打包 1-打包 2媒体分包(头条专用)
            'convert_source_type' => $convert_source_type,
            'convert_type' => '',
            'agent_id' => $agent_info->agent_id,
            'agent_group' => $agent_info->agent_group,
            'user_name' => $agent_info->user_name,
            'is_concat' => 1,
            'site_suffix_name' => "",
            'auto_download' => 0,
            'auto_download_second' => 0,
            'cps_divide_rate' => 0,
            'forbid_tuitan' => 0,
            'template_type' => 'nojump.html',
            'template_address' => '',
            'pay_type' => $agent_info->pay_type,
            'state' => 1,
        ];
        $site_param = new SiteConfigParam($site_data);
        $site_param = $site_service->addSite($site_param, $creator_id, $creator);
    } catch (Throwable $e) {
        echo "----------{$key} 失败：{$e->getMessage()}----------\n";
        continue;
    }

    echo "----------{$key} 创建渠道广告位成功----------\n";

//    foreach ($value as $item) {
//        $item = (array)$item;
//        try {
            // 查询账号最新token
//            $media_account_list[$item['account_id']] = $media_account_model->getDataByAccountId($item['account_id'], MediaType::BILIBILI);
//
//            if (empty($media_account_list[$item['account_id']])) {
//                throw new AppException('找不到账号');
//            }
//
//            $app_key = $media_account_list[$item['account_id']]->majordomo_name;
//            $access_token = $media_account_list[$item['account_id']]->access_token;
//
//            $data = json_decode($item['result'], true);
//            $data['unit']['monitor_list'][0]['url'] = $site_param->action_track_url;
//
//            if ($item['programmed_type'] == 1) {
////                $creative_model->updateProgrammedCreative($item['unit_id'], ['customized_click_url' => $site_param->action_track_url], $item['account_id'], $app_key, $access_token);
//            } elseif ($item['programmed_type'] == 2) {
////                $creative_model->updateCustomCreative($item['unit_id'], ['customized_click_url' => $site_param->action_track_url], $item['account_id'], $app_key, $access_token);
//            } elseif ($item['programmed_type'] == 0) {
//                $creative_model->saveCreative($data, $item['account_id'], $app_key, $access_token);
//            } else {
//                throw new AppException('3级创意类型错误');
//            }

            // 直接插修复表
//            $site_change_list[] = [
//                'platform' => $item['platform'],
//                'media_type' => MediaType::BILIBILI,
//                'account_id' => $item['account_id'],
//                'ad2_id' => $item['unit_id'],
//                'site_id' => $site_param->site_id,
//                'game_id' => $item['game_id'],
//                'start_cost_date_hour' => '2025-01-01 00:00:00',
//                'error_site_id' => $item['ad2_site'],
//                'mark' => '财务要求每个账户的消耗需要单独有一个渠道ID',
//            ];
//
//            echo "----------{$item['unit_id']} 完成----------\n";
//
//        } catch (Throwable $e) {
//            echo "----------{$item['unit_id']} 失败：{$e->getMessage()}----------\n";
//        }
//    }
//    echo "\n";
}

//foreach (array_chunk($site_change_list, 1000) as $chunk) {
//    $ad2_site_change_model->replace($chunk);
//}

$now = date('Y-m-d H:i:s');
echo "----------{$now} 全部成功----------\n";
