<?php


use App\Exception\AppException;
use App\Model\SqlModel\Zeda\VideoGetCoverTaskModel;
use App\MysqlConnection;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\VideoGetCoverTaskParam;
use App\Service\MediaAD\MediaToutiao;
use App\Model\HttpModel\Toutiao\Tools\VideoCoverModel;
use App\Model\HttpModel\Toutiao\File\VideoModel;
use Common\EnvConfig;
use App\Utils\Math;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

MysqlConnection::setConnection();
$connection_name = 'default';

//1、素材上传到：账户id：************
//2、获取素材封面并下载到本地https://ad.oceanengine.com/openapi/doc/index.html?id=358
//3、每个小时定时删除已下载封面的视频https://ad.oceanengine.com/openapi/doc/index.html?id=****************"

$account_id = ************;
$media_account_model = new MediaAccountModel();
$acc_info = $media_account_model->getDataByAccountId($account_id);

$media_toutiao = new MediaToutiao();
$to_id = 721103;
//所有视频
$material_files = MysqlConnection::getConnection($connection_name)
                    ->table('material_file')
                    ->where([['file_type', '=', 2], ['create_time', '>=', **********], ['id', '<', $to_id]])
                    ->orderByDesc('id')->get();
$video_get_cover_task_model = new VideoGetCoverTaskModel();
foreach ( $material_files as $material_file) {
    $re = $video_get_cover_task_model->getDataByFileId($material_file->id);
    if (!empty($re)) {
        echo "素材上传任务{$re->id}已存在".PHP_EOL;
        continue;
    }

    $id = $video_get_cover_task_model->add(new VideoGetCoverTaskParam(['material_id' => $material_file->material_id,
        'file_id' => $material_file->id,
        'filename' => $material_file->filename,
        'signature' => $material_file->signature]));

    try {
        $base_name = explode('.', $material_file->filename);
        array_pop($base_name);
        $base_name = implode('.', $base_name);

        //上传
        if (!file_exists("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename")) {
            throw new AppException('素材文件不存在');
        }
        echo 'uploading====='.$material_file->id.'======='.PHP_EOL;
        $video_get_cover_task_model->setTaskUploadStateCode($id, VideoGetCoverTaskModel::START_UPLOAD);
        $file_param = new MaterialFileParam($material_file);
        $media_file_info = $media_toutiao->uploadVideo($file_param, $acc_info->account_id, $acc_info->access_token);
        var_dump($media_file_info);
        $media_file_id = $media_file_info['id'];

        $video_get_cover_task_model->updateById($id, [
            'media_file_id' => $media_file_id,
            'state' => VideoGetCoverTaskModel::SUCCESS_UPLOAD,
            'error_msg' => ''
        ]);

        //等待
        echo 'sleeping=====10======='.PHP_EOL;
        sleep(10);

        $datas = [];
        $data = [];
        $data['platform'] = $material_file->platform;
        $data['media_type'] = $material_file->media_type;
        $data['material_id'] = $material_file->material_id;
        $data['file_type'] = MaterialFileModel::FILE_TYPE_COVER;
        $data['duration'] = $material_file->duration;
        $data['bitrate'] = 0;
        $data['format'] = 'jpeg';
        $data['uploader'] = $material_file->uploader;
        $data['create_time'] = $material_file->create_time;
        $data['update_time'] = $material_file->update_time;
        $data['is_del'] = $material_file->is_del;
        $data['notify'] = $material_file->notify;
        $data['video_hash_0'] = $material_file->video_hash_0;
        $data['video_hash_1'] = $material_file->video_hash_1;
        $data['video_hash_2'] = $material_file->video_hash_2;
        $data['video_hash_3'] = $material_file->video_hash_3;
        $data['video_hash_4'] = $material_file->video_hash_4;
        $data['video_hash_5'] = $material_file->video_hash_5;
        $data['video_hash_6'] = $material_file->video_hash_6;
        $data['video_hash_7'] = $material_file->video_hash_7;
        $data['video_hash_8'] = $material_file->video_hash_8;
        $data['video_hash_9'] = $material_file->video_hash_9;
        $data['video_hash_10'] = $material_file->video_hash_10;

        //下载智能图片
        echo 'fetching====='.$material_file->id.'======='.PHP_EOL;
        $result = (new VideoCoverModel())->getList($acc_info->account_id, $media_file_id, $acc_info->access_token);
        while ($result['status'] === 'RUNNING') {
            //等待
            echo 'sleeping=====10======='.PHP_EOL;
            sleep(10);
            $result = (new VideoCoverModel())->getList($acc_info->account_id, $media_file_id, $acc_info->access_token);
        }
        //var_dump($result);
        if (isset($result['status']) ) {
            if ($result['status'] === 'SUCCESS') {
                $video_get_cover_task_model->setTaskUploadStateCode($id, VideoGetCoverTaskModel::SUCCESS_GET);
                $i = 6;
                foreach ($result['list'] as $image) {
                    $i++;
                    if ($i > 12) {
                        break;
                    }
                    $filename = "$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}_creative_{$i}.jpg";

                    ob_start();
                    readfile($image['url']);
                    $img = ob_get_contents();
                    ob_end_clean();

                    $fp2 = @fopen($filename, "a");
                    fwrite($fp2, $img);
                    fclose($fp2);


                    $data['filename'] = "{$base_name}_creative_{$i}.jpg";
                    $size = getimagesize("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}_creative_{$i}.jpg");
                    $data['width'] = $size[0];
                    $data['height'] = $size[1];
                    $data['scale'] = Math::div($data['width'], $data['height']);
                    $data['url'] = EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/{$base_name}_creative_{$i}.jpg";
                    $data['signature'] = md5_file("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}_creative_{$i}.jpg");
                    $data['size'] = filesize("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}_creative_{$i}.jpg");
                    unset($data['id']);
                    $increment_id = MysqlConnection::getConnection($connection_name)->table('material_file')->insertGetId($data);
                    $data['id'] = $increment_id;
                    $datas[] = $data;
                }
            } elseif ($result['status'] === 'FAILED') {
                throw new AppException('封面生成失败');
            }
        }

        //正式数据要同步data_media
        if (EnvConfig::ENV === 'production') {
            $connection_data_media = 'data_media';
            $datas = array_map(function ($data){
                $data['insert_time'] = date("Y-m-d H:i:s", $data['create_time']);
                $data['update_time'] = date("Y-m-d H:i:s", $data['update_time']);
                unset($data['create_time']);
                return $data;
            }, $datas);
            //print_r($datas);
            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
        }

        $video_get_cover_task_model->setTaskUploadStateCode($id, VideoGetCoverTaskModel::SUCCESS_FIN);

//        //等待
//        sleep(10);
//        //删除视频
//// foreach (['v03033ac0000bun2q9bcdphlft4ksmfg', 'v02033490000buba5k8n607rcbffenq0', 'v02033b80000bub97rdo827likm34120',
////'v03033ed0000bub98sugfjuce2v5hlcg', 'v02033e70000bun4mkan6tf822puukug', 'v02033ad0000bun4mr80kv8f2mc895r0', 'v02033a80000bubbjabrijtbif0hclc0',
////'v03033920000bub6gaegfs9ufh22pmmg', ] as $media_file_id) {
//         echo 'deleting=====' . $media_file_id . '=======' . PHP_EOL;
//         $result = (new VideoModel())->deleteVideos($acc_info->account_id, $acc_info->access_token, [$media_file_id]);
//         var_dump($result);
//// }
    } catch (Throwable $exception) {
        $error_msg = $exception->getMessage();
        $video_get_cover_task_model->setTaskUploadStateCode($id, VideoGetCoverTaskModel::FAIL_UPLOAD, $error_msg);
        var_dump("素材上传任务{$material_file->id}出错，{$error_msg}");
        //return true;
    }
}