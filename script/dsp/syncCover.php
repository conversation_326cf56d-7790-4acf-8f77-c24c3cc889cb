<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要导入的平台'.PHP_EOL);
    die;
}
$platform = $params['p'];
$page = 1;
$rows = 2000;
MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
while (1) {
    $list = MysqlConnection::getConnection($connection_name)
        ->table('material_file')
        ->where('platform', $platform)
        ->where('file_type', MaterialFileModel::FILE_TYPE_COVER)
        ->forPage($page, $rows)
        ->get();
    if ($list->isEmpty()) {
        break;
    }
    $replace_data = [];
    foreach ($list as $item) {
        $item = (array)$item;
        $item['insert_time'] = date("Y-m-d H:i:s", $item['create_time']);
        $item['update_time'] = date("Y-m-d H:i:s", $item['update_time']);
        unset($item['create_time']);
        $replace_data[] = $item;
    }

    MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->replace($replace_data);
    $insert_row = count($replace_data);
    echo "ods_material_file_log{$insert_row}条 ============================>\n";
    $page++;
}