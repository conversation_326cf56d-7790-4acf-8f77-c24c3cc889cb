<?php

use App\MysqlConnection;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$materials = MysqlConnection::getConnection($connection_name)->table('material_bak')
    ->whereRaw("platform = '' AND theme_id >= 2000 AND theme_id <= 3000")
    ->get();

$material_ids = [];
foreach ($materials as $material) {
    if ($material->file_type == 1) {
        $sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
    } elseif ($material->file_type == 2) {
        $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
    } elseif ($material->file_type == 4) {
        $sub_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
    } else {
        return ;
    }

    echo "deleting_material_file==platform={$material->platform}====material_id=={$material->material_id}==================>\n";
    $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
    $upload_path = SRV_DIR . "/{$access_path}";
    $path = "$upload_path/{$material->platform}/{$material->material_id}";
    if (is_dir($path)) {
        delete_dir($path);
    }

    $material_ids[] = $material->material_id;
}

function delete_dir($path)
{
    $del_dir = opendir($path);
    while ($file = readdir($del_dir)) {
        if ($file != "." && $file != "..") {
            $full_path = $path."/".$file;
            if (!is_dir($full_path)) {
                if (file_exists($full_path)) {
                    echo "deleting_material_file==file={$file}=====================>\n";
                    unlink($full_path);
                }
            } else {
                delete_dir($full_path);
            }
        }
    }
    closedir($del_dir);
    rmdir($path);
}

//echo "deleting_ods_material_log=======================>\n";
//MysqlConnection::getConnection($connection_data_media)->table('ods_material_log')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//echo "deleting_ods_material_file_log=======================>\n";
//MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//echo "deleting_ods_rank_material=======================>\n";
//MysqlConnection::getConnection($connection_data_media)->table('ods_rank_material')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//echo "deleting_ods_material_authors_weight=======================>\n";
//MysqlConnection::getConnection($connection_data_media)->table('ods_material_authors_weight')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//echo "deleting_ods_material_effect_grade_log=======================>\n";
//MysqlConnection::getConnection($connection_data_media)->table('ods_material_effect_grade_log')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//
//echo "deleting_material=======================>\n";
//MysqlConnection::getConnection($connection_name)->table('material')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
//echo "deleting_material_file=======================>\n";
//MysqlConnection::getConnection($connection_name)->table('material_file')
//    ->where(['platform' => ''])
//    ->whereIn('material_id', $material_ids)
//    ->delete();
