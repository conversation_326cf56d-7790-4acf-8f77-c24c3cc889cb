<?php

use App\Model\RedisModel\AutoIncrementIdModel;
use App\MysqlConnection;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo "copy material_files============================>\n";




MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$db_materials = MysqlConnection::getConnection($connection_data_media)->select("SELECT
	`material`.*
FROM
	`ods_material_log` AS `material`
	LEFT JOIN (
	SELECT
		`platform`,
		`material_id`,
		group_concat(
			JSON_OBJECT( \"url\", url, \"duration\", duration, \"height\", height, \"width\", width, \"file_type\", file_type, \"notify\", notify )
		) AS file
	FROM
		`ods_material_file_log`
	WHERE
		`file_type` != 3
		AND `is_del` = 0
		AND ( `platform` = '915' )
	GROUP BY
		`platform`,
		`material_id`
	) AS `material_scale` ON `material_scale`.`platform` = `material`.`platform`
	AND `material_scale`.`material_id` = `material`.`material_id`
	LEFT JOIN ( SELECT platform, material_id, group_concat( label_id ) AS labels FROM `ods_material_label` WHERE ( `platform` = '915' ) GROUP BY `platform`, `material_id` ) AS `material_label` ON `material_label`.`platform` = `material`.`platform`
	AND `material_label`.`material_id` = `material`.`material_id`
	LEFT JOIN (
	SELECT
		platform,
		material_id,
		count( * ) AS author_num,
		group_concat( concat( author, \":\", weight, \"%\" ) ) AS authors_weight
	FROM
		`ods_material_authors_weight`
	WHERE
		( `platform` = '915' )
	GROUP BY
		`platform`,
		`material_id`
	) AS `material_authors_weight` ON `material_authors_weight`.`platform` = `material`.`platform`
	AND `material_authors_weight`.`material_id` = `material`.`material_id`
WHERE
	( `material`.`is_del` = 0 )
	AND ( `material`.`platform` = '915' )
	AND `material`.`material_id` IN (
		18879,
        18878,
        18877,
        18873,
        18872,
        18871,
        18870,
        18869,
        18868,
        18867,
        18866,
        18865,
        18864,
        18863,
        18855,
        18847,
        18841,
        18822,
        18821,
        18792,
        18791,
        18780,
        18765,
        18748,
        18743,
        18722,
        18718,
        18717,
        18688,
        18683,
        18671,
        18651,
        18645,
        18644,
        18618,
        18613,
        18610,
        18593,
        18587,
        18575,
        18562,
        18557,
        18550,
        18536,
        18533,
        18519,
        18509,
        18502,
        18485,
        18479,
        18477,
        18452,
        18446,
        18439,
        18420,
        18419,
        18416,
        18395,
        18394,
        18370,
        18327,
        18324,
        18311	

	)
	AND ( `material`.`media_type` = 0 ) ORDER BY material.insert_time ASC");

$material_id_theme = [
    18879 => 1006,
    18878 => 1006,
    18877 => 1006,
    18873 => 1006,
    18872 => 1006,
    18871 => 1006,
    18870 => 1006,
    18869 => 1006,
    18868 => 1006,
    18867 => 1006,
    18866 => 1006,
    18865 => 1006,
    18864 => 1006,
    18863 => 1006,
    18855 => 1006,
    18847 => 1006,
    18841 => 1006,
    18822 => 1006,
    18821 => 1006,
    18792 => 1006,
    18791 => 1006,
    18780 => 1006,
    18765 => 1006,
    18748 => 1006,
    18743 => 1006,
    18722 => 1006,
    18718 => 1006,
    18717 => 1006,
    18688 => 1006,
    18683 => 1006,
    18671 => 1006,
    18651 => 1006,
    18645 => 1006,
    18644 => 1006,
    18618 => 1006,
    18613 => 1006,
    18610 => 1006,
    18593 => 1006,
    18587 => 1006,
    18575 => 1006,
    18562 => 1006,
    18557 => 1006,
    18550 => 1006,
    18536 => 1006,
    18533 => 1006,
    18519 => 1006,
    18509 => 1006,
    18502 => 1006,
    18485 => 1006,
    18479 => 1006,
    18477 => 1006,
    18452 => 1006,
    18446 => 1006,
    18439 => 1006,
    18420 => 1006,
    18419 => 1006,
    18416 => 1006,
    18395 => 1006,
    18394 => 1006,
    18370 => 1006,
    18327 => 1006,
    18324 => 1006,
    18311 => 1006,
];

//foreach ($material_id_theme as $material_id => $theme_id) {
//    $db_material_files = MysqlConnection::getConnection($connection_data_media)
//        ->table('ods_material_file_log')
//        ->where([['platform', '=', 'TW'], ['filename', 'like', "$material_id%"]])
//        ->orderBy('id')
//        ->get();
//
//    foreach ($db_material_files as $material_file) {
//        if ($material_file->material_id == $material_id) {
//            continue;
//        }
//        $data = [];
//        $data['filename'] = str_replace($material_id . '_', $material_file->material_id . '_', $material_file->filename);
//        $data['url'] = str_replace($material_id . '/' . $material_id . '_', $material_file->material_id . '/' . $material_file->material_id . '_', $material_file->url);
//
//        MysqlConnection::getConnection($connection_name)->table('material_file')->where('id', $material_file->id)->update($data);
//        MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->where('id', $material_file->id)->update($data);
//
//        $new_material_id = $material_file->material_id;
//    }
//
//    if (isset($new_material_id)) {
//        $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
//        $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
//        $upload_path = SRV_DIR . "/{$access_path}";
//        $file_path = "$upload_path/TW/$new_material_id";
//        $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($file_path));
//        foreach ($files as $file) {
//            $file_name = $file->getFilename();
//            if (!$file->isDir()) {
//                $new_file_name = str_replace($material_id . '_', $new_material_id . '_', $file_name);
//                echo $file_path . '/' . $file_name . "==>" . $file_path . '/' . $new_file_name . PHP_EOL;
//                rename($file_path . '/' . $file_name, $file_path . '/' . $new_file_name);
//            }
//        }
//        unset($new_material_id);
//    }
//}



foreach ($db_materials as $material) {
    if ((int)$material->file_type === 1) {
        $sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
    } else {
        $sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
    }
    $access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
    $upload_path = SRV_DIR . "/{$access_path}";

    $platform = $material->platform;
    $material_id = $material->material_id;

    $material->platform = 'TW';
    $material->material_id = (new AutoIncrementIdModel())->getNextId(AutoIncrementIdModel::MATERIAL_ID, $material->platform);
    $material->theme_id = $material_id_theme[$material_id];

    $data = (array)$material;
    MysqlConnection::getConnection($connection_data_media)->table('ods_material_log')->insert($data);
    $data['create_time'] = strtotime($data['insert_time']);
    $data['update_time'] = strtotime($data['update_time']);
    unset($data['insert_time']);
    unset($data['cost']);
    unset($data['ori_cost']);
    unset($data['pay_count']);
    unset($data['cost_date_hour_count']);
    unset($data['cost_date_count']);
    unset($data['show']);
    unset($data['convert']);
    unset($data['reg_count']);
    unset($data['total_pay_money']);
    unset($data['day_first_day_pay_money']);
    unset($data['day_reg_uid_count']);
    unset($data['day_first_day_pay_count']);
    unset($data['click']);
    unset($data['approved_rate']);
    unset($data['is_effect_grade7']);
    unset($data['is_effect_grade30']);
    MysqlConnection::getConnection($connection_name)->table('material')->insert($data);

    $db_material_files = MysqlConnection::getConnection($connection_data_media)
        ->table('ods_material_file_log')
        ->where([['platform', '=', $platform], ['material_id', '=', $material_id]])
        ->orderBy('id')
        ->get();

    $datas = [];
    foreach ($db_material_files as $material_file) {
        unset($material_file->id);
        $material_file->platform = 'TW';
        $material_file->material_id = $material->material_id;
        $material_file->url = str_replace("/915/{$material_id}/{$material_id}_", "/TW/{$material_file->material_id}/{$material_file->material_id}_", $material_file->url);
        $material_file->filename = str_replace($material_id . '_', $material_file->material_id . '_', $material_file->filename);
        $data = (array)$material_file;
        $data['create_time'] = strtotime($data['insert_time']);
        $data['update_time'] = strtotime($data['update_time']);
        unset($data['insert_time']);
        unset($data['id']);
        unset($data['is_cost']);
        unset($data['script']);
        unset($data['video_hash_0']);
        unset($data['video_hash_1']);
        unset($data['video_hash_2']);
        unset($data['video_hash_3']);
        unset($data['video_hash_4']);
        unset($data['video_hash_5']);
        unset($data['video_hash_6']);
        unset($data['video_hash_7']);
        unset($data['video_hash_8']);
        unset($data['video_hash_9']);
        unset($data['video_hash_10']);
        $id = MysqlConnection::getConnection($connection_name)->table('material_file')->insertGetId($data);

        $material_file->id = $id;
        $datas[] = (array)$material_file;
    }
    MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);

    $file_path = "$upload_path/$platform/$material_id";
    $to_file_path = "$upload_path/$material->platform/$material->material_id";
    if (!file_exists($to_file_path)) {
        mkdir($to_file_path, 0755, true);
    }
    $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($file_path));
    foreach ($files as $file) {
        $file_name = $file->getFilename();
        if (!$file->isDir()) {
            $new_file_name = str_replace($material_id . '_', $material->material_id . '_', $file_name);
            echo $file_path .'/'. $file_name ."==>". $to_file_path .'/'. $new_file_name . PHP_EOL;
            copy($file_path .'/'. $file_name, $to_file_path .'/'. $new_file_name);
        }
    }

}

echo "copy material_files end============================>\n";