<?php


use App\MysqlConnection;
use Common\EnvConfig;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo "fix combine material_files============================>\n";


MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';


$db_material_files = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_material_file_log as mf')
    ->join('ods_material_log as m', function (JoinClause $join)  {
        $join->on("mf.platform", '=', 'm.platform');
        $join->on("mf.material_id", '=', 'm.material_id');
    })
    ->selectRaw('m.material_id, m.platform, mf.id, mf.filename, mf.signature, mf.file_type')
    ->whereRaw('m.`file_type` IN ( 5, 6, 7 ) and mf.is_ext = 0')
    ->orderByDesc('mf.id')
    ->get();

$datas = [];
foreach ($db_material_files as $value) {
   $datas[$value->platform .'_'. $value->material_id][] = $value;
}

foreach ($datas as $value) {
    $id = 0;
    $old_signature = '';
    $signature = [];
    foreach ($value as $material_file){
        if ($material_file->file_type > 1) {
            $id = $material_file->id;
            $old_signature = $material_file->signature;
        } else {
            $signature[] =  $material_file->signature;
        }
    }
    $signature = array_reverse($signature);
    $num = count($signature);
    $signature = json_encode($signature);

    if ($id == 0 || !in_array($num, [3,4,6])) {

    } else {
        echo "updating material_file==id==$id======signature==$signature======old_signature==$old_signature========>" . PHP_EOL;
        MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->where('id', $id)->update(['signature' => $signature]);
        MysqlConnection::getConnection($connection_name)->table('material_file')->where('id', $id)->update(['signature' => $signature]);
    }
}

