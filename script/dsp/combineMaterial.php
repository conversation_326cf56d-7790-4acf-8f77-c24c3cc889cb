<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

echo "combining material_files============================>\n";

$sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$db_materials = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_material_log')
    ->whereRaw('is_group > 1')
    ->get();

$i = 0;
foreach ($db_materials as $material) {
    $db_material_files = MysqlConnection::getConnection($connection_data_media)
        ->table('ods_material_file_log')
        ->where([['platform', '=', $material->platform], ['material_id', '=', $material->material_id]])
        ->get();

    $file_type = $db_material_files->pluck('file_type')->toArray();
    //未有合并的组图素材
    if (max($file_type) == 1) {$i++;
        echo "combining material_files==$i==platform==$material->platform===material_id===$material->material_id=>\n";

        $material_files_combine = [];
        foreach ($db_material_files as $material_file) {
            $filename = explode('.', $material_file->filename);
            $ext = end($filename);
            $material_files_combine[] = [
                'ext' => $ext,
                'width' => $material_file->width,
                'height' => $material_file->height,
                'signature' => $material_file->signature,
                'path' => "$upload_path/$material->platform/$material->material_id/$material_file->filename",
            ];
        }

        if (count($material_files_combine) > 0) {
            $ext = $material_files_combine[0]['ext'];
            $width = $material_files_combine[0]['width'];
            $height = $material_files_combine[0]['height'];
            $file_num = array_flip(MaterialFileModel::FILE_TYPE_COMBINE);
            $file_name = $material->material_id . '_' . $material->name . '_(' . $material->is_group . '组图).' . $ext;
            $paths = array_map(function ($item) {
                return $item['path'];
            }, $material_files_combine);//print_r($paths);echo "$upload_path/$material->platform/$material->material_id/$file_name"."\n";
            Image::combine($paths, "$upload_path/$material->platform/$material->material_id/$file_name", $width, $height);

            $data = [];
            $data['platform'] = $material->platform;
            $data['material_id'] = $material->material_id;
            $data['media_type'] = $material->media_type;
            $data['file_type'] = MaterialFileModel::FILE_TYPE_COMBINE[$material->is_group];
            $data['filename'] = $file_name;
            $data['url'] = EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material->platform/$material->material_id/$file_name";
            $data['width'] = $width;
            $data['height'] = $height;
            $data['scale'] = Math::div($width, $height);
            $signature = array_map(function ($item) {
                return $item['signature'];
            }, $material_files_combine);
            sort($signature);
            $data['signature'] = json_encode($signature);
            $data['size'] = filesize("$upload_path/$material->platform/$material->material_id/$file_name");
            $data['format'] = $db_material_files[0]->format;
            $data['uploader'] = $db_material_files[0]->uploader;
            $data['create_time'] = strtotime($db_material_files[0]->insert_time);
            $data['update_time'] = strtotime($db_material_files[0]->update_time);//print_r($data);

            $id = MysqlConnection::getConnection($connection_name)->table('material_file')->insertGetId($data);
            $data['id'] = $id;
            $data['insert_time'] = $db_material_files[0]->insert_time;
            $data['update_time'] = $db_material_files[0]->update_time;
            unset($data['create_time']);//print_r($data);
            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($data);
        }
    }
}