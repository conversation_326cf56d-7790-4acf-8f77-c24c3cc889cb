<?php

use App\Constant\MediaType;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要导入的平台'.PHP_EOL);
    die;
}

$platform = $params['p'];



MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datahub = 'datahub';
$agent_list = MysqlConnection::getConnection($connection_name)
    ->table('agent')
    ->select(['agent.*', 'agent_group.name as agent_group_name'])
    ->leftJoin('agent_group', 'agent.agent_group', '=', 'agent_group.id')
    ->where('agent.platform', $platform)
    ->get();

$limit = 2000;
$row = 0;

echo "delete v2_dim_agent_id_tmp=======================>\n";
MysqlConnection::getConnection($connection_datahub)->table('v2_dim_agent_id')->where('platform', $platform)->delete();
echo "importing ============================>\n";
foreach ($agent_list as $agent) {
    $insert_data[] = [
        'platform' => $agent->platform,
        'media_type_id' => $agent->media_type,
        'media_type_name' => MediaType::MEDIA_TYPE_MAP[$agent->media_type],
        'agent_group_id' => $agent->agent_group,
        'agent_group_name' => $agent->agent_group_name,
        'agent_id' => $agent->agent_id,
        'agent_name' => $agent->agent_name,
        'agent_leader' => $agent->agent_leader,
        'agent_leader_start_time' => date("Y-m-d", $agent->create_time) . ' 00:00:00',
        'agent_leader_end_time' => '2100-01-01 00:00:00',
    ];
    $row++;
    if ($row >= $limit) {
        MysqlConnection::getConnection($connection_datahub)->table('v2_dim_agent_id')->replace($insert_data);
        echo "插入{$limit}条 ============================>\n";
        $row = 0;
        $insert_data = [];
    }
}

if (!empty($insert_data)) {
    MysqlConnection::getConnection($connection_datahub)->table('v2_dim_agent_id')->replace($insert_data);
    $insert_row = count($insert_data);
    echo "插入{$insert_row}条 ============================>\n";
}
