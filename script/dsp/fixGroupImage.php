<?php

use App\Exception\AppException;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use App\Utils\Image;
use App\Utils\Math;
use Common\EnvConfig;

require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$datamedia_connection = MysqlConnection::getConnection('data_media');
$mysql_connection = MysqlConnection::getConnection('default');


$material_file_model = new MaterialFileModel();
$ods_material_file_log_model = new OdsMaterialFileLogModel();

$sub_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "{$access_path}";

//$file_type_list = [5, 6, 7];
$file_type_list = [7];
$platform_list = ['wanzi'];
//$platform_list = ['TW'];

$fix_time = 1609135215;
foreach ($file_type_list as $file_type) {
    $fix_file_type = $file_type;
    foreach ($platform_list as $platform) {
        $fix_platform = $platform;
        $material_id_list = $mysql_connection->table('material_file')->select('material_id')->where([
            'file_type' => $fix_file_type,
            'platform' => $fix_platform,
            'material_id' => '31155',
        ])->where('create_time', '<=', $fix_time)->orderBy('id')->get();
        if ($material_id_list->isNotEmpty()) {
            $file_num = array_flip(MaterialFileModel::FILE_TYPE_COMBINE);
            $material_id_list = $material_id_list->pluck('material_id')->toArray();
            foreach ($material_id_list as $material_id) {
                $file_list = $mysql_connection->table('material_file')->where([
                    'material_id' => $material_id,
                    'platform' => $fix_platform,
                ])->orderBy('id')->get();
                if ($file_list->isNotEmpty()) {
                    $origin_image_list = [];
                    $compress_image_list = [];
                    $combine_file_ext = '';
                    $combine_file_name = '';
                    $combine_file_type = '';
                    $combine_format = '';
                    $combine_uploader = '';
                    $platform = $file_list[0]->platform;
                    foreach ($file_list as $file_info) {
                        if ($file_info->file_type == 1) {
                            $name_array = explode('.', $file_info->filename);
                            array_pop($name_array);
                            $name_index = implode('.', $name_array);
                            if ($file_info->is_ext == 1) {
                                $compress_image_list[$name_index] = (array)$file_info;
                            } else {
                                $origin_image_list[$name_index] = (array)$file_info;
                            }
                        } else if (in_array($file_info->file_type, [5, 6, 7]) && $file_info->is_ext != 1) {
                            $combine_name_array = explode('.', $file_info->filename);
                            $combine_file_ext = array_pop($combine_name_array);
                            $combine_file_name = implode('.', $combine_name_array);
                            $combine_file_type = $file_info->file_type;
                            $combine_format = $file_info->format;
                            $combine_uploader = $file_info->uploader;
                        }
                    }

                    if (!$compress_image_list) {
                        foreach ($origin_image_list as $origin_file) {
                            if (($origin_file['size'] / 1024) < 140) {
                                continue;
                            }
                            $ext_array = explode('.', $origin_file['filename']);
                            $ext = array_pop($ext_array);
                            $base_name = implode('.', $ext_array);
                            $ext_name = "{$base_name}_(腾讯投放压缩)800x800.{$ext}";
                            $result = Image::compress(
                                "$upload_path/{$origin_file['platform']}/{$origin_file['material_id']}/{$origin_file['filename']}",
                                "$upload_path/{$origin_file['platform']}/{$origin_file['material_id']}/$ext_name",
                                120
                            );
                            if ($result) {
                                $material_file = [
                                    'platform' => $origin_file['platform'],
                                    'media_type' => 0,
                                    'material_id' => $origin_file['material_id'],
                                    'file_type' => $origin_file['file_type'],
                                    'filename' => $ext_name,
                                    'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/{$origin_file['platform']}/{$origin_file['material_id']}/$ext_name",
                                    'width' => 800,
                                    'height' => 800,
                                    'scale' => Math::div(800, 800),
                                    'signature' => md5_file("$upload_path/{$origin_file['platform']}/{$origin_file['material_id']}/$ext_name"),
                                    'bitrate' => 0,
                                    'size' => filesize("$upload_path/{$origin_file['platform']}/{$origin_file['material_id']}/$ext_name"),
                                    'format' => $origin_file['format'],
                                    'uploader' => $origin_file['uploader'],
                                    'create_time' => time(),
                                    'update_time' => time(),
                                    'is_del' => 0,
                                    'is_ext' => 1
                                ];
                                $material_file['id'] = $material_file_model->add($material_file);
                                if ($material_file['id']) {
                                    $compress_image_list["{$base_name}_(腾讯投放压缩)800x800"] = $material_file;
                                    if (EnvConfig::ENV === 'production' && $material_file['id']) {
                                        $material_file['insert_time'] = date("Y-m-d H:i:s", $material_file['create_time']);
                                        $material_file['update_time'] = date("Y-m-d H:i:s", $material_file['update_time']);
                                        unset($material_file['create_time']);
                                        $ods_material_file_log_model->add($material_file);
                                    }
                                    echo "插入压缩单图成功,id为{$material_file['id']}" . PHP_EOL;
                                }
                            } else {
                                throw new AppException('压缩腾讯图片失败');
                            }
                        }
                    }

                    // 组图特殊需求，超过140k的图片压缩后再合成
                    $file_name = "{$combine_file_name}(腾讯投放压缩)";
                    $material_files_url_combine_limit = [];
                    $material_files_md5_combine_limit = [];
                    foreach ($origin_image_list as $name_key => $material_file) {
                        $common_url = "$upload_path/{$material_file['platform']}/{$material_file['material_id']}";
                        if (isset($compress_image_list["{$name_key}_(腾讯投放压缩)800x800"])) {
                            $material_files_url_combine_limit[] = "{$common_url}/{$compress_image_list["{$name_key}_(腾讯投放压缩)800x800"]['filename']}";
                            $material_files_md5_combine_limit[] = $compress_image_list["{$name_key}_(腾讯投放压缩)800x800"]['signature'];

                        } else {
                            $material_files_url_combine_limit[] = "{$common_url}/{$material_file['filename']}";
                            $material_files_md5_combine_limit[] = $material_file['signature'];
                        }
                    }

                    if (!$material_file_model->getDataByName("{$file_name}.{$combine_file_ext}", $fix_platform)) {
                        Image::combine($material_files_url_combine_limit, "$upload_path/$platform/$material_id/$file_name.{$combine_file_ext}", 800, 800);
                        $material_file = [
                            'platform' => $platform,
                            'media_type' => 0,
                            'material_id' => $material_id,
                            'file_type' => $combine_file_type,
                            'filename' => "{$file_name}.{$combine_file_ext}",
                            'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$platform/$material_id/$file_name.{$combine_file_ext}",
                            'width' => 800,
                            'height' => 800,
                            'scale' => Math::div(800, 800),
                            'signature' => json_encode($material_files_md5_combine_limit),
                            'bitrate' => 0,
                            'size' => filesize("$upload_path/$platform/$material_id/$file_name.{$combine_file_ext}"),
                            'format' => $combine_format,
                            'uploader' => $combine_uploader,
                            'create_time' => time(),
                            'update_time' => time(),
                            'is_del' => 0,
                            'is_ext' => 1
                        ];
                        $material_file['id'] = $material_file_model->add($material_file);
                        if ($material_file['id']) {
                            if (EnvConfig::ENV === 'production' && $material_file['id']) {
                                $material_file['insert_time'] = date("Y-m-d H:i:s", $material_file['create_time']);
                                $material_file['update_time'] = date("Y-m-d H:i:s", $material_file['update_time']);
                                unset($material_file['create_time']);
                                $ods_material_file_log_model->add($material_file);
                            }
                            echo "插入压缩合成图成功,id为{$material_file['id']}" . PHP_EOL;
                        }
                    } else {
                        echo "文件{$file_name}.{$combine_file_ext}已存在,退出压缩逻辑" . PHP_EOL;
                    }


                }
            }
        }
    }
}
