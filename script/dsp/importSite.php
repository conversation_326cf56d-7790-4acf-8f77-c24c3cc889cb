<?php

use App\Constant\MediaType;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要导入的平台'.PHP_EOL);
    die;
}

$platform = $params['p'];

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datahub = 'datahub';
$connection_datamedia = 'data_media';

$page = 1;
$rows = 5000;
echo "delete v2_dim_site_id=======================>\n";
MysqlConnection::getConnection($connection_datahub)->table('v2_dim_site_id')->where('platform', $platform)->delete();
echo "delete dim_site_game_id=======================>\n";
MysqlConnection::getConnection($connection_datamedia)->table('dim_site_game_id')->where('platform', $platform)->delete();
echo "importing ============================>\n";
while (1) {
    $site_list = MysqlConnection::getConnection($connection_name)
        ->table('site')
        ->where('platform', $platform)
        ->forPage($page, $rows)
        ->get();
    if ($site_list->isEmpty()) {
        break;
    }

    $insert_site_data = $site_list->map(function ($item) {
        return [
            'agent_id' => $item->agent_id,
            'site_id' => $item->site_id,
            'site_name' => $item->site_name,
            'platform' => $item->platform,
        ];
    })->toArray();
    MysqlConnection::getConnection($connection_datahub)->table('v2_dim_site_id')->insert($insert_site_data);
    $insert_row = count($insert_site_data);
    echo "v2_dim_site_id插入{$insert_row}条 ============================>\n";

    $insert_site_game_data = $site_list->map(function ($item) {
        return [
            'site_id' => $item->site_id,
            'game_id' => $item->game_id,
            'platform' => $item->platform,
            'convert_id' => $item->convert_id ?: 0,
            'game_start_time' => date("Y-m-d H", $item->create_time) . ':00:00',
            'game_end_time' => '2100-01-01 00:00:00',
        ];
    })->toArray();
    MysqlConnection::getConnection($connection_datamedia)->table('dim_site_game_id')->insert($insert_site_game_data);
    echo "dim_site_game_id插入{$insert_row}条 ============================>\n";

    $page++;
}
