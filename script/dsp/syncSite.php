<?php

use App\Model\SqlModel\DataMedia\DimSiteGameIdModel;
use App\MysqlConnection;
use App\Service\OuterService;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要同步site的平台' . PHP_EOL);
    die;
}

$platform = $params['p'];

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datahub = 'datahub';

$page = 1;
$rows = 1000;
echo "importing ============================>\n";
while (1) {
    $site_list = MysqlConnection::getConnection($connection_name)
        ->table('site')
        ->select('site.*', 'agent.statistic_caliber')
        ->join('agent', function (JoinClause $join) {
            $join->on('agent.platform', '=', 'site.platform');
            $join->on('agent.agent_id', '=', 'site.agent_id');
        })
        ->where('site.platform', $platform)
        ->forPage($page, $rows)
        ->orderBy('site_id')
        ->get();
    if ($site_list->isEmpty()) {
        break;
    }
    $insert_site_data = [];
    foreach ($site_list as $item) {
        $settlement_type = (new OuterService())->switchSettlementPayType('', $item->pay_type);

        $settlement_base_value = 0;
        if ($settlement_type === 'cps' && $item->cps_divide_rate != -1) {
            $settlement_base_value = $item->cps_divide_rate / 100;
        } else if ($settlement_type === 'cpa' && $item->ad_price != -1) {
            $settlement_base_value = $item->ad_price;
        }

        $insert_site_data[] = [
            "agent_id" => $item->agent_id,
            "site_id" => $item->site_id,
            "site_name" => $item->site_name,
            "platform" => $item->platform,
            "settlement_type" => $settlement_type,
            "settlement_base_value" => $settlement_base_value,
            "deduction_type" => $item->ad_pop_zk_type,
            "deduction_value" => $item->ad_pop_zk / 100,
            "statistical_type" => $item->statistic_caliber,
            "tax_rate" => $item->tax_rate,
            "channel_fee_rate" => $item->channel_fee_rate,
            "create_time" => date('Y-m-d H:i:s', $item->create_time),
            "app_android_channel_package_id" => $item->app_android_channel_package_id,
            "convert_id" => $item->convert_id ?: 0,
            "convert_source_type" => $item->convert_source_type,
            "upt_state" => $item->upt_state,
            "media_type_id" => $item->media_type,
            "account_id" => $item->account_id,
            "is_remote" => 1,
        ];
    }
    MysqlConnection::getConnection($connection_datahub)->table('v2_dim_site_id')->replace($insert_site_data);
    $insert_row = count($insert_site_data);
    echo "v2_dim_site_id替换{$insert_row}条 ============================>\n";
    $insert_site_data = [];
    $page++;
}
