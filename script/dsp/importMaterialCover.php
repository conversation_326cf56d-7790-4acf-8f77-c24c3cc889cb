<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';


echo "importing material_files covers============================>\n";
$db_material_files = MysqlConnection::getConnection($connection_name)
    ->table('material_file as t1')
    ->where('file_type', MaterialFileModel::FILE_TYPE_VIDEO)
    ->whereRaw('! EXISTS(SELECT * from material_file WHERE platform = t1.platform and file_type =3 and material_id = t1.material_id AND filename like CONCAT(SUBSTRING_INDEX( t1.`filename`, ".", 1 ), "_creative%" ) )')
    ->get();
$all_datas = $datas = [];
$times = [1, 2, 3, 4, 5, 6];
$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";
$id = MysqlConnection::getConnection($connection_name)->table('material_file')->max('id');
foreach ($db_material_files as $material_file) {
    $id++;
    $url = $material_file->url;
    $data = ['id' => $id,
        'platform' => $material_file->platform,
        'media_type' => $material_file->media_type,
        'material_id' => $material_file->material_id,
        'file_type' => $material_file->file_type,
        'filename' => $material_file->filename,
        'url' => $url,
        'width' => $material_file->width,
        'height' => $material_file->height,
        'scale' => $material_file->scale,
        'signature' => $material_file->md5,
        'duration' => $material_file->duration,
        'bitrate' => $material_file->bitrate,
        'size' => $material_file->size,
        'format' => $material_file->format,
        'uploader' => $material_file->uploader,
        'create_time' => $material_file->create_time,
        'update_time' => $material_file->update_time,
        'is_del' => $material_file->is_del,
        'notify' => $material_file->notify,
        'video_hash_0' => $material_file->video_hash_0,
        'video_hash_1' => $material_file->video_hash_1,
        'video_hash_2' => $material_file->video_hash_2,
        'video_hash_3' => $material_file->video_hash_3,
        'video_hash_4' => $material_file->video_hash_4,
        'video_hash_5' => $material_file->video_hash_5,
        'video_hash_6' => $material_file->video_hash_6,
        'video_hash_7' => $material_file->video_hash_7,
        'video_hash_8' => $material_file->video_hash_8,
        'video_hash_9' => $material_file->video_hash_9,
        'video_hash_10' => $material_file->video_hash_10
    ];
//    $all_datas[] = $data;
//    $datas[] = $data;
    //视频插入截图数据
    if ($data['file_type'] == MaterialFileModel::FILE_TYPE_VIDEO) {
        $filename = explode('.', $data['filename']);
        if (is_array($filename) && count($filename) > 1) {
            array_pop($filename);
            $url = explode('/', $data['url']);
            array_pop($url);
            foreach ($times as $value) {
                //按时间截图
                $ext_name = implode('.', $filename) . '_creative_' . $value . '.jpg';
                if (file_exists("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name")) {
                    $sub_data = $data;
                    $id++;
                    $sub_data['id'] = $id;
                    $sub_data['file_type'] = MaterialFileModel::FILE_TYPE_COVER;
                    $sub_data['filename'] = $ext_name;
                    $sub_data['url'] = implode('/', $url) . "/$ext_name";
                    $sub_data['signature'] = md5_file("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name");
                    $sub_data['bitrate'] = 0;
                    $sub_data['size'] = filesize("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name");
                    $sub_data['format'] = 'jpeg';

                    $all_datas[] = $sub_data;
                    $datas[] = $sub_data;
                }
            }
        }
    }
    if (count($datas) >= 1000) {
        try {
            MysqlConnection::getConnection($connection_name)->table('material_file')->insert($datas);
            $datas = [];
        } catch (\Exception $e) {
            print_r($datas);
            Helpers::getLogger('import-material-file')->error("插入素材文件失败", [
                'data' => $datas,
                'error_message' => $e->getMessage()
            ]);
        }
    }
}

if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_name)->table('material_file')->insert($datas);
}

echo "importing datamedia material_files covers============================>\n";
$datas = [];
foreach ($all_datas as $data) {
    $data['insert_time'] = date("Y-m-d H:i:s", $data['create_time']);
    $data['update_time'] = date("Y-m-d H:i:s", $data['update_time']);
    unset($data['create_time']);
    $datas[] = $data;
    try {
        if (count($datas) >= 1000) {
            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
            $datas = [];
        }
    } catch (\Exception $e) {
        print_r($datas);
        Helpers::getLogger('import-material-file-datamedia')->error("插入素材文件失败", [
            'data' => $datas,
            'error_message' => $e->getMessage()
        ]);
    }
}
if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
}