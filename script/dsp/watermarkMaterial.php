<?php

use App\MysqlConnection;
use Common\EnvConfig;
use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$params = getopt('p:f:');
if (!isset($params['p'])) {
    print_r('-p传入需要导入的平台' . PHP_EOL);
    die;
}
$platform = $params['p'];
if (!isset($params['f'])) {
    print_r('-f传入需要开始的id'.PHP_EOL);
    die;
}
$from_id = $params['f'];

echo "watering material_files============================>\n";

$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

$process_num = 16;

MysqlConnection::setConnection();
$connection_name = 'default';
$db_material_files = MysqlConnection::getConnection($connection_name)
                    ->table('material_file')
                    ->where([['platform', '=', $platform], ['file_type', '=', 2], ['id', '>', $from_id]])
                    ->orderByDesc('id')
                    ->get();
$size = ceil(count($db_material_files) / $process_num);
$material_files = $db_material_files->chunk($size);

for ($n = 1; $n <= $process_num; $n++) {
    $pid = pcntl_fork();
    if ($pid < 0) {
        echo "创建进程失败" . PHP_EOL;
        exit;
    }
    if ($pid === 0) {
        //
        echo "watering process==={$n}=========================>\n";
        if (isset($material_files[$n - 1])) {
            waterFiles($material_files[$n - 1], $upload_path);
        }
        exit;
    }
}

function waterFiles($material_files, $upload_path)
{
    $ffmpeg = FFMpeg::create();
    foreach ($material_files as $material_file) {

        $file_path = "$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}";
        if (file_exists($file_path)) {
            $base_name = explode('.', $material_file->filename);
            array_pop($base_name);
            $base_name = implode('.', $base_name);

            if (!file_exists("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}" . ".wm")) {
                echo "watering material_files==={$material_file->id}=========================>\n";
                //给视频加水印
                $width = $material_file->width;
                $height = $material_file->height;
                $duration = $material_file->duration;
                $water_open_video = $ffmpeg->open($file_path);
                $coordinates = array('x' => '(' . $width / $duration . ')*t', 'y' => '(' . $height / $duration . ')*t');
                $water_open_video->filters()->watermark("$upload_path/watermark/shuiyin.png", $coordinates);
                $watered_file_name = $base_name . '(watered).mp4';
                $water_open_video->save(new X264('libfdk_aac'), "$upload_path/{$material_file->platform}/{$material_file->material_id}/$watered_file_name");
                $new_name = $base_name . '.wm';
                rename("$upload_path/{$material_file->platform}/{$material_file->material_id}/$watered_file_name", "$upload_path/{$material_file->platform}/{$material_file->material_id}/$new_name");

            }
        }
    }
}