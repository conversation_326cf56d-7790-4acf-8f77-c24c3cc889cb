<?php

use App\Constant\AgentType;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$agent_type_list = AgentType::AGENT_TYPE_MAP;
$tanwan_agent_type_list = [
    9 => '联运商店',
    11 => 'APP',
    14 => 'CPS',
    16 => '联运平台',
    21 => '竞价投放',
    22 => '媒体自然量',
    23 => 'WAP站群',
    25 => '摊销',
    26 => '代言人品宣',
];
$connection_name = 'default';
$user_list = MysqlConnection::getConnection($connection_name)->table('user')->get();
$media_type_list = MysqlConnection::getConnection($connection_name)->table('media_type')->get();
$agent_group_list = MysqlConnection::getConnection($connection_name)->table('agent_group')->get();
$tanwan_agent_group_list = MysqlConnection::getConnection($connection_name)->table('tanwan_agent_type')->get();
$tanwan_agent_list = MysqlConnection::getConnection($connection_name)->table('tanwan_agent')->get();
$agent_list = MysqlConnection::getConnection($connection_name)->table('agent')->get();
//$media_agent_list = MysqlConnection::getConnection($connection_name)->table('media_agent')->get();
$agent_data = [];
$insert_limit = 500;
foreach ($tanwan_agent_list as $tanwan_agent) {
    $agent_group_info = getAgentGroupByAgentTypeId($tanwan_agent->agent_type_id);
    $agent_group = $agent_group_info->id;
    $media_type = $agent_group_info->media_type;
    // 寻找user_id
    $user = $user_list->where('name', $tanwan_agent->chargeman)->first();
    $agent_leader = $tanwan_agent->chargeman;

    // 寻找media_account_id
    $account_id = 0;
//    $media_agent_info = $media_agent_list->where('platform', 'TW')->where('agent_id', $tanwan_agent->agent_id)->first();
//    if (!empty($media_agent_info)) {
//        $account_id = $media_agent_info->account_id;
//    }
    $bank_area = '';
    $bank_name = '';
    if (!empty($tanwan_agent->bank)) {
        $bank = explode(' ', $tanwan_agent->bank);
        $bank_area = ($bank[0] === '请选择' || $bank[1] === '请选择') ? '' : implode('/', [$bank[0], $bank[1]]);
        $bank_name = implode(' ', [$bank[2], $bank[3]]);
    }

    $agent_data[] = [
        'platform' => 'TW',
        'account_id' => $account_id,
        'media_type' => $media_type,
        'agent_group' => $agent_group,
        'agent_id' => $tanwan_agent->agent_id,
        'agent_name' => $tanwan_agent->agent_name,
        'agent_leader' => $agent_leader,
        'agent_type' => getAgentTypeByAgentType($tanwan_agent->agent_type),
        'own' => $tanwan_agent->own,
        'account_type' => $tanwan_agent->nature,
        'id_card' => $tanwan_agent->idcard ?: '',
        'user_name' => $tanwan_agent->user_name,
        'user_pwd' => $tanwan_agent->user_pwd,
        'bank_holder' => $tanwan_agent->account_name ?: '',
        'bank_name' => $bank_name,
        'bank_area' => $bank_area,
        'bank_card_number' => $tanwan_agent->account ?: '',
        'person' => $tanwan_agent->person ?: '',
        'qq' => $tanwan_agent->qq ?: '',
        'email' => $tanwan_agent->email ?: '',
        'mobile' => $tanwan_agent->mobile ?: '',
        'tel' => $tanwan_agent->tel ?: '',
        'protocol_number' => $tanwan_agent->contract_id ?: '',
        'protocol_type' => $tanwan_agent->contract_type,
        'creator_id' => 1,
        'creator' => '超管',
        'create_time' => strtotime($tanwan_agent->reg_date),
        'update_time' => time(),
        'state' => $tanwan_agent->auth,
    ];
    if (count($agent_data) >= $insert_limit) {
        MysqlConnection::getConnection($connection_name)->table('tmp_tanwan_agent')->insert($agent_data);
        $agent_data = [];
        print_r("插入{$insert_limit}条");
    }

}
if (count($agent_data) > 0) {
    MysqlConnection::getConnection($connection_name)->table('tmp_tanwan_agent')->insert($agent_data);
}

function getAgentGroupByAgentTypeId($agent_type_id)
{
    global $agent_group_list, $tanwan_agent_group_list;
    $tanwan_agent_group = $tanwan_agent_group_list->where('id', $agent_type_id)->first();
    if (empty($tanwan_agent_group)) {
        return (object)['media_type' => 0, 'id' => 0];
    }
    $agent_group = $agent_group_list->where('name', $tanwan_agent_group->name)->first();
    return $agent_group;
}

function getAgentTypeByAgentType($tanwan_agent_type)
{
    global $agent_type_list, $tanwan_agent_type_list;
    if (!isset($tanwan_agent_type_list[$tanwan_agent_type])) {
        return 0;
    }
    $agent_type = array_search($tanwan_agent_type_list[$tanwan_agent_type], $agent_type_list);
    return $agent_type;
}
