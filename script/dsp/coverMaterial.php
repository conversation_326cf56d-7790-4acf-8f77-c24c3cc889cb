<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

//$params = getopt('p:f:');
//if (!isset($params['p'])) {
//    print_r('-p传入需要导入的平台'.PHP_EOL);
//    die;
//}
//$platform = $params['p'];
//if (!isset($params['f'])) {
//    print_r('-f传入需要开始的id'.PHP_EOL);
//    die;
//}
//$from_id = $params['f'];

echo "covering material_files============================>\n";

$material_files = [];
$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";
$times = [1, 2, 3, 4, 5, 6];
$ffmpeg = FFMpeg::create();

MysqlConnection::setConnection();
$connection_name = 'default';
//$id = MysqlConnection::getConnection($connection_name)->table('material_file')->max('id');
//$db_material_files = MysqlConnection::getConnection($connection_name)
//                    ->table('material_file')
//                    ->where([['platform', '=', $platform], ['file_type', '=', 2], ['id', '>', $from_id]])
//                    ->orderByDesc('id')->get();
//foreach ($db_material_files as $material_file) {
//
//    $file_path = "$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}";
//    if (file_exists($file_path)) {
//        $base_name = explode('.', $material_file->filename);
//        array_pop($base_name);
//        $base_name = implode('.', $base_name);
//        //不存在截图的视频截图并插入数据记录
//        if(!file_exists("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$base_name}_creative_1.jpg")) {
//            echo "covering material_files==={$material_file->id}=========================>\n";
//            $open_video = $ffmpeg->open($file_path);
//
//            //时长比例
//            $duration = $open_video->getStreams()->videos()->first()->get("duration");
//            $width = $material_file->width;
//            $height = $material_file->height;
//            $times = array_map(function ($value) use ($duration) {
//                return (int)($duration * $value);
//            }, MaterialFileModel::MATERIAL_VIDEO_TIME_PERCENT);
//            $data = [];
//            $data['platform'] = $material_file->platform;
//            $data['media_type'] = $material_file->media_type;
//            $data['material_id'] = $material_file->material_id;
//            $data['file_type'] = MaterialFileModel::FILE_TYPE_COVER;
//            $data['width'] = $width;
//            $data['height'] = $height;
//            $data['scale'] = Math::div($width, $height);
//            $data['duration'] = $duration;
//            $data['bitrate'] = 0;
//            $data['format'] = 'jpeg';
//            $data['uploader'] = $material_file->uploader;
//            $data['create_time'] = $material_file->create_time;
//            $data['update_time'] = $material_file->update_time;
//            $data['is_del'] = $material_file->is_del;
//            $data['notify'] = $material_file->notify;
//            $data['video_hash_0'] = $material_file->video_hash_0;
//            $data['video_hash_1'] = $material_file->video_hash_1;
//            $data['video_hash_2'] = $material_file->video_hash_2;
//            $data['video_hash_3'] = $material_file->video_hash_3;
//            $data['video_hash_4'] = $material_file->video_hash_4;
//            $data['video_hash_5'] = $material_file->video_hash_5;
//            $data['video_hash_6'] = $material_file->video_hash_6;
//            $data['video_hash_7'] = $material_file->video_hash_7;
//            $data['video_hash_8'] = $material_file->video_hash_8;
//            $data['video_hash_9'] = $material_file->video_hash_9;
//            $data['video_hash_10'] = $material_file->video_hash_10;
//            //按时间截图
//            foreach ($times as $key => $value) {
//                $id++;
//                //按时间截图
//                $ext_name = $base_name . '_creative_' . ($key + 1) . '.jpg';
//                if ($key === 5) {
//                    $value = $value >= 1 ? $value - 1 : 0;
//                }
//                $second = TimeCode::fromSeconds($value);
//                $frame = $open_video->frame($second);//提取第几秒的图像
//                $frame->save("$upload_path/$material_file->platform/$material_file->material_id/$ext_name");
//
//                $data['id'] = $id;
//                $data['filename'] = $ext_name;
//                $data['url'] = EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/$ext_name";
//                $data['signature'] = md5_file("$upload_path/$material_file->platform/$material_file->material_id/$ext_name");
//                $data['size'] = filesize("$upload_path/$material_file->platform/$material_file->material_id/$ext_name");
//                MysqlConnection::getConnection($connection_name)->table('material_file')->insert($data);
//                //print_r($data);
//
//                $material_files[] = $data;
//            }
//        }
//    }
//}
//
////print_r($material_files);
////
//echo "covering datamedia material_files============================>\n";
//$datas = [];
//$connection_data_media = 'data_media';
//foreach ($material_files as $data) {
//    $data['insert_time'] = date("Y-m-d H:i:s", $data['create_time']);
//    $data['update_time'] = date("Y-m-d H:i:s", $data['update_time']);
//    unset($data['create_time']);
//    $datas[] = $data;
//    try {
//        if (count($datas) >= 1000) {
//            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
//            $datas = [];
//        }
//    } catch (\Exception $e) {
//        print_r($datas);
//        Helpers::getLogger('import-material-file-datamedia')->error("插入素材文件失败", [
//            'data' => $datas,
//            'error_message' => $e->getMessage()
//        ]);
//    }
//}
//if (count($datas) > 0) {
//    MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
//}



$connection_data_media = 'data_media';
$db_material_files = MysqlConnection::getConnection($connection_data_media)
    ->table('ods_material_file_log')
    ->select(['id','platform','material_id','file_type','filename', 'signature'])
    ->where([['file_type', '=', 3]])
    ->orderByDesc('material_id')
    ->get();
$datas1 = $datas2 =  [];
foreach ($db_material_files as $material_file) {
    if (file_exists("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}")) {
        $signature = md5_file("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
        if ($signature != $material_file->signature) {
            echo "material_file==filename={$material_file->filename}====md5=={$material_file->signature}!={$signature}==================>\n";
            $size = filesize("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
//            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')
//                ->where(['id' => $material_file->id])
//                ->update(['signature' => $signature, 'size' => $size]);
//            MysqlConnection::getConnection($connection_name)->table('material_log')
//                ->where(['id' => $material_file->id])
//                ->update(['signature' => $signature, 'size' => $size]);
            $datas1[] = ['id' => $material_file->id ,'signature' => $signature, 'size' => $size];
        }
    } else {
        $file_name = substr($material_file->filename,0,-15).'.mp4';
        if (file_exists("$upload_path/{$material_file->platform}/{$material_file->material_id}/$file_name")) {
            echo "material_file==filename={$material_file->filename}====md5=={$material_file->signature}===================>\n";
            $open_video = $ffmpeg->open("$upload_path/{$material_file->platform}/{$material_file->material_id}/$file_name");
            $num = substr($material_file->filename,-5,1);
            $value = (int)($material_file->duration * MaterialFileModel::MATERIAL_VIDEO_TIME_PERCENT[$num-1]);
            $second = TimeCode::fromSeconds($value);
            $frame = $open_video->frame($second);//提取第几秒的图像
            $frame->save("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}", true);
            $signature = md5_file("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
            $size = filesize("$upload_path/{$material_file->platform}/{$material_file->material_id}/{$material_file->filename}");
//            MysqlConnection::getConnection($connection_data_media)
//                ->table('ods_material_file_log')
//                ->where(['id' = >$material_file->id])
//                ->update(['signature' => $signature, 'size' => $size]);
//            MysqlConnection::getConnection($connection_name)
//                ->table('material_log')
//                ->where(['id' => $material_file->id])
//                ->update(['signature' => $signature, 'size' => $size]);
            $datas2[] = ['id' => $material_file->id ,'signature' => $signature, 'size' => $size];
        }
    }
}
print_r($datas1);
print_r($datas2);

