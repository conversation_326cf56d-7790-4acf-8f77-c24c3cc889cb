<?php

use App\MysqlConnection;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$upload_path = SRV_DIR . '/' . EnvConfig::UPLOAD_PATH . '/' . EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$file_path = SRV_DIR . '/' . EnvConfig::UPLOAD_PATH;

$params = getopt('f:');
if (!isset($params['f'])) {
    print_r('-f 文件绝对路径' . PHP_EOL);
    die;
}
echo "export material_files============================>\n";

$sql_file = $params['f'];
$file_info = pathinfo($sql_file);

$file_name = $file_info['filename'] . '.zip';
/* example for file_content
     SELECT
        `material`.*,
        `material_scale`.`file`
    FROM
        `ods_material_log` AS `material`
        LEFT JOIN (
        SELECT
            `platform`,
            `material_id`,
            group_concat(
                JSON_OBJECT( "filename", filename, "duration", duration, "height", height, "width", width, "file_type", file_type, "notify", notify )
            ) AS file
        FROM
            `ods_material_file_log`
        WHERE
            `file_type` != 3
            AND `is_del` = 0
            AND ( `platform` = 'hktw' )
        GROUP BY
            `platform`,
            `material_id`
        ) AS `material_scale` ON `material_scale`.`platform` = `material`.`platform`
        AND `material_scale`.`material_id` = `material`.`material_id`
    WHERE
        ( `material`.`is_del` = 0 )
        AND ( `material`.`platform` = 'hktw' )
        AND `material`.`theme_id` IN ( 2001, 2002, 2003, 2004, 2005 )
        AND ( `material`.`media_type` = 0 )
 */
$file_content = file_get_contents($sql_file);

MysqlConnection::setConnection();
$db_materials = MysqlConnection::getConnection('data_media')->select($file_content);

$zip = new ZipArchive();
$zip->open($file_path . '/' . $file_name, ZipArchive::OVERWRITE | ZipArchive::CREATE);
foreach ($db_materials as $material) {
    $db_material_files = json_decode("[" . $material->file . "]", true);
    foreach ($db_material_files as $material_file) {
        $filename = $material_file['filename'];
        if (file_exists("$upload_path/$material->platform/$material->material_id/$filename")) {
            echo "pack $material->platform/$material->material_id/$filename\n";
            $zip->addFile("$upload_path/$material->platform/$material->material_id/$filename",
                "$material->platform/$material->material_id/$filename");
        }
    }
}

@$zip->close();
echo "export material_files end============================>\n";
echo "https://dms.zx.com/upload/$file_name";