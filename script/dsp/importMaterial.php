<?php

use App\MysqlConnection;
use App\Utils\Helpers;
use Common\EnvConfig;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要导入的平台' . PHP_EOL);
    die;
}
$platform = $params['p'];
if ($platform === 'TW') {
    $domain = 'adsimg.tanwan.com';
} else {
    $domain = 'adsimg.' . $platform . '.com';
}


MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';
$db_materials = MysqlConnection::getConnection($connection_name)->table('db_materials')->get();
$users = MysqlConnection::getConnection($connection_name)->table('opgroup_user')->get();
$uid_names = $users->pluck('uName', 'uId');
$all_datas = $datas = [];
echo "importing material============================>\n";
foreach ($db_materials as $material) {
    $data = ['platform' => $platform,
        'material_id' => $material->id,
        'name' => $material->name,
        'media_type' => $material->media_type,
        'original' => $material->original,
        'theme_id' => $material->subtheme_id,
        'file_type' => $material->file_type,
        'is_group' => $material->is_group,
        'author' => $material->author,
        'effect_grade' => $material->effect_grade ?? '',
        'effect_grade2' => $material->effect_grade2 ?? '',
        'c_author' => isset($material->c_author) ? author_switch($material->c_author, $uid_names) : '[]',
        'a_author' => isset($material->a_author) ? author_switch($material->a_author, $uid_names) : '[]',
        'is_3d' => $material->is_3d ?? 0,
        'm1_author' => isset($material->m1_author) ? author_switch($material->m1_author, $uid_names) : '[]',
        'm2_author' => isset($material->m2_autho) ? author_switch($material->m2_author, $uid_names) : '[]',
        'm3_author' => isset($material->m3_author) ? author_switch($material->m3_author, $uid_names) : '[]',
        'm4_author' => isset($material->m4_author) ? author_switch($material->m4_author, $uid_names) : '[]',
        'm5_author' => isset($material->m5_author) ? author_switch($material->m4_author, $uid_names) : '[]',
        'is_immortal' => $material->is_immortal ?? 0,
        'actor' => isset($material->actor) ? author_switch($material->actor, $uid_names) : '[]',
        'shoot' => isset($material->shoot) ? author_switch($material->shoot, $uid_names) : '[]',
        'create_time' => (strtotime($material->created_time) > 0 ? strtotime($material->created_time) : 0),
        'update_time' => (strtotime($material->last_modified_time) > 0 ? strtotime($material->last_modified_time) : 0),
        'last_uploaded_time' => (strtotime($material->last_uploaded_time) > 0 ? strtotime($material->last_uploaded_time) : 0),
        'is_del' => $material->is_del,
        'is_public' => 1,
    ];

    $all_datas[] = $data;
    $datas[] = $data;
    if (count($datas) >= 1000) {
        try {
            MysqlConnection::getConnection($connection_name)->table('material')->insert($datas);
        } catch (\Exception $e) {
            print_r($datas);
            Helpers::getLogger('import-material')->error("插入素材失败", [
                'data' => $datas,
                'error_message' => $e->getMessage()
            ]);
        }
        $datas = [];
    }
}
if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_name)->table('material')->insert($datas);
}

echo "importing datamedia material============================>\n";
$datas = [];
foreach ($all_datas as $data) {
    unset($data['effect_grade']);
    unset($data['effect_grade2']);
    $data['insert_time'] = date("Y-m-d H:i:s", $data['create_time']);
    $data['update_time'] = date("Y-m-d H:i:s", $data['update_time']);
    unset($data['create_time']);
    unset($data['last_uploaded_time']);
    $datas[] = $data;
    try {
        if (count($datas) >= 1000) {
            MysqlConnection::getConnection($connection_data_media)->table('ods_material_log')->insert($datas);
            $datas = [];
        }
    } catch (\Exception $e) {
        print_r($datas);
        Helpers::getLogger('import-material-datamedia')->error("插入素材失败", [
            'data' => $datas,
            'error_message' => $e->getMessage()
        ]);
    }
}
if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_data_media)->table('ods_material_log')->insert($datas);
}

function author_switch($authors, $uid_names)
{
    if (!empty($authors)) {
        $authors = json_decode($authors, true);
        $authors = array_map(function ($uid) use ($uid_names) {
            return $uid_names[$uid];
        }, $authors);
        return json_encode($authors, JSON_UNESCAPED_UNICODE);
    } else {
        return '[]';
    }
}

echo "importing material_files============================>\n";
$db_material_files = MysqlConnection::getConnection($connection_name)->table('db_material_files')->get();
$all_datas = $datas = [];
$times = [1, 2, 3, 4, 5, 6];
$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";
$id = MysqlConnection::getConnection($connection_name)->table('material_file')->max('id');
foreach ($db_material_files as $material_file) {
    $id++;
    $url = '';
    if (!empty($material_file->url)) {
        $url = $material_file->url;
        $url = str_replace('http:', 'https:', $url);
        $url = str_replace($domain, parse_url(EnvConfig::DOMAIN)['host'], $url);
        $url = str_replace('/material/', '/material/' . $platform . '/', $url);
    }
    $data = ['id' => $id,
        'platform' => $platform,
        'media_type' => $material_file->media_type,
        'material_id' => $material_file->material_id,
        'file_type' => $material_file->file_type,
        'filename' => $material_file->filename,
        'url' => $url,
        'width' => $material_file->width,
        'height' => $material_file->height,
        'scale' => $material_file->scale,
        'signature' => $material_file->md5,
        'duration' => $material_file->duration,
        'bitrate' => $material_file->bitrate,
        'size' => $material_file->size,
        'format' => $material_file->format,
        'uploader' => $material_file->uploader,
        'create_time' => (strtotime($material_file->created_time) > 0 ? strtotime($material_file->created_time) : 0),
        'update_time' => (strtotime($material_file->last_modified_time) > 0 ? strtotime($material_file->last_modified_time) : 0),
        'is_del' => $material_file->is_del ?? 0,
        'notify' => $material_file->notify ?? 0,
        'video_hash_0' => $material_file->video_hash_0,
        'video_hash_1' => $material_file->video_hash_1,
        'video_hash_2' => $material_file->video_hash_2,
        'video_hash_3' => $material_file->video_hash_3,
        'video_hash_4' => $material_file->video_hash_4,
        'video_hash_5' => $material_file->video_hash_5,
        'video_hash_6' => $material_file->video_hash_6,
        'video_hash_7' => $material_file->video_hash_7,
        'video_hash_8' => $material_file->video_hash_8,
        'video_hash_9' => $material_file->video_hash_9,
        'video_hash_10' => $material_file->video_hash_10
    ];
    $all_datas[] = $data;
    $datas[] = $data;
    //视频插入截图数据
    if ($data['file_type'] == \App\Model\SqlModel\Zeda\MaterialFileModel::FILE_TYPE_VIDEO) {
        $filename = explode('.', $data['filename']);
        if (is_array($filename) && count($filename) > 1) {
            array_pop($filename);
            $url = explode('/', $data['url']);
            array_pop($url);
            foreach ($times as $value) {
                //按时间截图
                $ext_name = implode('.', $filename) . '_creative_' . $value . '.jpg';
                if (file_exists("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name")) {
                    $sub_data = $data;
                    $id++;
                    $sub_data['id'] = $id;
                    $sub_data['file_type'] = \App\Model\SqlModel\Zeda\MaterialFileModel::FILE_TYPE_COVER;
                    $sub_data['filename'] = $ext_name;
                    $sub_data['url'] = implode('/', $url) . "/$ext_name";
                    $sub_data['signature'] = md5_file("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name");
                    $sub_data['bitrate'] = 0;
                    $sub_data['size'] = filesize("$upload_path/{$data['platform']}/{$data['material_id']}/$ext_name");
                    $sub_data['format'] = 'jpeg';

                    $all_datas[] = $sub_data;
                    $datas[] = $sub_data;
                }
            }
        }
    }
    if (count($datas) >= 1000) {
        try {
            MysqlConnection::getConnection($connection_name)->table('material_file')->insert($datas);
            $datas = [];
        } catch (\Exception $e) {
            print_r($datas);
            Helpers::getLogger('import-material-file')->error("插入素材文件失败", [
                'data' => $datas,
                'error_message' => $e->getMessage()
            ]);
        }
    }
}
if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_name)->table('material_file')->insert($datas);
}

echo "importing datamedia material_files============================>\n";
$datas = [];
foreach ($all_datas as $data) {
    $data['insert_time'] = date("Y-m-d H:i:s", $data['create_time']);
    $data['update_time'] = date("Y-m-d H:i:s", $data['update_time']);
    unset($data['create_time']);
    $datas[] = $data;
    try {
        if (count($datas) >= 1000) {
            MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
            $datas = [];
        }
    } catch (\Exception $e) {
        print_r($datas);
        Helpers::getLogger('import-material-file-datamedia')->error("插入素材文件失败", [
            'data' => $datas,
            'error_message' => $e->getMessage()
        ]);
    }
}
if (count($datas) > 0) {
    MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->insert($datas);
}