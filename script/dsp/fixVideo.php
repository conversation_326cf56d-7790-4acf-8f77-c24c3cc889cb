<?php

use App\Exception\AppException;
use App\MysqlConnection;
use App\Utils\Video;
use Common\EnvConfig;
use FFMpeg\FFMpeg;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

//$signatures = ['5bcc157890055fe693be349465e47112',
//    '127ac2238012f2ee0e7e7a8c43492f19',
//    'cbe045531450310840b2432a375623ac',
//    'd17ea0631fa28978f2c7757bad40e9f8',
//    '5c261726005132cdac4fc9de11a3b30e',
//    '12cb754d81db7692229daaba870f13b4',
//    '920eafe34c7cf3b601de68fdff82f632',
//    'c7dbb6b9622cf2838b1f17c4a97b55f2',
//    '7c87ad5fb7264e87381460402a709400',
//    '449a024a8ee6140c6d909abfe28db4a1'
//];
//$signatures = ['f1169a9cb4a823df9b7fa108dbc16ae6'];

$sub_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
$access_path = EnvConfig::UPLOAD_PATH . '/' . $sub_path;
$upload_path = SRV_DIR . "/{$access_path}";

MysqlConnection::setConnection();
$connection_name = 'default';

//foreach ($signatures as $signature) {
//    $material_file = MysqlConnection::getConnection($connection_name)
//        ->table('material_file')
//        ->where([['signature', '=', $signature]])
//        ->orderByDesc('id')
//        ->first();
//    if (!empty($material_file)) {
//        echo $material_file->id . '========begin==========' . PHP_EOL;
//        $base_name = explode('.', $material_file->filename);
//        array_pop($base_name);
//        $base_name = implode('.', $base_name);
//
//        if (!file_exists("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename")) {
//            if (file_exists("$upload_path/$material_file->platform/$material_file->material_id/old_$material_file->filename")) {
//                rename("$upload_path/$material_file->platform/$material_file->material_id/old_$material_file->filename",
//                    "$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename");
//            } else {
//                echo $material_file->id . '素材文件不存在' . PHP_EOL;
//                continue;
//            }
//        }
//
//        rename("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename",
//            "$upload_path/$material_file->platform/$material_file->material_id/old_$material_file->filename");
//        $bitrate = intval($material_file->bitrate / 1000);
//        $command = 'ffmpeg -i "' . "$upload_path/$material_file->platform/$material_file->material_id/old_$material_file->filename" . '"  -y -acodec copy -b:v ' . $bitrate . 'k "' . "$upload_path/$material_file->platform/$material_file->material_id/new_$material_file->filename" . '"';
//        echo $command;
//        exec($command);
//
//        if (file_exists("$upload_path/$material_file->platform/$material_file->material_id/new_$material_file->filename")) {
//            $ffmpeg = FFMpeg::create();
//            $open_video = $ffmpeg->open("$upload_path/$material_file->platform/$material_file->material_id/new_$material_file->filename");
//            $duration = round($open_video->getFormat()->get("duration"), 3);
//            $bitrate = $open_video->getFormat()->get("bit_rate");
//            $size = $open_video->getFormat()->get("size");
//            $data = ['signature' => md5_file("$upload_path/$material_file->platform/$material_file->material_id/new_$material_file->filename"),
//                'duration' => $duration,
//                'bitrate' => $bitrate,
//                'size' => $size,
//                'filename' => 'new_'.$material_file->filename,
//                'url' => EnvConfig::MATERIAL_DOMAIN_NAME . "$access_path/$material_file->platform/$material_file->material_id/new_$material_file->filename"
//            ];
//            var_dump($data);
//            MysqlConnection::getConnection($connection_name)->table('material_file')->where('id', '=', $material_file->id)->update($data);
//            //正式数据要同步data_media
//            if (EnvConfig::ENV === 'production') {
//                $connection_data_media = 'data_media';
//                MysqlConnection::getConnection($connection_data_media)->table('ods_material_file_log')->where('id', '=', $material_file->id)->update($data);
//            }
//            echo $material_file->id . '========finish==========' . PHP_EOL;
//        } else {
//            echo $material_file->id . '生成失败' . PHP_EOL;
//        }
//    } else {
//        echo $signature . '对应记录不存在' . PHP_EOL;
//    }
//}

$material_files = MysqlConnection::getConnection($connection_name)
        ->table('material_file')
        ->whereRaw("`filename` LIKE 'new_%'")
        ->get();

foreach ($material_files as $material_file) {
    if (file_exists("$upload_path/$material_file->platform/$material_file->material_id/$material_file->filename")) {
        $file_name = substr($material_file->filename, 4, -4);
        if (file_exists("$upload_path/$material_file->platform/$material_file->material_id/$file_name.wm")) {
            echo $material_file->id . '========begin==rename========' . PHP_EOL;
            echo "$upload_path/$material_file->platform/$material_file->material_id/$file_name.wm".PHP_EOL;
            echo "$upload_path/$material_file->platform/$material_file->material_id/new_$file_name.wm".PHP_EOL;
            rename("$upload_path/$material_file->platform/$material_file->material_id/$file_name.wm",
            "$upload_path/$material_file->platform/$material_file->material_id/new_$file_name.wm");
        }
    }
}
