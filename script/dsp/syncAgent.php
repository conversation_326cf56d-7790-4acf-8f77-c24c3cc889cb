<?php

use App\Constant\AgentGroup;
use App\Constant\MediaType;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\MysqlConnection;
use Illuminate\Database\Query\JoinClause;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

$params = getopt('p:');
if (!isset($params['p'])) {
    print_r('-p传入需要同步agent的平台' . PHP_EOL);
    die;
}

$platform = $params['p'];

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datahub = 'datahub';

$page = 1;
$rows = 1000;
$last_site_info = (object)[
    'site_id' => 0,
    'platform' => ''
];


$list = MysqlConnection::getConnection($connection_name)
    ->table('agent')
    ->select('media_type', 'agent_group', 'agent.platform', 'agent.agent_id', 'agent_name', 'account_id', 'agent_leader_change_log.agent_leader', 'end_time', 'start_time')
    ->join('agent_leader_change_log', function (JoinClause $join) {
        $join->on('agent.platform', '=', 'agent_leader_change_log.platform');
        $join->on('agent.agent_id', '=', 'agent_leader_change_log.agent_id');
    })
    ->where('agent.platform', $platform)
    ->orderBy('platform')
    ->orderBy('agent_id')
    ->orderBy('start_time')
    ->get();

// 获取已有渠道的proxy_type
$agent_ids = $list->pluck('agent_id')->unique()->toArray();
$ori_adb_agent_list = (new V2DimAgentIdModel())->getAgentListByAgentIds($platform, $agent_ids);

echo "importing ============================>\n";

$insert_agent_data = [];
foreach ($list as $item) {
    $agent_info = $ori_adb_agent_list->where('agent_id', $item->agent_id)->first();
    $proxy_type = $agent_info->proxy_type ?? 1;
    $channel_id = $agent_info->channel_id ?? 0;
    $channel_name = $agent_info->channel_name ?? '';
    $insert_agent_data[] = [
        'media_type_id' => $item->media_type,
        'media_type_name' => MediaType::MEDIA_TYPE_MAP[$item->media_type],
        'agent_group_id' => $item->agent_group,
        'agent_group_name' => AgentGroup::TYPE_MAP[$item->agent_group],
        'platform' => $item->platform,
        'agent_id' => $item->agent_id,
        'agent_name' => $item->agent_name,
        'agent_leader' => $item->agent_leader,
        'account_id' => $item->account_id,
        'is_remote' => 1,
        'agent_leader_start_time' => date('Y-m-d', $item->start_time) . ' 00:00:00',
        'agent_leader_end_time' => (int)$item->end_time === ********** ? V2DimAgentIdModel::END_TIME : date('Y-m-d', strtotime('-1 day', $item->end_time)) . ' 23:59:59',
        'proxy_type' => $proxy_type,
        'channel_id' => $channel_id,
        'channel_name' => $channel_name,
    ];
}

MysqlConnection::getConnection($connection_datahub)->table('v2_dim_agent_id')->replace($insert_agent_data);
