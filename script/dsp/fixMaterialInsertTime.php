<?php

use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');


MysqlConnection::setConnection();
$connection_name = 'default';
$connection_data_media = 'data_media';

$materials = MysqlConnection::getConnection($connection_name)
    ->table('material')
    ->selectRaw('count(*) AS num,create_time,GROUP_CONCAT( platform, "-", material_id ) as platform_material_ids')
    ->groupBy('create_time')
    ->having('num', '>', 1)
    ->orderBy('create_time')
    ->get();

foreach ($materials as $material) {
    foreach (explode(',', $material->platform_material_ids) as $index => $platform_material_id) {
        [$platform, $material_id] = explode('-', $platform_material_id);
        $create_time = $material->create_time + $index;
        MysqlConnection::getConnection($connection_name)
            ->table('material')
            ->where('platform', $platform)
            ->where('material_id', $material_id)
            ->update([
                'create_time' => $create_time
            ]);
        MysqlConnection::getConnection($connection_name)
            ->table('ods_material_log')
            ->where('platform', $platform)
            ->where('material_id', $material_id)
            ->update([
                'insert_time' => date('Y-m-d H:i:s', $create_time)
            ]);
        echo "update $platform_material_id create_time: {$create_time}============================>\n";

    }
}