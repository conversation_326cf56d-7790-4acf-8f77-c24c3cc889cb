<?php

use App\Model\SqlModel\DataMedia\DimSiteGameIdModel;
use App\MysqlConnection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();
$connection_name = 'default';
$connection_datamedia = 'data_media';

$page = 1;
$rows = 1000;
$last_site_info = (object)[
    'site_id' => 0,
    'platform' => ''
];
echo "importing ============================>\n";
while (1) {
    $site_game_change_list = MysqlConnection::getConnection($connection_name)
        ->table('site_game_change_log')
        ->forPage($page, $rows)
        ->orderBy('platform')
        ->orderBy('site_id')
        ->orderBy('start_time')
        ->get();
    if ($site_game_change_list->isEmpty()) {
        break;
    }
    $insert_site_data = [];
    foreach ($site_game_change_list as $item) {
        if ($item->site_id === $last_site_info->site_id && $item->platform === $last_site_info->platform) {
            $game_start_time = date('Y-m-d H:00:00', strtotime('+1 hour', $item->start_time));
        } else {
            $game_start_time = DimSiteGameIdModel::START_TIME;
        }
        $last_site_info = $item;
        $insert_site_data[] = [
            'platform' => $item->platform,
            'site_id' => $item->site_id,
            'game_id' => $item->game_id,
            'convert_id' => $item->convert_id > 0 ? $item->convert_id : 0,
            'game_start_time' => $game_start_time,
            'game_end_time' => (int)$item->end_time === 4102416000 ? DimSiteGameIdModel::END_TIME : date('Y-m-d H:59:59', (int)$item->end_time),
        ];
    }
    MysqlConnection::getConnection($connection_datamedia)->table('dim_site_game_id')->replace($insert_site_data);
    $insert_row = count($insert_site_data);
    echo "dim_site_game_id_1替换{$insert_row}条 ============================>\n";
    $insert_site_data = [];
    $page++;
}
