bin目录下是一个Windows下的protobuf编译器，可以把proto文件编译成各种语言的protobuf文件。
进入bin目录，可以使用以下命令编译成PHP文件：

`protoc --php_out=../ --proto_path=../proto CsInfo.proto MessageBody.proto RepMessage.proto ReqMessage.proto SessionInfo.proto UserInfo.proto Preread.proto`

proto目录下有消息体定义的proto文件。这里有各种消息字段说明

GPBMetadata和Message目录是使用proto文件生成的对应的PHP类文件，已经实现自动加载

demo.php是简单的使用说明

新加proto原型如果是不同package，记得composer自动加载 composer dump-autoload


更多详情可以看这里：https://github.com/protocolbuffers/protobuf