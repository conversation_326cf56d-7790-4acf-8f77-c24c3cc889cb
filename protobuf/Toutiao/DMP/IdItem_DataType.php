<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ToutiaoDMP.proto

namespace Toutiao\DMP;

if (false) {
    /**
     * This class is deprecated. Use Toutiao\DMP\IdItem\DataType instead.
     * @deprecated
     */
    class IdItem_DataType {}
}
class_exists(IdItem\DataType::class);
@trigger_error('Toutiao\DMP\IdItem_DataType is deprecated and will be removed in the next major release. Use Toutiao\DMP\IdItem\DataType instead', E_USER_DEPRECATED);

