<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ToutiaoDMP.proto

namespace Toutiao\DMP;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 *上传文件每行一个base64编码的字符串，每个字符串包含一个完整的DmpData消息二进制字节串
 *
 * Generated from protobuf message <code>Toutiao.DMP.DmpData</code>
 */
class DmpData extends \Google\Protobuf\Internal\Message
{
    /**
     * 每行数据包含的idList大小不能超过10000
     *
     * Generated from protobuf field <code>repeated .Toutiao.DMP.IdItem idList = 1;</code>
     */
    private $idList;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Toutiao\DMP\IdItem[]|\Google\Protobuf\Internal\RepeatedField $idList
     *           每行数据包含的idList大小不能超过10000
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ToutiaoDMP::initOnce();
        parent::__construct($data);
    }

    /**
     * 每行数据包含的idList大小不能超过10000
     *
     * Generated from protobuf field <code>repeated .Toutiao.DMP.IdItem idList = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getIdList()
    {
        return $this->idList;
    }

    /**
     * 每行数据包含的idList大小不能超过10000
     *
     * Generated from protobuf field <code>repeated .Toutiao.DMP.IdItem idList = 1;</code>
     * @param \Toutiao\DMP\IdItem[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setIdList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Toutiao\DMP\IdItem::class);
        $this->idList = $arr;

        return $this;
    }

}

