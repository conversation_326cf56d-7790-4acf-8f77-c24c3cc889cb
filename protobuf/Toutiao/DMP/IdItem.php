<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ToutiaoDMP.proto

namespace Toutiao\DMP;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>Toutiao.DMP.IdItem</code>
 */
class IdItem extends \Google\Protobuf\Internal\Message
{
    /**
     *若不设置，默认以上传文件的创建时间为此条记录的创建时间
     *
     * Generated from protobuf field <code>uint32 timestamp = 1;</code>
     */
    private $timestamp = 0;
    /**
     *指定此id的类型，如IMEI、IDFA等
     *
     * Generated from protobuf field <code>.Toutiao.DMP.IdItem.DataType dataType = 2;</code>
     */
    private $dataType = 0;
    /**
     *根据dataType字段的类型，放置对应类型的id的字符串，需要小写
     *
     * Generated from protobuf field <code>string id = 3;</code>
     */
    private $id = '';
    /**
     *标识此id的业务标签字符串
     *
     * Generated from protobuf field <code>repeated string tags = 4;</code>
     */
    private $tags;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $timestamp
     *          若不设置，默认以上传文件的创建时间为此条记录的创建时间
     *     @type int $dataType
     *          指定此id的类型，如IMEI、IDFA等
     *     @type string $id
     *          根据dataType字段的类型，放置对应类型的id的字符串，需要小写
     *     @type string[]|\Google\Protobuf\Internal\RepeatedField $tags
     *          标识此id的业务标签字符串
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\ToutiaoDMP::initOnce();
        parent::__construct($data);
    }

    /**
     *若不设置，默认以上传文件的创建时间为此条记录的创建时间
     *
     * Generated from protobuf field <code>uint32 timestamp = 1;</code>
     * @return int
     */
    public function getTimestamp()
    {
        return $this->timestamp;
    }

    /**
     *若不设置，默认以上传文件的创建时间为此条记录的创建时间
     *
     * Generated from protobuf field <code>uint32 timestamp = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setTimestamp($var)
    {
        GPBUtil::checkUint32($var);
        $this->timestamp = $var;

        return $this;
    }

    /**
     *指定此id的类型，如IMEI、IDFA等
     *
     * Generated from protobuf field <code>.Toutiao.DMP.IdItem.DataType dataType = 2;</code>
     * @return int
     */
    public function getDataType()
    {
        return $this->dataType;
    }

    /**
     *指定此id的类型，如IMEI、IDFA等
     *
     * Generated from protobuf field <code>.Toutiao.DMP.IdItem.DataType dataType = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setDataType($var)
    {
        GPBUtil::checkEnum($var, \Toutiao\DMP\IdItem_DataType::class);
        $this->dataType = $var;

        return $this;
    }

    /**
     *根据dataType字段的类型，放置对应类型的id的字符串，需要小写
     *
     * Generated from protobuf field <code>string id = 3;</code>
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     *根据dataType字段的类型，放置对应类型的id的字符串，需要小写
     *
     * Generated from protobuf field <code>string id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkString($var, True);
        $this->id = $var;

        return $this;
    }

    /**
     *标识此id的业务标签字符串
     *
     * Generated from protobuf field <code>repeated string tags = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     *标识此id的业务标签字符串
     *
     * Generated from protobuf field <code>repeated string tags = 4;</code>
     * @param string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setTags($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->tags = $arr;

        return $this;
    }

}

