<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ToutiaoDMP.proto

namespace Toutiao\DMP\IdItem;

/**
 * Protobuf type <code>Toutiao.DMP.IdItem.DataType</code>
 */
class DataType
{
    /**
     * Generated from protobuf enum <code>IMEI = 0;</code>
     */
    const IMEI = 0;
    /**
     * Generated from protobuf enum <code>IDFA = 1;</code>
     */
    const IDFA = 1;
    /**
     * Generated from protobuf enum <code>UID = 2;</code>
     */
    const UID = 2;
    /**
     * Generated from protobuf enum <code>IMEI_MD5 = 4;</code>
     */
    const IMEI_MD5 = 4;
    /**
     * Generated from protobuf enum <code>IDFA_MD5 = 5;</code>
     */
    const IDFA_MD5 = 5;
    /**
     * Generated from protobuf enum <code>MOBILE_HASH_SHA256 = 6;</code>
     */
    const MOBILE_HASH_SHA256 = 6;
    /**
     * Generated from protobuf enum <code>OAID = 7;</code>
     */
    const OAID = 7;
    /**
     * Generated from protobuf enum <code>OAID = 8;</code>
     */
    const OAID_MD5 = 8;
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(DataType::class, \Toutiao\DMP\IdItem_DataType::class);

