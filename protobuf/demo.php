<?php

use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
//$dmp_data = new \Toutiao\DMP\DmpData();
//$id_item_list = [];
//$handle = fopen('./test.txt', 'r');
//$id_item_limit = 1;
//$row_limit = 1;
//$file_count = 0;
//while (!feof($handle)) {
//    $md5 = trim(fgets($handle));
//    if (empty($md5)) {
//        echo "空行";
//        break;
//    }
//    if (++$row_limit > 2000000) {
//        $file_count++;
//        $row_limit = 0;
//    }
//    $id_item = new \Toutiao\DMP\IdItem();
//    $id_item->setDataType(\Toutiao\DMP\IdItem\DataType::IMEI_MD5);
//    $id_item->setId($md5);
//    $id_item_list[] = $id_item;
//    // id_item_list 最多1000个
//    if (++$id_item_limit > 1000) {
//        $dmp_data->setIdList($id_item_list);
//        $dmp_string = $dmp_data->serializeToString();
//        $dmp_bs4 = base64_encode($dmp_string);
//        file_put_contents("encoded-{$file_count}.txt", $dmp_bs4 . PHP_EOL, FILE_APPEND);
//        $id_item_limit = 0;
//        $id_item_list = [];
//    }
//}
//fclose($handle);
//
//if (!empty($id_item_list)) {
//    $dmp_data->setIdList($id_item_list);
//    $dmp_string = $dmp_data->serializeToString();
//    $dmp_bs4 = base64_encode($dmp_string);
//    file_put_contents("encoded-{$file_count}.txt", $dmp_bs4 . PHP_EOL, FILE_APPEND);
//}
//
//for ($i = 0; $i <= $file_count; $i++) {
//    echo $i, PHP_EOL;
//    $zip = new ZipArchive;
//    $code = $zip->open(TMP_DIR . "/encoded-{$i}.zip", ZIPARCHIVE::CREATE | ZipArchive::OVERWRITE);
//    if ($code === TRUE) {
//        $zip->addFile("encoded-{$i}.txt", "encoded-{$i}.txt");
//        $zip->close();
//    } else {
//        print_r($code);
//    }
//}

$data_source_model = new \App\Model\HttpModel\Toutiao\DMP\DataSourceModel();
//for ($i = 0; $i <= 5; $i++) {
//    $res = $data_source_model->fileUpload('1643634493109260', TMP_DIR . "/encoded-{$i}.zip", md5_file(TMP_DIR . "/encoded-{$i}.zip"));
//    print_r($res);
//    print_r(PHP_EOL);
//}

//$res = $data_source_model->create('1643634493109260', 'lczg-android-1-test', '仅用于测试', [
//    "1643634493109260-5d0787c62194378e12999b6c554c196e",
//    "1643634493109260-caab58217b6b8b8c15217811cae7159d",
//    "1643634493109260-3844be1150b5eb02863e0b621708f615",
//    "1643634493109260-8fc1dae04781021a0835348c17c839dc",
//    "1643634493109260-09d23adf64a797376135763c3b15b592",
//    "1643634493109260-21f64e56e753c905067f2dbf3a55a493",
//]);

$res = $data_source_model->read('1643635161763843', 'f0c72afabfa3067f462bf5c480f4b93370b68368', ['1020976f4b4d4f6597149183c9a53db2']);
//print_r($res);
$custom_audience_model = new \App\Model\HttpModel\Toutiao\DMP\CustomAudienceModel();
//$res = $custom_audience_model->publish('1643634493109260', '5ecce6009722d68a928018b73eddd09c5c9977db', 301532299);
//$res = $custom_audience_model->read('1643634493109260', '5ecce6009722d68a928018b73eddd09c5c9977db', [301532299]);

print_r($res);
