<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ToutiaoDMP.proto

namespace GPBMetadata;

class ToutiaoDMP
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a9c020a10546f757469616f444d502e70726f746f120b546f757469616f" .
            "2e444d50222e0a07446d704461746112230a0669644c6973741801200328" .
            "0b32132e546f757469616f2e444d502e49644974656d22c2010a06496449" .
            "74656d12110a0974696d657374616d7018012001280d122e0a0864617461" .
            "5479706518022001280e321c2e546f757469616f2e444d502e4964497465" .
            "6d2e4461746154797065120a0a026964180320012809120c0a0474616773" .
            "180420032809225b0a08446174615479706512080a04494d454910001208" .
            "0a0449444641100112070a035549441002120c0a08494d45495f4d443510" .
            "04120c0a08494446415f4d4435100512160a124d4f42494c455f48415348" .
            "5f5348413235361006620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

