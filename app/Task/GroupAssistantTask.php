<?php

namespace App\Task;

use App\Constant\Environment;
use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\RedisModel\WechatAccessTokenModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantBotChatModel;
use App\Model\SqlModel\Zeda\FeishuUATModel;
use App\Service\DataBot\DataBotService;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\FileDeletionManager;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageSummary;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;
use RedisException;

class GroupAssistantTask
{

    /**
     * 监听每条消息的队列key
     */
    const QUEUE_KEY = 'group_assistant_queue';

    /**
     * 卡片回调的队列key
     */
    const CARD_QUEUE_KEY = 'group_assistant_card_queue';


    /**
     * 授权成功回调的队列key
     *
     */
    const GRANT_QUEUE_KEY = 'group_assistant_grant_queue';

    /**
     * 机器人进群的Redis队列key
     */
    const BOT_ADD_QUEUE_KEY = 'group_assistant_bot_add_queue';


    /**
     * 活跃时间，秒 300秒，5分钟
     */
    const ACTIVITY_TIME = 300;

    const TICK_LIST = [
//        [
//            'env'         => [Environment::PROD],
//            'desc'        => '群聊天助手, 监听是否需要创建待办事项',
//            'interval_ms' => 5000, // 每5s运行一次
//            /** @uses \App\Task\GroupAssistantTask::handlerSchedule() */
//            'method'      => 'handlerSchedule',
//        ],
        [
            'env'         => [Environment::PROD],
            'desc'        => '消息总结，定时总结群消息',
            'interval_ms' => 10000, // 每10s运行一次
            /** @uses \App\Task\GroupAssistantTask::handlerFileDelete() */
            'method'      => 'handlerFileDelete',
        ],

    ];


    /**
     * 处理飞书消息
     * @return void
     */
    public function queueConsumer()
    {
        $redis = RedisCache::getInstance();
        $logger = Helpers::getLogger('group_assistant');
        // 从消息队列获取一条信息
        try {
            $queue_data = $redis->rPop(self::QUEUE_KEY);
        } catch (RedisException $e) {
            // 出队失败。直接结束
            $logger->error('消息队列出队失败', ['message' => $e->getMessage()]);
            return;
        }

        if (!$queue_data) {
            return;
        }

        $logger->info('开始消耗消息', ['data' => $queue_data]);

        try {
            // 处理消息
            $data = json_decode($queue_data, true);
            $decrypt = $data['decrypt'];

            // 处理消息
            (new GroupAssistantService())->handlerSingleMessage($decrypt);

        } catch (AppException $app_exception) {
            $logger->error('app_exception:', [$app_exception->getMessage(), 's' => $app_exception->getTrace()]);
        } catch (\Throwable $exception) {
            $logger->error('发生错误:', [$exception->getMessage(), 'trace_string' => $exception->getTraceAsString(), 'trace' => $exception->getTrace()]);
        }

    }

    /**
     * 处理文件延迟删除
     * @return void
     */
    public function handlerFileDelete()
    {
        // 处理删除
        (new FileDeletionManager())->processDeletionQueue();
    }

//    public function handlerCartEvent()
//    {
//        $redis = RedisCache::getInstance();
//        $logger = Helpers::getLogger('group_assistant_card');
//        // 从消息队列获取一条信息
//        try {
//            $queue_data = $redis->rPop(self::CARD_QUEUE_KEY);
//        } catch (RedisException $e) {
//            // 出队失败。直接结束
//            $logger->error('消息队列出队失败', ['message' => $e->getMessage()]);
//            return;
//        }
//
//
//        if (!$queue_data) {
//            return;
//        }
//
//        $logger->info('开始消耗卡片回调消息', ['data' => $queue_data]);
//        $custom_model = new MessageModel();
//
//        // 先获取access_token
//        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
//            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
//
//
//        try {
//            // 处理消息
//            $data = json_decode($queue_data, true);
//            $union_id = $data['union_id'];
//            $action_value = $data['action_value'];
//
//            /**
//             * $context = ['message_id' => $message_id,
//             * 'schedule_data' => [
//             * 'start_time' => '',
//             * 'end_time'   => '',
//             * 'summary' => $summary,
//             * 'description' => $description,
//             * 'chat_schedule_id' => $chat_schedule_id,
//             * ]]
//             */
//            $context = $data['context'];
//            $service = new GroupAssistantService();
//
//            // 判断用户是接收还是拒绝
//            if ($action_value !== 'create') {
//                $logger->info('用户拒绝创建', ['action_value' => $action_value, 'message_id' => $context['message_id']]);
//                return;
//            }
//
//
//            // 用户接受创建，判断有没有授权信息，没有则需要用户去授权
//            $auth_data = (new FeishuUATModel())->getData($union_id, GroupAssistantService::APP_ID);
//            if (!$auth_data) {
//                // 发送授权链接给用户
//                // 授权完成之后，需要继续这个流程，所以需要携带上下文
//                $url = $service->createGrantUrl($union_id, $context['schedule_data']);
//                $msg_id = 0;
//                FeiShuService::streamOutput($msg_id, $tenant_access_token, "你还未对应用授权，请点击下面链接进行授权： [授权链接]($url)", $union_id, 'union_id');
//            } else {
//                // 已经有授权，则直接建日程
//                $service->createSchedule($union_id, $context['schedule_data']);
//            }
//
//
//        } catch (AppException $app_exception) {
//            // 下发一条消息提示出错
//            $custom_model->sendText($tenant_access_token, $union_id, $app_exception->getMessage());
//            $logger->info('app_exception:', [$app_exception->getMessage(), 's' => $app_exception->getTrace()]);
//        } catch (\Throwable $exception) {
//            // 下发一条消息提示出错
//            $custom_model->sendText($tenant_access_token, $union_id, $exception->getMessage());
//            $logger->error('发生未知错误:', [$exception->getMessage(), 's' => $exception->getTrace()]);
//        }
//    }


//    public function handlerGrantEvent()
//    {
//        $redis = RedisCache::getInstance();
//        $logger = Helpers::getLogger('group_assistant_grant');
//        // 从消息队列获取一条信息
//        try {
//            $queue_data = $redis->rPop(self::GRANT_QUEUE_KEY);
//        } catch (RedisException $e) {
//            // 出队失败。直接结束
//            $logger->error('消息队列出队失败', ['message' => $e->getMessage()]);
//            return;
//        }
//
//
//        if (!$queue_data) {
//            return;
//        }
//
//        $logger->info('开始消耗回调授权消息', ['data' => $queue_data]);
//        $custom_model = new MessageModel();
//
//        // 先获取access_token
//        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
//            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
//
//
//        try {
//            // 处理消息
//            $data = json_decode($queue_data, true);
//            $union_id = $data['union_id'];
//            $schedule_data = $data['schedule_data'];
//
//            /**
//             * 'schedule_data' => [
//             * 'start_time' => '',
//             * 'end_time'   => '',
//             * ]]
//             */
//            $service = new GroupAssistantService();
//
//            $service->createSchedule($union_id, $schedule_data);
//
//        } catch (AppException $app_exception) {
//            $custom_model->sendText($tenant_access_token, $union_id, $app_exception->getMessage());
//            $logger->info('app_exception:', [$app_exception->getMessage(), 's' => $app_exception->getTrace()]);
//        } catch (\Throwable $exception) {
//            // 下发一条消息提示出错
//            $custom_model->sendText($tenant_access_token, $union_id, $exception->getMessage());
//            $logger->error('发生未知错误:', [$exception->getMessage(), 's' => $exception->getTrace()]);
//        }
//    }


    /**
     * 处理待办事项，规则是五分钟内不说话，则说明需要处理
     * @return void
     * @throws RedisException
     */
    public function handlerSchedule()
    {
        $logger = Helpers::getLogger('assistant_task');
        $logger->info('开始检测消息活跃度了');
        $bot_chat_model = new FeiShuAssistantBotChatModel();
        $group_assistant_service = new GroupAssistantService();

        // 先去获取所有的群列表
        $chat_list = $bot_chat_model->getALL();

        // 循环处理每一个群。
        foreach ($chat_list as $item) {
            $chat_id = $item->chat_id;
            // 获得锁才往下执行
            if (!$this->lock($chat_id)) {
                continue;
            }

            try {
                // 判断当前时间是否已经过去5分钟
                $now = time();
                $last_activity_time = $group_assistant_service->getChatLastMessageActivityTime($chat_id);
                if (!$last_activity_time) {
                    $logger->info('没有活跃时间，不处理', ['chat_id' => $chat_id, 'now' => date("Y-m-d H:i:s", $now), 'last_activity_time' => $last_activity_time]);
                    // 解锁先
                    $this->unlock($chat_id);
                    continue;
                }

                if ($now - $last_activity_time < self::ACTIVITY_TIME) {
                    $logger->info('活跃时间间隔小于5分钟,不处理', ['chat_id' => $chat_id, 'now' => date("Y-m-d H:i:s", $now), 'last_activity_time' => date("Y-m-d H:i:s", $last_activity_time)]);
                    // 解锁先
                    $this->unlock($chat_id);
                    continue;
                }

                // 大于5分钟的话 ，就要开始处理消息了
                $logger->info('活跃时间间隔大于5分钟,开始处理', ['chat_id' => $chat_id, 'now' => date("Y-m-d H:i:s", $now), 'last_activity_time' => date("Y-m-d H:i:s", $last_activity_time)]);

                // 先去拿最后的消息处理时间，最后的最后活跃时间消息必须大于最后消息处理时间。
                $last_message_timestamp = $item->last_message_timestamp;
                if (!($last_activity_time > $last_message_timestamp)) {
                    $logger->info('最后的群活跃时间必须大于最后的消息处理时间', ['chat_id' => $chat_id, 'now' => date("Y-m-d H:i:s", $now), 'last_activity_time' => date("Y-m-d H:i:s", $last_activity_time), 'last_message_timestamp' => date("Y-m-d H:i:s", $last_message_timestamp)]);
                    // 解锁先
                    $this->unlock($chat_id);
                    continue;
                }

                // 开始获取消息，从当前时间开始往上获取消息 时间范围应该是：上一次处理消息的时间～当前时间
                if ($last_message_timestamp > 0) {
                    $message_start_time = date("Y-m-d H:i:s", $item->last_message_timestamp);
                } else {
                    $message_start_time = date("Y-m-d H:i:s", strtotime("-1 day"));
                }

                $message_end_time = date("Y-m-d H:i:s", $now);

                // 处理日程的真正逻辑
                $logger->info('开始处理真正的逻辑', ['chat_id' => $chat_id, 'last_activity_time' => date("Y-m-d H:i:s", $last_activity_time), 'last_message_timestamp' => date("Y-m-d H:i:s", $last_message_timestamp), 'message_start_time' => $message_start_time, 'message_end_time' => $message_end_time]);

                $group_assistant_service->handlerCheckSchedule($chat_id, $message_start_time, $message_end_time);

                // 最后重置一下last_message_timestamp
                $bot_chat_model->updateLastMessageTime($chat_id, $now);

                // 解锁
                $this->unlock($chat_id);
                $logger->info('chat_name: ' . $item->name . '处理完成', ['chat_id' => $chat_id]);
            } catch (AppException $exception) {
                // 解锁
                $this->unlock($chat_id);
                $logger->error('遇到系统异常：' . $exception->getMessage(), ['chat_id' => $chat_id, 'strace' => $exception->getTraceAsString()]);
            } catch (\Throwable $throwable) {
                // 解锁
                $this->unlock($chat_id);
                $logger->error('遇到未知错误：' . $throwable->getMessage(), ['chat_id' => $chat_id, 'strace' => $throwable->getTraceAsString()]);
            }
        }
    }



//    /**
//     * 处理机器人进群
//     * @param $data
//     * @return void
//     */
//    public function handlerBotAdd($data)
//    {
//        $chat_id = $data['chat_id'];
//        $chat_name = $data['chat_name'];
//        $union_id = $data['union_id'];
//        (new GroupAssistantService())->handlerGroupAssistantBotAdded($chat_id, $chat_name, $union_id);
//    }

    /**
     * 获取执行锁，用来判断当前任务是否正在执行。
     * 锁住的最大时间2个小时
     *
     * @param $chat_id
     *
     * @return bool  获锁成功返回true 否则返回false
     * @throws RedisException
     */
    public function lock($chat_id)
    {
        $key = 'running_handler_chat_' . $chat_id;
        if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 3600])) {
            Helpers::getLogger('assistant_group_lock')->info('成功获取执行锁', ['chat_id' => $chat_id]);
            return true;
        } else {
            Helpers::getLogger('assistant_group_lock')->info('获取执行锁失败', ['chat_id' => $chat_id]);
            return false;
        }
    }

    /**
     * 解锁
     *
     * @param $chat_id
     * @throws RedisException
     */
    public function unlock($chat_id)
    {
        $key = 'running_handler_chat_' . $chat_id;
        RedisCache::getInstance()->del($key);
        Helpers::getLogger('assistant_group_lock')->info('解锁成功', ['chat_id' => $chat_id]);
    }
}
