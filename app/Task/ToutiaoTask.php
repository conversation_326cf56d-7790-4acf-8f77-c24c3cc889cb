<?php


namespace App\Task;

use App\Constant\MediaType;
use App\Constant\RouteID;
use App\Constant\UserIDType;
use App\Logic\ToutiaoLogic;
use App\Model\HttpModel\Toutiao\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Toutiao\DMP\CustomAudienceModel;
use App\Model\HttpModel\Toutiao\DMP\DataSourceModel;
use App\Model\HttpModel\Toutiao\Majordomo\MajordomoModel;
use App\Model\HttpModel\Toutiao\Oauth2Model;
use App\Model\HttpModel\Toutiao\Subscribe\SubscribeModel;
use App\Model\HttpModel\Toutiao\User\UserModel as AuthUserModel;
use App\Model\RedisModel\AudienceChangeLogRedisModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\AudienceUploadFileModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\MediaMajordomoAccountModel;
use App\Model\SqlModel\Zeda\NoticeMessageModel;
use App\Model\SqlModel\Zeda\ToutiaoAudienceChangeLogModel;
use App\Model\SqlModel\Zeda\ToutiaoRdsSubscribeLogModel;
use App\MysqlConnection;
use App\Param\MediaAccountInfoParam;
use App\Service\AudienceService;
use App\Service\DeviceService;
use App\Service\MediaService;
use App\Service\NoticeService;
use App\Service\ToutiaoService;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Exception;

class ToutiaoTask
{
    const TICK_LIST = [
        [
            'env' => ['production'],
            'desc' => '刷新头条即将过期的access token',
            'interval_ms' => 180000, // 每3分钟运行一次
            /** @uses ToutiaoTask::refreshToken() */
            'method' => 'refreshToken',
        ],
        [
            'env' => ['production'],
            'desc' => '刷新头条账号管家即将过期的access token',
            'interval_ms' => 180000, // 每3分钟运行一次
            /** @uses ToutiaoTask::refreshMajordomoToken() */
            'method' => 'refreshMajordomoToken',
        ],
        [
            'env' => ['production'],
            'desc' => '查询头条上传包的状态',
            'interval_ms' => 300000, // 每5分钟运行一次
            /** @uses ToutiaoTask::readAudience() */
            'method' => 'readAudience',
        ],
        [
            'env' => ['production'],
            'desc' => '同步管家下面的广告主',
            'interval_ms' => 600000, // 每10分钟运行一次
            /** @uses ToutiaoTask::syncAccount() */
            'method' => 'syncAccount',
        ],
        [
            'env' => ['production'],
            'desc' => '刷新头条开发者即将过期的APP access token',
            'interval_ms' => 180000, // 每3分钟运行一次
            /** @uses ToutiaoTask::refreshAppToken() */
            'method' => 'refreshAppToken',
        ],
        [
            'env' => ['production'],
            'desc' => '订阅RDS',
            'interval_ms' => 600000, // 每10分钟运行一次
            /** @uses ToutiaoTask::subscribeRds() */
            'method' => 'subscribeRds',
        ]
    ];

    public function createDataSource($data)
    {
        Helpers::getLogger('toutiao')->info("create audience start.", [
            'table_audience_id' => $data['table_audience_id'],
        ]);
        $audience_model = new AudienceModel();
        $audience_info = $audience_model->getData($data['table_audience_id']);
        $file_list = $this->getFileList($audience_info);
        $toutiao_service = new ToutiaoService();
        $zip_paths = $toutiao_service->createDataSourceFile($data['table_audience_id'], $file_list, $audience_info->addition_type);

        $data_source_model = new DataSourceModel();

        $media_account = new MediaAccountModel();
        $media_account_info = $media_account->getDataByAccountId($audience_info->account_id);
        $file_paths = [];
        foreach ($zip_paths as $zip_path) {
            try {
                $upload_res = $data_source_model->fileUpload($audience_info->account_id, $media_account_info->access_token, $zip_path, md5_file($zip_path));
                $file_paths[] = $upload_res['file_path'];
            } catch (\Exception $e) {
                // 上传文件发生错误，退出本次创建数据源
                $audience_model->updateState($data['table_audience_id'], AudienceModel::ERROR);
                (new NoticeService())->addNotice(
                    $audience_info->creator_id,
                    NoticeService::LEVEL_DANGER,
                    "人群推送",
                    NoticeMessageModel::TYPE_AUDIENCE,
                    [$audience_info->company, $audience_info->audience_name, AudienceModel::STATE_MAP[AudienceModel::ERROR]],
                    RouteID::DMP_AUDIENCE
                );
                (new AudienceService())->syncAudienceInfo($data['table_audience_id'], AudienceModel::ERROR);
                return;
            }
        }

        try {
            $create_res = $data_source_model->create($audience_info->account_id, $media_account_info->access_token, $audience_info->audience_name, $audience_info->audience_desc, $file_paths, $audience_info->data_source_type);
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'file_paths 不能为空') !== false) {
                $error_code = AudienceModel::COVER_FAIL;
            }else {
                $error_code = AudienceModel::ERROR;
            }
            $audience_model->updateState($data['table_audience_id'], $error_code);
            // 创建数据源发生错误，退出本次创建数据源
            (new NoticeService())->addNotice(
                $audience_info->creator_id,
                NoticeService::LEVEL_DANGER,
                "人群推送",
                NoticeMessageModel::TYPE_AUDIENCE,
                [$audience_info->company, $audience_info->audience_name, AudienceModel::STATE_MAP[$error_code]],
                RouteID::DMP_AUDIENCE
            );
            (new AudienceService())->syncAudienceInfo($data['table_audience_id'], $error_code);
            return;
        }

        if (!empty($create_res)) {
            try {
                $audience_model->updateDataSourceId($data['table_audience_id'], $create_res['data_source_id']);
                (new AudienceService())->syncAudienceInfo($data['table_audience_id'], AudienceModel::PROCESSING);
            } catch (\Exception $e) {
                Helpers::getLogger('toutiao')->error("update data source fail", [
                    'table_audience_id' => $data['table_audience_id'],
                    'data_source_id' => $create_res['data_source_id']
                ]);
            }
        }
        Helpers::getLogger('toutiao')->info("create audience done.", [
            'table_audience_id' => $data['table_audience_id'],
        ]);
    }

    public function readAudience()
    {
        $audience_model = new AudienceModel();
        $toutiao_audience_list = $audience_model->getProcessingTaskByMediaType(MediaType::TOUTIAO);
        $data_source_model = new DataSourceModel();
        $custom_audience_model = new CustomAudienceModel();
        $media_account = new MediaAccountModel();
        $notice_service = new NoticeService();
        foreach ($toutiao_audience_list as $toutiao_audience) {
            $media_account_info = $media_account->getDataByAccountId($toutiao_audience->account_id);
            if (empty($toutiao_audience->audience_id)) {
                $list = $data_source_model->read($toutiao_audience->account_id, $media_account_info->access_token, [$toutiao_audience->data_source_id]);
                if (isset($list['data_list'][0]['default_audience']['custom_audience_id'])) {
                    $custom_audience_id = $list['data_list'][0]['default_audience']['custom_audience_id'];
                    try {
                        $custom_audience_model->publish($toutiao_audience->account_id, $media_account_info->access_token, $custom_audience_id);
                    } catch (\Exception $e) {
                        // log inside
                        continue;
                    }
                    $audience_model->updateAudienceId($toutiao_audience->id, $custom_audience_id);
                } elseif ($list['data_list'][0]['status'] == 2 && empty($list['data_list'][0]['default_audience'])) {
                    //背景：如果上传了错误的数据，会出现计算完成，但是没有人群包id的情况
                    //解决：数据源计算完成（status=2）且不生成custom_audience_id时，标记为失败
                    $audience_model->updateState($toutiao_audience->id, AudienceModel::COVER_FAIL);
                }
            } elseif (!empty($toutiao_audience->audience_id)) {
                $list = $custom_audience_model->read($toutiao_audience->account_id, $media_account_info->access_token, [$toutiao_audience->audience_id]);
                if ($list['custom_audience_list'][0]['status'] === 2) {
                    // 发布成功
                    $push_account_list = json_decode($toutiao_audience->push_account_list, true);
                    if (!empty($push_account_list)) {
                        try {
                            $push_account_list[] = $toutiao_audience->account_id;// 从2025开始，主账户也要放进推送列表
                            $push_account_list = array_map(function ($item) { // 格式化下，头条要求int
                                return (int)$item;
                            }, $push_account_list);
                            array_map(function ($push_account_list) use ($custom_audience_model, $toutiao_audience, $media_account_info) {
                                $custom_audience_model->push($toutiao_audience->account_id, $media_account_info->access_token, $toutiao_audience->audience_id, $push_account_list);
                            }, array_chunk($push_account_list, 100));
                        } catch (\Exception $e) {
                            // log inside
                            Helpers::getLogger('toutiao')->error("push toutiao audience fail", [
                                'audience_id' => $toutiao_audience->audience_id,
                                'push_account_list' => $push_account_list
                            ]);
                            continue;
                        }
                    }

                    $audience_model->updateSuccess($toutiao_audience->id, $list['custom_audience_list'][0]['upload_num'], $list['custom_audience_list'][0]['cover_num']);
                    $notice_service->addNotice(
                        $toutiao_audience->creator_id,
                        NoticeService::LEVEL_NOTICE,
                        "人群推送",
                        NoticeMessageModel::TYPE_AUDIENCE,
                        [$toutiao_audience->company, $toutiao_audience->audience_name, AudienceModel::STATE_MAP[AudienceModel::SUCCESS]],
                        RouteID::DMP_AUDIENCE
                    );
                    (new AudienceService())->syncAudienceInfo($toutiao_audience->id, AudienceModel::SUCCESS);
                } elseif ($list['custom_audience_list'][0]['status'] === 4) {
                    $audience_model->updateState($toutiao_audience->id, AudienceModel::COVER_FAIL);
                    $notice_service->addNotice(
                        $toutiao_audience->creator_id,
                        NoticeService::LEVEL_NOTICE,
                        "人群推送",
                        NoticeMessageModel::TYPE_AUDIENCE,
                        [$toutiao_audience->company, $toutiao_audience->audience_name, AudienceModel::STATE_MAP[AudienceModel::COVER_FAIL]],
                        RouteID::DMP_AUDIENCE
                    );
                    (new AudienceService())->syncAudienceInfo($toutiao_audience->id, AudienceModel::COVER_FAIL);
                }
            }
        }
    }

    public function uploadChangeAudience()
    {
        $audience_model = new AudienceModel();
        $data_source_model = new DataSourceModel();
        $media_account = new MediaAccountModel();
        $audience_change_log_model = new AudienceChangeLogModel();
        $audience_change_log_redis_model = new AudienceChangeLogRedisModel();
        $toutiao_audience_change_log_model = new ToutiaoAudienceChangeLogModel();
        $toutiao_audience_change_log_list = $toutiao_audience_change_log_model->getAllInStates([
            ToutiaoAudienceChangeLogModel::NOT_START,
            ToutiaoAudienceChangeLogModel::UPLOADING,
            ToutiaoAudienceChangeLogModel::PROCESSING
        ]);
        $process_audience_list = $toutiao_audience_change_log_list->whereIn('state', [ToutiaoAudienceChangeLogModel::UPLOADING, ToutiaoAudienceChangeLogModel::PROCESSING])->keyBy('audience_id');

        $handle_change_log_list = collect();
        foreach ($toutiao_audience_change_log_list->where('state', ToutiaoAudienceChangeLogModel::NOT_START) as $toutiao_audience_change_log) {
            if (!isset($process_audience_list[$toutiao_audience_change_log->audience_id])) {
                $handle_change_log_list->push($toutiao_audience_change_log);
            }
        }

        if ($handle_change_log_list->isNotEmpty()) {
            $toutiao_audience_change_log_model->updateStateInId($handle_change_log_list->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::UPLOADING);
        }

        Helpers::getLogger('toutiao')->info("头条uploadChangeAudience", [
            'time' => date('Y-m-d H:i:s'),
            'toutiao_audience_change_log_list' => $toutiao_audience_change_log_list->count(),
            'process_audience_list' => $process_audience_list->count(),
            'handle_change_log_list' => $handle_change_log_list->count(),
        ]);

        $handle_change_log_list = $handle_change_log_list->groupBy('audience_id');
        foreach ($handle_change_log_list as $audience_id => $handle_change_log) {
            $audience_info = $audience_model->getData($audience_id);
            if (empty($audience_info)) {
                $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::NOT_START);
                continue;
            }
            $media_account_info = $media_account->getDataByAccountId($audience_info->account_id);
            if (empty($media_account_info) || $media_account_info->state == 0) {
                Helpers::getLogger('toutiao')->warning("自动更新头条人群包获取account信息失败", [
                    'audience_id' => $audience_id,
                    'account_id' => $audience_info->account_id,
                ]);
                $audience_model->updateState($audience_id, AudienceModel::ACCOUNT_DISABLED);
                $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::FAIL);
                continue;
            }

            $change_time_list = $handle_change_log->pluck('change_time')->toArray();
            $audience_change_logs = $audience_change_log_model->getAllByAudienceIdAndChangeTimes($audience_id, $change_time_list);
            $audience_change_logs = $audience_change_logs->sortByDesc('change_time');
            $audience_change_log_model->updateStateInId($audience_change_logs->pluck('id')->toArray(), AudienceChangeLogModel::PROCESSING);

            $operation_type = $audience_change_logs[0]->operation_type;
            if ($operation_type == AudienceChangeLogModel::RESET) {
                // 如果是全量，则取最新change_time的三条记录（现在只有OAID/IMEI/IDFA这三个类型，后期可能会增加），然后进行压缩
                $zip_paths = $this->getFullFile($audience_id, $audience_change_logs);
            } else {
                // 如果是增量，将所有audience_log的日志进行文件压缩，然后缓存
                $zip_paths = $this->mergeIncrementalFile($audience_id, $audience_change_logs);
            }

            $all_file_path = [];
            foreach ($zip_paths as $wait_to_upload_zip) {
                $is_uploaded = $audience_change_log_redis_model->getToutiaoUploadFile($audience_id, $wait_to_upload_zip);
                if (!$is_uploaded) {
                    try {
                        $upload_res = $data_source_model->fileUpload($audience_info->account_id, $media_account_info->access_token, $wait_to_upload_zip, md5_file($wait_to_upload_zip));
                        $upload_file_path = $upload_res['file_path'];
                        $audience_change_log_redis_model->setToutiaoUploadFile($audience_id, $wait_to_upload_zip, $upload_file_path);
                        echo "audience_id: ", $audience_id, " audience_change_log_id: ", " device_task_id: ", $audience_info->device_task_id, " zip_path: ", $wait_to_upload_zip, " file_path: ", $upload_res['file_path'], PHP_EOL;
                    } catch (\Exception $e) {
                        Helpers::getLogger('toutiao')->error("自动更新头条人群包调用头条上传文件错误", [
                            'audience_id' => $audience_id,
                            'zip_path' => $wait_to_upload_zip,
                        ]);
                        $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::NOT_START);
                        $audience_change_log_model->updateStateInId($audience_change_logs->pluck('id')->toArray(), AudienceChangeLogModel::NOT_START);
                        continue 2;
                    }
                } else {
                    $upload_file_path = $is_uploaded;
                }
                $all_file_path[] = $upload_file_path;
            }

            if (empty($all_file_path)) {
                // 没有新设备
                $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::SUCCESS);
                $audience_change_log_model->updateStateInId($audience_change_logs->pluck('id')->toArray(), AudienceChangeLogModel::SUCCESS);
                continue;
            }

            try {
                // 头条单次更新的file_paths不能超过200个（不含200）
                if (count($all_file_path) >= 200) {
                    $all_file_path_chunks = array_chunk($all_file_path, 199);
                    foreach ($all_file_path_chunks as $key => $all_file_path_chunk) {
                        // 如果为全量更新，第一批数据以重置类型进行更新，第二批后的数据就不能重置类型了，会覆盖第一批数据，所以第二批后必须为添加类型
                        if ($operation_type == AudienceChangeLogModel::RESET && $key > 0) {
                            $operation_type = AudienceChangeLogModel::APPEND;
                        }
                        $data_source_model->update($audience_info->account_id, $media_account_info->access_token, $audience_info->data_source_id, $operation_type, $all_file_path_chunk);
                    }
                } else {
                    $data_source_model->update($audience_info->account_id, $media_account_info->access_token, $audience_info->data_source_id, $operation_type, $all_file_path);
                }

                $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::PROCESSING);
            } catch (\Exception $e) {
                Helpers::getLogger('toutiao')->error("头条数据源更新错误", [
                    'audience_id' => $audience_id,
                    'operation_type' => $operation_type,
                    'all_file_path' => $all_file_path,
                    'code' => $e->getCode(),
                    'message' => $e->getMessage()
                ]);
                if (strpos($e->getMessage(), '数据源不存在, 或无权操作') !== false) {
                    $audience_change_log_model->updateStateInId($audience_change_logs->pluck('id')->toArray(), AudienceChangeLogModel::FAIL);
                    $audience_model->updateState($audience_id, AudienceModel::AUDIENCE_INVALID);
                } else {
                    $toutiao_audience_change_log_model->updateStateInId($handle_change_log->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::NOT_START);
                    $audience_change_log_model->updateStateInId($audience_change_logs->pluck('id')->toArray(), AudienceChangeLogModel::NOT_START);
                }
            }
        }
    }

    public function readChangeAudience()
    {
        $media_account = new MediaAccountModel();
        $data_source_model = new DataSourceModel();
        $custom_audience_model = new CustomAudienceModel();
        $audience_change_log_model = new AudienceChangeLogModel();
        $audience_model = new AudienceModel();
        $toutiao_audience_change_log_model = new ToutiaoAudienceChangeLogModel();
        $toutiao_audience_change_log_list = $toutiao_audience_change_log_model->getAllInStates([ToutiaoAudienceChangeLogModel::PROCESSING]);
        $toutiao_audience_change_log_list = $toutiao_audience_change_log_list->groupBy('audience_id');
        foreach ($toutiao_audience_change_log_list as $audience_id => $toutiao_audience_change_logs) {
            /** @var \Illuminate\Support\Collection $toutiao_audience_change_logs */
            $last_publish_time = $toutiao_audience_change_logs->max('last_publish_time');
            // 24小时内只能发布一次
            if ((time() - $last_publish_time) < 24 * 3600) {
                continue;
            }

            $last_toutiao_audience_change_log = $toutiao_audience_change_logs->where('last_publish_time', $last_publish_time)->first();
            $media_account_info = $media_account->getDataByAccountId($last_toutiao_audience_change_log->account_id);
            if ($media_account_info->state === MediaAccountModel::STATE_INACTIVE) {
                // 账号失效，修改一下各个表的状态，避免重复的人群任务
                $audience_model->updateState($audience_id, AudienceModel::ACCOUNT_DISABLED);
                $toutiao_audience_change_log_model->updateStateInId($toutiao_audience_change_logs->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::FAIL);
                $audience_change_log_model->updateStateByAudienceIdAndChangeTimes($audience_id, $toutiao_audience_change_logs->pluck('change_time')->toArray(), AudienceChangeLogModel::FAIL);
                continue;
            }
            try {
                $list = $data_source_model->read($last_toutiao_audience_change_log->account_id, $media_account_info->access_token,[$last_toutiao_audience_change_log->data_source_id]);
            } catch (\Exception $e) {
                continue;
            }
            $change_log_list = $list['data_list'][0]['change_logs'] ?? [];
            // TODO 临时加个log排查
            if (empty($change_log_list)) {
                Helpers::getLogger('toutiao')->error("toutiao change_logs return null", [
                    'audience_id' => $audience_id,
                    'account_id' => $last_toutiao_audience_change_log->account_id
                ]);
            }
            $change_log = array_pop($change_log_list);
            if (isset($change_log['status']) && intval($change_log['status']) === 2) {
                try {
                    $custom_audience_model->publish($last_toutiao_audience_change_log->account_id, $media_account_info->access_token, $last_toutiao_audience_change_log->external_audience_id);
                } catch (\Exception $e) {
                    if (strpos($e->getMessage(), '人群包不存在') !== FALSE) {
                        $audience_model->updateState($last_toutiao_audience_change_log->audience_id, AudienceModel::AUDIENCE_DELETED);
                        $toutiao_audience_change_log_model->updateStateInId($toutiao_audience_change_logs->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::FAIL);
                        $audience_change_log_model->updateStateByAudienceIdAndChangeTimes($audience_id, $toutiao_audience_change_logs->pluck('change_time')->toArray(), AudienceChangeLogModel::FAIL);
                    }
                    if (strpos($e->getMessage(), '存在计算失败的人群包') !== FALSE) {
                        //计算失败的人群包重新上传
                        $toutiao_audience_change_log_model->updateStateInId($toutiao_audience_change_logs->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::NOT_START);
                    }
                    continue;
                }

                try {
                    MysqlConnection::getConnection('default')->beginTransaction();
                    $toutiao_audience_change_log_model->updateStateInId($toutiao_audience_change_logs->pluck('id')->toArray(), ToutiaoAudienceChangeLogModel::SUCCESS);
                    $audience_change_log_model->updateStateByAudienceIdAndChangeTimes($audience_id, $toutiao_audience_change_logs->pluck('change_time')->toArray(), AudienceChangeLogModel::SUCCESS);
                    $upload_num = $list['data_list'][0]['default_audience']['upload_num'];
                    $cover_num = $list['data_list'][0]['default_audience']['cover_num'];
                    $audience_model->updateSuccess($audience_id, $upload_num, $cover_num);
                    MysqlConnection::getConnection('default')->commit();
                } catch (\Throwable $e) {
                    MysqlConnection::getConnection('default')->rollBack();
                    continue;
                }
            }
        }
    }

    public function generateAudience()
    {
        $audience_model = new AudienceModel();
        $toutiao_audience_list = $audience_model->getRetryByMediaType(MediaType::TOUTIAO);
        // 先把状态修改 以免重复运行
        $audience_model->updateRetryProcessingInID($toutiao_audience_list->pluck('id'));
        foreach ($toutiao_audience_list as $toutiao_audience) {
            $this->createDataSource(['table_audience_id' => $toutiao_audience->id]);
        }
    }

    public function refreshToken()
    {
        $media_account_model = new MediaAccountModel();
        $media_account_list = $media_account_model->getExpiringListWithoutBindMajordomo(MediaType::TOUTIAO)->where('state', '!=', 0);
        $oauth2_model = new Oauth2Model();
        $developer_model = new MediaDeveloperAccountModel();
        $developer_info = [];
        foreach ($media_account_list as $media_account) {
            if (empty($developer_info["$media_account->platform-$media_account->company"])) {
                $developer_info["$media_account->platform-$media_account->company"] = $developer_model->getDataByPlatformMediaCompany($media_account->platform, MediaType::TOUTIAO, $media_account->company);
            }
            try {
                $refresh_token_info = $oauth2_model->refreshToken(
                    $developer_info["$media_account->platform-$media_account->company"]->app_id,
                    $developer_info["$media_account->platform-$media_account->company"]->app_secret,
                    $media_account->refresh_token
                );
            } catch (\Exception $e) {
                continue;
            }
            try {
                $media_account_model->refreshToken(
                    $media_account->id,
                    $refresh_token_info['access_token'],
                    request_time() + $refresh_token_info['expires_in'],
                    $refresh_token_info['refresh_token'],
                    request_time() + $refresh_token_info['refresh_token_expires_in']
                );
            } catch (\Exception $exception) {
                Helpers::getLogger('toutiao')->error("refresh token error", [
                    'toutiao_account' => $media_account,
                    'refresh_token_info' => $refresh_token_info
                ]);
            }
        }
    }

    public function refreshMajordomoToken()
    {
        $toutiao_majordomo_model = new MediaMajordomoAccountModel();
        $majordomo_list = $toutiao_majordomo_model->getAllExpiringByMediaType(MediaType::TOUTIAO);
        $oauth2_model = new Oauth2Model();
        $media_account_model = new MediaAccountModel();
        $developer_model = new MediaDeveloperAccountModel();

        $refresh_list = [];
        foreach ($majordomo_list as $majordomo) {
            $key = $majordomo->access_token;
            if (isset($refresh_list[$key])) {
                $refresh_list[$key]['ids'][] = $majordomo->id;
                $refresh_list[$key]['account_ids'][] = $majordomo->account_id;
            } else {
                $refresh_list[$key] = [
                    'ids' => [$majordomo->id],
                    'account_ids' => [$majordomo->account_id],
                    'platform' => $majordomo->platform,
                    'company' => $majordomo->company,
                    'developer_app_id' => $majordomo->developer_app_id,
                    'refresh_token' => $majordomo->refresh_token
                ];
            }
        }

        $developer_info = [];
        foreach ($refresh_list as $item) {
            $platform = $item['platform'];
            $company = $item['company'];
            $app_id = $item['developer_app_id'];
            $developer_key = "$platform-$company-$app_id";
            $refresh_start_time = date("Y-m-d H:i:s");
            if (empty($developer_info[$developer_key])) {
                $developer_info[$developer_key] =
                    $developer_model->getAppInfo(MediaType::TOUTIAO, $app_id)
                        ?: $developer_model->getDataByPlatformMediaCompany($platform, MediaType::TOUTIAO, $company);
            }

            try {
                $refresh_token_info = $oauth2_model->refreshToken(
                    $developer_info[$developer_key]->app_id,
                    $developer_info[$developer_key]->app_secret,
                    $item['refresh_token']
                );
            } catch (\Exception $e) {
                Helpers::getLogger("toutiao")->error("refresh_token: {$item['refresh_token']} refresh token error", [
                    'majordomo_account_info' => $item,
                    'error_msg' => $e->getMessage()
                ]);
                continue;
            }
            try {
                MysqlConnection::getConnection('default')->beginTransaction();
                $toutiao_majordomo_model->refreshTokenByIds(
                    $item['ids'],
                    $refresh_token_info['access_token'],
                    time() + $refresh_token_info['expires_in'],
                    $refresh_token_info['refresh_token'],
                    time() + $refresh_token_info['refresh_token_expires_in']
                );
                $media_account_model->updateAccessTokenByMajordomoIds(
                    MediaType::TOUTIAO,
                    $item['account_ids'],
                    $refresh_token_info['access_token']
                );
                MysqlConnection::getConnection('default')->commit();
            } catch (\Exception $exception) {
                Helpers::getLogger('toutiao')->error("refresh token error", [
                    'toutiao_account' => $item,
                    'refresh_token_info' => $refresh_token_info
                ]);
                MysqlConnection::getConnection('default')->rollBack();
            }

            Helpers::getLogger('toutiao')->info("refresh_token: {$item['refresh_token']} refresh token success", [
                'refresh_start_time' => $refresh_start_time,
                'refresh_end_time' => date("Y-m-d H:i:s"),
                'old_refresh_token' => $item['refresh_token'],
                'refresh_token_info' => $refresh_token_info
            ]);
        }
    }

    public function syncAccount()
    {
        Helpers::getLogger('toutiao')->info('sync account start', ['time' => date("Y-m-d H:i:s")]);
        $toutiao_majordomo_model = new MediaMajordomoAccountModel();
        $majordomo_list = $toutiao_majordomo_model
            ->getAllByMediaType(MediaType::TOUTIAO)
            ->where('state', 1);
        $majordomo_model = new MajordomoModel();
        $media_account_model = new MediaAccountModel();
        $advertiser_model = new AdvertiserModel();
        $media_service = new MediaService();
        $logic = new ToutiaoLogic();
        foreach ($majordomo_list as $majordomo) {
            // 特殊管家走新接口过渡
            if (in_array($majordomo->account_id, [****************, ****************])) {
                try {
                    $advertisers = $logic->getSubAccountList($majordomo->account_id, $majordomo->access_token);
                } catch (Exception $e) {
                    continue;
                }

                // 媒体上有的账号id
                $account_ids = [];
                foreach ($advertisers as $item) {
                    $account_ids[] = $item['advertiser_id'];
                }
            } else {
                try {
                    $advertisers = $majordomo_model->select($majordomo->account_id, $majordomo->access_token);
                } catch (Exception $e) {
                    continue;
                }

                // 媒体上有的账号id
                $account_ids = [];
                foreach ($advertisers['list'] as $item) {
                    $account_ids[] = $item['advertiser_id'];
                }
            }
            // 数据库纪录的账号id
            $advertiser_list = $media_account_model->getAllByMajordomoId($majordomo->account_id);
            $advertiser_account_ids = $advertiser_list->pluck('account_id')->toArray();
            // 开发者app_id
            $developer_app_id = $advertiser_list->where('agent', '!=', '')->first()->agent ?? '';

            // 对比后删除
            $delete_account_ids = array_diff($advertiser_account_ids, $account_ids);
            if (!empty($delete_account_ids)) {
                $media_account_model->removeAccountIds(MediaType::TOUTIAO, $delete_account_ids);
            }

            // 对比后添加
            $add_account_ids = array_diff($account_ids, $advertiser_account_ids);
            if (empty($add_account_ids)) {
                continue;
            }

            // 筛选掉已被其他管家授权的账号
            $other_majordomo_account_ids = [];
            foreach (array_chunk($add_account_ids, 1000) as $add_account_chunk) {
                $tmp_other_majordomo_account_ids = $media_account_model
                    ->getListInAccountIdsAndNotInMajordomoId($add_account_chunk, $majordomo->account_id)
                    ->pluck('account_id')
                    ->toArray();
                if ($tmp_other_majordomo_account_ids) {
                    $other_majordomo_account_ids = array_merge($other_majordomo_account_ids, $tmp_other_majordomo_account_ids);
                }
            }

            $add_account_ids = array_diff($add_account_ids, $other_majordomo_account_ids);
            if (empty($add_account_ids)) {
                continue;
            }

            // 订阅RDS
            $rds_account_ids = [];
            $need_update_budget_ids = [];
            foreach (array_chunk($add_account_ids, 99) as $add_account_ids) {
                try {
                    $tmp_ids = array_values($add_account_ids);
                    $advertiser_info_list = $advertiser_model->info(
                        $tmp_ids,
                        $majordomo->access_token,
                        ['id', 'name', 'company']);

                    // 获取日预算为无限的账号，然后设置10w预算
                    $budget_list = $advertiser_model->getBudget($tmp_ids, $majordomo->access_token);
                    foreach ($budget_list['list'] as $budget) {
                        if ($budget['budget_mode'] === 'BUDGET_MODE_INFINITE') {
                            $need_update_budget_ids[] = $budget['advertiser_id'];
                        }
                    }
                } catch (Exception $e) {
                    continue;
                }

                unset($advertiser_info_list['request_id']);
                foreach ($advertiser_info_list as $advertiser) {
                    $media_account_param = new MediaAccountInfoParam([
                        'media_type' => MediaType::TOUTIAO,
                        'platform' => $majordomo->platform,
                        'account_id' => $advertiser['id'],
                        'account_name' => $advertiser['name'],
                        'access_token' => $majordomo->access_token,
                        'access_token_expires' => 0,
                        'refresh_token' => '',
                        'refresh_token_expires' => 0,
                        'company' => $advertiser['company'],
                        'agent' => $developer_app_id,
                        'toutiao_majordomo_id' => $majordomo->account_id,
                    ]);
                    try {
                        $message = $media_service->saveAuthAccount($media_account_param, $majordomo->creator_id, $majordomo->creator);

                        // 需要订阅RDS的账号
                        if (strpos($message, '不允许授权') === false && in_array($advertiser['id'], $add_account_ids)) {
                            $rds_account_ids[] = (int)$advertiser['id'];
                        }

                        // 更新新账号预算 防止单账号跑多消耗
                        if (strpos($message, '不允许授权') !== false || !in_array($advertiser['id'], $need_update_budget_ids)) {
                            continue;
                        }
                        $advertiser_model->updateAdvertiserBudget([
                            'advertiser_id' => (int)$advertiser['id'],
                            'budget_mode' => 'BUDGET_MODE_DAY',
                            'budget' => 100000
                        ], $majordomo->access_token);
                    } catch (Exception $e) {
                        Helpers::getLogger('toutiao')->error("save auth account error", [
                            'code' => -1,
                            'majordomo' => $majordomo,
                            'advertiser' => $advertiser,
                            'error_message' => $e->getMessage()
                        ]);
                    }
                }
            }

            // 新增的账号订阅RDS
            if (!empty($rds_account_ids) && !in_array($developer_app_id, [****************, ''])) {
                (new ToutiaoLogic())->addRdsSubscribeLog($developer_app_id, $rds_account_ids);
            }
        }

        Helpers::getLogger('toutiao')->info('sync account end', ['time' => date("Y-m-d H:i:s")]);
    }

    public function refreshAppToken()
    {
        $media_account_model = new MediaAccountModel();
        $media_account_list = $media_account_model
            ->getExpiringListWithoutBindMajordomo(MediaType::TOUTIAO_DEVELOPER)
            ->where('state', '!=', 0);
        $oauth2_model = new Oauth2Model();
        $developer_model = new MediaDeveloperAccountModel();
        $developer_info = [];
        foreach ($media_account_list as $media_account) {
            $developer_key = "$media_account->platform-$media_account->account_id";
            if (empty($developer_info[$developer_key])) {
                $developer_info[$developer_key] = $developer_model->getAppInfo(MediaType::TOUTIAO, $media_account->account_id);
            }

            try {
                $refresh_token_info = $oauth2_model->appAccessToken(
                    $developer_info[$developer_key]->app_id,
                    $developer_info[$developer_key]->app_secret
                );
            } catch (\Exception $e) {
                continue;
            }

            try {
                $media_account_model->refreshToken(
                    $media_account->id,
                    $refresh_token_info['access_token'],
                    request_time() + $refresh_token_info['expires_in'],
                    "",
                    0
                );
            } catch (\Exception $exception) {
                Helpers::getLogger('toutiao')->error("refresh app token error", [
                    'account_info' => $media_account,
                    'refresh_token_info' => $refresh_token_info
                ]);
            }
        }
    }

    private function getFileList($audience_info)
    {
        if ($audience_info->addition_type === AudienceModel::TYPE_FILE_ADDITION) {
            $audience_file_model = new AudienceUploadFileModel();
            $file_info = $audience_file_model->getData($audience_info->device_task_id);
            $filename = AudienceService::AUDIENCE_DIR . '/' . $file_info->name;
            $file_list = [
                [
                    'name' => $filename,
                    'data_type' => $file_info->data_type,
                ]
            ];
        } else {
            $file_list = [
                [
                    'name' => DeviceService::getFile($audience_info->device_task_id, UserIDType::IDFA_MD5),
                    'data_type' => UserIDType::IDFA_MD5,
                ],
                [
                    'name' => DeviceService::getFile($audience_info->device_task_id, UserIDType::IMEI_MD5),
                    'data_type' => UserIDType::IMEI_MD5,
                ],
                [
                    'name' => DeviceService::getFile($audience_info->device_task_id, UserIDType::OAID), //sql生成的是原值文件，createDataSourceFile 再转 md5
                    'data_type' => UserIDType::OAID,
                ]
            ];
        }
        return $file_list;
    }

    /**
     * @param $audience_id
     * @param $audience_change_logs
     * @return array|bool
     */
    private function mergeIncrementalFile($audience_id, $audience_change_logs)
    {
        $audience_change_log_redis_model = new AudienceChangeLogRedisModel();
        $toutiao_service = new ToutiaoService();

        $all_zip_path = [];
        foreach ($audience_change_logs as $audience_change_log) {
            $is_compressed = $audience_change_log_redis_model->getToutiaoFileZip($audience_change_log->audience_upload_file_id, $audience_change_log->filename);
            if (!$is_compressed) {
                $file_uuid = "{$audience_id}-{$audience_change_log->filename}";
                $zip_path = $toutiao_service->createDataSourceFile($file_uuid, [
                    [
                        'name' => AudienceService::AUDIENCE_DIR . '/' . $audience_change_log->filename,
                        'data_type' => $audience_change_log->file_data_type,
                    ]
                ]);
                $audience_change_log_redis_model->setToutiaoFileZip($audience_change_log->audience_upload_file_id, $audience_change_log->filename, serialize($zip_path));
            } else {
                $zip_path = unserialize($is_compressed);
            }
            $all_zip_path = array_merge($all_zip_path, $zip_path);
        }

        return $all_zip_path;
    }

    /**
     * 获取全量的日志的压缩文件
     * @param $audience_id
     * @param $audience_change_logs
     * @return array
     */
    private function getFullFile($audience_id, $audience_change_logs): array
    {
        $audience_change_log_redis_model = new AudienceChangeLogRedisModel();
        $toutiao_service = new ToutiaoService();

        $all_zip_path = [];
        /** @var \Illuminate\Support\Collection $audience_change_logs */
        $latest_change_time = $audience_change_logs->max('change_time');
        $latest_audience_change_logs = $audience_change_logs->where('change_time', $latest_change_time);
        foreach ($latest_audience_change_logs as $audience_change_log) {
            $is_compressed = $audience_change_log_redis_model->getToutiaoFileZip($audience_change_log->audience_upload_file_id, $audience_change_log->filename);
            if (!$is_compressed) {
                $file_uuid = "{$audience_id}-{$audience_change_log->file_data_type}";
                $zip_path = $toutiao_service->createDataSourceFile(
                    $file_uuid,
                    [
                        [
                            'name' => AudienceService::AUDIENCE_DIR . '/' . $audience_change_log->filename,
                            'data_type' => $audience_change_log->file_data_type,
                        ]
                    ]
                );
                $audience_change_log_redis_model->setToutiaoFileZip($audience_change_log->audience_upload_file_id, $audience_change_log->filename, serialize($zip_path));
            } else {
                $zip_path = unserialize($is_compressed);
            }
            $all_zip_path = array_merge($all_zip_path, $zip_path);
        }

        return $all_zip_path;
    }

    /**
     * 订阅RDS
     * @return void
     */
    public function subscribeRds()
    {
        $subscribe_model = new SubscribeModel();
        $subscribe_log_model = new ToutiaoRdsSubscribeLogModel();

        // 接口一次最多500个账号
        $list = $subscribe_log_model->getUnsubscribeList();
        $list = $list->groupBy(function ($item) {
            return "{$item->app_id}-{$item->subscribe_task_id}-{$item->access_token}";
        });
        $core_user_id_list = [];
        $app_access_token_list = [];
        foreach ($list as $item) {
            $account_chunk = array_chunk($item->toArray(), 500);
            foreach ($account_chunk as $chunk) {
                $access_token = $chunk[0]->access_token;
                $app_id = $chunk[0]->app_id;
                $subscribe_task_id = $chunk[0]->subscribe_task_id;

                // 获取授权user信息
                if (!isset($core_user_id_list[$access_token])) {
                    try {
                        $auth_user_info = (new AuthUserModel())->info($access_token);
                        $core_user_id_list[$access_token] = (int)$auth_user_info['id'] ?? 0;
                    } catch (Exception $e) {
                        $msg = "获取授权user信息失败";
                        Helpers::getLogger('toutiao')->error($msg, [
                            'access_token' => $access_token
                        ]);
                        continue;
                    }
                }

                $core_user_id = $core_user_id_list[$access_token];

                // 特殊应用不用订阅
                if (in_array($app_id, [****************, ''])) {
                    continue;
                }

                if (!isset($app_access_token_list[$app_id])) {
                    $developer_token_info = (new MediaAccountModel())->getDataByAccountId($app_id, MediaType::TOUTIAO_DEVELOPER);
                    if (empty($developer_token_info)) {
                        $msg = "找不到该开发者的app_access_token";
                        Helpers::getLogger('toutiao')->error($msg, [
                            'app_id' => $app_id
                        ]);
                        continue;
                    }

                    $app_access_token_list[$app_id] = $developer_token_info->access_token;
                }

                $app_access_token = $app_access_token_list[$app_id];
                $account_ids = array_column($chunk, 'account_id');

                $data = [
                    'app_id' => (int)$app_id,
                    'subscribe_task_id' => (int)$subscribe_task_id,
                    'core_user_id' => (int)$core_user_id,
                    'advertiser_ids' => $account_ids
                ];

                try {
                    // 订阅
                    $subscribe_model->subscribeRDS($app_access_token, $data);

                    // 更新日志
                    $log_ids = array_column($chunk, 'id');
                    $subscribe_log_model->updateByIds($log_ids, ['state' => ToutiaoRdsSubscribeLogModel::STATE_SUBSCRIBE]);
                } catch (Exception $e) {
                    Helpers::getLogger('toutiao')->error('订阅RDS失败', [
                        'msg' => $e->getMessage(),
                        'data' => $data
                    ]);
                    continue;
                }
            }
        }
    }
}
