<?php
/**
 * Created by PhpStorm.
 * User: Lin
 * Date: 2022/3/21
 * Time: 10:45
 */

namespace App\Param\ADServing\Tencent\V3;

use App\Constant\TencentEum;
use App\Param\ADServing\Tencent\ADOtherSettingContentParam as TencentADOtherSettingContentParam;
use App\Utils\Helpers;

class ADOtherSettingContentParam extends TencentADOtherSettingContentParam
{
    public $is_use_material_package_id = 0;
    public $material_package_id = 0;

    public $marketing_scene = 'DEFAULT';

    public $auto_derived_creative_enabled = false;
    public $wxgame_playable_page_switch = true;
    public $wxgame_playable_page_path_map = [];
    public $wxgame_playable_cover = '';
    public $wxgame_playable_page_end_desc_mode = 'custom';
    public $wxgame_playable_page_end_desc = '';

    public $wxgame_playable_button_text = '';

    public $smart_delivery = '';

    public $iaap_mix_factor = 0;
    /**
     * 落地页类型
     * @var int $is_xijing
     */
    public $is_xijing = 0;

    /**
     * 朋友圈头像
     * @var array
     */
    public $profile_map = [];

    /**
     * 落地页
     * @var array
     */
    public $page_map = [];

    /**
     * 广告位类型
     * @var string
     */
    public $site_set_none = '';

    public $exploration_strategy = 'EXPLORATION_UNKNOW';

    public $pc_scene = [];

    public $priority_site_set = [];


    public $ad3_name = ['date_time', 'random'];

    /**
     * 广告位集合
     * @var array
     */
    public $site_set = [];

    /**
     * 创意模式
     * @var string
     */
    public $template_id = '';

    /**
     * 投放优化
     * @var array
     */
    public $scene_spec = [];

    /**
     * 落地页类型
     * @var string
     */
    public $page_type = '';

    /**
     * 自动版位
     * @var bool
     */
    public $automatic_site_enabled = false;

    /**
     * 版位突破
     * @var bool
     */
    public $enable_breakthrough_siteset = false;

    /**
     * 文字链接名称
     * @var string
     */
    public $link_name_type = '';

    /**
     * 文字链接文本
     * @var string
     */
    public $link_name_type_text = '';

    /**
     * 文字链接模板
     * @var string
     */
    public $link_name_type_text_template = '';

    /**
     * 图文创意组件开关
     * @var bool
     */
    public $mini_card_link_switch = false;

    public $mini_card_link_spec = [
        'desc' => '',
        'image' => '',
    ];

    /**
     * 文字链接
     * @var string
     */
    public $link_page_type = '';


    /**
     * 按钮文案
     * @var string
     */
    public $button_text = '';

    public $wechat_position = [];

    public $wechat_position_status = false;

    public $head_click_type = TencentEum::HEAD_CLICK_TYPE_LIVE_PROFILE;

    public $finder_object_visibility = true;

    public $promotion_card_id = '';

    public $promotion_title = '';

    public $promotion_desc = '';

    public $video_number_map = [];


    public $standard_switch = 'false';

    // 礼包码
    public $app_gift_pack_switch = false;

    public $app_gift_pack_code_code = '';
    public $app_gift_pack_code_tips = '';
    public $app_gift_pack_code_description = '';

    // 选择按钮
    public $choose_button_switch = false;
    public $chosen_button_text1 = '';
    public $chosen_button_text2 = '';

    public $dynamic_creative_type = 'DYNAMIC_CREATIVE_TYPE_COMMON';

    /**
     * 一方人群跑量加强开关状态
     * @var string
     */
    public $ecom_pkam_switch = '';

    public $live_video_mode = '';

    public $live_ad_type = 'VIDEO';

    /**
     * 一键下载
     * @var string
     */
    public $click_download = false;

    /**
     * 朋友圈首条评论
     * @var string
     */
    public $moment_first_comment = '';
    public $moment_first_comment_account_type = 'video';
    public $moment_first_comment_video_number_map = [];
    public $moment_first_comment_official_account_map = [];

    public $huxuan_material_switch = false;
    public $huxuan_material_map = [];

    /**
     * 参数初始化
     */
    public function paramHook()
    {
        // 视频号互选素材
        if (!$this->isHuxuanMaterialMode()) {
            $this->huxuan_material_switch = false;
            $this->huxuan_material_map = [];
        }
        if (!is_string($this->smart_delivery)) {
            if ($this->smart_delivery === true) {
                $this->smart_delivery = 'IAAP';
            } else {
                $this->smart_delivery = '';
            }
        } else {
            if ($this->smart_delivery === 'MINI_GANE') {
                $this->smart_delivery = 'MINI_GAME';
            }
        }

        if ($this->site_set_none !== 'pengyouquan') {
            $this->click_download = false;
        }
        if ($this->site_set_none !== 'pengyouquan' || !$this->choose_button_switch) {
            $this->choose_button_switch = false;
            $this->chosen_button_text1 = $this->chosen_button_text2 = '';
        }
        if ($this->head_click_type === TencentEum::HEAD_CLICK_TYPE_DEFAULT) {
            $this->finder_object_visibility = false;
        }
        if (isset($this->scene_spec['display_scene']) && !$this->scene_spec['display_scene']) {
            $this->scene_spec['display_scene'] = [];
        }
        if ($this->site_set_none == 'auto') {
            $this->automatic_site_enabled = true;
            $this->site_set = [];
        }
        if (!in_array('SITE_SET_MOBILE_UNION', $this->site_set) && !$this->automatic_site_enabled) {
            $this->scene_spec = [];
        }
        if (!in_array('SITE_SET_WECHAT', $this->site_set)) {
            $this->wechat_position_status = false;
            $this->wechat_position = [];
        } else {
            if ($this->wechat_position_status) {
                $this->scene_spec['wechat_position'] = $this->wechat_position;
            }
        }

        $this->profile_map = $this->tranObject($this->profile_map);
        $this->page_map = $this->tranObject($this->page_map);
        $this->moment_first_comment_video_number_map = $this->tranObject($this->moment_first_comment_video_number_map);
        $this->moment_first_comment_official_account_map = $this->tranObject($this->moment_first_comment_official_account_map);
        $this->wxgame_playable_page_path_map = $this->tranObject($this->wxgame_playable_page_path_map);

        if ($this->is_xijing == 4) {
            $this->page_type = 'PAGE_TYPE_MINI_GAME_WECHAT';
        } else {
            if ($rand_account_id = array_rand($this->page_map)) {
                $page_info = $this->getPageInfo($rand_account_id);
                $this->page_type = $page_info->page_type ?? 'PAGE_TYPE_DEFAULT';
            }
        }
    }

    /**
     * 校验参数
     */
    public function validate()
    {
        $error_msg_list = [];

        if ($this->click_download) {
//            if (!in_array($this->button_text,['立即下载','下载应用','下载游戏','立即安装'])) {}
            if (!in_array($this->link_name_type, ['DOWNLOAD_APP', 'DOWNLOAD_GAME'])) {
                $error_msg_list[] = [
                    'key' => 'template_id',
                    'value' => 0,
                    'msg' => "使用一键下载时，文字链接名称请使用'下载游戏'或'下载应用'"
                ];
            }
        }

        if ($this->site_set_none == 'search') {
            if ($this->dynamic_creative_type !== 'DYNAMIC_CREATIVE_TYPE_COMMON') {
                $error_msg_list[] = [
                    'key' => 'dynamic_creative_type',
                    'value' => 0,
                    'msg' => "搜索广告需要打开指定创意类型"
                ];
            }
        }

        if (in_array(TencentEum::SITE_SET_MOMENTS, $this->site_set) || $this->site_set_none == 'auto') {
            // 朋友圈首条评论
            if (Helpers::ADServingStrLen($this->moment_first_comment) > 60) {
                $error_msg_list[] = [
                    'key' => 'moment_first_comment',
                    'value' => 0,
                    'msg' => '首条评论不能超过30字'
                ];
            }
            // 朋友圈图文组件
            if ($this->mini_card_link_switch) {
                if (!$this->button_text) {
                    $error_msg_list[] = [
                        'key' => 'button_text',
                        'value' => 0,
                        'msg' => "图文组件需要设置按钮名称"
                    ];
                }
                if (!$this->mini_card_link_spec['desc']) {
                    $error_msg_list[] = [
                        'key' => 'mini_card_link_spec_desc',
                        'value' => 0,
                        'msg' => "请填写图文文案"
                    ];
                } elseif (Helpers::ADServingStrLen($this->mini_card_link_spec['desc']) > 16) {
                    $error_msg_list[] = [
                        'key' => 'mini_card_link_spec_desc',
                        'value' => 0,
                        'msg' => '文案不能超过8字'
                    ];
                }
                if (!$this->mini_card_link_spec['image']) {
                    $error_msg_list[] = [
                        'key' => 'mini_card_link_spec_image',
                        'value' => 0,
                        'msg' => '请选择图文图片'
                    ];
                }
            }
        }

        if ($this->app_gift_pack_switch) {
            if (!$this->app_gift_pack_code_tips && !$this->app_gift_pack_code_code) {
                $error_msg_list[] = [
                    'key' => 'app_gift_pack_switch',
                    'value' => 0,
                    'msg' => '开启礼包组件时，礼包码和礼包提示至少填写一个'
                ];
            }
            if ($this->app_gift_pack_code_tips && Helpers::ADServingStrLen($this->app_gift_pack_code_tips) > 24) {
                $error_msg_list[] = [
                    'key' => 'app_gift_pack_code_tips',
                    'value' => 0,
                    'msg' => '礼包提示字数不能超过12'
                ];
            }

            if ($this->app_gift_pack_code_description && Helpers::ADServingStrLen($this->app_gift_pack_code_description) > 36) {
                $error_msg_list[] = [
                    'key' => 'app_gift_pack_code_description',
                    'value' => 0,
                    'msg' => '礼包描述字数不能超过18'
                ];
            }
        }

        if ($this->site_set_none == 'xiaochengxu') {
            if ($this->wechat_position_status && !$this->wechat_position) {
                $error_msg_list[] = [
                    'key' => 'wechat_position',
                    'value' => 0,
                    'msg' => '开启公众号定投，需要选择定投场景'
                ];
            }
            if (!in_array(TencentEum::SITE_SET_WECHAT, $this->site_set)) {
                $error_msg_list[] = [
                    'key' => 'site_set',
                    'value' => 0,
                    'msg' => '微信公众号必选'
                ];
            }

        }

        if ($this->site_set_none == 'datongtou') {

//            if (count($this->site_set) < 2) {
//                $error_msg_list[] = [
//                    'key' => 'site_set',
//                    'value' => 0,
//                    'msg' => '选择大通投时，至少选择两个版位'
//                ];
//            }

            /*if (!in_array($this->is_xijing, [1, 2, 4]) && in_array($this->template_id, [720, 721])) {
                $error_msg_list[] = [
                    'key' => 'page_map',
                    'value' => $this->page_map,
                    'msg' => '选择大通投时，需要选择蹊径落地页'
                ];
            }*/
        }

        /*if ($this->is_xijing == 4) {
            if ($this->link_name_type &&
                !in_array($this->link_name_type, ['TRY_NOW', 'TRY_PLAY_NOW', 'ENTER_MINI_GAME', 'OPEN_MINI_GAME', 'PLAY_NOW', 'MORE_INFO'])) {
                $error_msg_list[] = [
                    'key' => 'link_name_type',
                    'value' => $this->link_name_type,
                    'msg' => '小游戏的情况下，文字链接只能选择"立即体验","立即试玩","进入小游戏","打开游戏","立即玩","了解更多"'
                ];
            }
        }*/

        // 选择蹊径性能版落地页
        if ($this->is_xijing == 2) {
            if (in_array(TencentEum::SITE_SET_QQSHOPPING, $this->site_set)) {
                $error_msg_list[] = [
                    'key' => 'page_map',
                    'value' => 0,
                    'msg' => 'QQ购物不支持蹊径性能版落地页'
                ];
            }
        }

//        if ($this->is_xijing == 1) {
//            if (!in_array(TencentEum::SITE_SET_MOMENTS, $this->site_set) && !in_array(TencentEum::SITE_SET_WECHAT, $this->site_set)) {
//                $error_msg_list[] = [
//                    'key' => 'page_map',
//                    'value' => $this->page_map,
//                    'msg' => "蹊径落地页仅支持微信朋友圈、微信公众号投放站点"
//                ];
//            }
//        }

        if (in_array(TencentEum::SITE_SET_CHANNELS, $this->site_set)) {
            if (!$this->promotion_card_id) {
                $error_msg_list[] = [
                    'key' => 'promotion_card_id',
                    'value' => $this->promotion_card_id,
                    'msg' => "视频号投放版位需要选择推广图片"
                ];
            }
            if (!$this->promotion_title) {
                $error_msg_list[] = [
                    'key' => 'promotion_title',
                    'value' => $this->promotion_title,
                    'msg' => "没有填写推广卡片标题"
                ];
            } else {
                $title_len = mb_strlen($this->promotion_title);
                if ($title_len > 10) {
                    $error_msg_list[] = [
                        'key' => 'promotion_title',
                        'value' => $this->promotion_title,
                        'msg' => "推广卡片标题长度不能超过10"
                    ];
                }
                if (in_array(TencentEum::SITE_SET_QQ_MUSIC_GAME, $this->site_set) || in_array(TencentEum::SITE_SET_KANDIAN, $this->site_set)) {
                    if ($title_len > 6) {
                        $error_msg_list[] = [
                            'key' => 'promotion_title',
                            'value' => $this->promotion_title,
                            'msg' => "推广卡片标题长度不能超过6( 视频号 + 腾讯看点/QQ音乐 )"
                        ];
                    }
                }
            }
            if (!$this->promotion_desc) {
                $error_msg_list[] = [
                    'key' => 'promotion_desc',
                    'value' => $this->promotion_desc,
                    'msg' => "没有填写推广卡片描述"
                ];
            } else {
                $promotion_len = mb_strlen($this->promotion_desc);
                if ($promotion_len > 14) {
                    $error_msg_list[] = [
                        'key' => 'promotion_desc',
                        'value' => $this->promotion_desc,
                        'msg' => "推广卡片描述长度不能超过14"
                    ];
                }
                if (in_array(TencentEum::SITE_SET_QQ_MUSIC_GAME, $this->site_set) || in_array(TencentEum::SITE_SET_KANDIAN, $this->site_set)) {
                    if ($promotion_len > 8) {
                        $error_msg_list[] = [
                            'key' => 'promotion_desc',
                            'value' => $this->promotion_desc,
                            'msg' => "推广卡片描述不能超过8( 视频号 + 腾讯看点/QQ音乐 )"
                        ];
                    }
                }
            }

            if (!$this->button_text) {
                $error_msg_list[] = [
                    'key' => 'button_text',
                    'value' => $this->button_text,
                    'msg' => "需要选择按钮文案"
                ];
            }
        }

        return $error_msg_list;
    }

    /**
     * 获取账号落地页信息
     * @param $account_id
     * @return array|mixed
     */
    public function getPageInfo($account_id)
    {
        if ($page_info = $this->page_map[$account_id] ?? '') {
            return json_decode($page_info);
        }
        return [];
    }

    /**
     * 获取账号朋友圈头像信息
     * @param $account_id
     * @return array|mixed
     */
    public function getProfileInfo($account_id)
    {
        if ($profile_info = $this->profile_map[$account_id] ?? '') {
            return json_decode($profile_info);
        }
        return [];
    }

    /**
     * @param $account_id
     * @return array|mixed
     */
    public function getPlayablePageInfo($account_id)
    {
        if ($playable_page_info = $this->wxgame_playable_page_path_map[$account_id] ?? '') {
            return json_decode($playable_page_info, true);
        }
        return [];
    }

    /**
     * 获取视频号信息
     * @param $account_id
     * @return array|mixed
     */
    public function getVideoNumberInfo($account_id)
    {
        if ($this->video_number_map && $video_number_info = $this->video_number_map[$account_id] ?? '') {
            return json_decode($video_number_info);
        }
        return [];
    }

    /**
     * 获取视频号
     * @param $account_id
     * @return int|string
     */
    public function getVideoNumberId($account_id)
    {
        if ($this->video_number_map && $video_number_info = $this->video_number_map[$account_id] ?? '') {
            $video_number_data = json_decode($video_number_info);
            return $video_number_data->promoted_object_id ?? '';
        }
        return '';
    }

    /**
     * 获取视频号信息
     * @param $account_id
     * @return array|mixed
     */
    public function getFirstCommentVideoNumberInfo($account_id)
    {
        if ($this->moment_first_comment_video_number_map && $moment_first_comment_video_number_info = $this->moment_first_comment_video_number_map[$account_id] ?? '') {
            return json_decode($moment_first_comment_video_number_info);
        }
        return [];
    }

    /**
     * 获取公众号信息
     * @param $account_id
     * @return array|mixed
     */
    public function getFirstCommentOfficialAccountInfo($account_id)
    {
        if ($this->moment_first_comment_official_account_map && $moment_first_comment_official_account_info = $this->moment_first_comment_official_account_map[$account_id] ?? '') {
            return json_decode($moment_first_comment_official_account_info);
        }
        return [];
    }

    /**
     * 微信单版位
     * @return bool
     */
    public function isWechatSingleSite()
    {
        return in_array(TencentEum::SITE_SET_MOMENTS, $this->site_set) && count($this->site_set) === 1;
    }

    /**
     * @return bool
     */
    public function isHuxuanMaterialMode()
    {
        return in_array(TencentEum::SITE_SET_CHANNELS, $this->site_set) && count($this->site_set) === 1 && $this->huxuan_material_switch;
    }

    /**
     * 参数格式化
     */
    public function format()
    {
    }

}
