<?php

namespace App\Param\DMS;

use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\CpMoneyDivideLogic;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\CpMoneyDivideConfigDetailModel;
use App\Model\SqlModel\Zeda\GamePayMoneyModel;
use App\Param\AbstractParam;
use App\Utils\DateTool;
use App\Utils\Math;
use FormulaInterpreter\Compiler;

class ComputeCpDivideMoneyParam extends AbstractParam
{
    /**
     * @var int
     */
    public $id = 0;

    /**
     * 统计维度：root_game, main_game, game
     * @var string
     */
    public $dimension = 'root_game';

    public $dimension_id = 0;

    /**
     * @var string
     */
    public $platform = '';

    /**
     * 1买量 2发行
     * @var int
     */
    public $proxy_type = 1;

    /**
     * 游戏参数
     */
    public $diy_param;

    /**
     * ios分成比例
     */
    public $ios_divide = 0;

    /**
     * 应用宝分成比例
     */
    public $yyb_divide = 0;

    /**
     * 小程序分成比例
     */
    public $applet_divide = 0;

    /**
     * 公式表达式
     * @var
     */
    public $formula;

    public $formula_expression;

    public $total_pay_money;
    public $min_pay_date;
    public $max_pay_date;
    public $diy_param_time_range_list;
    public $diy_per_param = [];
    public $date_and_per_profit = [];

    public $game_list_detail;
    public $total_pay_money_compute = false; // 累计触达的特殊计算方式入口
    public $monthly_segmentation_compute = false; // 当月分段计的特殊计算方式入口

    public function paramHook()
    {
        $this->formula_expression = $this->formula[0];
    }

    public function setDimensionId($dimension_id)
    {
        return $this->dimension_id = $dimension_id;
    }

    public function getGameTotalPayMoneyAndPayDateRange()
    {
        $game_pay_money_model = new GamePayMoneyModel();
        $res = $game_pay_money_model->getTotalPayMoneyAndPayDateRange($this->platform, $this->dimension, $this->dimension_id, $this->proxy_type);
        $this->total_pay_money = $res->total_pay_money ?? 0;
        $this->min_pay_date = $res->start_date;
        $this->max_pay_date = $res->end_date;
    }

    public function getDiyParamTimeRangeList()
    {
        foreach ($this->diy_param as $index => $diy_item) {
            if ($diy_item['pay_money']['date_range_value']) {
                $time_range = [
                    date('Y-m-d 00:00:00', strtotime($diy_item['pay_money']['date_range_value'][0])),
                    date('Y-m-d 23:59:59', strtotime($diy_item['pay_money']['date_range_value'][1])),
                ];
            } else {
                $time_range = [
                    date('Y-m-d 00:00:00', strtotime($this->min_pay_date)),
                    date('Y-m-d 23:59:59', strtotime($this->max_pay_date)),
                ];
            }
            $this->diy_param_time_range_list[$index] = $time_range;
        }
    }

    public function computeDiyParamPerMoneyDivide()
    {
        $diy_per_param = [];
        $compiler = new Compiler();
        // 统计开始  注意确认统计维度dimension
        foreach ($this->diy_param as $index => $data) {

            // date_range_type=1:指定时间
            if ($data['pay_money']['date_range_type'] == 1) {
                $game_pay_model = new GamePayMoneyModel();
                $sql_data = $game_pay_model->getPayMoneyAndTotalMoney($data['pay_money'], $this->platform, $this->proxy_type, $this->dimension_id, $this->dimension);
                if (empty($sql_data)) {
                    throw new AppException("游戏范围所选游戏为空，请检查配置");
                }
                $pay_money_list = $sql_data['list'];
                foreach ($data['condition_param'] as $sub_param) {
                    $param_name = $sub_param['param_name'];
                    $res = (new CpMoneyDivideLogic)->judgePayMoney($pay_money_list->pay_money, $sub_param['op'], $sub_param['value']);
                    if ($res) {
                        $executable = $compiler->compile($sub_param['if_true'][0]);
                    } else {
                        $executable = $compiler->compile($sub_param['if_false'][0]);
                    }
                    $variables[$param_name] = $executable->run(['pay_money' => $pay_money_list->dimension_game_pay_money]);
                    $diy_per_param[$param_name][] = [
                        'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                        'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                        'condition_param_time_start' => $this->diy_param_time_range_list[$index][0],
                        'condition_param_time_end' => $this->diy_param_time_range_list[$index][1],
                        'variables_money' => $variables[$param_name],
                        'dimension_game_pay_money' => $pay_money_list->dimension_game_pay_money,
                        'per' => $pay_money_list->dimension_game_pay_money > 0 ?
                            bcdiv($variables[$param_name], $pay_money_list->dimension_game_pay_money, 10) :
                            0,
                    ];
                }
            } else {
                // 计算diy里面的游戏范围的流水和维度选择游戏的流水，按月分类
                $logic = new CpMoneyDivideLogic();
                $game_pay_model = new GamePayMoneyModel();
                $sql_data = $game_pay_model->getPayMoneyAndTotalMoney($data['pay_money'], $this->platform, $this->proxy_type, $this->dimension_id, $this->dimension);
                if (empty($sql_data)) {
                    throw new AppException("游戏范围所选游戏为空，请检查配置");
                }
                $pay_money_list = $sql_data['list'];
                foreach ($data['condition_param'] as $sub_param) {
                    $param_name = $sub_param['param_name'];
                    if (!$pay_money_list) {
                        $diy_per_param[$param_name][] = [
                            'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                            'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                            'condition_param_time_start' => $this->diy_param_time_range_list[$index][0],
                            'condition_param_time_end' => $this->diy_param_time_range_list[$index][1],
                            'variables_money' => 0,
                            'dimension_game_pay_money' => 0,
                            'per' => 0,
                        ];
                    } else {
                        // 聚合参数 判断任意满足或全部满足
                        $res_var_list = []; // 初始化结果集

                        /*------------------------- 计算结果集 -------------------------*/
                        if ($sub_param['type'] == 1) {
                            // 任意满足
                            foreach ($pay_money_list as $pay_money) {
                                $res = $logic->judgePayMoney($pay_money->pay_money, $sub_param['op'], $sub_param['value']);
                                if ($res) {
                                    $executable = $compiler->compile($sub_param['if_true'][0]);
                                } else {
                                    $executable = $compiler->compile($sub_param['if_false'][0]);
                                }
                                // 参数结果集存起来
                                $compute_res = $executable->run(['pay_money' => $pay_money->dimension_game_pay_money]);
                                $res_var_list[] = $compute_res;
                                $diy_per_param[$param_name][] = [
                                    'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                                    'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                                    'condition_param_time_start' => date('Y-m-01 00:00:00', strtotime($pay_money->month)), // 月初
                                    'condition_param_time_end' => date('Y-m-t 23:59:59', strtotime($pay_money->month)), // 月末
                                    'variables_money' => $compute_res,
                                    'dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                                    'per' => $pay_money->dimension_game_pay_money > 0 ? bcdiv($compute_res, $pay_money->dimension_game_pay_money, 10) : 0,
                                ];
                            }
                        } else if ($sub_param['type'] == 2) {
                            // 全部满足
                            $all_judge_res = true;
                            foreach ($pay_money_list as $pay_money) {
                                $res = $logic->judgePayMoney($pay_money->pay_money, $sub_param['op'], $sub_param['value']);
                                if (!$res) {
                                    $all_judge_res = false;
                                    break;
                                }
                            }
                            foreach ($pay_money_list as $pay_money) {
                                if ($all_judge_res) {
                                    $executable = $compiler->compile($sub_param['if_true'][0]);
                                } else {
                                    $executable = $compiler->compile($sub_param['if_false'][0]);
                                }
                                // 参数结果集存起来
                                $compute_res = $executable->run(['pay_money' => $pay_money->dimension_game_pay_money]);
                                $res_var_list[] = $compute_res;
                                $diy_per_param[$param_name][] = [
                                    'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                                    'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                                    'condition_param_time_start' => date('Y-m-01 00:00:00', strtotime($pay_money->month)),
                                    'condition_param_time_end' => date('Y-m-t 23:59:59', strtotime($pay_money->month)),
                                    'variables_money' => $compute_res,
                                    'dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                                    'per' => $pay_money->dimension_game_pay_money > 0 ? bcdiv($compute_res, $pay_money->dimension_game_pay_money, 10) : 0,
                                ];
                            }
                        } else if ($sub_param['type'] == 3) {
                            // 触达
                            $judge_res = false;
                            foreach ($pay_money_list as $pay_money) {
                                if (!$judge_res) {
                                    $judge_res = $logic->judgePayMoney($pay_money->pay_money, $sub_param['op'], $sub_param['value']);
                                }
                                if ($judge_res) {
                                    $executable = $compiler->compile($sub_param['if_true'][0]);
                                } else {
                                    $executable = $compiler->compile($sub_param['if_false'][0]);
                                }
                                // 参数结果集存起来
                                $compute_res = $executable->run(['pay_money' => $pay_money->dimension_game_pay_money]);
                                $res_var_list[] = $compute_res;
                                $diy_per_param[$param_name][] = [
                                    'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                                    'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                                    'condition_param_time_start' => date('Y-m-01 00:00:00', strtotime($pay_money->month)),
                                    'condition_param_time_end' => date('Y-m-t 23:59:59', strtotime($pay_money->month)),
                                    'variables_money' => $compute_res,
                                    'dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                                    'per' => $pay_money->dimension_game_pay_money > 0 ? bcdiv($compute_res, $pay_money->dimension_game_pay_money, 10) : 0,
                                ];
                            }
                        } else if ($sub_param['type'] == 4) {
                            // 触达则下一次
                            $judge_res = false;
                            foreach ($pay_money_list as $pay_money) {
                                if ($judge_res) {
                                    $executable = $compiler->compile($sub_param['if_true'][0]);
                                } else {
                                    $executable = $compiler->compile($sub_param['if_false'][0]);
                                }
                                if (!$judge_res) {
                                    $judge_res = $logic->judgePayMoney($pay_money->pay_money, $sub_param['op'], $sub_param['value']);
                                }
                                // 参数结果集存起来
                                $compute_res = $executable->run(['pay_money' => $pay_money->dimension_game_pay_money]);
                                $res_var_list[] = $compute_res;
                                $diy_per_param[$param_name][] = [
                                    'diy_param_time_start' => $this->diy_param_time_range_list[$index][0],
                                    'diy_param_time_end' => $this->diy_param_time_range_list[$index][1],
                                    'condition_param_time_start' => date('Y-m-01 00:00:00', strtotime($pay_money->month)),
                                    'condition_param_time_end' => date('Y-m-t 23:59:59', strtotime($pay_money->month)),
                                    'variables_money' => $compute_res,
                                    'dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                                    'per' => $pay_money->dimension_game_pay_money > 0 ? bcdiv($compute_res, $pay_money->dimension_game_pay_money, 10) : 0,
                                ];
                            }
                        } else if ($sub_param['type'] == 5) {
                            $this->total_pay_money_compute = true; // 累计触达，计算方式有点特殊和复杂，从其他入口去计算
                        } else if ($sub_param['type'] == 6) {
                            $this->monthly_segmentation_compute = true; // 当月分段计算
                        }
                        /*------------------------- 计算结果集 -------------------------*/

                        // 处理结果集 需要判断聚合类型
                        switch ($sub_param['aggregation']) {
                            case 'sum':
                                $agg_value = array_sum($res_var_list);
                                break;
                            case 'avg':
                                $agg_value = Math::div(array_sum($res_var_list), count($pay_money_list));
                                break;
                            case 'max':
                                $agg_value = max($res_var_list);
                                break;
                            case 'min':
                                $agg_value = min($res_var_list);
                                break;
                            default:
                                throw new AppException('聚合类型错误', ResponseCode::PARAM_ERROR);
                                break;
                        }

                        // 保存结果
                        $variables[$param_name] = $agg_value;

                    }
                }


            }
        }

        $this->diy_per_param = $diy_per_param;
    }

    public function computeDateProfit()
    {
        if ($this->total_pay_money_compute) {
            $this->computeDateProfitByTotalPayCompute();
        } else if ($this->monthly_segmentation_compute) {
            $this->computeDateProfitByMonthlySegmentation();
        } else {
            // 获取统计日期
            $date_range = [];

            // 总公式做个符合位的索引
            $formula_index = [];
            foreach ($this->formula[1] as $index => $item) {
                $formula_index['f_' . $item['data']] = $index;
            }

            $game_pay_model = new GamePayMOneyModel();
            $compiler = new Compiler();

            $pay_money_list = [];
            if ($this->diy_param) {
                // 获取自定义参数里的时间区间集合
                foreach ($this->diy_param_time_range_list as $item) {
                    $start_date = date('Y-m-d', strtotime($item[0]));
                    $end_date = date('Y-m-d', strtotime($item[1]));
                    $date_array = DateTool::getDateRangeDataByDateRange($start_date, $end_date);
                    $date_range = array_unique(array_merge($date_range, $date_array));
                }

                // 获取每天的充值金额
                $date_pay_money = [];
                foreach ($this->diy_param as $index => $diy_param_item) {
                    $date_pay_money[] = $game_pay_model->getDimensionDatePayMoney(
                        strtotime($this->diy_param_time_range_list[$index][0]),
                        strtotime($this->diy_param_time_range_list[$index][1]),
                        $this->platform,
                        $this->dimension,
                        $this->dimension_id,
                        $this->proxy_type,
                        $diy_param_item['pay_money']['is_channel']
                    )->keyBy('date')->toArray();
                }
                foreach ($date_pay_money as $item) {
                    $pay_money_list = array_merge($pay_money_list, $item);
                }
            } else {
                // 没有自定义参数，就拿最小和最大付费时间区间集合
                $start_date = date('Y-m-d', strtotime($this->min_pay_date));
                $end_date = date('Y-m-d', strtotime($this->max_pay_date));
                $date_range = DateTool::getDateRangeDataByDateRange($start_date, $end_date);

                // 不存在自定义参数的时候 直接获取总充值金额
                $pay_money_list = $game_pay_model->getDimensionDatePayMoney(
                    strtotime($this->min_pay_date),
                    strtotime($this->max_pay_date),
                    $this->platform,
                    $this->dimension, // TODO 改成兼容根和子游戏id
                    $this->dimension_id, // TODO 改成兼容根和子游戏id
                    $this->proxy_type
                )->keyBy('date')->toArray();
            }

            $executable = $compiler->compile($this->formula_expression);        // 先代入公式
            foreach ($date_range as $date) {
                $variables['total_pay_money'] = isset($pay_money_list[$date]) ? $pay_money_list[$date]->pay_money : 0;
                // 算出参数值
                foreach ($this->diy_per_param as $sub_param_name => $diy_per_param_item) {
                    foreach ($diy_per_param_item as $item) {
                        // 判断在不在时间范围内
                        $timestamp = strtotime($date);
                        if (
                            ($timestamp >= strtotime($item['diy_param_time_start']) && $timestamp <= strtotime($item['diy_param_time_end']))
                            &&
                            ($timestamp >= strtotime($item['condition_param_time_start']) && $timestamp <= strtotime($item['condition_param_time_end']))
                        ) {
                            $per = $item['per'];
                            // 判断当天有木有流水
                            if (isset($pay_money_list[$date])) {
                                $variables[$sub_param_name] = bcmul($per, $pay_money_list[$date]->pay_money, 10);
                            } else {
                                $variables[$sub_param_name] = 0; // 没有流水就是0
                            }
                            break;
                        } else {
                            // 不在这里范围里面 就不好搞了 要去判断到底是用0 还是1  这个时候要判断这个参数用来 */ 还是 +-
                            if (!isset($formula_index['f_' . $sub_param_name])) {
                                // 公式如果没用上这个参数 则不进行下去
                                continue;
                            }
                            $f_index = $formula_index['f_' . $sub_param_name]; // 拿到参数在公式中的位置
                            $last = $pre = '';  // 初始化前后位置的操作符
                            if (isset($param->formula[1][$f_index + 1])) {
                                $last = $param->formula[1][$f_index + 1]['data'];
                            }
                            if (isset($param->formula[1][$f_index - 1])) {
                                $pre = $param->formula[1][$f_index - 1]['data'];
                            }
                            if (in_array($last, ['*', '/']) || in_array($pre, ['*', '/'])) {
                                $variables[$sub_param_name] = 1;
                            } else {
                                $variables[$sub_param_name] = 0;
                            }
                        }
                    }
                }
                $date_profit = $executable->run($variables);
                $per_profit = $variables['total_pay_money'] == 0 ? 0 : bcdiv($date_profit, $variables['total_pay_money'], 10);
                // 关键参数
                // platform, game_id, proxy_type, date, per_profit, date_pay_money
                // 计算出主游戏某天每元需要分出去的比例后，可以应用到主游戏下的所有game_id
                $this->date_and_per_profit[] = [
                    'date' => $date,
                    'per_profit' => $per_profit
                ];
            }
        }
    }

    public function combinedGameProfit(): array
    {
        // 找出当前dimension_id对应最细力度的子游戏集合
        $game_list_detail = $this->getGameListDetail();
        foreach ($this->date_and_per_profit as $item) {
            if ($item['per_profit'] == 0) {
                continue;
            }
            foreach ($game_list_detail as $game_item) {
                $insert_data[] = [
                    'cp_money_divide_config_id' => $this->id,
                    'platform' => $this->platform,
                    'root_game_id' => $game_item->root_game_id,
                    'main_game_id' => $game_item->main_game_id,
                    'game_id' => $game_item->game_id,
                    'proxy_type' => $this->proxy_type,
                    'profit_date' => $item['date'],
                    'per_profit' => $item['per_profit'],
                    'ios_divide' => $this->ios_divide,
                    'yyb_divide' => $this->yyb_divide,
                    'applet_divide' => $this->applet_divide,
                ];
            }
        }
        return $insert_data ?? [];
    }

    public function getGameListDetail()
    {
        // tw-15852这个子游戏在game_pay_money里,既有买量又有发行,这就导致dim_game_id里的is_channel不适用来判断买量发行
        /***************废除*****************/
//        if ($this->dimension == 'root_game') {
//            $game_list = (new V2DimGameIdModel())->getAllGamesByRootGames($this->platform, [$this->dimension_id]);
//        }
//
//        if ($this->dimension == 'main_game') {
//            $game_list = (new V2DimGameIdModel())->getAllGamesByMainGames($this->platform, [$this->dimension_id]);
//        }
//
//        if ($this->dimension == 'game') {
//            $game_list = (new V2DimGameIdModel())->getListInGameId($this->platform, [$this->dimension_id]);
//        }
//
//        foreach ($game_list ?? [] as $item) {
//
//            // 自营或联运区分暂时在这判断吧，最好是通过sql区分出来
//            if ($this->proxy_type == 1 && $item->is_channel == 0) {
//                $this->game_list_detail[] = $item;
//            }
//
//            if ($this->proxy_type == 2 && $item->is_channel == 1) {
//                $this->game_list_detail[] = $item;
//            }
//
//        }
        /***************废除*****************/

        $game_list = (new GamePayMoneyModel())->getGameListByDimensionId($this->platform, $this->dimension, [$this->dimension_id], $this->proxy_type);
        $this->game_list_detail = $game_list;

        return $this->game_list_detail;
    }

    /**
     * 累计触达计算，计算每天均摊的值
     * @return void
     */
    public function computeDateProfitByTotalPayCompute()
    {
        $logic = new CpMoneyDivideLogic;
        // 累计触达 按天按根统计流水,然后按天累计充值金额,然后去判断阈值,切分阈值金额,算出当天的比例
        foreach ($this->diy_param as $index => $data) {
            // 获取每日充值金额
            $date_pay_money_list = (new GamePayMoneyModel())->getDimensionDatePayMoneyByGameRange(
                strtotime($this->diy_param_time_range_list[$index][0]),
                strtotime($this->diy_param_time_range_list[$index][1]),
                $this->platform,
                $data['pay_money']['game_range'],// todo 要改用diy_param里面的game_range
                $this->proxy_type,
                $data['pay_money']['is_channel']
            )->keyBy('date')->toArray();

            // 获取每日累充
            $total_pay_money = 0;
            foreach ($date_pay_money_list as &$date_item) {
                $total_pay_money += $date_item->pay_money;
                $date_item->total_pay_money = $total_pay_money;
            }

            // 构造条件参数的帮助参数，供后面使用
            $condition_help_param = [];
            foreach ($data['condition_param'] as $condition_index => $sub_param) {
                $condition_help_param[$condition_index] = [
                    'min_value' => $sub_param['value'][0],
                    'max_value' => $sub_param['value'][1],
                    'rate' => $sub_param['if_true'][0]
                ];
            }

            // 计算每天充值对应的研发分成，计算出当天的per
            $touch_max_range = false;
            $touch_condition_index_range = [];// 用来记录触达过某个区间
            foreach ($date_pay_money_list as $date => $date_data) {
                // 当天没有充值的可以跳过了，没有充值，对应的分成比率就是0
                if ($date_data->pay_money == 0) continue;

                // 判断累充属于那个W，一个累充只能属于一个区间
                foreach ($data['condition_param'] as $condition_index => $sub_param) {
                    $res = $logic->judgePayMoney($date_data->total_pay_money, $sub_param['op'], $sub_param['value']);
                    if (!$res) {
                        continue;
                    } else {
                        if ($condition_index == 0 || $touch_max_range || in_array($condition_index, $touch_condition_index_range)) {
                            // 累充属于第一个最小的区间，这种最简单了，直接当天充值*倍率就可以了，也就相当于当天每元的分成为倍率
                            // 触达最大区间后，往后的流水都是按最大的区间倍数来算了
                            $this->date_and_per_profit[] = [
                                'date' => $date,
                                'per_profit' => $condition_help_param[$condition_index]['rate'],
                                'cp_money' => $condition_help_param[$condition_index]['rate'] * $date_data->pay_money,
                                'pay_money' => $date_data->pay_money,
                                'total_pay_money' => $date_data->total_pay_money,
                                'compute_per_profit' => bcdiv($condition_help_param[$condition_index]['rate'] * $date_data->pay_money, $date_data->pay_money, 10)
                            ];
                        } else {
                            // 累充属于非第一个最小区间的，找出上个区间的最大阈值C，然后累充减去C，就得到
                            $previous_condition_max_value = $condition_help_param[$condition_index - 1]['max_value'];
                            $over_money = $date_data->total_pay_money - $previous_condition_max_value;
                            $over_rate = $condition_help_param[$condition_index]['rate'];

                            $not_over_money = $date_data->pay_money - $over_money;
                            $not_over_rate = $condition_help_param[$condition_index - 1]['rate'];

                            $cp_money = ($over_money * $over_rate) + ($not_over_money * $not_over_rate);
                            $cp_rate = bcdiv($cp_money, $date_data->pay_money, 10);
                            $this->date_and_per_profit[] = [
                                'date' => $date,
                                'per_profit' => $cp_rate,
                                'cp_money' => $cp_money,
                                'pay_money' => $date_data->pay_money,
                                'total_pay_money' => $date_data->total_pay_money,
                                'compute_per_profit' => $cp_rate
                            ];

                            if ($condition_index == (count($condition_help_param) - 1)) {
                                // 如果累计触达最大的区间，那么下次的所有充值都是按最大区间的倍数了，无需再拆分去计算
                                $touch_max_range = true;
                            }

                            /**
                             * W1: 0 <x <=1000
                             * W2: 1001 <x <=5000
                             * W3: x < 5000
                             * 每天的充值金额：D1:500元, D2:600元 D3:100元 D4：100元
                             * D2到达W2档位时，需要分割下当天的充值金额去不同的档位，D3如果还是在W2档位，那么D3就不需要去分割了，因为D2已经分割过了
                             * 总结：第一次触达W2，需要和上个区间去平均比例，后面如果还是触达W2，那就一直按照W2的比例了
                             */
                            $touch_condition_index_range[] = $condition_index;

                            // TODO 还没有考虑跨区间的情况，一般不太可能跨度太大，后面在考虑优化
                        }
                    }
                }
            }

        }
    }

    /**
     * 当月分段计
     * @return void
     */
    public function computeDateProfitByMonthlySegmentation()
    {
        // 流程：
        // 游戏范围的按月充值金额 + 所选游戏的按月充值金额
        // 判断游戏范围月充金额的区间，然后直接切割去计算分成
        // 得出某个月的整体分成比例
        $game_pay_model = new GamePayMoneyModel();
        $logic = new CpMoneyDivideLogic();
        foreach ($this->diy_param as $data) {
            $sql_data = $game_pay_model->getPayMoneyAndTotalMoney($data['pay_money'], $this->platform, $this->proxy_type, $this->dimension_id, $this->dimension);
            if (empty($sql_data)) {
                throw new AppException("游戏范围所选游戏为空，请检查配置");
            }
            $pay_money_list = $sql_data['list'];
            if (!$pay_money_list) {
                // 如果没有流水，则没有分成，所以当月每天的分成为0，分成为0的date_per_profit不用入库，所以跳过
                continue;
            }

            foreach ($data['condition_param'] as $sub_param) {
                // 注意：目前默认都是按 大于阈值 这样的配置格式来配置
                $over_rate = $sub_param['if_true'][0];
                $not_over_rate = $sub_param['if_false'][0];
                $threshold = $sub_param['value'][0]; // 阈值
                foreach ($pay_money_list as $pay_money) {
                    if ($pay_money->dimension_game_pay_money <= 0) {
                        // 如果统计维度的游戏当月流水为0，对应的分成也为0，可以跳过，无需统计无效比例
                        continue;
                    }
                    $date_range = DateTool::getDateRangeDataByDateRange(date('Y-m-01', strtotime($pay_money->month)), date('Y-m-t', strtotime($pay_money->month)));
                    // 轮询判断每个月的流水是否超过阈值
                    if (!$logic->judgePayMoney($pay_money->pay_money, $sub_param['op'], $sub_param['value'])) {
                        // 不超过阈值，这种最简单，直接按否则的倍数去算每天的分成比例
                        foreach ($date_range as $date) {
                            $this->date_and_per_profit[] = [
                                'date' => $date,
                                'per_profit' => $not_over_rate,
                                'month_pay_money' => $pay_money->pay_money,
                                'month_dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                            ];
                        }
                    } else {
                        // 如果充值超出了阈值，那就分段计算
                        $over_money = $pay_money->pay_money - $threshold;
                        $cp_money = ($over_money * $over_rate) + ($threshold * $not_over_rate);
                        $cp_rate = bcdiv($cp_money, $pay_money->dimension_game_pay_money, 10);
                        foreach ($date_range as $date) {
                            $this->date_and_per_profit[] = [
                                'date' => $date,
                                'per_profit' => $cp_rate,
                                'month_pay_money' => $pay_money->pay_money,
                                'month_dimension_game_pay_money' => $pay_money->dimension_game_pay_money,
                                'over_money' => $over_money,
                            ];
                        }
                    }
                }
            }
        }

    }
}