<?php

namespace App\Model\SqlModel\DataMedia;

use App\Constant\MediaType;
use App\Param\FinanceListParam;
use Illuminate\Database\Query\JoinClause;

class OdsTencentFundStatementsDailyLogModel extends AbstractDataMediaSqlModel
{
    protected $table = 'ods_tencent_fund_statements_daily_log';

    const FUND_TYPE = [
        'FUND_TYPE_CASH' => '现金账户',
        'FUND_TYPE_GIFT' => '赠送账户',
        'FUND_TYPE_CREDIT_ROLL' => '固定信用金',
        'FUND_TYPE_CREDIT_TEMPORARY' => '临时信用金',
        'FUND_TYPE_SPECIAL_PROMOTION' => '专项推广资金',
        'FUND_TYPE_CREDIT' => '信用金',
        'FUND_TYPE_PAYMENT_DUE' => '账期',
        'FUND_TYPE_MINIPROGRAM' => '小程序启动金',
        'FUND_TYPE_SHARED'  => '分成账户',
        'FUND_TYPE_COMPENSATE_VIRTUAL' => '补偿虚拟金账户',
        'FUND_TYPE_BANK' => '银证账户',
        'FUND_TYPE_TEST_VIRTUAL' => '测试虚拟金',
        'FUND_TYPE_MP_GAME_DEVELOPER_WORKING_FUND' => '微信小游戏内购快周转',
        'FUND_TYPE_MP_GAME_DEVELOPER_GIFT' => '微信小游戏内购赠送金',
        'FUND_TYPE_SPECIAL_GIFT' => '专用账户',
        'FUND_TYPE_ANDROID_ORIENTED_GIFT' => '安卓定向赠送金',
        'FUND_TYPE_INTERNAL_QUOTA' => '内部领用金',
        'FUND_TYPE_CONTRACT_CREDIT' => '合约信用账户',
        'FUND_TYPE_CONTRACT_GIFT_VIRTUAL' => '合约信用赠送虚拟金',
        'FUND_TYPE_UNSUPPORTED' => '不支持',
        'FUND_TYPE_GAME_CHARGE_INDIRECT_REWARDS_GIFT' => '小游戏转充激励金资金账户',
        'FUND_TYPE_GAME_CHARGE_DIRECT_REWARDS_GIFT' => '小游戏直接激励金资金账户',
        'FUND_TYPE_FLOW_SOURCE_AD_FUND' => '流量主广告金',
        'FUND_TYPE_LOCATION_PROMOTION_REWARDS' => '附近推激励金',
        'FUND_TYPE_GIFT_RESTRICT' => '微信专用小游戏抵用金',
        'FUND_TYPE_DEBT_FIXED' => '微信专用竞价合约混用滚动信用账',
        'FUND_TYPE_DEBT_TEMP' => '微信专用竞价合约混用临时信用账户',
        'FUND_TYPE_MP_GAME_PC_TARGET_GIFT' => '小游戏PC定向广告金',
        'FUND_TYPE_AD_RECHARGE' => '广告充值金',
        'FUND_TYPE_TCC_GIFT' => 'TCC赠送虚拟金账户',
        'FUND_TYPE_SHORT_PLAY_PUBLISHER_GIFT' => '短剧流量主赠送金资金账户',
    ];
    const TRADE_TYPE = [
        'CHARGE' => '充值',
        'PAY' => '消费',
        'TRANSFER_BACK' => '回划',
        'EXPIRE' => '过期',
        'TRANSFER_IN' => '转入',
        'UNKNOWN' => '未知',
    ];

    /**
     * @param FinanceListParam $param
     * @return array
     */
    public function getList(FinanceListParam $param)
    {
        $builder = $this->builder;
        $builder->from($this->table . ' as daily_log');

        if ($param->platform) {
            $builder->whereIn('daily_log.platform', $param->platform);
        }

        if ($param->account_id) {
            $builder->whereIn('daily_log.account_id', $param->account_id);
        }

        if ($param->fund_type) {
            $builder->whereIn('daily_log.fund_type', $param->fund_type);
        }

        if ($param->trade_type) {
            $builder->whereIn('daily_log.trade_type', $param->trade_type);
        }


        $builder->Join('ods_tencent_account_log as account_log', function (JoinClause $join) {
            $join->on("account_log.platform", '=', 'daily_log.platform');
            $join->on("account_log.account_id", '=', 'daily_log.account_id');
        });

        // 负责人分组
        $builder->leftJoin('tanwan_datahub.v2_dim_agent_leader_group as alg', function (JoinClause $join) {
            $join->on("account_log.account_leader", '=', 'alg.agent_leader');
        });

        if ($param->company) {
            $builder->whereIn('account_log.corporation_name', $param->company);
        }
        if ($param->creator) {
            $builder->whereIn('account_log.creator', $param->creator);
        }
        if ($param->agent_leader) {
            $builder->whereIn('account_log.account_leader', $param->agent_leader);
        }

        $builder->leftJoin('ods_media_account_agency_change_log as agency_change', function (JoinClause $join) {
            $join->on("account_log.account_id", '=', 'agency_change.account_id');
            $join->whereRaw("daily_log.date BETWEEN agency_change.start_date and agency_change.end_date");
            $join->on("account_log.platform", '=', 'agency_change.platform');
            $join->where("agency_change.media_type", '=', MediaType::TENCENT);
        });
        if ($param->agency_name) {
            $builder->whereIn('agency_change.agency_full_name', $param->agency_name);
        }
        if ($param->settle_company) {
            $builder->whereIn('agency_change.settle_company', $param->settle_company);
        }

        if ($param->agency_short_name) {
            $builder->whereIn('agency_change.agency_short_name', $param->agency_short_name);
        }

        if ($param->date) {
            $builder->whereBetween('daily_log.date', $param->date);
        }

        // 只查询发生额不为0的记录，不然太多0的记录很卡
        $builder->where('daily_log.amount', '!=', 0);

        $day_log_builder = $this->builder;

        $day_log_builder->from('tanwan_datahub.v2_dwd_day_cost_log as data_log');
        $day_log_builder->Join('tanwan_datahub.v2_dim_site_id as site_log', function (JoinClause $join) {
            $join->on("site_log.platform", '=', 'data_log.platform');
            $join->on("site_log.site_id", '=', 'data_log.site_id');
            $join->where("site_log.media_type_id", '=', MediaType::TENCENT);
            $join->where("site_log.account_id", '>', 0);
        });

        if ($param->platform) {
            $day_log_builder->whereIn('data_log.platform', $param->platform);
        }
        if ($param->account_id) {
            $day_log_builder->whereIn('account_id', $param->account_id);
        }
        if ($param->date) {
            $cost_date[0] = $param->date[0] . ' 00:00:00';
            $cost_date[1] = $param->date[1] . ' 23:59:59';
            $day_log_builder->whereBetween('tdate', $cost_date);
        }
        $day_log_builder->whereRaw("ABS(ori_money) > 0");
        $day_log_builder->groupBy(['account_id', 'date']);

        $this->injectAgentLeaderPermission($builder, $param->agent_permission, 'creator', 'account_log');

        if ($param->aggregation_time === '分月') {
            $day_log_builder->selectRaw("data_log.platform, account_id, sum( ori_money ) AS ad_cost, year_month( DATE_FORMAT( tdate, '%Y-%m-%d' ) ) AS date");
            $builder->withExpression('day_log', $day_log_builder);
            $builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'daily_log.account_id');
                $join->on('day_log.date', '=', $join->raw("year_month(daily_log.date)"));
                $join->where('daily_log.trade_type', '=', "PAY");
            });
            $builder->selectRaw('
                daily_log.platform, daily_log.account_id, YEAR_MONTH(daily_log.date) as m_date, account_log.account_name, 
                account_log.creator, daily_log.fund_type as fund_type, daily_log.trade_type as trade_type, SUM(daily_log.amount) as amount, 
                account_log.corporation_name as company, agency_change.agency_full_name as agency_name, agency_change.agency_short_name as agency_short_name, 
                agency_change.settle_company as settle_company, agency_change.project_team as project_team, account_log.account_leader as agent_leader, 
                COALESCE(account_log.agency_account_id, agency_change.agency_id) as agency_id,
                ad_cost AS day_cost, ABS( sum(sum( amount )) over(PARTITION by daily_log.account_id,year_month(daily_log.date),trade_type)  - day_log.ad_cost) AS day_cost_diff,
                IFNULL( alg.agent_leader_group_name, "" ) AS agent_leader_group_name
            ');
            $builder->groupBy(['daily_log.platform', 'daily_log.account_id', 'm_date', 'fund_type','trade_type']);
        } else {
            $day_log_builder->selectRaw("data_log.platform, account_id, sum( ori_money ) AS ad_cost, DATE_FORMAT( tdate, '%Y-%m-%d' ) AS date");
            $builder->withExpression('day_log', $day_log_builder);
            $builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'daily_log.account_id');
                $join->on('day_log.date', '=', "daily_log.date");
                $join->where('daily_log.trade_type', '=', "PAY");
            });
            $builder->selectRaw('
                daily_log.*, account_log.account_name, account_log.creator, 
                account_log.corporation_name as company, agency_change.agency_full_name as agency_name, agency_change.agency_short_name as agency_short_name, 
                agency_change.settle_company as settle_company, agency_change.project_team as project_team, account_log.account_leader as agent_leader, 
                COALESCE(account_log.agency_account_id, agency_change.agency_id) as agency_id,
                day_log.ad_cost AS day_cost, ABS( sum( amount ) over ( PARTITION BY daily_log.account_id,trade_type ) - day_log.ad_cost ) AS day_cost_diff,
                IFNULL( alg.agent_leader_group_name, "" ) AS agent_leader_group_name
            ');
        }

        $all = $builder->get();
        $all = $all->sortByDesc('day_cost');
        $count = $all->count();

        return [
            'total' => $count,
            'sql' => $this->getSql($builder),
            'list' => $all->forPage($param->page, $param->rows)->values(),
            'all' => $all
        ];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getFundType()
    {
        return $this->builder->selectRaw('DISTINCT fund_type')->get();
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getTradeType()
    {
        return $this->builder->selectRaw('DISTINCT trade_type')->get();
    }
}
