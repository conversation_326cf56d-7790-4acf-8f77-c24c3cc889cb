<?php

namespace App\Model\SqlModel\Zeda;

use App\Model\SqlModel\Database\ZDBuilder;
use App\Param\ADServing\ADIntelligentComposeParam;
use App\Param\ADServing\ADIntelligentComposeSearchParam;
use Matrix\Builder;

class ADIntelligentComposeModel extends AbstractZedaSqlModel
{
    protected $table = 'ad_intelligent_compose';

    public function getDataById($id)
    {
        return $this->builder->where(['id' => $id])->first();
    }

    public function getListByIds(array $ids)
    {
        return $this->builder->whereIn('id', $ids)->get();
    }

    public function updateCreateADWeek(array $ids, array $weeks)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'create_week_day_list' => json_encode($weeks)
        ]);
    }

    public function updateCreateADTime(array $ids, $time)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'ad_create_time' => $time
        ]);
    }

    public function batchStartADIntelligentCompose(array $ids)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'status' => 1
        ]);
    }

    public function batchEndADIntelligentCompose(array $ids)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'status' => 2
        ]);
    }

    public function batchUpdateCustomMaterialPacket(array $ids, $custom_material_packet_list)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'custom_material_packet_list' => $custom_material_packet_list
        ]);
    }

    public function batchUpdateADIntelligentComposeMaterialFilter($id, $top, $potential, $new)
    {
        return $this->builder->where('id', $id)->update([
            'top_material_filter' => $top,
            'potential_material_filter' => $potential,
            'new_material_filter' => $new
        ]);
    }

    public function batchDeleteADIntelligentCompose(array $ids)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'status' => 0
        ]);
    }

    public function batchUpdateMaterialWordNum($ids, $material_num, $word_num)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'material_num' => $material_num,
            'word_num' => $word_num,
        ]);
    }

    public function batchUpdateDispatchType($ids, $dispatch_type)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'dispatch_type' => $dispatch_type,
        ]);
    }


    public function batchUpdateIntelligentComposeMaterialComposeType(array $ids, $material_compose_type)
    {
        return $this->builder->whereIn('id', $ids)->update([
            'material_compose_type' => $material_compose_type
        ]);
    }

    /**
     * 获取组合参数控制配置列表
     * @param ADIntelligentComposeSearchParam $param
     * @param $user_list
     * @return array
     */
    public function getList(ADIntelligentComposeSearchParam $param, $user_list)
    {
        $builder = $this->builder->where('status', '!=', 0);
        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }

        if ($param->id) {
            $builder->where(['id' => $param->id]);
        }

        if ($param->media_type) {
            $builder->where(['media_type' => $param->media_type]);
        }

        if ($param->name) {
            $builder->where('name', 'like', "%$param->name%");
        }

        if ($param->creator) {
            $builder->where('creator', 'like', "%$param->creator%");
        }

        if ($param->compose_id) {
            $builder->where(['compose_id' => $param->compose_id]);
        }

        if ($param->create_week_day_list) {
            $builder->where(function (ZDBuilder $query) use ($param) {
                foreach ($param->create_week_day_list as $week_day) {
                    $query->where('create_week_day_list', 'like', "%$week_day%");
                }
            });
        }

        if (is_numeric($param->status)) {
            $builder->where(['status' => $param->status]);
        }

        if ($param->order) {
            foreach ($param->order as $sk => $sort) {
                $builder->orderBy($sk, str_replace('ending', '', $sort));
            }
        } else {
            $builder->orderByDesc('id');
        }

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->forPage($param->page, $param->rows)->get()
            ];
        }
    }

    public function getExecIntelligentCompose()
    {
        $builder = $this->builder;
        $builder->select(['ad_intelligent_compose.*']);
        $builder->leftJoin('user', 'ad_intelligent_compose.creator', '=', 'user.name');
        $builder->where(['status' => 1]);
        $builder->where('user.state', 1);
        $builder->where('compose_id', '>', 0);
        $builder->where('ad_intelligent_compose.name', '!=', '');
        $builder->where(['ad_create_time' => date("H:00:00")]);
        $w = date('w');
        $w = $w == 0 ? 7 : $w;
        $builder->where('create_week_day_list', 'like', "%$w%");
        $builder->where('exec_date_time', '!=', date("Y-m-d H"));

        return $builder->get();
    }

    public function stopIntelligentCompose($id)
    {
        $builder = $this->builder;
        return $builder->where(['id' => $id])->update(['status' => 2]);
    }

    public function startIntelligentCompose($id)
    {
        $builder = $this->builder;
        return $builder->where(['id' => $id])->update(['status' => 1]);
    }

    public function setExecDateTime($id, $date_time)
    {
        return $this->builder->where('id', $id)->update(['exec_date_time' => $date_time]);
    }

    /**
     * 修改组合参数控制配置
     * @param $id
     * @param ADIntelligentComposeParam $data
     * @param array $user_list
     * @return int
     */
    public function editADIntelligentComposer($id, ADIntelligentComposeParam $data, $user_list = [])
    {
        $builder = $this->builder;
        $data = $data->toData();
        $where = [
            'id' => $id,
        ];
        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }
        return $builder->where($where)->update($data);
    }

    public function editStatus($id, $status)
    {
        $this->builder->where(['id' => $id])->update(['status' => $status]);
    }

    /**
     * 增加组合参数控制配置
     * @param ADIntelligentComposeParam $data
     * @return int
     */
    public function addADIntelligentCompose(ADIntelligentComposeParam $data)
    {
        return $this->builder->insertGetId($data->toData());
    }

    /**
     * 删除组合参数控制配置
     * @param $id
     * @param $user_list
     * @return int
     */
    public function deleteADIntelligentCompose($id, $user_list)
    {
        $builder = $this->builder;
        $where = [
            'id' => $id,
        ];
        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }
        return $builder->where($where)->update([
            'status' => 0,
        ]);
    }

    public function setSetError($id, $error)
    {
        return $this->builder->where([
            'id' => $id,
        ])->update([
            'error_msg' => $error,
            'status' => 3
        ]);
    }
}
