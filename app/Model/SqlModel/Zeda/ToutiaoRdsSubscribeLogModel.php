<?php

namespace App\Model\SqlModel\Zeda;


use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class ToutiaoRdsSubscribeLogModel extends AbstractZedaSqlModel
{
    protected $table = 'toutiao_rds_subscribe_log';

    const STATE_UNSUBSCRIBE = 0;
    const STATE_SUBSCRIBE = 1;

    /**
     * @param $data
     * @return void
     */
    public function addMultiple($data)
    {
        array_map(function ($item) {
            $this->builder->insertUpdate($item);
        }, array_chunk($data, 5000));
    }

    /**
     * @return Collection
     */
    public function getUnsubscribeList()
    {
        $builder = $this->builder->from($this->table . ' as log');
        $builder->join('media_account as account', function (JoinClause $join) {
            $join->on('log.account_id', '=', 'account.account_id');
            $join->on('log.app_id', '=', 'account.agent');
            $join->where('account.state', 1);
        });

        $builder
            ->selectRaw("
                log.*,
                account.access_token
            ")
            ->where('log.state', self::STATE_UNSUBSCRIBE);

        return $builder->get();
    }

    /**
     * @param $ids
     * @param $data
     * @return int
     */
    public function updateByIds($ids, $data)
    {
        return $this->builder->whereIn('id', $ids)->update($data);
    }
}
