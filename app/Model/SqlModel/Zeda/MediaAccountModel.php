<?php

namespace App\Model\SqlModel\Zeda;

use App\Constant\MediaType;
use App\Model\SqlModel\Database\ZDBuilder;
use App\Param\ADLabServing\ADLabGroupAccountSearchParam;
use App\Param\MediaAccountListParam;
use App\Service\MediaService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class MediaAccountModel extends AbstractZedaSqlModel
{
    /**
     * 数据库表名
     * @var string
     */
    protected $table = 'media_account';

    const STATE_ACTIVE = 1;
    const STATE_INACTIVE = 0;
    const DAILY_TRANSFER_IN_LIMIT_FIELD = 'daily_transfer_in_limit';

    /**
     * @param $account_id
     * @param $platform
     * @param null $media_type
     * @return Model|null|object|static
     */
    public function getDataByAccountIdPlatform($account_id, $platform, $media_type = null)
    {
        $builder = $this->builder->where('account_id', $account_id);
        $builder->where('platform', $platform);
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }
        return $builder->first();
    }

    /**
     * @param $account_id
     * @param null $media_type
     * @return Model|null|object|static
     */
    public function getDataByAccountId($account_id, $media_type = null)
    {
        $builder = $this->builder->where('account_id', $account_id);
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }
        return $builder->first();
    }

    /**
     * 根据account_id_list去获取data-无权限涉及
     * @param array $account_id_list
     * @param null $media_type
     * @return Collection
     */
    public function getDataInAccountIds(array $account_id_list, $media_type = null)
    {
        $builder = $this->builder->whereIn('account_id', $account_id_list);
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }
        return $builder->get();
    }

    /**
     * @param $account_name
     * @param null $media_type
     * @return ZDBuilder|Builder|Model|object|null
     */
    public function getDataByAccountName($account_name, $media_type = null)
    {
        $builder = $this->builder->where('account_name', $account_name);
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }
        return $builder->first();
    }

    /**
     * @param      $account_name
     * @param null $media_type
     * @return Collection
     */
    public function getListLikeAccountName($account_name, $media_type = null)
    {
        $builder = $this->builder->where('account_name', 'like', "%{$account_name}%");
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }

        return $builder->get();
    }

    /**
     * 用媒体类型随机获取一个账号id-暂时用来头条的使用
     * @param $media_type
     * @return Model|Builder|null|object
     */
    public function getRandomDataByMediaType($media_type)
    {
        return $this->builder->select([
            'media_account.*',
        ])
            ->where(['media_type' => $media_type])
            ->where('state', '=', 1)
            ->first();
    }

    public function getDataByPlatformAgentId($platform, $agent_id)
    {
        $builder = $this->builder->select([
            'media_account.*',
            'agent.agent_id',
            'agent.agent_name',
            'agent.agent_leader',
            'agent.agent_leader_id',
            'agent.agent_type',
            'agent.own',
            'agent.state'
        ])
            ->leftJoin('agent', function (JoinClause $join) {
                $join->on("agent.platform", '=', 'media_account.platform');
                $join->on("agent.media_type", '=', 'media_account.media_type');
                $join->on("agent.account_id", '=', 'media_account.account_id');
            });
        return $builder->where('media_account.platform', $platform)->where('agent_id', $agent_id)->first();
    }

    public function getAll()
    {
        return $this->builder->where('state', self::STATE_ACTIVE)->get();
    }

    public function getAllLeftJoinAgentInIds($ids)
    {
        return $this->builder
            ->select(['media_account.*', 'agent.agent_id'])
            ->leftJoin('agent', function (JoinClause $join) {
                $join->on("agent.platform", '=', 'media_account.platform');
                $join->on("agent.media_type", '=', 'media_account.media_type');
                $join->on("agent.account_id", '=', 'media_account.account_id');
            })
            ->whereIn('id', $ids)
            ->get();
    }

    public function getUserCreateList(MediaAccountListParam $mal, $plat_ids, $root_game_ids, $leader_permission, $creator_list)
    {
        $builder = $this->builder;
        if (!empty($mal->keyword)) {
            $builder->where(function (Builder $query) use ($mal) {
                $separator = strpos($mal->keyword, ',') !== false ? ',' : ' ';
                $account_ids = explode($separator, trim($mal->keyword));
                if (count($account_ids) > 1) {
                    $query->whereIn('account_id', $account_ids);
                } else {
                    $query->where('account_id', 'like', "%$mal->keyword%");
                    $query->orWhere('account_name', 'like', "%$mal->keyword%");
                    $query->orWhere('toutiao_majordomo_id', 'like', "%$mal->keyword%");
                    $query->orWhere('wechat_account_name', 'like', "%$mal->keyword%");
                }
            });
        } else {
            $builder->where('state', 1);
        }

        if ($mal->platform) {
            $builder->where('platform', $mal->platform);
        }

        if ($mal->media_type) {
            $builder->where('media_type', $mal->media_type);
        }

        if ($mal->creator_id) {
            $builder->where('creator_id', $mal->creator_id);
        }

        if ($mal->toutiao_majordomo_id) {
            $builder->where('toutiao_majordomo_id', $mal->toutiao_majordomo_id);
        }

        if ($mal->wechat_account_name) {
            $builder->where('wechat_account_name', $mal->wechat_account_name);
        }

        if ($mal->agent_leader) {
            $builder->where('agent_leader', (int)$mal->agent_leader !== -1 ? $mal->agent_leader : '');
        }

        if ($mal->company) {
            $builder->where('company', 'like', "%$mal->company%");
        }

        if ($mal->create_start_time) {
            $builder->where('create_time', '>=', $mal->create_start_time);
        }

        if ($mal->create_end_time) {
            $builder->where('create_time', '<=', $mal->create_end_time);
        }

        $media_account_label_builder = $this->builder
            ->select('media_account_id')
            ->selectRaw("GROUP_CONCAT(CONCAT(root_game_id, ',', plat_id) SEPARATOR '|') as plat_root_game_id")
            ->from('media_account_label')
            ->groupBy('media_account_id');
        if ($plat_ids !== -1) {
            $media_account_label_builder
                ->whereIn('root_game_id', $root_game_ids)
                ->whereIn('plat_id', $plat_ids);
        }

        $builder->leftJoinSub($media_account_label_builder, 'media_account_label', 'media_account_id', '=', 'id');

        $builder->where(function (Builder $sub_builder) use ($creator_list, $leader_permission) {
            if ($creator_list !== -1) {
                $sub_builder->orWhereIn('creator_id', $creator_list);
            }
            $sub_builder->orWhere(function (Builder $permission_builder) use ($leader_permission) {
                $this->injectAgentLeaderPermission($permission_builder, $leader_permission, 'agent_leader');
            });
        });


        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect(),
            ];
        }

        if ($mal->order_column && $mal->order_direction) {
            $builder->orderBy($mal->order_column, $mal->order_direction);
        } else {
            $builder
                ->orderBy('platform')
                ->orderByRaw('length(account_name)')
                ->orderBy('account_name');
        }

        $list = $builder
            ->select('media_account.*', 'plat_root_game_id')
            ->forPage($mal->page, $mal->rows)
            ->get();
        return [
            'total' => $total,
            'list' => $list
        ];
    }

    /**
     * 获取账号列表
     * @param       $keyword
     * @param       $options
     * @param mixed $leader_creator_permission
     * @return Collection
     */
    public function getListLikeAccount($keyword, $options, $leader_creator_permission = -1)
    {
        $builder = $this->builder
            ->distinct()
            ->selectRaw("{$this->table}.platform, {$this->table}.account_id, {$this->table}.account_name")
            ->limit(50);
        if ($keyword !== '') {
            $builder->where(function (Builder $query) use ($keyword) {
                $account_ids = explode(',', $keyword);
                if (count($account_ids) > 1) {
                    $query->whereIn('account_id', $account_ids);
                } else {
                    $query->orWhere('account_id', 'like', "%{$keyword}%");
                    $query->orWhere('account_name', 'like', "%{$keyword}%");
                }
            });
        }
        $builder->where(function (Builder $builder) use ($leader_creator_permission) {
            $builder->orWhere(function (Builder $builder) use ($leader_creator_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_creator_permission, 'agent_leader');
            });
            $builder->orWhere(function (Builder $builder) use ($leader_creator_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_creator_permission, 'creator');
            });
        });
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->whereRaw($condition, $value);
            }
        }

        return $builder->get();
    }


    /**
     * 获取负责人列表
     * @param       $keyword
     * @param       $options
     * @param mixed $leader_permission
     * @param mixed $creator_permission
     * @return Collection
     */
    public function getListLikeAgentLeader($keyword, $options, $leader_permission = -1, $creator_permission = -1)
    {
        $builder = $this->builder
            ->distinct()
            ->selectRaw("{$this->table}.platform, {$this->table}.agent_leader")
            ->where("{$this->table}.agent_leader", '<>', '')
            ->limit(50);
        if ($keyword !== '') {
            $builder->where(function (Builder $query) use ($keyword) {
                $query->orWhere("{$this->table}.agent_leader", 'like', "%{$keyword}%");
            });
        }
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $this->injectAgentLeaderPermission($builder, $creator_permission, 'creator');
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->WhereRaw($condition, $value);
            }
        }
        return $builder->get();
    }

    /**
     * 获取负责人列表
     * @param       $keyword
     * @param       $options
     * @param mixed $leader_permission
     * @param mixed $creator_permission
     * @return Collection
     */
    public function getListLikeCreator($keyword, $options, $leader_permission = -1, $creator_permission = -1)
    {
        $builder = $this->builder
            ->distinct()
            ->selectRaw("{$this->table}.platform, {$this->table}.creator")
            ->where("{$this->table}.creator", '<>', '')
            ->limit(50);
        if ($keyword !== '') {
            $builder->where(function (Builder $query) use ($keyword) {
                $query->orWhere("{$this->table}.creator", 'like', "%{$keyword}%");
            });
        }
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $this->injectAgentLeaderPermission($builder, $creator_permission, 'creator');
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->WhereRaw($condition, $value);
            }
        }
        return $builder->get();
    }

    /**
     * 获取公司列表
     * @param       $keyword
     * @param       $options
     * @param mixed $leader_permission
     * @param mixed $creator_permission
     * @return Collection
     */
    public function getListLikeCompany($keyword, $options, $leader_permission = -1, $creator_permission = -1)
    {
        $builder = $this->builder
            ->distinct()
            ->selectRaw("{$this->table}.company")->where("{$this->table}.company", '<>', '')
            ->limit(50);
        if ($keyword !== '') {
            $builder->where("{$this->table}.company", 'like', "%{$keyword}%");
        }
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $this->injectAgentLeaderPermission($builder, $creator_permission, 'creator');
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->WhereRaw($condition, $value);
            }
        }
        return $builder->get();
    }

    /**
     * 获取管家名称列表
     * @param       $keyword
     * @param       $options
     * @param mixed $leader_permission majordomo_name
     * @param mixed $creator_permission
     * @return Collection
     */
    public function getListLikeMajorDomeName($keyword, $options, $leader_permission = -1, $creator_permission = -1)
    {
        $builder = $this->builder
            ->distinct()
            ->selectRaw("{$this->table}.majordomo_name")->where("{$this->table}.majordomo_name", '<>', '')
            ->limit(50);
        if ($keyword !== '') {
            $builder->where("{$this->table}.majordomo_name", 'like', "%{$keyword}%");
        }
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $this->injectAgentLeaderPermission($builder, $creator_permission, 'creator');
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->WhereRaw($condition, $value);
            }
        }
        return $builder->get();
    }

    public function getOperateList(MediaAccountListParam $mal, $leader_permission)
    {
        $builder = $this->builder
            ->where(function (Builder $sub_builder) use ($leader_permission) {
                $sub_builder->orWhere(function (Builder $permission_builder) use ($leader_permission) {
                    $this->injectAgentLeaderPermission($permission_builder, $leader_permission, 'creator');
                });
                $sub_builder->orWhere(function (Builder $permission_builder) use ($leader_permission) {
                    $this->injectAgentLeaderPermission($permission_builder, $leader_permission, 'agent_leader');
                });
            });
        if ($mal->platform) {
            $builder->where('platform', $mal->platform);
        }

        if ($mal->media_type) {
            $builder->where('media_type', $mal->media_type);
        }

        if ($mal->creator_id) {
            $builder->where('creator_id', $mal->creator_id);
        }

        if ($mal->agent_leader) {
            $builder->where('agent_leader', $mal->agent_leader);
        }

        if ($mal->account_id) {
            $builder->whereIn('account_id', $mal->account_id);
        }

        if (!empty($mal->keyword)) {
            $builder->where(function (Builder $query) use ($mal) {
                $query->where('account_id', 'like', "%$mal->keyword%");
                $query->orWhere('account_name', 'like', "%$mal->keyword%");
            });
        }

        $list = $builder
            ->orderBy('platform')
            ->orderByRaw('length(account_name)')
            ->orderBy('account_name')
            ->forPage($mal->page, $mal->rows)
            ->get();
        return $list;
    }

    /**
     * 根据条件返回指定的数据出来-涉及数据权限
     * @param $platform
     * @param $media_type
     * @param $page
     * @param $rows
     * @return array
     */
    public function getListByPlatformMedia($platform, $media_type, $page, $rows)
    {
        $builder = $this->builder->where('platform', $platform)->where('media_type', $media_type);
        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => 0,
                'list' => collect(),
            ];
        }
        return [
            'total' => $total,
            'list' => $builder->forPage($page, $rows)->get(),
        ];
    }

    /**
     * 求token即将过期的用户列表
     * @param $media_type
     * @return Collection
     * <AUTHOR>
     */
    public function getAllExpiringByMediaType($media_type)
    {
        //  过期时间-15分钟  当前时间   过期时间
        // |——————————————|—————————|————————|
        return $this->builder
            ->where('media_type', $media_type)
            ->where('access_token_expires', '<', request_time() + 900)
            ->get();
    }

    /**
     * 获取即将过期的token
     * 不获取没绑定管家账号的token
     * @param $media_type
     * @return Collection
     * <AUTHOR>
     */
    public function getExpiringListByMediaType($media_type)
    {
        //  过期时间-15分钟  当前时间   过期时间
        // |——————————————|—————————|————————|
        return $this->builder
            ->where('media_type', $media_type)
            ->where('access_token_expires', '>', 0)
            ->where('access_token_expires', '<', request_time() + 900)
            ->get();
    }

    /**
     * 获取即将过期的token
     * 不获取没绑定管家账号的token
     * @param $media_type
     * @return Collection
     * <AUTHOR>
     */
    public function getExpiringListWithoutBindMajordomo($media_type)
    {
        //  过期时间-15分钟  当前时间   过期时间
        // |——————————————|—————————|————————|
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', 0)
            ->where('access_token_expires', '<', request_time() + 900)
            ->get();
    }

    /**
     * 获取即将过期的refresh_token
     * 不获取没绑定管家账号的refresh_token
     * @param $media_type
     * @return Collection
     */
    public function getExpiringRefreshTokenWithoutBindMajordomo($media_type)
    {
        //  过期时间-15分钟  当前时间   过期时间
        // |——————————————|—————————|————————|
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', 0)
            ->where('refresh_token_expires', '<', request_time() + 900)
            ->get();
    }

    /**
     * @param $toutiao_majordomo_id
     * @return Collection
     * <AUTHOR>
     */
    public function getAllByMajordomoId($toutiao_majordomo_id)
    {
        return $this->builder
            ->where('toutiao_majordomo_id', $toutiao_majordomo_id)
            ->where('state', 1)
            ->get();
    }


    /**
     * @param $media_type
     * @param $wechat_account_name
     * @return Collection
     * <AUTHOR>
     */
    public function getAllByWechatAccount($media_type, $wechat_account_name)
    {
        return $this->builder
            ->where('wechat_account_name', $wechat_account_name)
            ->where('media_type', $media_type)
            ->get();
    }

    public function add($data)
    {
        $result = $this->builder->insertGetId($data);
        return $result;
    }

    public function edit($id, $data)
    {
        return $this->builder->where('id', '=', $id)->update($data);
    }

    public function editInIds($ids, $data)
    {
        return $this->builder->whereIn('id', $ids)->update($data);
    }

    public function removeAccountIds($media_type, $account_ids)
    {
        $data = [
            'state' => 0,
            'update_time' => time(),
        ];

        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('account_id', $account_ids)
            ->update($data);
    }

    public function refreshToken($id, $access_token, $access_token_expires, $refresh_token, $refresh_token_expires)
    {
        $data = [
            'access_token' => $access_token,
            'access_token_expires' => $access_token_expires,
            'refresh_token' => $refresh_token,
            'refresh_token_expires' => $refresh_token_expires,
            'update_time' => request_time(),
        ];

        return $this->builder->where('id', $id)->update($data);
    }

    public function updateAccessTokenByMajordomoId($media_type, $majordomo_id, $access_token)
    {
        $data = [
            'access_token' => $access_token,
            'update_time' => request_time(),
        ];

        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->update($data);
    }

    public function updateAccessTokenByMajordomoIds($media_type, $majordomo_ids, $access_token)
    {
        $data = [
            'access_token' => $access_token,
            'update_time' => request_time(),
        ];

        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('toutiao_majordomo_id', $majordomo_ids)
            ->update($data);
    }

    public function getAllAccountTag($platform_list)
    {
        $builder = $this->builder
            ->distinct()
            ->select(
                'media_account.media_type',
                'game_tag.tag_name',
                'media_account.account_id',
                'media_account.account_name',
                'media_account.company',
                'media_account.majordomo_name',
                'media_account.wechat_account_name',
                'media_account.toutiao_majordomo_id'
            )
            ->join('media_account_label', 'media_account.id', '=', 'media_account_label.media_account_id')
            ->join('game_tag_game', 'game_tag_game.root_game_id', '=', 'media_account_label.root_game_id')
            ->join('game_tag', 'game_tag_game.tag_id', '=', 'game_tag.id')
            ->where('game_tag.tag_type', GameTagModel::GAME_TYPE);
        if ($platform_list !== -1) {
            $builder->whereIn('media_account.platform', $platform_list);
        }
        return $builder->get();
    }

    public function getCountByMediaType($media_type)
    {
        return $this->builder->where('media_type', $media_type)->where('state', 1)->count();
    }

    public function getListByPage($page, $row, $media_type)
    {
        $builder = $this->builder->where('media_type', $media_type)->orderByDesc('platform')->orderByDesc('id');
        $builder->where('state', 1);
        return $builder->forPage($page, $row)->get(['id', 'account_id', 'platform', 'toutiao_majordomo_id']);
    }

    /**
     * 获取系统内所有主体列表
     * @param $media_type
     * @param $platform
     * @param $leader_permission
     * @return Collection
     */
    public function getCompanyList($media_type, $platform, $leader_permission)
    {
        $builder = $this->builder;
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $platform && $builder->where('platform', $platform);
        return $builder
            ->select('media_account.company')
            ->where('media_type', $media_type)
            ->where('state', "!=", 0)
            ->where('company', "!=", '')
            ->groupBy('company')
            ->get();
    }

    /**
     * 根据条件返回指定的数据出来-涉及数据权限
     * @param MediaAccountListParam $param
     * @param $leader_permission
     * @return array
     */
    public function getListByCondition(MediaAccountListParam $param, $leader_permission)
    {
        $builder = $this->getConditionBuilder($param);
        $builder->select(['media_account.account_id', 'media_account.account_name', 'media_account.agent_leader', 'media_account.company']);
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => 0,
                'list' => collect(),
            ];
        }
        return [
            'total' => $total,
            'list' => $builder->forPage($param->page, $param->rows)->get(),
        ];
    }

    /**
     * 根据条件返回指定的数据出来-涉及数据权限
     * @param MediaAccountListParam $param
     * @param $leader_permission
     * @return array
     */
    public function getMoreQueryListByCondition(MediaAccountListParam $param, $leader_permission)
    {
        $builder = $this->getConditionBuilder($param);
        $builder->select(['media_account.account_id', 'media_account.account_name', 'media_account.agent_leader', 'media_account.company']);
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => 0,
                'list' => collect(),
            ];
        }
        return [
            'total' => $total,
            'list' => $builder->forPage($param->page, $param->rows)->get(),
        ];
    }

    /**
     * @param int $media_type
     * @param string $platform
     * @param int $account_id
     * @param $leader_permission
     * @return Collection
     */
    public function getListByAccountId(int $media_type, string $platform, int $account_id, $leader_permission)
    {
        $builder = $this->builder;
        $builder->select(['media_account.account_id', 'media_account.account_name', 'media_account.agent_leader', 'media_account.company']);
        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
        $builder->where('media_type', '=', $media_type);
        $platform && $builder->where('platform', '=', $platform);
        $builder->where('company', '=', function ($query) use ($account_id) {
            /* @var  Builder $query */
            $query->select('company');
            $query->from($this->table);
            $query->where('account_id', '=', $account_id);
        });
        $builder->where('account_id', '!=', $account_id);
        return $builder->get();
    }

    /**
     * @param array $account_id_list
     * @return Collection
     */
    public function getListByAccountIds(array $account_id_list)
    {
        $builder = $this->builder;
        $builder->whereIn('account_id', $account_id_list);
        return $builder->get();
    }

    /**
     * @param $media_type
     * @param array $account_id_list
     * @return Collection
     */
    public function getListByMediaTypeAccountIds($media_type, array $account_id_list): Collection
    {
        $builder = $this->builder;
        $builder->where('media_type', '=', $media_type);
        $builder->whereIn('account_id', $account_id_list);
        return $builder->get();
    }

    /**
     * 根据条件返回所有指定数据回来-不涉及数据权限
     * @param MediaAccountListParam $param
     * @return Collection
     */
    public function getAllByCondition(MediaAccountListParam $param)
    {
        $builder = $this->getConditionBuilder($param);
        return $builder->get();
    }

    /**
     * 此表公共组合的条件删选器
     * @param MediaAccountListParam $param
     * @return Builder
     */
    private function getConditionBuilder(MediaAccountListParam $param)
    {
        $builder = $this->builder;

        $builder->where(['state' => 1]);

        if ($param->id) {
            $builder->where(['media_account.id' => $param->id]);
        }

        if (is_numeric($param->agent)) {
            $builder->where(['media_account.agent' => $param->agent]);
        }

        if ($param->agent_leader) {
            $builder->where('media_account.agent_leader', 'LIKE', "%{$param->agent_leader}%");
        }

        if ($param->account_id) {

            // 这里可以空格分开
            $account_id_list = array_filter(explode(' ', $param->account_id));

            $builder->whereIn('media_account.account_id', $account_id_list);
        }

        if ($param->keyword) {

            // 这里可以空格分开
            $account_keyword_list = array_filter(explode(' ', $param->keyword));

            $builder->where(function ($query_sub) use ($account_keyword_list) {
                /* @var  Builder $query_sub */
                $query_sub->orWhere(function ($query_sub_1) use ($account_keyword_list) {
                    /* @var  Builder $query_sub_1 */
                    $query_sub_1->whereIn('media_account.account_name', $account_keyword_list);
                });
                $query_sub->orWhere(function ($query_sub_2) use ($account_keyword_list) {
                    /* @var  Builder $query_sub_2 */
                    $query_sub_2->whereIn('media_account.account_id', $account_keyword_list);
                });
            });
        }

        if ($param->company) {
            $builder->where(['media_account.company' => $param->company]);
        }

        if ($param->platform) {
            $builder->where(['media_account.platform' => $param->platform]);
        }

        if ($param->media_type) {
            $builder->where(['media_account.media_type' => $param->media_type]);
        }

        if ($param->account_name) {
            $builder->where('media_account.account_name', 'LIKE', "%{$param->account_name}%");
        }
        return $builder;
    }

    public function updateOrInsertWithMajordomo($media_type, $account_id, $majordomo_id, $data)
    {
        return $this->builder->updateOrInsert([
            'account_id' => $account_id,
            'media_type' => $media_type,
            'toutiao_majordomo_id' => $majordomo_id
        ], $data);
    }

    public function getDataHasMajordomo($media_type, $account_id)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('account_id', $account_id)
            ->where('toutiao_majordomo_id', '>', 0)
            ->where('state', self::STATE_ACTIVE)
            ->first();
    }

    public function fillMajordomoName($media_type, $majordomo_id, $majordomo_name)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->update([
                'majordomo_name' => $majordomo_name
            ]);
    }

    /**
     * group by 代理商和创建人
     * 朋友圈用
     *
     * @param int $media_type
     * @return Collection
     */
    public function getAllMajordomo($media_type)
    {
        return $this->builder
            ->selectRaw('toutiao_majordomo_id, access_token')
            ->where('media_type', $media_type)
            ->where('state', 1)
            ->groupBy(
                'toutiao_majordomo_id',
                'access_token'
            )
            ->get();
    }

    /**
     * 根据代理商和creator_id获取已授权广告主列表
     *
     * @param int $media_type
     * @param int $majordomo_id
     * @param string $access_token
     * @return Collection
     */
    public function getListByMajordomo($media_type, $majordomo_id, $access_token)
    {
        return $this->builder
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->where('access_token', $access_token)
            ->where('media_type', $media_type)
            ->where('state', 1)
            ->get();
    }

    /**
     * 停用广告主，目前朋友圈用
     *
     * @param int $media_type
     * @param array<int> $account_ids
     * @return int
     */
    public function removeAccountsWithMajordomo($media_type, $account_ids)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', '>', 0)
            ->whereIn('account_id', $account_ids)
            ->update([
                'state' => 0
            ]);
    }

    public function getListByPlatform($platform_list)
    {
        $builder = $this->builder
            ->where('state', 1);
        if ($platform_list !== -1) {
            $builder->whereIn('platform', $platform_list);
        }
        return $builder->orderByRaw('if (media_type = 2, account_id, account_name)')->get();
    }

    public function getDataWithMediaType($account_id, $media_type)
    {
        return $this->builder
            ->where('account_id', $account_id)
            ->where('media_type', $media_type)
            ->where('state', 1)
            ->first();
    }

    public function getBMAccounts($media_type)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('state', 1)
            ->where('toutiao_majordomo_id', '>', 0)
            ->get();
    }

    /**
     * 批量添加媒体账户
     *
     * @param array $data
     * @return bool
     */
    public function batchAdd($data)
    {
        return $this->builder->insert($data);
    }

    public function getDataByAccessToken($media_type, $majordomo_id, $access_token)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->where('access_token', $access_token)
            ->orderBy('create_time')
            ->first();
    }

    public function getMPMajordomoCount()
    {
        $result = $this->builder
            ->selectRaw('count(DISTINCT toutiao_majordomo_id, access_token) cnt')
            ->where('media_type', MediaService::MP)
            ->where('state', 1)
            ->first();
        return $result->cnt;
    }

    public function getMPList($page, $rows)
    {
        return $this->builder
            ->select('toutiao_majordomo_id', 'access_token')
            ->where('media_type', MediaService::MP)
            ->where('state', 1)
            ->groupBy('toutiao_majordomo_id', 'access_token')
            ->forPage($page, $rows)
            ->get();
    }

    /**
     * 根据多个account_id并注入权限获取数据集
     * @param $account_ids
     * @param null $media_type
     * @param int $leader_permission
     * @return Collection
     */
    public function getAccessTokenByAccountIdsInjectPermission($account_ids, $media_type = null, $leader_permission = -1)
    {
        $builder = $this->builder
            ->where('state', 1)
            ->whereIn('account_id', $account_ids)
            ->select('toutiao_majordomo_id', 'access_token', 'account_id', 'account_name', 'company', 'platform', 'refresh_token');
        if (!is_null($media_type)) {
            $builder->where('media_type', $media_type);
        }
        $builder->where(function (Builder $builder) use ($leader_permission) {
            $builder->orWhere(function (Builder $builder) use ($leader_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
            });
            $builder->orWhere(function (Builder $builder) use ($leader_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_permission, 'creator');
            });
        });

        return $builder->get();
    }

    /**
     * 根据多个account_id获取数据集
     * @param $account_ids
     * @param null $media_type
     * @return Collection
     */
    public function getAccessTokenInAccountIds($account_ids, $media_type = null)
    {
        $builder = $this->builder;
        if ($media_type) {
            $builder->where('media_type', $media_type);
        }
        return $builder->where('state', 1)
            ->whereIn('account_id', $account_ids)
            ->get();
    }

    /**
     * 根据一个account_id获取access_token
     * @param $account_id
     * @return Collection
     */
    public function getAccessTokenByAccountId($account_id)
    {
        return $this->builder
            ->where('state', 1)
            ->where('account_id', $account_id)
            ->value('access_token');
    }

    public function updateByAccountMajordomoId($media_type, $account_id, $majordomo_id, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('account_id', $account_id)
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->where('state', self::STATE_ACTIVE)
            ->update($data);
    }

    public function updateByMajordomoId($media_type, $majordomo_id, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('toutiao_majordomo_id', $majordomo_id)
            ->update($data);
    }

    public function updateByWechatAccount($media_type, $wechat_account_name, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('wechat_account_name', $wechat_account_name)
            ->update($data);
    }


    public function updateInMajordomoId($media_type, $majordomo_ids, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('toutiao_majordomo_id', $majordomo_ids)
            ->update($data);
    }

    public function updateInAccountName($media_type, $account_names, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('account_name', $account_names)
            ->update($data);
    }

    public function updateInAccountIds($media_type, $account_ids, array $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('account_id', $account_ids)
            ->update($data);
    }

    /**
     * 获取百度首次授权账户信息
     * @param $company
     * @param int $media_type
     * @return ZDBuilder|Model|Builder|object|null
     */
    public function getBaiduFirstAccountInfo($company, $media_type = MediaType::BAIDU)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('state', self::STATE_ACTIVE)
            ->where('company', $company)
            ->orderBy('create_time')
            ->first();
    }

    public function getListByAgentLeader($media_types, $agent_leader)
    {
        $builder = $this->builder
            ->where('state', self::STATE_ACTIVE)
            ->whereIn('media_type', $media_types);
        if ($agent_leader) {
            $builder->where('agent_leader', $agent_leader);
        }
        return $builder->get();
    }

    public function getInactiveAccounts($media_type, array $account_ids)
    {
        return $this->builder
            ->select('account_id')
            ->where('media_type', $media_type)
            ->whereIn('account_id', $account_ids)
            ->where('state', self::STATE_INACTIVE)
            ->get();
    }

    public function updateDefaultSite($media_type, $account_id, $default_agent_id, $default_site_id)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('account_id', $account_id)
            ->update(
                [
                    'default_agent_id' => $default_agent_id,
                    'default_site_id' => $default_site_id,
                ]
            );
    }

    public function updateByAccountId($media_type, $account_id, $data)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('account_id', $account_id)
            ->update($data);
    }

    /**
     * 更新账号当日已转账数额
     * @param $field
     * @param $update_data
     */
    public function updateAccountDailyTransferAmount($field, $update_data)
    {
        foreach ($update_data as $account_id => $amount) {
            $this->builder->where('account_id', $account_id)->update([$field => $amount]);
        }
    }

    /**
     * 每天零点刷新当天已转账金额
     * @param $field
     */
    public function flushAccountDailyTransferAmount($field)
    {
        $this->builder->update([$field => 0]);
    }

    /**
     * 修改每日转账金额上限
     * @param $edit_field
     * @param $account_ids
     * @param $limit_value
     */
    public function editAccountDailyTransferLimit($edit_field, $account_ids, $limit_value)
    {
        $this->builder->whereIn('account_id', $account_ids)->update([$edit_field => $limit_value]);
    }

    /**
     * 根据account ids获取头条账号数据
     * @param array $account_ids
     * @param $leader_permission
     * @return Collection
     */
    public function getAccountInfosInAccountIds(array $account_ids, $leader_permission = false)
    {
        $builder = $this->builder
            ->whereIn('account_id', $account_ids)
            ->select('toutiao_majordomo_id', 'account_id', 'account_name', 'creator', 'agent_leader', 'platform');

        $builder->where(function (Builder $builder) use ($leader_permission) {
            $builder->orWhere(function (Builder $builder) use ($leader_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');
            });
            $builder->orWhere(function (Builder $builder) use ($leader_permission) {
                $this->injectAgentLeaderPermission($builder, $leader_permission, 'creator');
            });
        });

        return $builder->get();
    }

    /**
     * 检查账号路由白名单
     * @param $platform
     * @param $media_type
     * @param $account_id
     * @param $api
     * @return bool
     */
    public function checkApiWhiteList($platform, $media_type, $account_id, $api)
    {
        return $this->builder
            ->where('platform', $platform)
            ->where('media_type', $media_type)
            ->where('account_id', $account_id)
            ->whereJsonContains('api_white_list', $api)
            ->exists();
    }

    public function getListForADLab(ADLabGroupAccountSearchParam $param, $leader_permission)
    {
        $builder = $this->builder
            ->select(
                "$this->table.account_id",
                "$this->table.company",
                "$this->table.account_name",
                "$this->table.agent_leader",
            )
            ->where('media_type', $param->media_type);

        $param->not_in_account_id_list && $builder->whereNotIn("$this->table.account_id", $param->not_in_account_id_list);
        $param->must_not_in_account_id_list && $builder->whereNotIn("$this->table.account_id", $param->must_not_in_account_id_list);

        $param->account_id && $builder->where("$this->table.account_id", 'LIKE', "%{$param->account_id}%");
        $param->account_name && $builder->where("$this->table.account_name", 'LIKE', "%{$param->account_name}%");
        $param->agent_leader && $builder->where("$this->table.agent_leader", '=', $param->agent_leader);
        $param->company && $builder->where("$this->table.company", '=', $param->company);

        $this->injectAgentLeaderPermission($builder, $leader_permission, 'agent_leader');

        $total = $builder->count();

        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->forPage($param->page, $param->rows)->get(),
                'sql' => $this->getSql($builder)
            ];
        }
    }

    public function getPushaccountList($platform_list, $input)
    {
        $builder = $this->builder
            ->select(['id', 'account_id', 'account_name', 'company', 'media_type', 'platform', 'state'])
            ->where('state', 1);
        if ($platform_list !== -1) {
            $builder->whereIn('platform', $platform_list);
        }

        if (isset($input['media_type']) && !empty($input['media_type'])) {
            $builder->where('media_type', $input['media_type']);
        }

        if (isset($input['keyword']) && !empty($input['keyword'])) {
            $builder->where(function (Builder $query) use ($input) {
                $query->orWhere("account_id", 'LIKE', "%{$input['keyword']}%");
                $query->orWhere("account_name", 'LIKE', "%{$input['keyword']}%");
            });
        }

        $builder->limit(50);

        return $builder->orderByRaw('if (media_type = 2, account_id, account_name)')->get();
    }

    /**
     * 获取账号信息并带有管家access_token过期时间
     * @param $account_id
     * @param null $media_type
     * @return Model|null|object|static
     */
    public function getDataWithMajorExpiresByAccountId($account_id, $media_type = null)
    {
        $builder = $this->builder->selectRaw("
            media_account.id,
            media_account.platform,
            media_account.media_type,
            media_account.default_agent_id,
            media_account.default_site_id,
            media_account.account_id,
            media_account.account_name,
            media_account.account_password,
            media_account.access_token,
            IF( major.access_token_expires > 0, major.access_token_expires, media_account.access_token_expires ) AS access_token_expires, 
            media_account.email,
            media_account.company,
            media_account.agent,
            media_account.agent_leader,
            media_account.create_time,
            media_account.update_time,
            media_account.state,
            media_account.creator_id,
            media_account.creator,
            media_account.editor_id,
            media_account.editor,
            media_account.toutiao_majordomo_id,
            media_account.majordomo_name,
            media_account.wechat_account_name,
            media_account.daily_transfer_in_limit,
            media_account.today_transfer_in_amount,
            media_account.api_white_list,
            media_account.ext
        ");

        $builder->leftJoin('media_majordomo_account as major', function (JoinClause $join) {
            $join->on("major.media_type", "=", "media_account.media_type");
            $join->on("major.account_id", "=", "media_account.toutiao_majordomo_id");
            $join->where("media_account.toutiao_majordomo_id", "!=", 0);
            $join->where("media_account.toutiao_majordomo_id", "!=", "");
        });

        $builder->where('media_account.account_id', $account_id);
        if (!is_null($media_type)) {
            $builder->where('media_account.media_type', $media_type);
        }

        return $builder->first();
    }

    /**
     * 获取头条星图数据
     * @param $company
     * @return Collection
     */
    public function getToutiaoStarAccountList($company, $type)
    {
        $builder = $this->builder;
        if ($type == 2) {
            $builder->whereRaw("JSON_EXTRACT(ext, '$.spider') = 1");
        }
        return $builder
            ->select(['account_id', 'account_name'])
            ->where('company', $company)
            ->where('state', 1)
            ->where('media_type', MediaType::XINGTU)
            ->get();
    }

    /**
     * 获取多个管家下的有效子账号
     * @param $media_type
     * @param $majordomo_ids
     * @return Collection
     */
    public function getAllInMajordomoIds($media_type, $majordomo_ids)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->whereIn('toutiao_majordomo_id', $majordomo_ids)
            ->where('state', self::STATE_ACTIVE)
            ->get();
    }

    /**
     * 通过主体简称获取支付宝开发者APPID
     * @param $company_short_name
     * @return ZDBuilder|Model|Builder|object|null
     */
    public function getAlipayAppIdByCompanyShortName($company_short_name)
    {
        return $this->builder
            ->select(['account_id', 'account_name'])
            ->whereRaw("JSON_EXTRACT(ext, '$.account_type') = '2'")
            ->whereRaw("JSON_EXTRACT(ext, '$.company_short_name') = '$company_short_name'")
            ->where('state', self::STATE_ACTIVE)
            ->where('media_type', MediaType::ALIPAY)
            ->first();
    }

    /**
     * 获取某个媒体的所有账号
     * @param $media_type
     * @return Collection
     */
    public function getListByMediaType($media_type)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('state', self::STATE_ACTIVE)
            ->get();
    }

    /**
     * 获取需要订阅RDS的账号
     * @param $media_type
     * @return Collection
     */
    public function getNeedSubscribeRDSList($media_type)
    {
        return $this->builder
            ->where('media_type', $media_type)
            ->where('state', self::STATE_ACTIVE)
            ->where('wechat_account_name', '!=', 'subscribe_rds')
            ->where('agent', ****************) // TODO delete 临时跑
            ->get();
    }

    /**
     * 获取特定账号且管家不是 $toutiao_majordomo_id 的列表
     * @param $account_ids
     * @param $toutiao_majordomo_id
     * @return Collection
     */
    public function getListInAccountIdsAndNotInMajordomoId($account_ids, $toutiao_majordomo_id)
    {
        return $this->builder
            ->select(['account_id'])
            ->whereIn('account_id', $account_ids)
            ->where('toutiao_majordomo_id', '!=', $toutiao_majordomo_id)
            ->where('state', 1)
            ->get();
    }
}
