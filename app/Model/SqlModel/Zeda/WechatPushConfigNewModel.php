<?php

namespace App\Model\SqlModel\Zeda;

use App\Utils\Helpers;
use Illuminate\Database\Query\JoinClause;

class WechatPushConfigNewModel extends AbstractZedaSqlModel
{
    protected $table = 'wechat_push_config_new';

    public function list($super_user, $user_id, $module, $page, $rows, $keyword = '')
    {
        $builder = $this->builder;
        $builder
            ->select(
                $builder->raw("{$this->table}.*"),
                $builder->raw("user.name as user_name")
            )
            ->join('user', function (JoinClause $join) {
                $join->on("user.id", "=", "{$this->table}.user_id");
            })
            ->where('module', $module);
        if (!$super_user) {
            $builder->where('user_id', $user_id);
        }
        if ($keyword) {
            $builder->where('theme', 'like', "%" . $keyword . "%");
        }
        $cnt = $builder->count();
        if ($cnt == 0) {
            return [
                'list'  => collect(),
                'total' => $cnt
            ];
        }
        $list = $builder->forPage($page, $rows)->get();
        return [
            'list'  => $list,
            'total' => $cnt
        ];
    }

    public function add($user_id, $module, $theme, array $target, array $config, $push_hour, $push_minute, $editor, $push_channel, $webhook)
    {
        $now = time();
        $data = [
            'user_id'      => $user_id,
            'module'       => $module,
            'theme'        => $theme,
            'target'       => json_encode($target, JSON_UNESCAPED_UNICODE),
            'config'       => json_encode($config, JSON_UNESCAPED_UNICODE),
            'create_time'  => $now,
            'update_time'  => $now,
            'push_hour'    => $push_hour,
            'push_minute'  => $push_minute,
            'editor'       => $editor,
            'push_channel' => json_encode($push_channel),
            'webhook'      => $webhook,
        ];
        return $this->builder
            ->insert($data);
    }

    public function edit($user_id, $module, $theme, array $target, array $config, $push_hour, $push_minute, $editor, $push_channel, $webhook)
    {
        $now = time();
        return $this->builder
            ->where('user_id', $user_id)
            ->where('module', $module)
            ->where('theme', $theme)
            ->update([
                'target'       => json_encode($target, JSON_UNESCAPED_UNICODE),
                'config'       => json_encode($config, JSON_UNESCAPED_UNICODE),
                'update_time'  => $now,
                'push_hour'    => $push_hour,
                'push_minute'  => $push_minute,
                'editor'       => $editor,
                'push_channel' => json_encode($push_channel),
                'webhook'      => $webhook,
            ]);
    }

    public function delete($user_id, $module, $theme)
    {
        return $this->builder
            ->where('user_id', $user_id)
            ->where('module', $module)
            ->where('theme', $theme)
            ->delete();
    }

    public function getAll()
    {
        $builder = $this->builder;
        $builder
            ->select(
                $builder->raw("{$this->table}.*"),
                $builder->raw("user.name as username")
            )
            ->join('user', function (JoinClause $join) {
                $join->on("user.id", "=", "{$this->table}.user_id");
            });
        return $builder->get();
    }

    public function getConfig($user_id, $theme)
    {
        return $this->builder
            ->where('user_id', $user_id)
            ->where('theme', $theme)
            ->first();
    }

    public function getModuleConfig($user_id, $module, $theme)
    {
        return $this->builder->from($this->table . ' as t')
            ->join('user as u', 't.user_id', '=', 'u.id')
            ->where('t.user_id', $user_id)
            ->where('t.module', $module)
            ->where('t.theme', $theme)
            ->first(['t.*', 'u.account']);
    }

    public function getListByTime($minute)
    {
        $builder = $this->builder;
        $builder
            ->select(
                $builder->raw("{$this->table}.*"),
                $builder->raw("user.name as username"),
                $builder->raw("user.account")
            )
            ->join('user', function (JoinClause $join) {
                $join->on("user.id", "=", "{$this->table}.user_id");
            })
            ->where('push_minute', $minute)
            ->where('user.state', 1);

        return $builder->get();
    }

    public function getList()
    {
        $builder = $this->builder;
        $builder
            ->select(
                $builder->raw("{$this->table}.*"),
                $builder->raw("user.name as username"),
                $builder->raw("user.account")
            )
            ->join('user', function (JoinClause $join) {
                $join->on("user.id", "=", "{$this->table}.user_id");
            })
            ->where('user.state', 1);

        return $builder->get();
    }

    public function getListByID($theme, $user_id)
    {
        $builder = $this->builder;
        $builder
            ->select(
                $builder->raw("{$this->table}.*"),
                $builder->raw("user.name as username"),
                $builder->raw("user.account")
            )
            ->join('user', function (JoinClause $join) {
                $join->on("user.id", "=", "{$this->table}.user_id");
            })
            ->where('user.state', 1)
            ->where('theme', $theme)
            ->where('user_id', $user_id)
            ->where('module', 'dms');

        return $builder->get();
    }

    public function updatePushTime($user_id, $theme, $push_hour, $push_minute)
    {
        return $this->builder
            ->where('user_id', $user_id)
            ->where('theme', $theme)
            ->update([
                'push_hour'   => $push_hour,
                'push_minute' => $push_minute
            ]);
    }

    public function updateConfigByPK($user_id, $module, $theme, $config)
    {
        return $this->builder
            ->where('user_id', $user_id)
            ->where('module', $module)
            ->where('theme', $theme)
            ->update([
                'config'      => $config,
                'update_time' => time()
            ]);
    }
}
