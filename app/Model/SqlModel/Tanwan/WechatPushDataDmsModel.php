<?php

namespace App\Model\SqlModel\Tanwan;


use App\Constant\Environment;
use App\Constant\TableTimeColumn;
use App\Model\SqlModel\Tanwan\DataAnalysis\DataAnalysisModel;
use App\Utils\Helpers;
use Common\EnvConfig;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;


class WechatPushDataDmsModel extends AbstractTanwanSqlModel
{
    protected $table = 'wechat_push_data_dms';

    public function __construct()
    {
        parent::__construct();
        if (EnvConfig::ENV !== Environment::PROD) {
            $this->table = 'tanwan_datahub_test.wechat_push_data_dms';// 测试库
        }
    }

    public function batchAdd($data)
    {
        return $this->builder
            ->replace($data);
    }

    public function getDau($permission, $date, $hour, $user_permission)
    {
        // 默认给wanzi平台权限，因为要合并丸子和buer的数据
        if ($user_permission['agent_permission'] != -1) {
            $user_permission = $this->mergePlatform($user_permission);
        }

        $builder = $this->builder;
        $builder->from($this->table . ' as t');
        $builder
            ->select(
                $builder->raw("sum(uid_count) as sum_dau"),
                'tdate', 'thour'
            )
            ->where('tdate', $date)
            ->where('thour', $hour)
            ->groupBy(['tdate', 'thour']);
        $this->addPanelPermission($builder, $permission);
        $this->joinAgent($builder, 't');
        $this->injectAgentPermission($builder, $user_permission['agent_permission']);
        return $builder->first();
    }

    public function getPanelData($permission, $date, $hour, $user_permission)
    {
        // 默认给wanzi平台权限，因为要合并丸子和buer的数据
        if ($user_permission['agent_permission'] != -1) {
            $user_permission = $this->mergePlatform($user_permission);
        }
        $main_builder = $this->builder;

        $builder = $this->builder;
        $builder->from($this->table . ' as t');
        $builder
            ->select(
                $builder->raw("sum(ori_money) sum_ori_money"),
                $builder->raw("sum(reg_uid_count) sum_reg_uid_count"),
                $builder->raw("sum(total_pay_money) sum_total_pay_money"),
                $builder->raw("sum(uid_count) as sum_dau"),
                'tdate', 'thour'
            )
            ->where('tdate', $date)
            ->where('thour', $hour)
            ->groupBy(['tdate', 'thour']);
        $this->addPanelPermission($builder, $permission);
        $this->joinAgent($builder, 't');
        $this->injectAgentPermission($builder, $user_permission['agent_permission']);
        $main_builder->withExpression('t1', $builder);

        $open_service_builder = $this->builder;
        $open_service_sub_builder = $this->builder;
        $open_service_sub_builder->from($this->table . ' as t');
        $open_service_sub_builder
            ->select(
                $open_service_sub_builder->raw("DISTINCT mix_id"),
                "open_service",
                "tdate",
                "thour"
            )
            ->where('tdate', $date)
            ->where('thour', $hour);
        $this->addPanelPermission($open_service_sub_builder, $permission);
        $this->joinAgent($open_service_sub_builder, 't');
        $this->injectAgentPermission($builder, $user_permission['agent_permission']);
        $open_service_builder
            ->fromSub($open_service_sub_builder, 'op')
            ->select(
                $open_service_builder->raw("sum(open_service) as sum_open_service"),
                "tdate",
                "thour"
            )
            ->groupBy(['tdate', 'thour']);
        $main_builder->withExpression('t2', $open_service_builder);

        $main_builder
            ->from('t1')
            ->join('t2', function (JoinClause $join) {
                $join->on('t2.tdate', '=', 't1.tdate');
                $join->on('t2.thour', '=', 't1.thour');
            });
        return $main_builder->first();
    }

    private function addPanelPermission(Builder &$builder, $permission)
    {
        $builder->where(function (Builder $query_0) use ($permission) {
            foreach ($permission as $sub_theme) {
                $query_0->orWhere(function (Builder $query_1) use ($sub_theme) {
                    foreach ($sub_theme as $type) {
                        $game_data = $type['game'];
                        $alg_data = $type['alg'];
                        $is_apportioned = $type['is_apportioned'] ?? 1;
                        $query_1->orWhere(function (Builder $query) use ($game_data, $alg_data, $is_apportioned) {
                            foreach ($game_data as $platform => $game) {
                                $platform = strval($platform);
                                $query->orWhere(function (Builder $main_query) use ($platform, $game, $alg_data, $is_apportioned) {
                                    $main_query->where('t.platform', $platform);
                                    $main_query->where(function (Builder $sub_query) use ($game) {
                                        foreach ($game as $game_cate => $game_id) {
                                            if (!empty($game_id)) {
                                                $sub_query->whereIn('t.' . $game_cate, $game_id);
                                            }
                                        }
                                    });
                                    $main_query->where('is_apportioned', '=', $is_apportioned);
                                    if (count($alg_data) > 0) {
                                        $main_query->whereIn('t.agent_leader_group_name', $alg_data);
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
    }

    public function getTypeReport($config, $game_data, $alg_data, $date, $last_date, $hour, $permission, $is_apportioned)
    {
        // 默认给wanzi平台权限，因为要合并他们的数据
        if ($permission['agent_permission'] != -1) {
            $permission = $this->mergePlatform($permission);
        }
        $main_builder = $this->builder;
        $builder = $this->builder;
        $open_service_builder = $this->builder;
        $open_service_sub_builder = $this->builder;

        $type = $config['type'];
        $month_total_pay_money_goal = isset($config['month_total_pay_money_goal']) && is_numeric($config['month_total_pay_money_goal']) ? $config['month_total_pay_money_goal'] : 0;
        $builder->from($this->table . ' as t');
        $builder->where('t.is_apportioned', '=', $is_apportioned);
        $builder
            ->select(
                $builder->raw("'{$type}' as type"),
                $builder->raw("sum(ori_money) as ori_money"),
                $builder->raw("sum(money) as money"),
                $builder->raw("sum(reg_uid_count) as reg_uid_count"),
                $builder->raw("cast( ifnull(sum(dby_second_login_count) / sum(dby_reg_uid_count), 0) AS DECIMAL ( 12, 3 ) ) as dby_second_login_rate"),
                $builder->raw("sum(dby_second_login_count) as dby_second_login_count"),
                $builder->raw("sum(month_reg_uid_count) as month_reg_uid_count"),
                $builder->raw("sum(dby_reg_uid_count) as dby_reg_uid_count"),
                $builder->raw("sum(reg_uid_new_pay_count) as reg_uid_new_pay_count"),
                $builder->raw("sum(uid_count) as uid_count"),
                $builder->raw("sum(old_uid_count) as old_uid_count"),
                $builder->raw("sum(total_pay_money) as total_pay_money"),
                $builder->raw("sum(first_day_pay_money) as first_day_pay_money"),
                $builder->raw("ifnull(sum(money) / sum(reg_uid_new_pay_count), 0) as cost_per_reg_uid_new_pay"),
                $builder->raw("sum(month_ori_money) as month_ori_money"),
                $builder->raw("sum(month_money) as month_money"),
                $builder->raw("sum(month_total_pay_money) as month_total_pay_money"),
                $builder->raw("sum(day_pay_count) as day_pay_count"),
                'tdate', 'thour'
            );
        $builder->addSelect($builder->raw("{$month_total_pay_money_goal} as month_total_pay_money_goal"));
        if ($month_total_pay_money_goal == 0) {
            $builder->addSelect($builder->raw("0 as month_total_pay_money_progress"));
        } else {
            $builder->addSelect($builder->raw("sum(month_total_pay_money) / {$month_total_pay_money_goal} as month_total_pay_money_progress"));
        }
        $this->addPermission($builder, $game_data, $alg_data);
        $this->joinAgent($builder, 't');
        $this->injectAgentPermission($builder, $permission['agent_permission']);
        $builder
            ->whereBetween('tdate', [$last_date, $date])
            ->where('thour', $hour)
            ->groupBy(['tdate', 'thour']);
        $main_builder->withExpression('t1', $builder);

        $open_service_sub_builder->from($this->table . ' as t');
        $open_service_sub_builder->where('t.is_apportioned', '=', $is_apportioned);
        $open_service_sub_builder
            ->select(
                $open_service_sub_builder->raw("DISTINCT mix_id"),
                "open_service",
                "tdate",
                "thour"
            )
            ->whereBetween('tdate', [$last_date, $date])
            ->where('thour', $hour);
        $this->addPermission($open_service_sub_builder, $game_data, $alg_data);
        $this->joinAgent($open_service_sub_builder, 't');
        $this->injectAgentPermission($open_service_sub_builder, $permission['agent_permission']);
        $open_service_builder
            ->fromSub($open_service_sub_builder, 'op')
            ->select(
                $open_service_builder->raw("SUM(open_service) as open_service"),
                "tdate",
                "thour"
            )
            ->groupBy(['tdate', 'thour']);
        $main_builder->withExpression('t2', $open_service_builder);
        $main_builder
            ->from('t1')
            ->join('t2', function (JoinClause $join) {
                $join->on('t2.tdate', '=', 't1.tdate');
                $join->on('t2.thour', '=', 't1.thour');
            });

        return $main_builder->get();
    }

    public function getDataSum($type_info_sum, $total_goal, $date, $last_date, $hour, $permission)
    {
        // 默认给wanzi平台权限，因为要合并丸子的数据
        if ($permission['agent_permission'] != -1) {
            $permission = $this->mergePlatform($permission);
        }

        $main_builder = $this->builder;
        $builder = $this->builder;
        $open_service_builder = $this->builder;
        $open_service_sub_builder = $this->builder;

        $builder->from($this->table . ' as t');
        $builder
            ->select(
                $builder->raw("'合计汇总' as type"),
                $builder->raw("sum(ori_money) as ori_money"),
                $builder->raw("sum(money) as money"),
                $builder->raw("sum(reg_uid_count) as reg_uid_count"),
                $builder->raw("cast( ifnull(sum(dby_second_login_count) / sum(dby_reg_uid_count), 0) AS DECIMAL ( 12, 3 ) ) as dby_second_login_rate"),
                $builder->raw("sum(dby_second_login_count) as dby_second_login_count"),
                $builder->raw("sum(month_reg_uid_count) as month_reg_uid_count"),
                $builder->raw("sum(dby_reg_uid_count) as dby_reg_uid_count"),
                $builder->raw("sum(reg_uid_new_pay_count) as reg_uid_new_pay_count"),
                $builder->raw("sum(uid_count) as uid_count"),
                $builder->raw("sum(old_uid_count) as old_uid_count"),
                $builder->raw("sum(total_pay_money) as total_pay_money"),
                $builder->raw("sum(first_day_pay_money) as first_day_pay_money"),
                $builder->raw("ifnull(sum(money) / sum(reg_uid_new_pay_count), 0) as cost_per_reg_uid_new_pay"),
                $builder->raw("sum(month_ori_money) as month_ori_money"),
                $builder->raw("sum(month_money) as month_money"),
                $builder->raw("sum(month_total_pay_money) as month_total_pay_money"),
                $builder->raw("sum(day_pay_count) as day_pay_count"),
                'tdate', 'thour'
            );
        $builder->addSelect($builder->raw("{$total_goal} as month_total_pay_money_goal"));
        if ($total_goal == 0) {
            $builder->addSelect($builder->raw("0 as month_total_pay_money_progress"));
        } else {
            $builder->addSelect($builder->raw("sum(month_total_pay_money) / {$total_goal} as month_total_pay_money_progress"));
        }
        $builder->whereBetween('tdate', [$last_date, $date])
            ->where('thour', $hour)
            ->groupBy(['tdate', 'thour']);
        $this->addPanelPermission($builder, $type_info_sum);
        $this->joinAgent($builder, 't');
        $this->injectAgentPermission($builder, $permission['agent_permission']);
        $main_builder->withExpression('t1', $builder);

        $open_service_sub_builder->from($this->table . ' as t');
        $open_service_sub_builder
            ->select(
                $open_service_sub_builder->raw("DISTINCT mix_id"),
                "open_service",
                "tdate",
                "thour"
            )
            ->whereBetween('tdate', [$last_date, $date])
            ->where('thour', $hour);
        $this->addPanelPermission($open_service_sub_builder, $type_info_sum);
        $this->joinAgent($open_service_sub_builder, 't');
        $this->injectAgentPermission($open_service_sub_builder, $permission['agent_permission']);
        $open_service_builder
            ->fromSub($open_service_sub_builder, 'op')
            ->select(
                $open_service_builder->raw("SUM(open_service) as open_service"),
                "tdate",
                "thour"
            )
            ->groupBy(['tdate', 'thour']);
        $main_builder->withExpression('t2', $open_service_builder);

        $main_builder->from('t1')
            ->join('t2', function (JoinClause $join) {
                $join->on('t2.tdate', '=', 't1.tdate');
                $join->on('t2.thour', '=', 't1.thour');
            });
        return $main_builder->get();
    }

    private function addPermission(Builder &$builder, $game_data, $alg_data)
    {
        $builder->where(function (Builder $query) use ($game_data, $alg_data) {
            foreach ($game_data as $platform => $game) {
                $platform = strval($platform);
                $query->orWhere(function (Builder $main_query) use ($platform, $game, $alg_data) {
                    $main_query->where('t.platform', $platform);
                    $main_query->where(function (Builder $sub_query) use ($game) {
                        foreach ($game as $game_cate => $game_id) {
                            if (!empty($game_id)) {
                                $sub_query->WhereIn('t.' . $game_cate, $game_id);
                            }
                        }
                    });
                    if (count($alg_data) > 0) {
                        $main_query->whereIn('t.agent_leader_group_name', $alg_data);
                    }
                });
            }
        });
    }

    private function addPermissionSum(Builder &$builders, $game_data_sum, $alg_data_sum)
    {
        $builders->where(function (Builder $queries) use ($game_data_sum, $alg_data_sum) {
            foreach ($game_data_sum as $key => $game_data) {
                $alg_data = $alg_data_sum[$key];
                $queries->orWhere(function (Builder $builder) use ($game_data, $alg_data) {
                    $builder->where(function (Builder $query) use ($game_data, $alg_data) {
                        foreach ($game_data as $platform => $game) {
                            $platform = strval($platform);
                            $query->orWhere(function (Builder $main_query) use ($platform, $game, $alg_data) {
                                $main_query->where('t.platform', $platform);
                                $main_query->where(function (Builder $sub_query) use ($game) {
                                    foreach ($game as $game_cate => $game_id) {
                                        if (!empty($game_id)) {
                                            $sub_query->WhereIn('t.' . $game_cate, $game_id);
                                        }
                                    }
                                });
                                if (count($alg_data) > 0) {
                                    $main_query->whereIn('t.agent_leader_group_name', $alg_data);
                                }
                            });
                        }
                    });
                });
            }
        });
    }

    public function delete()
    {
        $builder_7 = $this->builder;
        $builder_30 = $this->builder;
        //删除按小时的(7天)
        $builder_7
            ->where('tdate', '<', date('Y-m-d', strtotime("-7 days")))
            ->where('thour', '<>', 0)
            ->delete();
        //删除按天的(30天)
        $builder_30
            ->where('tdate', '<', date('Y-m-d', strtotime("-30 days")))
            ->delete();
    }

    public function getSumDataByDate($date, $thour)
    {
        // 这里要 platform 不等于 shenghong,因为不知道 shenghong 是谁在插入
        return $this->builder
            ->where('tdate', $date)
            ->where('thour', $thour)
            ->where('platform', '!=', 'shenghong')
            ->count();
    }

    /**
     * 重载joinAgent,要连v2_dim_agent_id_external表。
     * 这是因为要跟万紫的数据合并
     *
     * builder inner join v2_dim_agent_id as agent
     *
     * @param Builder $builder
     * @param string $alias
     *
     * <AUTHOR>
     */
    public function joinAgent(Builder $builder, $alias)
    {
        $table_name = $this->getTableNameOrAlias($builder);
        $builder->join('v2_dim_agent_id_external as agent', function (JoinClause $join) use ($alias, $table_name) {
            $join->on("$alias.platform", '=', 'agent.platform');
            $join->on("$alias.agent_id", '=', 'agent.agent_id');
            $column = TableTimeColumn::MAP[$table_name] ?? '';
            // 当有直播间或者官网分成的时候 特殊处理一下
            if ($alias === DataAnalysisModel::APPORTION_ALIAS) {
                $column = 'log_date';
            }
            if ($column) {
                $join->whereRaw("$alias.$column between agent.agent_leader_start_time and agent.agent_leader_end_time");
            } else {
                Helpers::getLogger()->warning('table time column 找不到对应的字段。table name :' . $table_name);
            }
        });
    }


    /**
     * 合并这些平台的推送
     *
     * @param $permission
     *
     * @return array
     */
    private function mergePlatform($permission)
    {
//        $permission['agent_permission']['wanzi'] = [];  去掉 丸子，不给贪玩看了
        $permission['agent_permission']['buer'] = [];
        $permission['agent_permission']['GRBB'] = [];
        $permission['agent_permission']['shenghong'] = [];
        $permission['agent_permission']['AX'] = [];
        $permission['agent_permission']['xinxin'] = [];
        $permission['agent_permission']['915'] = [];
        $permission['agent_permission']['9k'] = [];
        $permission['agent_permission']['DY'] = [];
        $permission['agent_permission']['DYHD'] = [];
        $permission['agent_permission']['wenxue'] = [];
        $permission['agent_permission']['guangfeng'] = [];
        $permission['agent_permission']['GR'] = [];
        $permission['agent_permission']['ZW'] = [];
        $permission['agent_permission']['ZWWX'] = [];
        $permission['agent_permission']['twgh'] = [];
        $permission['agent_permission']['haibo'] = [];
        $permission['agent_permission']['43wan'] = [];
        $permission['agent_permission']['JH'] = [];
        $permission['agent_permission']['1377'] = [];
        $permission['agent_permission']['flamingo'] = [];
        $permission['agent_permission']['YY'] = [];
        $permission['agent_permission']['andou'] = [];
        $permission['agent_permission']['beyoung'] = [];
        $permission['agent_permission']['quwan'] = [];
        $permission['agent_permission']['hktw'] = [];
        $permission['agent_permission']['TWSJ'] = [];
        $permission['agent_permission']['shunwan'] = [];
        $permission['agent_permission']['BX'] = [];
        $permission['agent_permission']['zhijian'] = [];
        $permission['agent_permission']['996'] = [];
        $permission['agent_permission']['hkhp'] = [];
        $permission['agent_permission']['onecheer'] = [];
        $permission['agent_permission']['aomu'] = [];
        $permission['agent_permission']['aomu-tg'] = [];

        return $permission;
    }
}
