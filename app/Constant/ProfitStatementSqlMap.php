<?php

namespace App\Constant;

class ProfitStatementSqlMap
{
    // 整体情况指标
    const OVERALL_SITUATION_TARGET = [
        'cost_money' => "sum( cost_money {apportion_str}) AS cost_money",
        'reg_uid_count' => "sum( reg_uid_count {apportion_str}) AS reg_uid_count",
        'inspire_money' => "sum( inspire_money {apportion_str}) AS inspire_money",
        'ly_pay_money' => "sum( ly_pay_money {apportion_str}) AS ly_pay_money",
        'tencent_ad_incentive_cost' => "sum( tencent_ad_incentive_cost {apportion_str}) AS tencent_ad_incentive_cost",
        'true_divide_applet_pay_ratio' => "0.4 AS true_divide_applet_pay_ratio",
        'true_divide_applet_pay_money' => "SUM(applet_pay_money {apportion_str}) * 0.4 AS true_divide_applet_pay_money",
        'uid_count' => "sum( uid_count {apportion_str} ) AS uid_count",
        'total_pay_money' => "sum( total_pay_money {apportion_str} ) AS total_pay_money",
        'first_day_pay_money' => "sum( first_day_pay_money {apportion_str} ) AS first_day_pay_money",
        'first_day_pay_money_exclude_cps' => "sum( IF( t.settlement_type != 'cps', first_day_pay_money {apportion_str} , 0 ))  AS first_day_pay_money_exclude_cps",
        'reg_total_pay_money' => "sum( reg_total_pay_money {apportion_str} ) AS reg_total_pay_money",
        'current_multiple' => "sum( reg_total_pay_money {apportion_str} )/ sum( first_day_pay_money {apportion_str} ) AS current_multiple",
        'applet_pay_money' => "sum( applet_pay_money {apportion_str} ) AS applet_pay_money",
        'applet_divide_money' => "sum( applet_divide_money {apportion_str} ) AS applet_divide_money",
        'applet_rate' => "ifnull( sum( applet_divide_money {apportion_str} ) / sum( applet_os_money {apportion_str} ), 0 ) AS applet_rate",
        'apple_pay_money' => "sum( apple_pay_money {apportion_str} ) AS apple_pay_money",
        'applet_os_money' => "sum( applet_os_money {apportion_str} ) AS applet_os_money",
        'ios_pay_money' => "sum( ios_pay_money {apportion_str} ) AS ios_pay_money",
        'plate_cost' => "sum( {is_all_cost}_plate_cost {apportion_str} ) AS plate_cost",
        'endorsement_cost' => "sum( {is_all_cost}_endorsement_cost {apportion_str} ) AS endorsement_cost",
        'license_cost' => "sum( {is_all_cost}_license_cost {apportion_str} ) AS license_cost",
        'brand_promotion_cost' => "sum( brand_promotion_cost {apportion_str} ) AS brand_promotion_cost",
        'outsourcing_cost' => "sum( outsourcing_cost {apportion_str} ) AS outsourcing_cost",
        'apple_divide_money' => "sum( apple_divide_money {apportion_str} ) AS apple_divide_money",
        'ios_rate' => "ifnull( sum( apple_divide_money {apportion_str} )/ sum( ios_pay_money {apportion_str} ), 0 ) AS ios_rate",
        'cp_money' => "sum( cp_money {apportion_str} ) AS cp_money",
        'cp_rate' => "sum( cp_money {apportion_str} ) / sum( total_pay_money {apportion_str} ) AS cp_rate",
        'dehan_pay_money' => "sum( dehan_pay_money {apportion_str} ) AS dehan_pay_money",
        'dehan_divide_money' => "sum( dehan_divide_money {apportion_str} ) AS dehan_divide_money",
        'dehan_rate' => "sum( dehan_divide_money {apportion_str} )/ sum( dehan_pay_money {apportion_str} ) AS dehan_rate",
        'total_rate' => "( sum( applet_divide_money {apportion_str} ) + sum( apple_divide_money {apportion_str} ) + sum(cp_money {apportion_str} ) + sum( dehan_divide_money {apportion_str} ) + sum( pay_way_ip_money {apportion_str} ))/ sum( total_pay_money {apportion_str} ) AS total_rate",// 注意这个字段估计有坑
        'share_total_rate' => "( sum( share_applet_divide_money {apportion_str} ) + sum( share_apple_divide_money {apportion_str} ) + sum(cp_money {apportion_str} ) + sum( dehan_divide_money {apportion_str} ) + sum( pay_way_ip_money {apportion_str} ))/ sum( total_pay_money {apportion_str} ) AS share_total_rate",
        'total_divide_money' => "sum( applet_divide_money {apportion_str} ) + sum( apple_divide_money {apportion_str} ) + sum(cp_money {apportion_str} ) + sum( dehan_divide_money {apportion_str} ) + sum( pay_way_ip_money {apportion_str} ) AS total_divide_money",// 注意这个字段估计有坑
        'share_total_divide_money' => "sum( share_applet_divide_money {apportion_str} ) + sum( share_apple_divide_money {apportion_str} ) + sum(cp_money {apportion_str} ) + sum( dehan_divide_money {apportion_str} ) + sum( pay_way_ip_money {apportion_str} ) AS share_total_divide_money",
        'server_money' => "sum( {is_all_cost}_server_money {apportion_str} ) AS server_money",
        'pay_way_pay_money' => "sum( pay_way_pay_money {apportion_str} ) AS pay_way_pay_money",
        'other_cost' => "sum( {is_all_cost}_plate_cost {apportion_str} )+ sum( {is_all_cost}_endorsement_cost {apportion_str} )+ sum( {is_all_cost}_license_cost {apportion_str} )+ sum( brand_promotion_cost {apportion_str}) + sum( outsourcing_cost {apportion_str} ) AS other_cost",
        'current_month_profit' => "sum( {is_all_cost}_current_month_profit {apportion_str} ) AS current_month_profit",
        'current_month_profit_exclude_other_cost' => "sum( ({is_all_cost}_current_month_profit + {is_all_cost}_server_money + {is_all_cost}_plate_cost + {is_all_cost}_endorsement_cost + {is_all_cost}_license_cost + brand_promotion_cost + outsourcing_cost) {apportion_str} ) AS current_month_profit_exclude_other_cost",
        'current_month_share_profit' => "sum( current_month_share_profit {apportion_str} ) AS current_month_share_profit",
        'current_month_share_profit_exclude_other_cost' => "sum( (current_month_share_profit + {is_all_cost}_server_money + {is_all_cost}_plate_cost + {is_all_cost}_endorsement_cost + {is_all_cost}_license_cost + brand_promotion_cost + outsourcing_cost) {apportion_str} ) AS current_month_share_profit_exclude_other_cost",
        'channel_pay_money' => "sum( channel_pay_money {apportion_str} ) AS channel_pay_money",
        'reg_applet_pay_money' => "sum( reg_applet_pay_money {apportion_str} ) AS reg_applet_pay_money",
        'count_month' => "TIMESTAMPDIFF(MONTH, t.tdate, t.run_date) + 1 AS count_month",
        'profit_rate' => "ifnull( sum( {is_all_cost}_current_month_profit {apportion_str} )/ sum( cost_money {apportion_str} ), 0 ) AS profit_rate",
        'box_agent_money' => "sum( box_agent_money {apportion_str} ) AS box_agent_money",
        'box_agent_divide_money' => "sum( box_agent_money {apportion_str} ) * 0.4 AS box_agent_divide_money",
        'pay_way_ip_money' => "sum( pay_way_ip_money {apportion_str} ) AS pay_way_ip_money",
        'pre_box_agent_money' => "sum( pre_box_agent_money {apportion_str} ) AS pre_box_agent_money",
        'pre_box_agent_divide_money' => "sum( pre_box_agent_money {apportion_str} ) *0.4 AS pre_box_agent_divide_money",
        'pre_pay_way_ip_money' => "sum( pre_pay_way_ip_money {apportion_str} ) AS pre_pay_way_ip_money",
        'share_applet_divide_money' => "sum( share_applet_divide_money {apportion_str} ) AS share_applet_divide_money",
        'share_apple_divide_money' => "sum( share_apple_divide_money {apportion_str} ) AS share_apple_divide_money",
    ];

    // 预估情况指标
    const ESTIMATED_SITUATION_TARGET = [
        'pre_day_360_pay' => "sum( pre_day_360_pay {apportion_str} ) AS pre_day_360_pay",
        'pre_day_360_pay_exclude_cps' => "sum( IF( t.settlement_type != 'cps', pre_day_360_pay {apportion_str} , 0 ) ) AS pre_day_360_pay_exclude_cps",
        'multiple' => "cast( sum( pre_day_360_pay {apportion_str} )/ sum( first_day_pay_money {apportion_str} ) AS DECIMAL ( 12, 4 ) ) AS multiple",
        'pre_pay_way_pay_money' => "sum( pre_pay_way_pay_money {apportion_str} ) AS pre_pay_way_pay_money",
        'pre_cp_money' => "sum( pre_cp_money {apportion_str} ) AS pre_cp_money",
        'pre_server_money' => "sum( pre_server_money {apportion_str} ) AS pre_server_money",
        'pre_dehan_divide_money' => "sum( pre_dehan_divide_money {apportion_str} ) AS pre_dehan_divide_money",
        'pre_day_360_profit' => "sum( {is_all_cost}_pre_day_360_profit {apportion_str} ) as pre_day_360_profit",
        'pre_day_360_share_profit' => "sum( pre_day_360_share_profit {apportion_str} ) as pre_day_360_share_profit",
        'pre_apple_divide_money' => "sum( pre_apple_divide_money {apportion_str} ) AS pre_apple_divide_money",
        'pre_applet_divide_money' => "sum( pre_applet_divide_money {apportion_str} ) AS pre_applet_divide_money",
        'pre_profit_rate' => "ifnull( sum( {is_all_cost}_pre_day_360_profit {apportion_str} )/ sum( cost_money {apportion_str} ), 0 ) AS pre_profit_rate",
        'pre_day_360_profit_exclude_other_cost' => "sum( {is_all_cost}_pre_day_360_profit {apportion_str} )+ sum( {is_all_cost}_plate_cost {apportion_str} ) +sum( {is_all_cost}_endorsement_cost {apportion_str} ) + sum( {is_all_cost}_license_cost {apportion_str} ) + sum( brand_promotion_cost {apportion_str} ) + sum( outsourcing_cost {apportion_str} ) as pre_day_360_profit_exclude_other_cost",
        'pre_share_day_360_profit_exclude_other_cost' => "sum( pre_day_360_share_profit {apportion_str} )+ sum( plate_cost {apportion_str} ) +sum( endorsement_cost {apportion_str} ) + sum( license_cost {apportion_str} )  + sum( brand_promotion_cost {apportion_str} )  + sum( outsourcing_cost {apportion_str} ) as pre_share_day_360_profit_exclude_other_cost",
        'pre_share_applet_divide_money' => "sum( pre_share_applet_divide_money {apportion_str} ) AS pre_share_applet_divide_money",
        'pre_share_apple_divide_money' => "sum( pre_share_apple_divide_money {apportion_str} ) AS pre_share_apple_divide_money",
        'multiple_exclude_cps' => "cast( sum( IF( t.settlement_type != 'cps', pre_day_360_pay {apportion_str} , 0 ) )/ sum( IF( t.settlement_type != 'cps', first_day_pay_money {apportion_str} , 0 )) AS DECIMAL ( 12, 4 ) ) AS multiple_exclude_cps",
        'pre_profit_share_rate' => "ifnull( sum( pre_day_360_share_profit {apportion_str} )/ sum( cost_money {apportion_str} ) , 0 ) AS pre_profit_share_rate",
        'pre_profit_share_rate_exclude_other_cost' => "ifnull( sum(( pre_day_360_share_profit + plate_cost + endorsement_cost + license_cost + brand_promotion_cost + outsourcing_cost) {apportion_str} )/ sum( cost_money {apportion_str} ) , 0 ) AS pre_profit_share_rate_exclude_other_cost",
    ];

    // 累计指标
    const CUMULATIVE_TARGET = [
        'total_reg_uid_count' => "SUM( total_reg_uid_count {apportion_str} ) AS total_reg_uid_count",
        'total_cost_money' => "SUM( total_cost_money {apportion_str} ) AS total_cost_money",
        'total_total_pay_money' => "SUM( total_total_pay_money {apportion_str} ) AS total_total_pay_money",
        'total_current_month_profit' => "SUM( {is_all_cost}_total_current_month_profit {apportion_str} ) AS total_current_month_profit",
        'total_multiple' => "SUM( total_pre_day_360_pay {apportion_str} ) / SUM( total_first_day_pay_money {apportion_str} ) AS total_multiple",
        'total_pre_day_360_profit' => "SUM( {is_all_cost}_total_pre_day_360_profit {apportion_str} ) AS total_pre_day_360_profit",
        'total_pre_day_360_pay' => "SUM( total_pre_day_360_pay {apportion_str} ) AS total_pre_day_360_pay",
        'total_first_day_pay_money' => "SUM( total_first_day_pay_money {apportion_str} ) AS total_first_day_pay_money",
    ];

    const COMPUTE_TARGET = [
        'total_multiple' => ['total_pre_day_360_pay', 'total_first_day_pay_money'],
        'current_multiple' => ['reg_total_pay_money', 'first_day_pay_money'],
        'applet_rate' => ['applet_divide_money', 'applet_os_money'],
        'ios_rate' => ['apple_divide_money', 'ios_pay_money'],
        'cp_rate' => ['cp_money', 'total_pay_money'],
        'dehan_rate' => ['dehan_divide_money', 'dehan_pay_money'],
        'total_rate' => ['applet_divide_money', 'apple_divide_money', 'cp_money', 'dehan_divide_money', 'pay_way_ip_money', 'total_pay_money'],
        'share_total_rate' => ['share_applet_divide_money', 'share_apple_divide_money', 'cp_money', 'dehan_divide_money', 'pay_way_ip_money', 'total_pay_money'],
        'total_divide_money' => ['applet_divide_money', 'apple_divide_money', 'cp_money', 'dehan_divide_money', 'pay_way_ip_money'],
        'share_total_divide_money' => ['share_applet_divide_money', 'share_apple_divide_money', 'cp_money', 'dehan_divide_money', 'pay_way_ip_money'],
        'other_cost' => ['plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost'],
        'profit_rate' => ['current_month_profit', 'cost_money'],
        'multiple' => ['pre_day_360_pay', 'first_day_pay_money'],
        'pre_profit_rate' => ['pre_day_360_profit', 'cost_money'],
        'pre_day_360_profit_exclude_other_cost' => ['pre_day_360_profit', 'plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost'],
        'pre_share_day_360_profit_exclude_other_cost' => ['pre_day_360_share_profit', 'plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost'],
        'true_divide_applet_pay_money' => ['applet_pay_money', 'true_divide_applet_pay_ratio'],
        'box_agent_divide_money' => ['box_agent_money', 'cost_money'],
        'pre_box_agent_divide_money' => ['pre_box_agent_money', 'cost_money'],
        'pre_profit_share_rate' => ['pre_day_360_share_profit', 'cost_money'],
        'pre_profit_share_rate_exclude_other_cost' => ['pre_day_360_share_profit', 'plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost', 'cost_money'],
        'multiple_exclude_cps' => ['pre_day_360_pay_exclude_cps', 'first_day_pay_money_exclude_cps'],
        'current_month_profit_exclude_other_cost' => ['current_month_profit', 'server_money', 'plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost'],
        'current_month_share_profit_exclude_other_cost' => ['current_month_share_profit', 'server_money', 'plate_cost', 'endorsement_cost', 'license_cost', 'brand_promotion_cost', 'outsourcing_cost'],
    ];

    // 保留小数点两位的指标
    const DECIMAL_TWO = [
        'multiple',
        'current_multiple',
        'total_multiple',
        'true_divide_applet_pay_ratio',
        'multiple_exclude_cps',
    ];

    // 导出指标中文映射
    const EXPORT_HEADER_MAP = [
        'project_team_id' => '项目组id',
        'project_team_name' => '项目组名称',
        'run_date' => '版本月',
        'tdate' => '统计月',
        'count_month' => '投放月数',
        'cost_money' => '实际消耗',
        'reg_uid_count' => '注册',
        'inspire_money' => '激励广告收入（扣除研发分成）',
        'ly_pay_money' => '联运流水',
        'tencent_ad_incentive_cost' => '腾讯广告激励金消耗',
        'true_divide_applet_pay_ratio' => '小程序真实分成比例',
        'true_divide_applet_pay_money' => '小程序真实分成金额',
        'applet_os_money' => '小程序端原安卓流水',
        'uid_count' => '月活',
        'total_pay_money' => '总流水',
        'first_day_pay_money' => '首日付费',
        'multiple' => '产品倍数',
        'pre_day_360_pay' => '预估终身付费',
        'applet_pay_money' => '小程序流水',
        'applet_divide_money' => '小程序扣除激励金分成金额',
        'applet_rate' => '小程序分成比例',
        'apple_pay_money' => 'IOS流水',
        'apple_divide_money' => 'IOS分成金额',
        'ios_rate' => 'IOS分成比例',
        'cp_money' => '研发分成金额',
        'cp_rate' => '研发分成比例',
        'dehan_pay_money' => '德寒流水',
        'dehan_divide_money' => '德寒分成',
        'dehan_rate' => '德寒分成比例',
        'total_rate' => '总分成比例',
        'share_total_rate' => '总分成比例（共享比率）',
        'total_divide_money' => '总分成金额',
        'share_total_divide_money' => '总分成金额（共享比率）',
        'server_money' => '服务器费用',
        'pay_way_pay_money' => '渠道费',
        'other_cost' => '其他成本',
        'current_month_profit' => '当月运营利润（含其他成本）',
        'current_month_share_profit' => '当月运营利润（共享比率含其他成本）',
        'channel_pay_money' => '支付渠道流水',
        'reg_applet_pay_money' => '归因小程序真实流水',
        'pre_pay_way_pay_money' => '预估渠道费',
        'pre_cp_money' => '预估研发分成金额',
        'pre_server_money' => '预估服务器费用',
        'pre_dehan_divide_money' => '预估德寒分成',
        'reg_total_pay_money' => '累计付费',
        'current_multiple' => '当前倍数',
        'total_reg_uid_count' => '累计注册',
        'total_cost_money' => '累计实际消耗',
        'total_total_pay_money' => '累计总流水',
        'total_current_month_profit' => '累计运营利润',
        'total_multiple' => '综合倍数',
        'total_pre_day_360_profit' => '累计预估运营利润（含其他成本）',
        'pre_day_360_profit' => '预估运营利润（含其他成本）',
        'pre_day_360_share_profit' => '预估运营利润（共享比率）',
        'pre_day_360_profit_exclude_other_cost' => '预估运营利润',
        'pre_share_day_360_profit_exclude_other_cost' => '预估运营利润（共享比率含其他成本）',
        'pre_apple_divide_money' => '预估iOS分成',
        'pre_applet_divide_money' => '预估小程序分成',
        'profit_rate' => '当期利润率',
        'pre_profit_rate' => '预估利润率',
        'plate_cost' => '钣金费用',
        'endorsement_cost' => '代言费',
        'license_cost' => '授权金',
        'brand_promotion_cost' => '宣发费',
        'outsourcing_cost' => '外包费',
        'box_agent_money' => '盒子流水',
        'box_agent_divide_money' => '盒子分成',
        'pay_way_ip_money' => '支付渠道IP费',
        'pre_box_agent_divide_money' => '预估盒子分成',
        'pre_pay_way_ip_money' => '预估支付渠道IP费',
        'share_applet_divide_money' => '小程序分成金额(共享比率)',
        'share_apple_divide_money' => 'IOS分成金额(共享比率)',
        'pre_share_applet_divide_money' => '预估小程序分成(共享比率)',
        'pre_share_apple_divide_money' => '预估IOS分成(共享比率)',
        'multiple_exclude_cps' => '产品倍数(非cps)',
        'pre_profit_share_rate_exclude_other_cost' => '预估利润率(共享比率)',
        'pre_profit_share_rate' => '预估利润率(共享比率含其他成本)',
        'current_month_profit_exclude_other_cost' => '当月运营利润',
        'current_month_share_profit_exclude_other_cost' => '当月运营利润(共享比率)',
    ];

    const All_TARGET = [
        "cost_money" => "t.cost_money",
        "uid_count" => "t.uid_count",
        "reg_uid_count" => "t.reg_uid_count",
        "inspire_money" => "t.inspire_money",
        "ly_pay_money" => "t.ly_pay_money",
        "tencent_ad_incentive_cost" => "t.tencent_ad_incentive_cost",
        "true_divide_applet_pay_ratio" => "0.4 AS true_divide_applet_pay_ratio",
        "total_pay_money" => "t.total_pay_money",
        "pre_day_360_pay" => "t.pre_day_360_pay",
        "applet_pay_money" => "t.applet_pay_money",
        "applet_os_money" => "t.applet_os_money",
        "pre_applet_divide_money" => "t.pre_applet_divide_money",
        "apple_pay_money" => "t.apple_pay_money",
        "ios_pay_money" => "t.ios_pay_money",
        "apple_divide_money" => "t.apple_divide_money",
        "cp_money" => "t.cp_money",
        "pre_cp_money" => "t.pre_cp_money",
        "pay_way_pay_money" => "t.pay_way_pay_money",
        "channel_pay_money" => "t.channel_pay_money",
        "reg_applet_pay_money" => "t.reg_applet_pay_money",
        "pre_pay_way_pay_money" => "t.pre_pay_way_pay_money",
        "dehan_divide_money" => "t.dehan_divide_money",
        "dehan_pay_money" => "t.dehan_pay_money",
        "pre_server_money" => "t.pre_server_money",
        "pre_dehan_divide_money" => "t.pre_dehan_divide_money",
        "first_day_pay_money" => "t.first_day_pay_money",
        "reg_total_pay_money" => "t.reg_total_pay_money",
        "proxy_type" => "t.proxy_type",
        "insert_time" => "t.insert_time",
        "pre_apple_divide_money" => "t.pre_apple_divide_money",
        "applet_divide_money" => "t.applet_divide_money",
        "box_agent_money" => "t.box_agent_money",
        "pay_way_ip_money" => "t.pay_way_ip_money",
        "pre_box_agent_money" => "t.pre_box_agent_money",
        "pre_pay_way_ip_money" => "t.pre_pay_way_ip_money",
        "share_applet_divide_money" => "t.share_applet_divide_money",
        "share_apple_divide_money" => "t.share_apple_divide_money",
        "pre_share_applet_divide_money" => "t.pre_share_applet_divide_money",
        "pre_share_apple_divide_money" => "t.pre_share_apple_divide_money",
        // 累计字段
        "total_reg_uid_count" => "sum(reg_uid_count) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_reg_uid_count",
        "total_cost_money" => "sum(cost_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_cost_money",
        "total_total_pay_money" => "sum(total_pay_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_total_pay_money",
        "total_current_month_profit" => "sum({is_all_cost}_current_month_profit) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_current_month_profit",
        "total_pre_day_360_pay" => "sum(pre_day_360_pay) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_pre_day_360_pay",
        "total_pre_day_360_profit" => "sum({is_all_cost}_pre_day_360_profit) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_pre_day_360_profit",
        "total_first_day_pay_money" => "sum(first_day_pay_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate) AS total_first_day_pay_money",

        // all字段
        "plate_cost" => "t.{is_all_cost}_plate_cost AS plate_cost",
        "endorsement_cost" => "t.{is_all_cost}_endorsement_cost AS endorsement_cost",
        "license_cost" => "t.{is_all_cost}_license_cost AS license_cost",
        "server_money" => "t.{is_all_cost}_server_money AS server_money",
        "current_month_profit" => "t.{is_all_cost}_current_month_profit AS current_month_profit",
        "pre_day_360_profit" => "t.{is_all_cost}_pre_day_360_profit AS pre_day_360_profit",
        // total_current_month_profit, total_pre_day_360_profit 也是包含all的, 归到累计字段里
    ];

    const AGGREGATION_TARGET = [
        // 累计字段
        "total_reg_uid_count" => "FIRST_VALUE(total_reg_uid_count) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_reg_uid_count",
        "total_cost_money" => "FIRST_VALUE(total_cost_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_cost_money",
        "total_total_pay_money" => "FIRST_VALUE(total_total_pay_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_total_pay_money",
        "total_current_month_profit" => "FIRST_VALUE(total_current_month_profit) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_current_month_profit",
        "total_pre_day_360_pay" => "FIRST_VALUE(total_pre_day_360_pay) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_pre_day_360_pay",
        "total_pre_day_360_profit" => "FIRST_VALUE(total_pre_day_360_profit) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_pre_day_360_profit",
        "total_first_day_pay_money" => "FIRST_VALUE(total_first_day_pay_money) OVER (PARTITION BY t.run_date, {group_by_expression} ORDER BY t.tdate DESC) AS total_first_day_pay_money",

        // all字段
        "plate_cost" => "plate_cost AS plate_cost",
        "endorsement_cost" => "endorsement_cost AS endorsement_cost",
        "license_cost" => "license_cost AS license_cost",
        "server_money" => "server_money AS server_money",
        "current_month_profit" => "current_month_profit AS current_month_profit",
        "pre_day_360_profit" => "pre_day_360_profit AS pre_day_360_profit",
    ];

    // 累加类型字段的合计
    const SUM_COLUMN = [
        'total_pre_day_360_profit',
        'total_cost_money',
        'total_current_month_profit',
        'total_reg_uid_count',
        'total_total_pay_money',
        'total_pre_day_360_pay',
        'total_first_day_pay_money',
    ];

    const APPORTION_STR = " * IFNULL( oda.apportion_rate, 1 )";
}