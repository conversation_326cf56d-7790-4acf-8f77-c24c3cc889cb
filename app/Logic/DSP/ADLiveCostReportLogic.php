<?php

namespace App\Logic\DSP;

use App\Constant\ADFieldsENToCNMap;
use App\Constant\ADLiveCostSqlMap;
use App\Constant\ADLiveMediaTypeMap;
use App\Constant\Environment;
use App\Constant\StarDemandSettleTemplate;
use App\Container;
use App\Controller\API\ApproveController;
use App\Exception\AppException;
use App\Model\HttpModel\Bpm\WorkProjectApproveInst\WorkProjectApproveInstModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\RedisModel\StarDemandReceiveModel;
use App\Model\SqlModel\DataMedia\DwdOrderFieldLogModel;
use App\Model\SqlModel\DataMedia\OdsCompanyInvoiceInfoModel;
use App\Model\SqlModel\DataMedia\OdsStarAnchorLogModel;
use App\Model\SqlModel\DataMedia\OdsStarAuthorLogModel;
use App\Model\SqlModel\DataMedia\OdsStarAuthorPaybackLogModel;
use App\Model\SqlModel\DataMedia\OdsStarAuthorPriceLogModel;
use App\Model\SqlModel\DataMedia\OdsStarBasicDataLogModel;
use App\Model\SqlModel\DataMedia\OdsStarCompanyLogModel;
use App\Model\SqlModel\DataMedia\OdsStarCreateOrderTaskModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveApproveLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\DataMedia\OdsStarSpecialFundLog;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Model\SqlModel\Zeda\ToutiaoStarAnchorModel;
use App\Model\SqlModel\Zeda\ToutiaoStarAnchorOperateLogModel;
use App\Model\SqlModel\Zeda\ToutiaoStarAnchorPriceModel;
use App\Model\SqlModel\Zeda\ToutiaoStarOrderOperateLogModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Param\ADLiveCostReportFilterParam;
use App\Service\UserService;
use App\Struct\Input;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;
use CURLFile;
use Exception;
use Illuminate\Support\Collection;
use Mpdf\Mpdf;
use Mpdf\Output\Destination;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use ZipArchive;
use function Matrix\add;

class ADLiveCostReportLogic
{
    const FREEZE_KEY = "live_cost_freeze_last_month";

    const AUTHOR_PRICE_END_TIME = '2100-01-01 00:00:00';

    const FINANCE_WHITE_USER = [
        "谢希怡",
        "余曼湘",
        "李晓婷",
        "中旭未来",
        "黄铭灿",
    ];

    const BUSINESS_WHITE_USER = [
        "陈南西",
        "叶智爵",
        "中旭未来",
        "谢希怡",
        "余曼湘",
        "李晓婷",
        "黄铭灿",
    ];

    const EXPORT_SETTLE_TYPE_SPLIT = 1; // 按照结算标识分开打印
    const EXPORT_SETTLE_TYPE_COMBINE = 2; // 多个结算标识合并打印

    /**
     * 获取直播成本核算
     * @param ADLiveCostReportFilterParam $param
     * @param int $limit
     * @return array
     */
    public function getReport(ADLiveCostReportFilterParam $param, $limit = 10000)
    {
        $t1 = microtime(true);

        $param->limit = $limit;
        $data = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData($param);

        $t2 = microtime(true);
        $result = $this->calculateTarget($data['list'], $param->all_fields, $limit);
        $t3 = microtime(true);
        return [
            'time' => [
                '总时间' => (string)round($t3 - $t1, 4),
                '获取数据时间' => (string)round($t2 - $t1, 4),
                '计算时间' => (string)round($t3 - $t2, 4),
            ],
            'total' => $data['total'],
            'sum' => $result['sum'],
            'list' => $result['list'],
            'sql' => $data['sql'],
        ];
    }

    /**
     * 计算需要运算的指标
     * @param Collection $data
     * @param            $all_fields
     * @param            $limit
     * @return array
     */
    private function calculateTarget(Collection $data, $all_fields, $limit)
    {
        //新的完整数据
        $final_result = [];
        //新的单条数据
        $single_result = [];
        //总计数据
        $total = [];
        //获取数据总条数
        $count_data = count($data);

        foreach ($data as $key => $value) {
            $value = (array)$value;
            $single_result = $value;
            foreach ($all_fields as $field) {
                switch ($field) {
                    case 'is_extra':
                    case 'is_server_open':
                    case 'com_is_extra':
                    case 'is_settled':
                    case 'order_is_extra':
                        $single_result[$field] = ADFieldsENToCNMap::YES_OR_NO[$single_result[$field]];
                        break;
                    case 'pay_way':
                        $single_result[$field] = ADFieldsENToCNMap::TOUTIAO_STAR_PAY_WAY[$single_result[$field]];
                        break;
                    case 'universal_order_status':
                        $single_result[$field] = ADFieldsENToCNMap::LIVE_ORDER_STATUS_TYPE[$single_result[$field]];
                        break;
                    case 'price_type':
                        $single_result[$field] = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$single_result[$field]];
                        break;
                    case 'author_type':
                        $single_result[$field] = ADFieldsENToCNMap::TOUTIAO_STAR_AUTHOR_TYPE[$single_result[$field] ?? 0];
                        break;
                    case 'dsp_order_type':
                        $single_result[$field] = ADFieldsENToCNMap::TOUTIAO_STAR_DSP_ORDER_TYPE[$single_result[$field] ?? 0];
                        break;
                    case 'live_mode':
                        $single_result[$field] = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_MODE_TYPE[$single_result[$field] ?? 0];
                        break;
                    case 'live_media_type':
                        $single_result[$field] = ADLiveMediaTypeMap::MEDIA_TYPE_MAP[$single_result[$field] ?? 0];
                        break;
                    default:
                        break;
                }
                // 转成百分比 xx率
                if (in_array($field, ADLiveCostSqlMap::ALL_CALCULATE_PERCENTAGE)) {
                    $single_result[$field] = Math::decimal($single_result[$field] * 100, 2) . '%';
                }
                // 初始化total值，以免出现undefined
                if (!isset($total[$field])) {
                    $total[$field] = 0;
                }
                // 需要合计
                if (in_array($field, ADLiveCostSqlMap::ALL_NEED_AMOUNT)) {
                    $single_result[$field] = strval($value[$field] + 0);
                    // 累加
                    $total[$field] = bcadd($total[$field], $value[$field] ?? 0,4);
                    // 最后一次循环
                    if ($key + 1 === $count_data) {
                        $total[$field] = strval($total[$field] + 0);
                    }
                } else {
                    $total[$field] = '';
                }
            }
            $final_result[] = $single_result;
        }

        $list = array_slice($final_result, 0, $limit);
        return [
            'total' => count($list),
            'sum' => $total,
            'list' => $list,
        ];
    }


    /**
     * @param $data_list
     * @return bool
     */
    public function importOrderField($data_list)
    {
        $username = Container::getSession()->name;

        $fields = ['order_id', 'twlanv_yd_settlement_date'];

        $error_message = '';

        $date_validate_format = ['Y/m/d','Y/m/d H:i:s'];

        $list = collect();

        foreach ($data_list as $index => $data) {

            // 跳过表头
            if ($index == 0) {
                continue;
            }

            $row = $index + 1;
            $item = [];

            foreach ($fields as $key => $field) {

                if ('order_id' === $field) {
                    if(!is_numeric($data[$key])) {
                        $error_message .= "第{$row}行数据错误，订单ID输入不正确\n";
                    }
                }

                if ('twlanv_yd_settlement_date' === $field) {

                    if(is_numeric($data[$key])) {
                        // 读取日期格式会被转化成float数值 这里重新格式化
                        $data[$key] = Date::excelToDateTimeObject($data[$key])->format('Y/m/d H:i:s');
                    }

                    if (!Helpers::validDateFormat($data[$key], $date_validate_format)) {
                        $error_message .= "第{$row}行数据错误，结算日期不正确\n";
                    } else {
                        $data[$key] = date('Y-m-d H:i:s', strtotime($data[$key]));
                    }
                }

                $item[$field] = $data[$key] ?? '';
            }

            $item["creator"] = $username;
            $list->push($item);
        }

        if ($error_message) {
            throw new AppException($error_message);
        }

        $order_ids = $list->pluck('order_id');

        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $auth_order_list = (new OdsStarDemandOrderListLogModel())->getAuthOrderIdList($order_ids, $user_list, $leader_permission);

        $diff_order = $order_ids->diff($auth_order_list->pluck('live_order_id'));

        if ($diff_order->isNotEmpty()) {
            throw new AppException('您无法操作以下订单，请删除后重试：' . json_encode($diff_order));
        }

        (new DwdOrderFieldLogModel)->replace($list->toArray());

        return true;

    }

    /**
     * @param $condition
     * @param $page
     * @param $rows
     * @return array
     */
    public function getSpecialFundLog($condition, $page, $rows): array
    {
        $data = (new OdsStarSpecialFundLog)->getList($condition, $page, $rows);
        $data['list']->map(function ($item) {
            $item->status_cn = ToutiaoStarLogic::APPROVE_STATUS_MAP[$item->status];
            $item->approve_url = $item->work_project_approve_inst_id > 0 ? (new ToutiaoStarLogic())->getBpmApproveUrl(27, 154, $item->work_project_approve_inst_id) : '';
            $item->attachments = json_decode($item->attachments, true) ?: [];
        });
        return $data;
    }

    /**
     * @param $condition
     * @param $page
     * @param $rows
     * @return string
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function exportSpecialFundLog($condition): string
    {
        $list_data = $this->getSpecialFundLog($condition, 1, 9999);
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', '订单ID');
        $sheet->setCellValue('B1', '违规扣款');
        $sheet->setCellValue('C1', '扣款原因');
        $sheet->setCellValue('D1', '加班费');
        $sheet->setCellValue('E1', '加班明细');
        $sheet->setCellValue('F1', '主播ID');
        $sheet->setCellValue('G1', '录入人');
        $writer = new Xlsx($spreadsheet);
        $i = 1;
        foreach ($list_data['list'] as $item) {
            $i++;
            $sheet->setCellValueExplicit("A$i", $item->order_id, DataType::TYPE_STRING);
            $sheet->setCellValue("B$i", $item->deduction_amount);
            $sheet->setCellValue("C$i", $item->deduction_desc);
            $sheet->setCellValue("D$i", $item->extra_payment);
            $sheet->setCellValue("E$i", $item->extra_desc);
            $sheet->setCellValue("F$i", $item->anchor_log_id);
            $sheet->setCellValue("G$i", $item->creator);
        }
        $file = TMP_DIR . '/违规扣款-加班费录入.xlsx';
        $writer->save($file);
        return $file;
    }

    /**
     * @param $data_list
     * @param $input
     * @return array
     */
    public function importSpecialFund($data_list, $input)
    {
        $username = Container::getSession()->name;

        $fields = ['order_id', 'deduction_amount', 'deduction_desc', 'extra_payment', 'extra_desc', 'anchor_log_id'];

        $error_message = '';

        $list = collect();
        foreach ($data_list as $index => $data) {

            // 跳过表头
            if ($index == 0) {
                continue;
            }

            $row = $index + 1;
            $item = [];

            foreach ($fields as $key => $field) {

                if ('order_id' === $field) {
                    if(!is_numeric($data[$key])) {
                        $error_message .= "第{$row}行数据错误，订单ID输入不正确\n";
                    }
                }

                if ('deduction_amount' === $field) {
                    if (!is_numeric($data[$key])) {
                        $error_message .= "第{$row}行数据错误，违规扣款输入不正确\n";
                    } else {
                        $data[$key] = abs($data[$key]);
                    }
                }

                if ('extra_payment' === $field) {
                    if (!is_numeric($data[$key])) {
                        $error_message .= "第{$row}行数据错误，加班费输入不正确\n";
                    } else {
                        $data[$key] = abs($data[$key]);
                    }
                }

                $item[$field] = $data[$key] ?? '';
            }

            $item["creator"] = $username;
            $list->push($item);
        }

        if ($error_message) {
            throw new AppException($error_message);
        }

        $order_ids = $list->pluck('order_id');

        // 校验订单结算情况
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId($order_ids);
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法录入加班费：' . json_encode($receive_order_data->pluck('order_id')));
        }

        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $auth_order_list = (new OdsStarDemandOrderListLogModel())->getAuthOrderIdList($order_ids, $user_list, $leader_permission);

        $diff_order = $order_ids->diff($auth_order_list->pluck('live_order_id'));

        if ($diff_order->isNotEmpty()) {
            throw new AppException('您无法操作以下订单，请删除后重试：' . json_encode($diff_order));
        }

        $order_map = [];
        $anchor_info = [];
        $order_anchor_first_live_time = [];
        foreach($auth_order_list as $order) {
            $order_map[$order->live_order_id][] = $order->anchor_log_id;
            $anchor_info[$order->anchor_log_id] = [
                'anchor_name' => $order->live_anchor_name,
                'company_name' => $order->anchor_company,
                'anchor_log_id' => $order->anchor_log_id,
                'true_name' => $order->live_true_name
            ];
            $order_anchor_first_live_time["{$order->live_order_id}-{$order->anchor_log_id}"] = $order->first_live_time;
        }

        $list->transform(function (&$datum) use ($order_map) {
            if (!$datum['anchor_log_id']) {
                if (count($order_map) > 1) {
                    throw new AppException("多主播订单{$datum['order_id']}需明确指定主播");
                }
                if (empty($order_map[$datum['order_id']][0])) {
                    throw new AppException("订单{$datum['order_id']}没有关联主播");
                }
                $datum['anchor_log_id'] = $order_map[$datum['order_id']][0];
            } else {
                if (!in_array($datum['anchor_log_id'], $order_map[$datum['order_id']])) {
                    throw new AppException("订单{$datum['order_id']}所指定主播填写错误");
                }
            }
            $datum['work_project_approve_inst_id'] = 0;
            $datum['status'] = ToutiaoStarLogic::APPROVE_STATUS_WAIT;
            return $datum;
        });

        // 检查是否有审批中的订单
        $model = new OdsStarSpecialFundLog;
        $approve_list = $model->getApproveList($list);
        if ($approve_list->isNotEmpty()) {
            $approve_order_anchor_list = [];
            foreach ($approve_list as $item) {
                $approve_order_anchor_list[] = [
                    'order_id' => $item->order_id,
                    'anchor_log_id' => $item->anchor_log_id
                ];
            }
            throw new AppException("以下费用正在审批中：" . json_encode($approve_order_anchor_list));
        }

        // 找出审批人的工号
        $user_model = new UserModel();

        // 把费用列表分为需要审批和不需要审批
        $need_approve_list = [];
        $pass_list = [];
        $fund_list = [];
        foreach ($list as $item) {
            [$need_approve, $need_copy_approve] = $this->getSpecialFundApproveCopyStatus(
                $item['extra_payment'],
                $order_anchor_first_live_time["{$item['order_id']}-{$item['anchor_log_id']}"] ?? ''
            );

            $tmp_item = $item;
            if ($need_approve) {
                if (!isset($approve_user_info)) {
                    $approve_user_info = $user_model->getInfoByName($input['approve_user']);
                    if (empty($approve_user_info)) {
                        throw new AppException("审批人不存在");
                    }
                }

                if (empty($approve_user_info['staff_number'])) {
                    throw new AppException("审批人缺失工号，请联系管理员补充");
                }

                if (trim($approve_user_info['staff_number']) === trim(Container::getSession()->staff_number)) {
                    throw new AppException("审批人不能选择自己");
                }

                $tmp_item['approve_staff_number'] = trim($approve_user_info['staff_number']);
                $tmp_item['need_copy_approve'] = $need_copy_approve;

                $need_approve_list[] = $tmp_item;
            } else {
                $pass_list[] = $tmp_item;
            }
        }

        if ($need_approve_list) {
            foreach ($need_approve_list as $need_approve_key => $item) {
                // 扣款不需要审批
//                if ($item['deduction_amount'] >= 0) {
//                    $fund_list[] = array_merge($anchor_info[$item['anchor_log_id']], [
//                        'price_type' => '扣费',
//                        'amount' => $item['deduction_amount'],
//                        'desc' => $item['deduction_desc'],
//                        'order_id' => $item['order_id'],
//                        'anchor' => [
//                            'name' => $anchor_info[$item['anchor_log_id']]['anchor_name'],
//                            'true_name' => $anchor_info[$item['anchor_log_id']]['true_name'],
//                            'company_name' => $anchor_info[$item['anchor_log_id']]['company_name'],
//                            'anchor_log_id' => $item['anchor_log_id']
//                        ],
//                        'approve_staff_number' => $item['approve_staff_number'],
//                    ]);
//                }

                if ($item['extra_payment'] >= 0) {
                    $fund_list[] = array_merge($anchor_info[$item['anchor_log_id']], [
                        'price_type' => '加班费',
                        'amount' => $item['extra_payment'],
                        'desc' => $item['extra_desc'],
                        'order_id' => $item['order_id'],
                        'anchor' => [
                            'name' => $anchor_info[$item['anchor_log_id']]['anchor_name'],
                            'true_name' => $anchor_info[$item['anchor_log_id']]['true_name'],
                            'company_name' => $anchor_info[$item['anchor_log_id']]['company_name'],
                            'anchor_log_id' => $item['anchor_log_id']
                        ],
                        'approve_staff_number' => $item['approve_staff_number'],
                        'need_copy_approve' => $item['need_copy_approve']
                    ]);
                }

                // 把审批人和抄送人去掉 方便入表
                unset($need_approve_list[$need_approve_key]['approve_staff_number']);
                unset($need_approve_list[$need_approve_key]['need_copy_approve']);
            }

            // 向BPM发起审批
            $inst_list = (new WorkProjectApproveInstModel())->addSpecialFundInst([
                "space_id" => 27,
                "work_project_id" => 154,
                "staff_number" => Container::getSession()->staff_number,
                "field_values" => [
                    "approve_type" => "主播费用录入",
                    "reason" => $data['reason'] ?? '',
                    "fund_list" => $fund_list,
                    "attachment_url" => ''
                ]
            ]);

            foreach ($inst_list as $inst_info) {
                foreach ($inst_info['fund_list'] as $fund_info) {
                    foreach ($need_approve_list as $need_approve_key => $item) {
                        if ($fund_info['anchor_log_id'] == $item['anchor_log_id'] && $fund_info['order_id'] == $item['order_id']) {
                            $need_approve_list[$need_approve_key]['work_project_approve_inst_id'] = $inst_info['inst_id'];
                        }
                    }
                }
            }

            $model->add($need_approve_list);
        }

        if ($pass_list) {
            $model->add($pass_list);
            $this->handleSpecialFundPass(collect($pass_list));
        }

        // 对应订单的录入历史
//        $history_log = $model->getDataByPrimaryList($list);

//        $model->add($list->toArray());

//        if($history_log->isNotEmpty()) {
//            // 删除已录入订单
//            $model->removeByIds($history_log->pluck('id')->toArray());
//        }

        return [
            'approve_url' => $need_approve_list ? (new ToutiaoStarLogic())->getBpmApproveUrl(27, 154, $need_approve_list[0]['work_project_approve_inst_id']) : ''
        ];
    }

    /**
     * 修改订单加班费/违规扣款
     * @param $order_id
     * @param $data
     * @return array
     */
    public function operateSpecialFund($order_id, $data)
    {
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $auth_order = (new OdsStarDemandOrderListLogModel())->getAuthOrderIdList([$order_id], $user_list, $leader_permission);

        if ($auth_order->isEmpty()) {
            throw new AppException('您无法操作该订单');
        }

        // 校验订单结算情况
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId([$order_id]);
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法录入加班费：' . json_encode($receive_order_data->pluck('order_id')));
        }

        $order_map = [];
        $anchor_info = [];
        $order_anchor_first_live_time = [];
        foreach($auth_order as $order) {
            $order_map[$order->live_order_id][] = $order->anchor_log_id;
            $anchor_info[$order->anchor_log_id] = [
                'anchor_name' => $order->live_anchor_name,
                'company_name' => $order->anchor_company,
                'anchor_log_id' => $order->anchor_log_id,
                'true_name' => $order->live_true_name
            ];
            $order_anchor_first_live_time["{$order->live_order_id}-{$order->anchor_log_id}"] = $order->first_live_time;
        }

        if(!in_array($data['anchor_log_id'], $order_map[$order_id])) {
            throw new AppException("订单{$order_id}所指定主播有误");
        }

        $insert_data = [
            'order_id' => $order_id,
            'anchor_log_id' => $data['anchor_log_id'],
            'deduction_amount' => abs($data['deduction_amount'] ?: 0),
            'deduction_desc' => $data['deduction_desc'] ?: '',
            'extra_payment' => abs($data['extra_payment'] ?: 0),
            'extra_desc' => $data['extra_desc'] ?: '',
            'creator' => Container::getSession()->name,
            'work_project_approve_inst_id' => 0,
            'status' => ToutiaoStarLogic::APPROVE_STATUS_WAIT,
            'attachments' => json_encode($data['attachments'] ?? [], JSON_UNESCAPED_SLASHES)
        ];

        // 检查是否有审批中的订单
        $model = new OdsStarSpecialFundLog;
        $approve_list = $model->getApproveList([$insert_data]);
        if ($approve_list->isNotEmpty()) {
            $approve_order_anchor_list = [];
            foreach ($approve_list as $item) {
                $approve_order_anchor_list[] = [
                    'order_id' => $item->order_id,
                    'anchor_log_id' => $item->anchor_log_id
                ];
            }
            throw new AppException("以下费用正在审批中：" . json_encode($approve_order_anchor_list));
        }

        // 检查是否需要发起审批
        [$need_approve, $need_copy_approve] = $this->getSpecialFundApproveCopyStatus(
            $insert_data['extra_payment'],
            $order_anchor_first_live_time["{$insert_data['order_id']}-{$insert_data['anchor_log_id']}"] ?? ''
        );

        if ($need_approve) {
            // 找出渠道负责人 广告位直播运营 直播运营
            $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
                "dimension" => [
                    "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                    "live_demand_name", "live_author_id", "live_author_name", "live_anchor_name",
                    "live_media_type", "dsp_order_type"
                ],
                "filter" => [
                    "live_order_id" => [
                        "column" => "live_order_id",
                        "value" => [$order_id]
                    ],
                    "anchor_log_id" => [
                        "column" => "anchor_log_id",
                        "value" => [$data['anchor_log_id']]
                    ]
                ],
                "target" => [
                    "agent_leader", "star_interface_person", "site_int_person"
                ],
                "search_all" => true
            ]));

            $anchor_order_unique_key_list = ["{$order_id}-{$data['anchor_log_id']}"];

            // 筛选出本次选的订单+主播ID
            $list['list'] = $list['list']->filter(function ($value) use ($anchor_order_unique_key_list) {
                return in_array("{$value->live_order_id}-{$value->anchor_log_id}", $anchor_order_unique_key_list);
            });

            $agent_leader = $list['list']->first()->agent_leader ?? '';
            $star_interface_person = $list['list']->first()->star_interface_person ?? '';
            $site_int_person = $list['list']->first()->site_int_person ?? '';
            if (empty($site_int_person)) {
                $site_int_person = $star_interface_person;
            }

            if (!empty($data['approve_user'] ?? '')) {
                $approve_user = $data['approve_user'];
            } else {
                // 自动选择审批人
                switch ($insert_data['creator']) {
                    case $agent_leader:
                        $approve_user = $site_int_person;
                        break;
                    case $site_int_person:
                    default:
                        $approve_user = $agent_leader;
                        break;
                }
            }

            $user_model = new UserModel();
            // 找出审批人的工号
            $approve_user_info = $user_model->getDataByName($approve_user);
            if (empty($approve_user_info)) {
                throw new AppException("审批人不存在");
            }

            if (empty($approve_user_info['staff_number'])) {
                throw new AppException("审批人缺失工号，请联系管理员补充");
            }

            if (trim($approve_user_info['staff_number']) === trim(Container::getSession()->staff_number)) {
                throw new AppException("审批人不能选择自己");
            }

            // 向BPM发起审批
            $fund_list = [];
            // 扣款不需要审批
//            if ($insert_data['deduction_amount'] >= 0) {
//                $fund_list[] = array_merge($anchor_info[$insert_data['anchor_log_id']], [
//                    'price_type' => '扣费',
//                    'amount' => $insert_data['deduction_amount'],
//                    'desc' => $insert_data['deduction_desc'],
//                    'order_id' => $insert_data['order_id'],
//                    'anchor' => [
//                        'name' => $anchor_info[$insert_data['anchor_log_id']]['anchor_name'],
//                        'true_name' => $anchor_info[$insert_data['anchor_log_id']]['true_name'],
//                        'company_name' => $anchor_info[$insert_data['anchor_log_id']]['company_name'],
//                        'anchor_log_id' => $insert_data['anchor_log_id']
//                    ],
//                    "approve_staff_number" => trim($approve_user_info['staff_number']),
//                ]);
//            }

            if ($insert_data['extra_payment'] >= 0) {
                $fund_list[] = array_merge($anchor_info[$insert_data['anchor_log_id']], [
                    'price_type' => '加班费',
                    'amount' => $insert_data['extra_payment'],
                    'desc' => $insert_data['extra_desc'],
                    'order_id' => $insert_data['order_id'],
                    'anchor' => [
                        'name' => $anchor_info[$insert_data['anchor_log_id']]['anchor_name'],
                        'true_name' => $anchor_info[$insert_data['anchor_log_id']]['true_name'],
                        'company_name' => $anchor_info[$insert_data['anchor_log_id']]['company_name'],
                        'anchor_log_id' => $insert_data['anchor_log_id']
                    ],
                    "approve_staff_number" => trim($approve_user_info['staff_number']),
                    "need_copy_approve" => $need_copy_approve
                ]);
            }

            $work_project_approve_inst_model = new WorkProjectApproveInstModel();

            // 上传附件到BPM
            $attachments = [];
            if ($data['attachments'] ?? []) {
                foreach ($data['attachments'] as $attachment) {
                    $attachment_result = $work_project_approve_inst_model->uploadAttachment([
                        'space_id' => 27,
                        'work_project_id' => 154,
                        'work_project_inst_id' => 0,
                        'work_project_field_id' => 339,
                        'file' => new CURLFile(SRV_DIR . $attachment['path'], "", $attachment['filename']),
                        'staff_number' => Container::getSession()->staff_number
                    ]);

                    $attachments[] = [
                        'name' => $attachment['filename'],
                        'domain' => $attachment_result['domain'],
                        'url' => $attachment_result['path']
                    ];
                }
            }

            $inst_list = $work_project_approve_inst_model->addSpecialFundInst([
                "space_id" => 27,
                "work_project_id" => 154,
                "staff_number" => Container::getSession()->staff_number,
                "field_values" => [
                    "approve_type" => "主播费用录入",
                    "reason" => $data['reason'] ?? '',
                    "fund_list" => $fund_list,
                    "attachments" => $attachments
                ]
            ]);

            foreach ($inst_list as $inst_info) {
                foreach ($inst_info['fund_list'] as $fund_info) {
                    if ($fund_info['anchor_log_id'] == $insert_data['anchor_log_id'] && $fund_info['order_id'] == $insert_data['order_id']) {
                        $insert_data['work_project_approve_inst_id'] = $inst_info['inst_id'];
                        break;
                    }
                }
            }

            if ($insert_data['work_project_approve_inst_id'] == 0) {
                throw new AppException("创建审批实例失败，请联系管理员");
            }
        }

        // 对应订单的录入历史
//        $history_log = $model->getDataByOrderIdAndAnchorId($order_id, $data['anchor_log_id']);

        $model->add($insert_data);

        if (!$need_approve) {
            $fund_info_list = collect([$insert_data]);
            $this->handleSpecialFundPass($fund_info_list);
        }

//        if($history_log->isNotEmpty()) {
//            // 删除已录入订单
//            $model->removeByIds($history_log->pluck('id')->toArray());
//        }

        return [
            'approve_url' => $need_approve ? (new ToutiaoStarLogic())->getBpmApproveUrl(27, 154, $insert_data['work_project_approve_inst_id']) : ''
        ];
    }

    /**
     * 审批回调后 更新主播费用录入
     * @param $data
     * @return void
     */
    public function updateSpecialFund($data)
    {
        $model = new OdsStarSpecialFundLog();
        $fund_info_list = $model->getDataByWorkProjectApproveInstId($data['work_project_approve_inst_id']);
        if (empty($fund_info_list)) {
            throw new AppException('审批实例所关联的主播价格记录不存在');
        }

        // 审批通过
        if (in_array($data['type'], [ApproveController::APPROVE_TYPE_PASSED, ApproveController::APPROVE_TYPE_SUBMIT_PROCESSING])) {
            $this->handleSpecialFundPass($fund_info_list);
        } elseif ($data['type'] == ApproveController::APPROVE_TYPE_REJECT) {
            foreach ($fund_info_list as $fund_info) {
                $fund_info = (array)$fund_info;
                $model->customUpdate(
                    $fund_info['order_id'],
                    $fund_info['anchor_log_id'],
                    ['status' => ToutiaoStarLogic::APPROVE_STATUS_REJECT]
                );
            }
        }
    }

    /**
     * @param $order_id
     * @return array
     */
    public function getSpecialFundByOrderId($order_id)
    {
        $data = $this->getOrderAndAnchorByOrderId($order_id);
        $anchor_map = (new ToutiaoStarAnchorModel())->getDataByIdList($data->anchor_id_list)->pluck(null,'id');
        $fund_data = (new OdsStarSpecialFundLog)->getDataByOrderId($order_id)->pluck(null, 'anchor_log_id');

        $result = [];
        $result['order_id'] = $order_id;
        $result['demand_name'] = $data->demand_name;
        if (!empty($data)) {
            $data->anchor_list = [];
            foreach ($data->anchor_id_list as $anchor_id) {
                $data->anchor_list[] = [
                    'id' => (string)$anchor_id,
                    'anchor_name' => $anchor_map[$anchor_id]->anchor_name,
                    'company' => $anchor_map[$anchor_id]->company,
                    'deduction_amount' => isset($fund_data[$anchor_id]->deduction_amount) ? round($fund_data[$anchor_id]->deduction_amount, 2) : 0,
                    'deduction_desc' => $fund_data[$anchor_id]->deduction_desc ?? '',
                    'extra_payment' => isset($fund_data[$anchor_id]->extra_payment) ? round($fund_data[$anchor_id]->extra_payment, 2) : 0,
                    'extra_desc' => $fund_data[$anchor_id]->extra_desc ?? '',
                    'anchor_log_id' => $fund_data[$anchor_id]->anchor_log_id ?? '',
                    'attachments' => json_decode($fund_data[$anchor_id]->attachments ?? '[]', true)
                ];
            }

            $result['anchor_list'] = $data->anchor_list;
        }
        return $result;
    }

    /**
     * 获取订单主播ID
     * @param $order_id
     * @return \App\Model\SqlModel\Database\ZDBuilder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getOrderAndAnchorByOrderId($order_id)
    {
        $order_anchor = (new OdsStarCreateOrderTaskModel())->getOrderAndAnchor($order_id);
        $order_anchor->multi_anchor = $order_anchor->multi_anchor ? json_decode($order_anchor->multi_anchor, true): [];
        $order_anchor->anchor_id_list = $order_anchor->anchor_log_id ? [$order_anchor->anchor_log_id] : array_column((array)$order_anchor->multi_anchor, 'anchor_log_id');
        return $order_anchor;
    }

    /**
     * @param $anchor_ids
     * @param $order_ids
     * @return array
     */
    public function getAnchorPriceByOrderId($anchor_ids, $order_ids): array
    {
        $order_data = (new OdsStarDemandOrderListLogModel())->getOrderData($order_ids);

        $data = [];
        $isset_item = []; // 获取最新的
        foreach ($order_data as $order) {
            if (!$order->first_live_time) {
                throw new AppException("订单{$order->order_id}未开播，不能绑定主播");
            }
            $price_data = (new ToutiaoStarAnchorPriceModel())->getPriceByPublishTime($anchor_ids, $order->first_live_time);
            if ($price_data->isEmpty()) {
                throw new AppException("订单{$order->order_id}开播时间{$order->first_live_time}未录入当前主播价格");
            }
            $anchor_count = count($anchor_ids);
            foreach ($price_data as $anchor_price_info) {
                // 主播数大于1，取多人直播间价格
                $anchor_price_info->price = $anchor_count > 1 ? round($anchor_price_info->multi_price, 2) : round($anchor_price_info->price, 2);
                $anchor_price_info->live_hour = $anchor_count > 1 ? round($anchor_price_info->multi_live_hour, 2) : round($anchor_price_info->live_hour, 2);
                unset($anchor_price_info->multi_price);
                unset($anchor_price_info->multi_live_hour);
                if (!isset($isset_item[$anchor_price_info->anchor_log_id . '-' . $order->order_id . '-' . $anchor_price_info->price_type])) {
                    $isset_item[$anchor_price_info->anchor_log_id . '-' . $order->order_id . '-' . $anchor_price_info->price_type] = true;
                    $data[$anchor_price_info->anchor_log_id][$order->order_id]['order_id'] = $order->order_id;
                    $data[$anchor_price_info->anchor_log_id][$order->order_id]['first_live_time'] = $order->first_live_time;
                    $data[$anchor_price_info->anchor_log_id][$order->order_id]['price_list'][] = (array)$anchor_price_info;
                }
            }
        }

        foreach($data as &$datum) {
            $datum = array_values($datum);
        }
        return $data;
    }

    /**
     * 星图订单关联主播
     * @param array $bind_detail
     * @return array
     */
    public function bindAnchorPrice(array $bind_detail): array
    {
        // 准备参数
        $order_map = [];
        $anchor_map = [];
        $price_ids = [];
        foreach ($bind_detail as $datum) {
            $order_map[$datum['order_id']][] = $datum;
            $anchor_map[$datum['anchor_id']][] = $datum;
            $price_ids[] = $datum['price_id'];
        }

        // 准备数据
        $anchor_ids = array_keys($anchor_map);
        $anchor_count = count($anchor_ids);
        $order_data = (new OdsStarDemandOrderListLogModel())->getOrderData(array_keys($order_map));
        $all_price_data = (new ToutiaoStarAnchorPriceModel())->getDataByIds($price_ids);
        $anchor_info_map = (new ToutiaoStarAnchorModel())->getDataByIdList($anchor_ids)->pluck(null, 'id')->toArray();

        // 校验订单结算情况
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId(array_keys($order_map));
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法关联主播：' . json_encode($receive_order_data->pluck('order_id')));
        }

        // 校验数据 循环校验完毕再关联,避免部分成功部分失败
        $order_price_data = [];
        foreach ($order_data as $order) {
            if (!$order->first_live_time) {
                throw new AppException("订单{$order->order_id}未开播,不能绑定主播,请重新选择");
            }
            if (Helpers::hasOverlappingIntervals(array_column($order_map[$order->order_id], 'live_time'))) {
                throw new AppException("订单{$order->order_id}预计直播时间有交叉，请检查");
            }
            // 价格信息
            $price_list = array_column($order_map[$order->order_id], 'price_id');
            $price_data = $all_price_data->whereIn('id', $price_list)->toArray();
            foreach ($price_data as $anchor_price_info) {
                // 转数组 否则修改将影响源对象
                $anchor_price_datum = (array)$anchor_price_info;
                // 主播数大于1，取多人直播间价格和时长
                $anchor_price_datum['price'] = $anchor_count > 1 ? round($anchor_price_info->multi_price, 2) : round($anchor_price_info->price, 2);
                $anchor_price_datum['live_hour'] = $anchor_count > 1 ? round($anchor_price_info->multi_live_hour, 2) : round($anchor_price_info->live_hour, 2);
                unset($anchor_price_datum['multi_price']);
                unset($anchor_price_datum['multi_live_hour']);
                if (!$anchor_price_datum['price']) {
                    if ($anchor_price_datum['price_type'] == 5) {
                        $anchor_price_datum['price'] = 0;
                    } else {
                        throw new AppException('主播价格未设置，请检查');
                    }
                }
                if (!$anchor_price_datum['live_hour']) {
                    if ($anchor_price_datum['price_type'] == 5) {
                        $anchor_price_datum['live_hour'] = 0;
                    } else {
                        throw new AppException('主播最低应播时长未设置，请检查');
                    }
                }
                $order_price_data[$order->order_id][] = $anchor_price_datum;
            }
        }

        $task_model = new OdsStarCreateOrderTaskModel();

        // 更新的结果
        $result_data = [];

        foreach ($order_data as $order) {

            // 价格信息
            $anchor_map = array_column($order_map[$order->order_id], null,'anchor_id');
            $price_data = array_column($order_price_data[$order->order_id],null,'anchor_log_id');

            // 初始数据
            $prepare_data = [
                'account_id' => $order->account_id,
                'campaign_id' => $order->campaign_id,
            ];

            // 多人直播间 存multi_anchor
            if ($anchor_count > 1) {
                $multi_anchor = [];
                foreach ($anchor_ids as $anchor_id) {
                    $item = [];
                    $item['true_name'] = $anchor_info_map[$anchor_id]->true_name ?? '';
                    $item['anchor_name'] = $anchor_info_map[$anchor_id]->anchor_name ?? '';
                    $item['name_company'] = $anchor_info_map[$anchor_id]->company ?? '';
                    $item['idcard'] = $anchor_info_map[$anchor_id]->idcard ?? '';
                    $item['anchor_log_id'] = $anchor_id;
                    $item['price'] = $price_data[$anchor_id]['price'] ?? 0;
                    $item['live_hour'] = $price_data[$anchor_id]['live_hour'] ?? 0;
                    $item['price_type'] = $price_data[$anchor_id]['price_type'] ?? '';
                    $item['anchor_price_id'] = $price_data[$anchor_id]['id'] ?? 0;
                    $item['live_start_time'] = $anchor_map[$anchor_id]['live_time'][0] ?? '';
                    $item['live_end_time'] = $anchor_map[$anchor_id]['live_time'][1] ?? '';
                    $multi_anchor[] = $item;
                }
                $prepare_data['true_name'] = $prepare_data['anchor_name'] = $prepare_data['name_company'] = $prepare_data['idcard'] = '';
                $prepare_data['anchor_log_id'] = $prepare_data['price'] = $prepare_data['live_hour'] = $prepare_data['price_type'] = $prepare_data['anchor_price_id'] = 0;
                $prepare_data['multi_anchor'] = json_encode($multi_anchor);
            } else {
                // 单人直播间
                $anchor_id = $anchor_ids[0];
                $prepare_data['true_name'] = $anchor_info_map[$anchor_id]->true_name ?? '';
                $prepare_data['anchor_name'] = $anchor_info_map[$anchor_id]->anchor_name ?? '';
                $prepare_data['name_company'] = $anchor_info_map[$anchor_id]->company ?? '';
                $prepare_data['idcard'] = $anchor_info_map[$anchor_id]->idcard ?? '';
                $prepare_data['anchor_log_id'] = $anchor_id;
                $prepare_data['price'] = $price_data[$anchor_id]['price'] ?? 0;
                $prepare_data['live_hour'] = $price_data[$anchor_id]['live_hour'] ?? 0;
                $prepare_data['price_type'] = $price_data[$anchor_id]['price_type'] ?? '';
                $prepare_data['anchor_price_id'] = $price_data[$anchor_id]['id'] ?? 0;
                $prepare_data['multi_anchor'] = '[]';
            }

            $order_task = (new OdsStarCreateOrderTaskModel())->getTask($order->account_id, $order->campaign_id);

            if (!empty($order_task)) {
                $task_model->update($order->account_id, $order->campaign_id, $prepare_data);
                // 记录订单操作日志
                $this->addOrderOperateLog(
                    ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ANCHOR,
                    ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_EDIT,
                    $order->order_id,
                    array_merge((array)$order_task, $prepare_data), (array)$order_task);
            } else {
                // 填充订单数据
                $prepare_data['platform'] = $order->platform;
                $prepare_data['demand_name'] = $order->demand_name;
                $prepare_data['create_time'] = $order->create_time;
                $prepare_data['android_site_id'] = $order->android_site_id;
                $prepare_data['ios_site_id'] = $order->ios_site_id;
                $prepare_data['author_id'] = $order->author_id;
                $prepare_data['author_name'] = $order->author_name;
                $prepare_data['creator'] = Container::getSession()->name;
                $task_model->insertIgnore($prepare_data);
                // 记录订单操作日志
                $this->addOrderOperateLog(
                    ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ANCHOR,
                    ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_ADD,
                    $order->order_id,
                    $prepare_data);
            }
            $result_data[] = $prepare_data;
        }

        (new ToutiaoTaskModel())->refreshLiveOrderCostCalc(array_keys($order_map));

        return $result_data;
    }

    /**
     * @param $order_ids
     */
    public function deleteADLiveOrder($order_ids)
    {
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $auth_order_list = (new OdsStarDemandOrderListLogModel())->getAuthOrderIdList($order_ids, $user_list, $leader_permission);

        $diff_order = collect($order_ids)->diff($auth_order_list->pluck('live_order_id'));

        if ($diff_order->isNotEmpty()) {
            throw new AppException('您无法操作以下订单，请删除后重试：' . json_encode($diff_order));
        }

        // 内部订单order_id=campaign_id
        // 之后有其他订单的情况需将order_id转为campaign_id才能操作ods_star_create_order_task表
        if ($auth_order_list->whereNotIn('dsp_order_type', [2])->isNotEmpty()) {
            throw new AppException('只能删除内部订单');
        }

        // 校验订单结算情况
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId($order_ids);
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法删除：' . json_encode($receive_order_data->pluck('order_id')));
        }

        (new OdsStarCreateOrderTaskModel())->remove($order_ids);

        foreach($order_ids as $order_id) {
            // 记录订单操作日志
            $this->addOrderOperateLog(
                ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ORDER,
                ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_REMOVE,
                $order_id, []);
        }

        (new ToutiaoTaskModel())->refreshLiveOrderCostCalc($order_ids);
    }

    /**
     * @param $order_ids
     * @param $data
     * @return void
     */
    public function editADLiveOrder($order_data)
    {
        $order_ids = array_column($order_data,'order_id');
        $leader_permission = (new PermissionLogic())->getLoginUserLeaderPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $auth_order_list = (new OdsStarDemandOrderListLogModel())->getAuthOrderIdList($order_ids, $user_list, $leader_permission);

        $diff_order = collect($order_ids)->diff($auth_order_list->pluck('live_order_id'));

        if ($diff_order->isNotEmpty()) {
            throw new AppException('您无法操作以下订单：' . json_encode($diff_order));
        }

        // 校验订单结算情况
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId($order_ids);
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法修改：' . json_encode($receive_order_data->pluck('order_id')));
        }

        $campaign_id_map = $auth_order_list->pluck('campaign_id','live_order_id')->toArray();
        $order_task_list = (new OdsStarCreateOrderTaskModel())->getDataByCampaignIds(array_values($campaign_id_map))->pluck(null, 'campaign_id')->toArray();

        $update_data = []; // 更新数据集
        $log_data = []; // 更新日志

        foreach ($order_data as $order_data_datum) {
            $edit_data = [];
            if (isset($order_data_datum['live_mode'])) {
                $edit_data['live_mode'] = $order_data_datum['live_mode'];
            }
            if (isset($order_data_datum['android_site_id'])) {
                $edit_data['android_site_id'] = $order_data_datum['android_site_id'];
            }
            if (!empty($edit_data)) {
                $wait_edit_data = $order_task_list[$campaign_id_map[$order_data_datum['order_id']]];
                $edit_data = array_merge((array)$wait_edit_data, $edit_data);
                $update_data[] = $edit_data;
                $log_data[] = [
                    'order_id' => $order_data_datum['order_id'],
                    'new_data' => json_encode($edit_data),
                    'old_data' => json_encode((array)$wait_edit_data),
                ];
            }
        }

        if(!empty($update_data)) {
            $android_site_id_list = array_values(array_filter(array_column($update_data,'android_site_id')));
            $site_id_list = (new SiteModel())->getAllBySite($android_site_id_list)->pluck('site_id');
            $diff_site_id = collect($android_site_id_list)->diff($site_id_list);
            if ($diff_site_id->isNotEmpty()) {
                throw new AppException('广告位不存在： ' . json_encode($diff_site_id));
            }
            (new OdsStarCreateOrderTaskModel())->replace($update_data);
        }

        if (!empty($log_data)) {
            (new ToutiaoStarOrderOperateLogModel())->add(array_map(function ($datum) {
                return array_merge($datum,[
                    'service_type' => ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ORDER,
                    'operate_type' => ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_EDIT,
                    'creator' => Container::getSession()->name,
                ]);
            }, $log_data));
        }

        (new ToutiaoTaskModel())->refreshLiveOrderCostCalc($order_ids);
    }

    /**
     * 记录订单操作日志
     * @param $service_type
     * @param $operate_type
     * @param $order_id
     * @param $new_data
     * @param array $old_data
     * @return void
     */
    public function addOrderOperateLog($service_type, $operate_type, $order_id, $new_data, $old_data = [])
    {
        (new ToutiaoStarOrderOperateLogModel)->add([
            'service_type' => $service_type,
            'order_id' => $order_id,
            'operate_type' => $operate_type,
            'new_data' => json_encode($new_data),
            'old_data' => json_encode($old_data),
            'creator' => Container::getSession()->name,
        ]);
    }

    /**
     * @param $param
     * @param $page
     * @param $rows
     * @return array
     */
    public function getOrderOperateLog($param, $page, $rows): array
    {
        $data = (new ToutiaoStarOrderOperateLogModel())->getList($param, $page, $rows);

        $need_show_field = [
            ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ANCHOR => [
                'true_name', 'price_type', 'price', 'live_hour', 'anchor_name', 'name_company', 'anchor_log_id', 'idcard', 'anchor_price_id'
            ],
            ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ORDER => [
                'live_mode','android_site_id'
            ],
        ];

        foreach ($data['list'] as $datum) {
            $datum->new_data = json_decode($datum->new_data, true);
            $datum->old_data = json_decode($datum->old_data, true);
            $operate_type_word = ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_MAP[$datum->operate_type];
            $change_object = $datum->order_id;
            $service_type_word = ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_MAP[$datum->service_type];
            $datum->operate_object = $service_type_word . "[$operate_type_word]" . "[$change_object]";
            $keys = array_keys($datum->new_data);
            $operate_detail = '';
            $datum->operate_detail = $operate_detail;

            switch (true) {
                // 删除订单
                case $datum->service_type == ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ORDER &&
                    $datum->operate_type == ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_REMOVE :
                    break;
                // 修改订单
                case $datum->service_type == ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ORDER &&
                    $datum->operate_type == ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_EDIT :
                    foreach ($keys as $key) {
                        if (in_array($key, $need_show_field[$datum->service_type])) {
                            if ($datum->new_data[$key] != $datum->old_data[$key]) {
                                switch ($key) {
                                    default:
                                        $old_value = $datum->old_data[$key];
                                        $new_value = $datum->new_data[$key];
                                        break;
                                }
                                $old_value = $old_value ?: '空';
                                $key_paraphrase = ToutiaoStarOrderOperateLogModel::FIELD_PARAPHRASE[$datum->service_type][$key] ?? $key;
                                $operate_detail .= "[将**{$key_paraphrase}**由{$old_value}修改为{$new_value}] ";
                            }
                        }
                    }
                    break;
                // 新增主播关联
                case $datum->service_type == ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ANCHOR &&
                    $datum->operate_type == ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_ADD :
                    $multi_anchor = json_decode($datum->new_data['multi_anchor'],true);
                    if (!empty($multi_anchor)) {
                        $operate_details = [];
                        foreach ($multi_anchor as $anchor) {
                            $price_word = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$anchor['price_type']];
                            $operate_details[] = "[主播：{$anchor['anchor_name']}  单价：{$anchor['price']}  价格类型：{$price_word}   最低应播时长：{$anchor['live_hour']}   直播时间：{$anchor['live_start_time']}-{$anchor['live_end_time']}]";
                        }
                        $operate_detail = '[设置多主播] ' . implode('', $operate_details);
                    } else {
                        foreach ($keys as $key) {
                            if(in_array($key, $need_show_field[$datum->service_type])) {
                                switch ($key) {
                                    case 'price_type':
                                        $new_value = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$datum->new_data[$key]] ?? $datum->new_data[$key];
                                        break;
                                    default:
                                        $new_value = $datum->new_data[$key];
                                        break;
                                }
                            }
                        }
                    }
                    $key_paraphrase = ToutiaoStarOrderOperateLogModel::FIELD_PARAPHRASE[$datum->service_type][$key] ?? $key;
                    $operate_detail .= "[将**{$key_paraphrase}**设置为{$new_value}] ";
                    break;
                // 修改主播关联
                case $datum->service_type == ToutiaoStarOrderOperateLogModel::SERVICE_TYPE_ANCHOR &&
                    $datum->operate_type == ToutiaoStarOrderOperateLogModel::OPERATE_TYPE_EDIT :
                    $multi_anchor = json_decode($datum->new_data['multi_anchor'], true);
                    if (!empty($multi_anchor)) {
                        $operate_details = [];
                        foreach ($multi_anchor as $anchor) {
                            $price_word = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$anchor['price_type']];
                            $operate_details[] = "[主播：{$anchor['anchor_name']}  单价：{$anchor['price']}  价格类型：{$price_word}   最低应播时长：{$anchor['live_hour']}   直播时间：{$anchor['live_start_time']}-{$anchor['live_end_time']}]";
                        }
                        $operate_detail = '[设置多主播] ' . implode('', $operate_details);
                    } else {
                        foreach ($keys as $key) {
                            if (in_array($key, $need_show_field[$datum->service_type])) {
                                if ($datum->new_data[$key] != $datum->old_data[$key]) {
                                    switch ($key) {
                                        case 'price_type':
                                            $old_value = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$datum->old_data[$key]] ?? $datum->old_data[$key];
                                            $new_value = ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$datum->new_data[$key]] ?? $datum->new_data[$key];
                                            break;
                                        default:
                                            $old_value = $datum->old_data[$key];
                                            $new_value = $datum->new_data[$key];
                                            break;
                                    }
                                    $old_value = $old_value ?: '空';
                                    $key_paraphrase = ToutiaoStarOrderOperateLogModel::FIELD_PARAPHRASE[$datum->service_type][$key] ?? $key;
                                    $operate_detail .= "[将**{$key_paraphrase}**由{$old_value}修改为{$new_value}] ";
                                }
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            $datum->operate_detail = $operate_detail ?: '字段无变动';
        }

        return $data;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAuthorPriceList(Input $input)
    {
        $model = new OdsStarAuthorPriceLogModel();
        return $model->getList($input['keyword'] ?? "", $input['page'] ?? 1, $input['rows'] ?? 50);
    }

    /**
     * @param $data_list
     * @param $tmp_name
     * @return void
     * @throws Exception
     */
    public function importAuthorPrice($data_list, $tmp_name)
    {
        $username = Container::getSession()->name;

        $error_message = '';

        $fields = ["author_id", "author_price", "price_start_date"];

        $list = collect();
        foreach ($data_list as $index => $data) {
            // 跳过表头
            if ($index == 0) {
                continue;
            }

            $row = $index + 1;
            $is_empty = false;
            $item = [];
            foreach ($fields as $key => $field) {
                if (empty($data[$key])) {
                    $is_empty = true;
                    break;
                }
                $item[$field] = trim($data[$key]);
                if ($field === "price_start_date") {
                    if (is_numeric($item[$field])) {
                        $item[$field] = gmdate('Y-m-d 00:00:00', intval(($item[$field] - 25569) * 3600 * 24));
                    } elseif (strtotime($item[4])) {
                        $item[$field] = date('Y-m-d 00:00:00', strtotime($item[$field]));
                    }
                    $item[$field] = date("Y-m-d 00:00:00", strtotime($item[$field]));
                }
            }

            if ($is_empty) {
                $error_message .= "第{$row}行数据错误，有空数据\n";
                continue;
            }

            $item["creator"] = $username;
            $list->push($item);
        }

        if ($error_message != "") {
            throw new AppException($error_message);
        }

        $min_price_start_date = $list->pluck("price_start_date")->min();
        $this->checkImportDate($min_price_start_date);

        $author_ids = $list->pluck("author_id")->toArray();
        $model = new OdsStarAuthorPriceLogModel();
        $ori_list = $model->getListByAuthorIds($author_ids);
        $ori_list = $ori_list->groupBy("author_id")->map(function (Collection $item) {
            return $item->sortBy("price_start_date");
        });

        // 达人聚合 开始时间排序
        $list = $list->groupBy("author_id")->map(function (Collection $item) {
            return $item->sortBy("price_start_date");
        });

        // 把达人单价的情况查出来
        // 如果是业务 只能修改非冻结月份的单价
        // 先把导入日期之后的全删除 然后按排序重新排
        $result = collect();
        $delete_list = [];
        foreach ($list as $author_id => $price_list) {
            // 先把原有数据与新导数据重合的月份删除
            $tmp_price_list = $price_list;
            if ($ori_list->has($author_id)) {
                foreach ($ori_list[$author_id] as $ori_key => $ori_item) {
                    foreach ($price_list as $item) {
                        // 只要在导入时间之后的单价全部删除
                        $import_first_date = strtotime(date("Y-m-01", strtotime($item["price_start_date"])));
                        if (strtotime($ori_item->price_start_date) >= $import_first_date) {
                            $delete_list[] = (array)$ori_item;
                            unset($ori_list[$author_id][$ori_key]);
                        }
                        break;
                    }
                }
                $tmp_price_list = $price_list->merge($ori_list[$author_id])->values()->filter();
            }
            $result[$author_id] = $tmp_price_list;
        }

        $result = collect($result);
        $result = $result->map(function (Collection $item) {
            return $item->sortBy("price_start_date")->values();
        });

        $replace_data = [];
        foreach ($result as $price_list) {
            foreach ($price_list as $key => $price) {
                $price = (array)$price;
                $price["price_end_date"] = isset($price_list[$key + 1]) ? date("Y-m-d 23:59:59", strtotime($price_list[$key + 1]["price_start_date"] . ' -1 day')) : self::AUTHOR_PRICE_END_TIME;
                unset($price["insert_time"]);
                $replace_data[] = $price;
            }
        }

        // 备份导入文件
        $access_path = EnvConfig::UPLOAD_PATH . '/author_price';
        $upload_path = SRV_DIR . "/{$access_path}";
        // 判断上传目录是否存在，不存在则创建
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0755, true);
        }

        // 保存的文件名
        $time = date("YmdHis");
        $full_name = "{$upload_path}/{$time}_{$username}.xlsx";
        // 将文件上传到指定目录下
        move_uploaded_file($tmp_name, $full_name);

        // 删除数据
        if ($delete_list) {
            $model->deleteMultiple($delete_list);
        }

        $model->replace($replace_data);
    }

    /**
     * 检查导入日期是否已被冻结
     * @param $start_date
     * @return void
     * @throws Exception
     */
    public function checkImportDate($start_date)
    {
        $white_user_list = [
            '黄铭灿', '中旭未来', '谢希怡', '余曼湘'
        ];

        if (in_array(Container::getSession()->name, $white_user_list)) {
            return;
        }

        // 判断是否已经被冻结
        $is_freeze = RedisCache::getInstance()->get(self::FREEZE_KEY);

        // 已冻结 只能录入这个月1号以后的
        if ($is_freeze) {
            $date = date("Y-m-01");
        } else {
            // 未冻结 只能录入上个月1号以后的
            $date = Helpers::getSameDayOfPrevMonth(date("Y-m-01"));
        }

        if (strtotime($start_date) < strtotime($date)) {
            throw new AppException($date . '前的达人单价与返款已被锁定，无法改动，如有需要请联系财务同学处理。');
        }
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAuthorPaybackList(Input $input)
    {
        $model = new OdsStarAuthorPaybackLogModel();
        return $model->getList($input['keyword'] ?? "", $input['page'] ?? 1, $input['rows'] ?? 50);
    }

    /**
     * @param Input $input
     * @return int
     */
    public function deleteAuthorPayback(Input $input)
    {
        $model = new OdsStarAuthorPaybackLogModel();
        $info = $model->getData($input["trade_number"], $input["flow_number"]);
        if (empty($info)) {
            throw new AppException("找不到该条返款，请重新查询");
        }

        $this->checkImportDate($info->pay_date);

        return $model->delete($input["trade_number"], $input["flow_number"]);
    }

    /**
     * @param $data_list
     * @param $tmp_name
     * @return void
     * @throws Exception
     */
    public function importAuthorPayback($data_list, $tmp_name)
    {
        $username = Container::getSession()->name;

        $error_message = '';

        $fields = ["pay_date", "trade_number", "flow_number", "pay_back", "author_id"];

        $list = collect();
        foreach ($data_list as $index => $data) {
            // 跳过表头
            if ($index == 0) {
                continue;
            }

            $row = $index + 1;
            $is_empty = false;
            $item = [];
            foreach ($fields as $key => $field) {
                if (empty($data[$key])) {
                    $is_empty = true;
                    break;
                }
                $item[$field] = trim($data[$key]);
                if ($field === "pay_date") {
                    if (is_numeric($item[$field])) {
                        $item[$field] = gmdate('Y-m-d H:i:s', intval(($item[$field] - 25569) * 3600 * 24));
                    } elseif (strtotime($item[4])) {
                        $item[$field] = date('Y-m-d H:i:s', strtotime($item[$field]));
                    }
                    $item[$field] = date("Y-m-d H:i:s", strtotime($item[$field]));
                }
            }

            if ($is_empty) {
                $error_message .= "第{$row}行数据错误，有空数据\n";
                continue;
            }

            $item["creator"] = $username;
            $list->push($item);
        }

        if ($error_message != "") {
            throw new AppException($error_message);
        }

        $min_pay_date = $list->pluck("pay_date")->min();
        $this->checkImportDate($min_pay_date);

        $replace_data = $list->values()->toArray();

        // 备份导入文件
        $access_path = EnvConfig::UPLOAD_PATH . '/author_payback';
        $upload_path = SRV_DIR . "/{$access_path}";
        // 判断上传目录是否存在，不存在则创建
        if (!file_exists($upload_path)) {
            mkdir($upload_path, 0755, true);
        }

        // 保存的文件名
        $time = date("YmdHis");
        $full_name = "{$upload_path}/{$time}_{$username}.xlsx";
        // 将文件上传到指定目录下
        move_uploaded_file($tmp_name, $full_name);

        $model = new OdsStarAuthorPaybackLogModel();
        $model->replace($replace_data);
    }


    /**
     * 生成跳转支付宝收款的二维码
     * @param $input
     * @return string
     */
    public function genTradeQRCode($input)
    {
        if (EnvConfig::ENV === Environment::PROD) {
            $schema_url = "https://ext.zxzt123.com" . "/upload/star_demand/index.html?trade_no=";
        } else {
            $schema_url = "http://dms-mc.zeda.cn" . "/upload/star_demand/index.html?trade_no=";
        }

        if ($input['trade_no']) {
            return $schema_url . $input['trade_no'];
        } else {
            throw new AppException("请选择结算订单");
        }

//        if (!$input['order_ids']) {
//            throw new AppException("请选择订单");
//        }
//
//        // 判断表里是否已有该订单的二维码
//        // 有就报错
//        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
//        if (
//            $receive_model->existByOrderIdsAndStatus($input['order_ids'], [
//                OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN,
//                OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_PAY,
//                OdsStarDemandQrcodeReceiveLogModel::STATUS_SUCCESS
//            ])
//        ) {
//            throw new AppException("已有待付款的二维码，请勿重复生成");
//        }
//
//        // 生成一个系统订单号，作为二维码的识别
//        $trade_no = date("YmdHis") . time() .rand(100000, 999999);
//
//        // 查出金额
//        $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
//            "dimension" => [
//                "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
//                "live_demand_name", "live_author_id", "live_author_name"
//            ],
//            "filter" => [
//                "live_order_id" => [
//                    "column" => "live_order_id",
//                    "value" => $input['order_ids']
//                ]
//            ],
//            "target" => [
//                "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
//                "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type"
//            ]
//        ]));
//
//        // 不同机构报错
//        $company_name_list = $list['list']->pluck('anchor_company')->unique()->toArray();
//        if (count($company_name_list) > 1) {
//            throw new AppException("不同机构订单不允许生成收款码，已选机构：" . implode(",", $company_name_list));
//        }
//
//        // 无机构 不同达人报错
//        if (!$company_name_list) {
//            $aweme_account_list = $list['list']->pluck('star_aweme_account')->filter()->unique()->toArray();
//            if (count($aweme_account_list) > 1) {
//                throw new AppException("不允许多个无机构达人生成收款码，已选达人：" . implode(",", $aweme_account_list));
//            }
//        }
//
//        // 无机构报错
//        if (empty($company_name_list[0])) {
//            throw new AppException("请前往星图主播管理，补充对应主播信息");
//        }
//
//        $report_order_ids = $list['list']->pluck('live_order_id')->unique()->toArray();
//        $order_diff = array_diff($input['order_ids'], $report_order_ids);
//        $report_order_diff = array_diff($report_order_ids, $input['order_ids']);
//        if ($order_diff || $report_order_diff) {
//            throw new AppException("查询数据有误，请重新尝试");
//        }
//
//        // 收款支付宝主体不一致 报错
////        $pay_company_list = (new OdsStarDemandOrderListLogModel())->getPayCompanyListByOrderIds($input['order_ids']);
////        $pay_company_list = $pay_company_list->pluck('company')->unique()->toArray();
//
//        $pay_company_list = $list['list']->pluck('payway_company')->filter()->unique()->toArray();
//        $all_pay_company_list = [];
//        foreach ($pay_company_list as $pay_company) {
//            $all_pay_company_list[] = explode(",", $pay_company)[0];
//        }
//
//        $all_pay_company_list = array_unique($all_pay_company_list);
//
//        if (!$all_pay_company_list) {
//            throw new AppException("收款主体缺失，请联系管理员");
//        }
//
//        if (count($all_pay_company_list) > 1) {
//            throw new AppException("不允许多个收款主体同时生成收款码，请重新选择订单");
//        }
//        $pay_company = $all_pay_company_list[0];
//
//        // 找出收款支付宝应用APP_ID
//        $alipay_account_info = (new MediaAccountModel())->getAlipayAppIdByCompanyShortName($pay_company);
//        if (empty($alipay_account_info)) {
//            throw new AppException("找不到对应主体的开发者，请联系管理员");
//        }
//
//        // 找出订单对应的主播手机号或机构负责人手机号
//        $anchor_log_ids = $list['list']->pluck('anchor_log_id')->unique()->toArray();
//        $anchor_info = (new OdsStarAnchorLogModel())->getDataByIdList($anchor_log_ids);
//
//        if ($anchor_info->isEmpty()) {
//            throw new AppException("找不到对应的主播信息");
//        }
//
//        $anchor_mobile[] = trim($anchor_info->first()->telephone);
//        if ($company_name_list[0] !== '个人') {
//            $company_info = (new OdsStarCompanyLogModel())->info($company_name_list[0]);
//            if (empty($company_info)) {
//                throw new AppException("找不到对应的机构信息");
//            }
//
//            $anchor_mobile[] = trim($company_info->telephone);
//        }
//
//        $anchor_mobile = array_unique($anchor_mobile);
//
//        $user_name = Container::getSession()->name;
//        $order_list = [];
//        $cache_list = [];
//        // 按开播日期排序
//        $list["list"] = $list['list']->sortBy('first_live_time');
//        foreach ($list["list"] as $item) {
//            if ($item->should_return_money <= 0) {
//                continue;
//            }
//
//            $order_list[] = [
//                "trade_no" => $trade_no,
//                "platform" => $item->platform,
//                "account_id" => $item->live_account_id,
//                "demand_id" => $item->live_demand_id,
//                "demand_name" => $item->live_demand_name,
//                "order_id" => $item->live_order_id,
//                "author_id" => $item->live_author_id,
//                "author_name" => $item->live_author_name,
//                "aweme_account" => $item->star_aweme_account,
//                "company_name" => $item->anchor_company ?? "",
//                "amount" => $item->should_return_money, // 主播星图提现金额 - 主播实际工资 = 主播需返款金额
//                "business_account_id" => $alipay_account_info->account_id,
//                "business_company_name" => $pay_company,
//                "first_live_time" => $item->first_live_time,
//                "anchor_order_fee" => $item->anchor_order_fee,
//                "anchor_cost" => $item->anchor_cost,
//                "creator" => $user_name,
//                "status" => OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN
//            ];
//
//            $cache_list[] = [
//                "first_live_time" => $item->first_live_time,
//                "author_name" => $item->live_author_name,
//                "live_price" => $item->anchor_order_fee, // 主播星图提现金额
//                "anchor_cost" => $item->anchor_cost, // 主播实际工资
//                "should_return_money" => $item->should_return_money, // 主播需返款金额
//                "login_mobile" => "",
//                "status" => OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN,
//                "business_account_id" => $alipay_account_info->account_id,
//                "anchor_mobile" => $anchor_mobile,
//                "scan_time" => ""
//            ];
//        }
//
//        if (empty($order_list)) {
//            throw new AppException('所选订单应返款数额都为0，请重新选择');
//        }
//
//        // 插入表中 订单号+金额+系统订单号
//        $receive_model->replace($order_list);
//
//        // 写入redis
//        (new StarDemandReceiveModel())->set($trade_no, $cache_list);
//
//        return $schema_url . $trade_no;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getSettlementList(Input $input)
    {
        $model = new OdsStarDemandQrcodeReceiveLogModel();
        $data = $model->getList($input->getData());
        $sum = [];
        $sum_fields = [
            'amount', 'pay_amount', 'tw_pay_yd', 'cloud_account_fee', 'yd_out_tax', 'yd_company_fee'
        ];

        $list = [];
        foreach ($data['all'] as $datum) {
            $item = $datum;
            $item->status_cn = OdsStarDemandQrcodeReceiveLogModel::STATUS_MAP[$item->status];
            $item->trade_type_cn = OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_MAP[$item->trade_type];

//            if ($item->amount > $item->pay_amount) {
//                $item->amount = $item->amount - $item->pay_amount;
//                $item->pay_amount = 0;
//            } else {
//                $item->pay_amount = $item->pay_amount - $item->amount;
//                $item->amount = 0;
//                $item->status_cn = $item->status == OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN ? "待对账" : $item->status_cn;
//            }

            if ($item->trade_type == OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_RETURN) {
                $item->amount = round($item->amount - $item->pay_amount, 2);
                $item->pay_amount = 0;
            } else {
                $item->pay_amount = $item->after_tax_pay_amount != 0 ? abs($item->after_tax_pay_amount) : $item->pay_amount - $item->amount;
                $item->amount = 0;
                $item->status_cn = $item->status == OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN ? "待对账" : $item->status_cn;
            }

            $payee_list = json_decode($item->payee_list, true);
            $tmp_payee_list = [];
            if ($payee_list) {
                foreach ($payee_list as $payee) {
                    $tmp_payee_list[] = [
                        'payee' => $payee['company'],
                        'address' => $payee['address'],
                        'bank' => $payee['bank'],
                        'bank_card_no' => $payee['bank_card_no'],
                    ];
                }
            }
            $item->payee_list = $tmp_payee_list;

            $price_type_cn = $live_media_type_cn = $dsp_order_type_cn = [];

            $price_type = explode(",", $item->price_type);
            $live_media_type = explode(",", $item->live_media_type);
            $dsp_order_type = explode(",", $item->dsp_order_type);
            foreach ($price_type as $tmp_price_type) {
                $price_type_cn[] = $this->formatPriceType($tmp_price_type);
            }

            foreach ($live_media_type as $tmp_live_media_type) {
                $live_media_type_cn[] = ADLiveMediaTypeMap::MEDIA_TYPE_MAP[$tmp_live_media_type] ?? '';
            }

            foreach ($dsp_order_type as $tmp_dsp_order_type) {
                $dsp_order_type_cn[] = ADFieldsENToCNMap::TOUTIAO_STAR_DSP_ORDER_TYPE[$tmp_dsp_order_type] ?? '';
            }

            $item->price_type_cn = implode("、", array_unique($price_type_cn));
            $item->live_media_type_cn = implode("、", array_unique($live_media_type_cn));
            $item->dsp_order_type_cn = implode("、", array_unique($dsp_order_type_cn));

            $item->approve_status = $item->approve_status === '' ? -1 : $item->approve_status;
            $item->approve_status_cn = ToutiaoStarLogic::APPROVE_STATUS_MAP[$item->approve_status] ?? '';
            $item->approve_url = $item->work_project_approve_inst_id ?
                (new ToutiaoStarLogic())->getBpmApproveUrl(30, 176, $item->work_project_approve_inst_id) : '';

            $item->author_types = explode(",", $item->author_types);

            $list[] = $item;

            // 合计
            foreach ($sum_fields as $sum_field) {
                if (!$sum[$sum_field]) {
                    $sum[$sum_field] = 0;
                }

                $sum[$sum_field] += $datum->$sum_field;
            }
        }

        foreach ($sum_fields as $sum_field) {
            $sum[$sum_field] = round($sum[$sum_field], 4);
        }

        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 50;
        $offset = max(0, ($page - 1) * $rows);

        return [
            'list' => array_slice($list, $offset, $rows),
            'sum' => $sum,
            'sql' => $data['sql'],
            'total' => $data['total']
        ];
    }

    /**
     * @param $trade_no
     * @return Collection
     */
    public function getSettlementDetails($trade_no)
    {
        $model = new OdsStarDemandQrcodeReceiveLogModel();
        return $model->getDetailsByTradeNo($trade_no);
    }

    /**
     * @param $trade_no
     */
    public function cancelTradeQRCode($trade_no)
    {
        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        $trade_order_list = $receive_model->getDetailsByTradeNo($trade_no);
        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_CANCEL])->isNotEmpty()) {
            throw new AppException("该收款码已取消，请勿重复操作");
        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_SUCCESS])->isNotEmpty()) {
            throw new AppException("该收款码已收款，取消失败");
        }

        // 扫描后5分钟内不能取消
//        $scan_order = $trade_order_list->where('status', $receive_model::STATUS_WAIT_PAY)->first();
//        if (time() < (strtotime($scan_order->scan_time) + 300)) {
//            throw new AppException("该收款码已被扫描，若要取消请5分钟后重试");
//        }

        $receive_model->updateByTradeNo($trade_no, ['status' => $receive_model::STATUS_CANCEL]);
        (new StarDemandReceiveModel())->delete($trade_no);
    }

    /**
     * @param Input $input
     * @return bool
     */
    public function checkSettlementOrder(Input $input)
    {
        $settle_anchor_log_ids = $settle_order_ids = $anchor_order_unique_key_list = [];
        foreach ($input['order_list'] as $item) {
            if (empty($item['anchor_log_id'] ?? '')) {
                throw new AppException("自定义指标需要选中主播ID后，再进行结算");
            }
            $settle_anchor_log_ids[] = $item['anchor_log_id'];
            $settle_order_ids[] = $item['order_id'];
            $anchor_order_unique_key_list[] = "{$item['order_id']}-{$item['anchor_log_id']}";
        }

        if (!$settle_anchor_log_ids || !$settle_order_ids) {
            throw new AppException("请选择订单");
        }

        $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
            "dimension" => [
                "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                "live_demand_name", "live_author_id", "live_author_name", "live_anchor_name"
            ],
            "filter" => [
                "live_order_id" => [
                    "column" => "live_order_id",
                    "value" => $settle_order_ids
                ],
                "anchor_log_id" => [
                    "column" => "anchor_log_id",
                    "value" => $settle_anchor_log_ids
                ]
            ],
            "target" => [
                "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
                "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type", "should_pay_money",
                "author_mcn", "tw_pay_yd", "cloud_account_fee", "yd_out_tax", "yd_company_fee", "universal_order_status"
            ]
        ]));

        // 筛选出本次选的订单+主播ID
        $list['list'] = $list['list']->filter(function ($value) use ($anchor_order_unique_key_list) {
            return in_array("{$value->live_order_id}-{$value->anchor_log_id}", $anchor_order_unique_key_list);
        });

        // 不同机构报错
        $status_list = $list['list']->pluck('universal_order_status')->unique()->toArray();
        return in_array('CANCELED', $status_list);
    }

    /**
     * 结算计算税后的金额
     * @param $cloud_account_tax
     * @param $pay_way
     * @param $cloud_account_tax_author_cover_rate
     * @param $com_is_extra
     * @param $company_tax
     * @param $amount
     * @return float|int
     */
    public function settleCalcAfterTaxAmount($cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $amount)
    {
        $tmp_amount = abs($amount);
        // 对公 + 是承担开票税
        if ($pay_way == 1 && $com_is_extra) {
            $tmp_amount = $tmp_amount - ($tmp_amount / 1.06 * 0.06 * 1.12 - ($tmp_amount / (1 + $company_tax) * ($company_tax * 1.12)));
        } elseif ($pay_way == 2) {
            $tmp_amount = $tmp_amount * ($cloud_account_tax - $cloud_account_tax_author_cover_rate);
        }

        if ($amount < 0) {
            $amount = - $tmp_amount;
        } else {
            $amount = $tmp_amount;
        }

        return round($amount, 4);
    }

    /**
     * 结算
     * @param Input $input
     * @return string
     */
    public function settlement(Input $input)
    {
        if (!isset($input['order_list'])) {
            throw new AppException("功能已更新，请刷新页面重试");
        }

        $settle_anchor_log_ids = $settle_order_ids = $anchor_order_list = $anchor_order_unique_key_list = [];
        foreach ($input['order_list'] as $item) {
            if (empty($item['anchor_log_id'] ?? '')) {
                throw new AppException("自定义指标需要选中主播ID后，再进行结算");
            }
            $settle_anchor_log_ids[] = $item['anchor_log_id'];
            $settle_order_ids[] = $item['order_id'];
            $anchor_order_list[] = [
                'anchor_log_id' => $item['anchor_log_id'],
                'order_id' => $item['order_id']
            ];
            $anchor_order_unique_key_list[] = "{$item['order_id']}-{$item['anchor_log_id']}";
        }

        if (!$settle_anchor_log_ids || !$settle_order_ids) {
            throw new AppException("请选择订单");
        }

        $settle_anchor_log_ids = array_unique($settle_anchor_log_ids);
        $settle_order_ids = array_unique($settle_order_ids);

        // 检查是否有审批中的费用 分批查询 不然sql太长
        $special_fund_model = new OdsStarSpecialFundLog;
        $special_fund_approve_list = collect();
        foreach (array_chunk($anchor_order_list, 500) as $chunk) {
            $tmp_special_fund_approve_list = $special_fund_model->getApproveList($chunk);
            if ($tmp_special_fund_approve_list->isNotEmpty()) {
                $special_fund_approve_list = $special_fund_approve_list->merge($tmp_special_fund_approve_list);
            }
        }

        if ($special_fund_approve_list->isNotEmpty()) {
            $approve_order_anchor_list = [];
            foreach ($special_fund_approve_list as $item) {
                $approve_order_anchor_list[] = [
                    'order_id' => $item->order_id,
                    'anchor_log_id' => $item->anchor_log_id
                ];
            }
            throw new AppException("以下订单有正在审批中的主播费用：" . json_encode($approve_order_anchor_list));
        }

        // 判断表里是否已有该订单的二维码
        // 有就报错 分500个查询一次
        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        foreach (array_chunk($anchor_order_list, 500) as $chunk) {
            if (
                $receive_model->existByOrderIdsAndStatus($chunk, [
                    OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN,
                    OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_PAY,
                    OdsStarDemandQrcodeReceiveLogModel::STATUS_SUCCESS,
                    OdsStarDemandQrcodeReceiveLogModel::STATUS_CONFIRM
                ])
            ) {
                throw new AppException("已有结算的记录，请勿重复结算");
            }
        }

        // 生成一个系统订单号，作为二维码的识别
        $trade_no = date("YmdHis") . time() .rand(100000, 999999);

        // 查出金额
        $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
            "dimension" => [
                "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                "live_demand_name", "live_author_id", "live_author_name", "live_anchor_name",
                "live_media_type", "dsp_order_type"
            ],
            "filter" => [
                "live_order_id" => [
                    "column" => "live_order_id",
                    "value" => $settle_order_ids
                ],
                "anchor_log_id" => [
                    "column" => "anchor_log_id",
                    "value" => $settle_anchor_log_ids
                ]
            ],
            "target" => [
                "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
                "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type", "should_pay_money",
                "author_mcn", "tw_pay_yd", "cloud_account_fee", "yd_out_tax", "yd_company_fee",
                "cloud_account_tax_author_cover_rate", "com_is_extra", "is_extra", "anchor_order_extra",
                "author_type", "anchor_order_tax", "pay_way", "company_tax", "pre_should_pay_money", "universal_order_status",
                "price_type"
            ]
        ]));

        // 筛选出本次选的订单+主播ID
        $list['list'] = $list['list']->filter(function ($value) use ($anchor_order_unique_key_list) {
            return in_array("{$value->live_order_id}-{$value->anchor_log_id}", $anchor_order_unique_key_list);
        });

        // 订单状态需为 已完成或已取消
        $err_status_list = $list['list']->whereNotIn('universal_order_status', ['FINISHED','CANCELED'])->pluck('live_order_id')->unique()->toArray();
        if ($err_status_list) {
            throw new AppException("以下订单尚未完成，不可结算，订单：" . implode(",", $err_status_list));
        }

        // 不同媒体报错
        $media_type_list = $list['list']->pluck('media_type')->unique()->toArray();
        if (count($media_type_list) > 1) {
            throw new AppException("不同媒体订单不允许结算");
        }

        // 不同机构报错
        $company_name_list = $list['list']->pluck('anchor_company')->unique()->toArray();
        if (count($company_name_list) > 1) {
            throw new AppException("不同机构订单不允许结算，已选机构：" . implode(",", $company_name_list));
        }

        // 无机构 不同达人报错
        if (!$company_name_list) {
            $live_author_id_list = $list['list']->pluck('live_author_id')->unique()->toArray();
            $live_author_name_list = $list['list']->pluck('live_author_name')->unique()->toArray();
            if (count($live_author_id_list) > 1) {
                throw new AppException("不允许多个无机构达人结算，已选达人：" . implode(",", $live_author_name_list));
            }
        }

        if ($company_name_list[0] === "个人") {
            $anchor_log_id_list = $list['list']->pluck('anchor_log_id')->unique()->toArray();
            $live_anchor_name_list = $list['list']->pluck('live_anchor_name')->unique()->toArray();
            if (count($anchor_log_id_list) > 1) {
                throw new AppException("不允许多个无机构主播结算，已选主播：" . implode(",", $live_anchor_name_list));
            }
        }

        // 无机构报错
        if (empty($company_name_list[0])) {
            throw new AppException("请前往星图主播管理，补充对应主播信息");
        }

        // 不同MCN机构报错
        $mcn_name_list = $list['list']->pluck('author_mcn')->unique()->toArray();
        if (in_array('游点文化', $mcn_name_list) && count($mcn_name_list) > 1) {
            throw new AppException("游点机构不允许与其他机构合并结算，已选MCN机构：" . implode(",", $mcn_name_list));
        }

        // 请求的订单数对比查询的订单数是否一致
        $report_order_ids = $list['list']->pluck('live_order_id')->unique()->toArray();
        $order_diff = array_diff($settle_order_ids, $report_order_ids);
        $report_order_diff = array_diff($report_order_ids, $settle_order_ids);
        if ($order_diff || $report_order_diff) {
            throw new AppException("查询数据有误，请重新尝试");
        }

        // 收款支付宝主体不一致 报错
        $pay_company_list = $list['list']->pluck('payway_company')->unique()->toArray();
        $all_pay_company_list = [];
        foreach ($pay_company_list as $pay_company) {
            $all_pay_company_list[] = explode(",", $pay_company)[0];
        }

        $all_pay_company_list = array_unique($all_pay_company_list);

        if (!$all_pay_company_list) {
            throw new AppException("收款主体缺失，请联系管理员");
        }

        if (count($all_pay_company_list) > 1) {
            throw new AppException("不允许多个收款主体结算，请重新选择订单");
        }
        $pay_company = $all_pay_company_list[0];

        // 找出收款支付宝应用APP_ID
        $alipay_account_info = (new MediaAccountModel())->getAlipayAppIdByCompanyShortName($pay_company);
        if (empty($alipay_account_info)) {
            throw new AppException("找不到对应主体的开发者，请联系管理员");
        }

        // 找出订单对应的主播手机号或机构负责人手机号
        $anchor_log_ids = $list['list']->pluck('anchor_log_id')->unique()->toArray();
        $anchor_info = (new OdsStarAnchorLogModel())->getDataByIdList($anchor_log_ids);

        if ($anchor_info->isEmpty()) {
            throw new AppException("找不到对应的主播信息");
        }

        $anchor_mobile[] = trim($anchor_info->first()->telephone);
        if ($company_name_list[0] !== '个人') {
            $company_info = (new OdsStarCompanyLogModel())->getData($company_name_list[0]);
            if ($company_info->isEmpty()) {
                throw new AppException("找不到对应的机构信息");
            }

            foreach ($company_info as $company_item) {
                $anchor_mobile[] = trim($company_item->telephone);
            }
        }

        // 判断[主播收款方式],[承担云账户打款税点],[是否承担开票税],[对方开票税率]是否一致
        $pay_way_list = $list['list']->pluck('pay_way')->unique()->toArray();
        if (count($pay_way_list) > 1) {
            throw new AppException("不同主播收款方式订单不允许结算");
        }

        $cloud_account_tax_author_cover_rate_list = $list['list']->pluck('cloud_account_tax_author_cover_rate')->unique()->toArray();
        if (count($cloud_account_tax_author_cover_rate_list) > 1) {
            throw new AppException("不同承担云账户打款税点订单不允许结算");
        }

        $com_is_extra_list = $list['list']->pluck('com_is_extra')->unique()->toArray();
        if (count($com_is_extra_list) > 1) {
            throw new AppException("不同是否承担开票税订单不允许结算");
        }

        $company_tax_list = $list['list']->pluck('company_tax')->unique()->toArray();
        if (count($company_tax_list) > 1) {
            throw new AppException("不同对方开票税率订单不允许结算");
        }

        $first_live_time_list = $list['list']->pluck('first_live_time')->map(function ($item) {
            return date("Y-m-d", strtotime($item));
        })->unique()->toArray();

        // 不允许合并结算跨3月的订单
        $special_date = "2025-03-01";
        foreach ($first_live_time_list as $item) {
            if (in_array($special_date, $first_live_time_list) && strtotime($item) < strtotime($special_date)) {
                throw new AppException("不允许3月前与3月订单一起结算");
            }
        }

        // 价格类型不同 不允许结算
        $price_type_list = $list['list']->pluck('price_type')->unique()->toArray();
        if (in_array(5, $price_type_list) && count($price_type_list) > 1) {
            throw new AppException("只能允许同时为CPS或非CPS的订单结算");
        }

        // 贪玩蓝V和游点蓝V 不允许结算
        $author_type_list = $list['list']->pluck('author_type')->unique()->toArray();
        if (in_array(1, $author_type_list) && in_array(2, $author_type_list)) {
            throw new AppException("贪玩蓝V和游点蓝V不允许合并结算");
        }

        // 检查主播价格是否有在审批中的
        $anchor_price_conditions = $list['list']->map(function($item) {
            return [
                'anchor_log_id' => $item->anchor_log_id,
                'price_type' => $item->price_type,
                'first_live_time' => $item->first_live_time,
            ];
        })->toArray();
        $anchor_price_model = new ToutiaoStarAnchorPriceModel();
        $in_approve_anchor_price_list = $anchor_price_model->getInApproveListInCondition($anchor_price_conditions);
        if ($in_approve_anchor_price_list->isNotEmpty()) {
            $in_approve_anchor_price_ids = $in_approve_anchor_price_list->pluck('anchor_log_id')->toArray();
            throw new AppException("以下主播有正在审批中的主播价格：" . json_encode($in_approve_anchor_price_ids));
        }

        $anchor_mobile = array_unique($anchor_mobile);

        $user_name = Container::getSession()->name;
        $order_list = [];
        $cache_list = [];
        $settlement_amount = 0;
        // 按开播日期排序
        $list["list"] = $list['list']->sortBy('first_live_time');
        $cloud_account_tax = 0;

        // 先计算出抵扣后的金额 判断返款或补款
        foreach ($list["list"] as $item) {
            if (strtotime($item->first_live_time) < strtotime($special_date)) {
                $cloud_account_tax = 1.063;
            } else {
                $cloud_account_tax = 1.064;
            }

            // 是否承担星图提现手续费
//            $is_extra = $item->is_extra;
//            if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
//                $is_extra = 0;
//            }

//            if ($is_extra) {
//                $amount = $item->anchor_order_fee - $item->anchor_cost + $item->anchor_order_extra;
//            } else {
//                $amount = $item->anchor_order_fee - $item->anchor_cost;
//            }

//            // 贪玩蓝V 主播不能提现，所以没有提现金额，直接就是主播工资
//            if ($item->author_type == 2) {
//                $amount = - $item->anchor_cost;
//            }

            $settlement_amount += ($item->pre_should_pay_money - $item->should_return_money);
        }

        // $settlement_amount 正数或等于0是应补款 负数就是应返款

        // 补款特殊计算
        $pay_way = $pay_way_list[0];
        $cloud_account_tax_author_cover_rate = $cloud_account_tax_author_cover_rate_list[0];
        $com_is_extra = $com_is_extra_list[0];
        $company_tax = $company_tax_list[0];
        if ($settlement_amount >= 0) {
            $settlement_amount = $this->settleCalcAfterTaxAmount(
                $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $settlement_amount
            );
        }

        $order_ids = [];
        foreach ($list["list"] as $item) {
            $should_return_money = $item->should_return_money;
            $pre_should_pay_money = $item->pre_should_pay_money;

            if ($should_return_money != 0 && $pre_should_pay_money != 0) {
                throw new AppException("订单：{$item->live_order_id}同时有应返款与应补款，请联系管理员");
            }

            $is_extra = $item->is_extra;
            if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
                $is_extra = 0;
            }

            // 当游点的特殊情况，应返应补都应该为0
            $is_youdian_special = false;
            if (in_array($item->anchor_company, ['广州游点文化传媒有限公司', '游点自孵化']) || $item->author_mcn === '游点文化' || $item->author_type == 1) {
                $is_youdian_special = true;
            }

            $amount = $pre_should_pay_money - $should_return_money;

            $after_tax_deduction_amount = $amount;
            // 抵扣完是补款，需要计算每一笔的税后应补款或税后应返款
            if ($settlement_amount >= 0) {
                $after_tax_deduction_amount = $this->settleCalcAfterTaxAmount(
                    $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $after_tax_deduction_amount
                );
            }

            if ($is_youdian_special) {
                $after_tax_deduction_amount = 0;
                $settlement_amount = 0;
            }

            $order_list[] = [
                "trade_no" => $trade_no,
                "platform" => $item->platform,
                "account_id" => $item->live_account_id,
                "demand_id" => $item->live_demand_id,
                "demand_name" => $item->live_demand_name,
                "order_id" => $item->live_order_id,
                "author_id" => $item->live_author_id,
                "author_name" => $item->live_author_name,
                "aweme_account" => $item->star_aweme_account,
                "company_name" => $item->anchor_company ?? "",
                "amount" => $is_youdian_special ? 0 : $should_return_money, // 税前应返款
                "pay_amount" => $is_youdian_special ? 0 : $pre_should_pay_money, // 税前应补款
                "business_account_id" => $alipay_account_info->account_id,
                "business_company_name" => $pay_company,
                "first_live_time" => $item->first_live_time,
                "anchor_order_fee" => $item->anchor_order_fee ?? 0, // 主播星图提现金额
                "anchor_cost" => $item->anchor_cost, // 主播实际工资
                "anchor_log_id" => $item->anchor_log_id,
                "creator" => $user_name,
                "status" => OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN,
                "tw_pay_yd" => $item->tw_pay_yd,
                "cloud_account_fee" => 0, // 原为【游点云账户下发工资】 合并到 【游点下发工资】
                "yd_out_tax" => $item->yd_out_tax,
                "yd_company_fee" => $item->yd_company_fee, // 游点下发工资
                "author_mcn" => $item->author_mcn,
                "pay_way" => $pay_way,
                "cloud_account_tax_author_cover_rate" => $cloud_account_tax_author_cover_rate,
                "com_is_extra" => $com_is_extra,
                "company_tax" => $company_tax,
                "anchor_order_extra" => $item->anchor_order_extra,
                "is_extra" => $is_extra,
                "author_type" => $item->author_type,
                "trade_type" => $settlement_amount >= 0 || $is_youdian_special ? OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_PAY : OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_RETURN,
                "after_tax_pay_amount" => $settlement_amount,
                "after_tax_deduction_amount"  => $after_tax_deduction_amount,
                "price_type" => $item->price_type,
                "live_media_type" => $item->live_media_type,
                "dsp_order_type" => $item->dsp_order_type,
            ];

            $cache_list[] = [
                "first_live_time" => $item->first_live_time,
                "author_name" => $item->live_author_name,
                "live_price" => $item->anchor_order_fee, // 主播星图提现金额
                "anchor_cost" => $is_extra ? $item->anchor_cost - $item->anchor_order_extra : $item->anchor_cost, // 主播实际工资 已经包含手续费 如果需要承担手续费则要减去手续费
                "should_return_money" => -$amount, // 主播需返款金额 因为是应补款-应返款=负数 负数是应返款，在二维码上应为正数，正数为应补款，在二维码上应为负数
                "login_mobile" => "",
                "status" => OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN,
                "business_account_id" => $alipay_account_info->account_id,
                "anchor_mobile" => $anchor_mobile,
                "scan_time" => ""
            ];

            $order_ids[] = $item->live_order_id;
        }

        if (empty($order_list)) {
            throw new AppException('订单结算失败，请重新选择');
        }

        // 插入表中 订单号+金额+系统订单号
        $receive_model->replace($order_list);

        // 需要返款 写入redis
        if ($cache_list && $settlement_amount < 0) {
            (new StarDemandReceiveModel())->set($trade_no, $cache_list);
        }

        // 触发直播成本清洗逻辑
        (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($order_ids);

        return $trade_no;
    }

    /**
     * 确认结算
     * @param $trade_no
     */
    public function confirmSettlement($trade_no)
    {
        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        $trade_order_list = $receive_model->getDetailsByTradeNo($trade_no);

        if ($trade_order_list->isEmpty()) {
            throw new AppException("该结算单不存在");
        }

        $amount = $trade_order_list->pluck("amount")->sum();
        $pay_amount = $trade_order_list->pluck("pay_amount")->sum();

        if ($amount > $pay_amount) {
            throw new AppException("该结算单属于返款订单，请勿付款");
        }

        $user_name = Container::getSession()->name;

        $is_youdian = false;
        foreach ($trade_order_list as $item) {
            if (in_array($item->company_name, ['广州游点文化传媒有限公司', '游点自孵化']) || $item->author_type == 1 || $item->author_mcn === '游点文化') {
                $is_youdian = true;
                break;
            }
        }

        if (!$is_youdian && !in_array($user_name, self::FINANCE_WHITE_USER)) {
            throw new AppException("请联系财务人员确认对账");
        }

        if ($is_youdian && !in_array($user_name, self::BUSINESS_WHITE_USER)) {
            throw new AppException("请联系商务人员确认对账");
        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_CANCEL])->isNotEmpty()) {
            throw new AppException("该结算单已取消，请勿重复操作");
        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_SUCCESS, $receive_model::STATUS_CONFIRM])->isNotEmpty()) {
            throw new AppException("该结算单已对账，请勿重复操作");
        }

        $receive_model->updateByTradeNo($trade_no, ['status' => $receive_model::STATUS_CONFIRM, 'confirm_time' => date('Y-m-d H:i:s')]);

        // 触发直播成本清洗逻辑
        $order_ids = $trade_order_list->pluck("order_id")->unique()->toArray();
        (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($order_ids);
    }

    /**
     * 取消结算
     * @param $trade_no
     */
    public function cancelSettlement($trade_no)
    {
        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        $trade_order_list = $receive_model->getDetailsByTradeNo($trade_no);

        if ($trade_order_list->isEmpty()) {
            throw new AppException("该结算订单不存在");
        }

        $trade_type = $trade_order_list->first()->trade_type ?? $receive_model::TRADE_TYPE_PAY;

        $is_youdian = false;
        foreach ($trade_order_list as $item) {
            if (in_array($item->company_name, ['广州游点文化传媒有限公司', '游点自孵化']) || $item->author_type == 1 || $item->author_mcn === '游点文化') {
                $is_youdian = true;
                break;
            }
        }

        $user_name = Container::getSession()->name;

//        if ($settlement_amount <= 0 && !in_array(Container::getSession()->name, self::FINANCE_WHITE_USER)) {
//            throw new AppException("请联系财务人员取消");
//        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_CANCEL])->isNotEmpty()) {
            throw new AppException("该结算订单已取消，请勿重复操作");
        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_SUCCESS])->isNotEmpty()) {
            throw new AppException("该结算订单已确认，取消失败");
        }

        if ($trade_type == $receive_model::TRADE_TYPE_PAY) {
            if (!$is_youdian && $trade_order_list->whereIn('status', [$receive_model::STATUS_CONFIRM])->isNotEmpty()) {
                if (!in_array($user_name, self::FINANCE_WHITE_USER)) {
                    throw new AppException("没有权限取消该结算订单，取消失败");
                } else {
                    // 非游点订单 已对账+用章申请拒绝状态下 可取消
                    $chapter_approve_info = (new OdsStarDemandQrcodeReceiveApproveLogModel())->getLastApproveDataByTradeNo($trade_no);
                    if (!empty($chapter_approve_info) && $chapter_approve_info['status'] != ToutiaoStarLogic::APPROVE_STATUS_REJECT) {
                        throw new AppException("该已对账结算订单存在未被拒绝的用章申请，取消失败");
                    }
                }
            }

            if ($is_youdian && $trade_order_list->whereIn('status', [$receive_model::STATUS_CONFIRM])->isNotEmpty() && !in_array($user_name, self::BUSINESS_WHITE_USER)) {
                throw new AppException("没有权限取消该游点结算订单，取消失败");
            }
        }

        $receive_model->updateByTradeNo($trade_no, ['status' => $receive_model::STATUS_CANCEL, 'invoice_time' => '1970-01-01', 'update_time' => date('Y-m-d H:i:s')]);

        if ($trade_type == $receive_model::TRADE_TYPE_RETURN) {
            (new StarDemandReceiveModel())->delete($trade_no);
        }

        // 触发直播成本清洗逻辑
        $order_ids = $trade_order_list->pluck("order_id")->unique()->toArray();
        (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($order_ids);

        // 终止BPM的用章申请实例
        if (!$is_youdian) {
            $approve_model = new OdsStarDemandQrcodeReceiveApproveLogModel();
            $approve_info = $approve_model->getInApproveDataByTradeNo($trade_no);
            if (!empty($approve_info)) {
                (new WorkProjectApproveInstModel())->terminationInst([
                    'space_id' => 33,
                    'work_project_id' => 176,
                    'inst_id' => $approve_info['work_project_approve_inst_id'],
                    'termination_reason_type' => 3,
                    'termination_reason' => "【{$user_name}】取消结算",
                    'staff_number' => Container::getSession()->staff_number
                ]);

                $approve_info['status'] = ToutiaoStarLogic::APPROVE_STATUS_REJECT;
                $approve_model->replace($approve_info);
            }
        }
    }

    /**
     * 获取结算单中关联主播与机构的收款人列表
     * @param $trade_nos
     * @return array
     */
    public function getPayeeList($trade_nos)
    {
        $settle_details_list = (new OdsStarDemandQrcodeReceiveLogModel())->getDetailsWithAnchorCompanyByTradeNos($trade_nos);
        $list = [];
        foreach ($settle_details_list as $item) {
            if (!isset($list[$item->trade_no])) {
                $list[$item->trade_no] = [
                    "trade_no" => $item->trade_no,
                    "payee_list" => []
                ];
            }

            $anchor_payee_list = json_decode($item->anchor_list_payee, true) ?: [];
            $company_payee_list = json_decode($item->company_list_payee, true) ?: [];

            foreach ([$anchor_payee_list, $company_payee_list] as $payee_list) {
                foreach ($payee_list as $payee) {
                    $unique_key = "{$payee['payee']}-{$payee['address']}-{$payee['bank']}-{$payee['bank_card_no']}";
                    if (!isset($list[$item->trade_no]['payee_list'][$unique_key])) {
                        $list[$item->trade_no]['payee_list'][$unique_key] = $payee;
                    }
                }
            }
        }

        $result = [];
        foreach ($list as $trade_no => $item) {
            $result[] = [
                'trade_no' => $trade_no,
                'payee_list' => array_values($item['payee_list'])
            ];
        }

        return $result;
    }


    /**
     * @param $seconds
     * @return string
     */
    public function formatLiveTime($seconds) {
        if (!$seconds) {
            $seconds = 0;
        }

        $hours = floor($seconds / 3600);
        $minutes = ceil(($seconds % 3600) / 60);

        if ($minutes == 60) {
            $hours += 1;
            $minutes = 0;
        }

        if ($hours == 0 && $minutes == 0) {
            $minutes = 0;
        }

        return $hours . '小时' . $minutes . '分钟';
    }

    /**
     * @param $settle_list
     * @param $export_type
     * @return array
     */
    public function exportSettlement($settle_list, $export_type, $file_type = 'excel')
    {
        $trade_nos = array_column($settle_list, 'trade_no');
        $model = new OdsStarDemandQrcodeReceiveLogModel();
        $settle_details_list = $model->getDetailsInTradeNos($trade_nos);
        if ($settle_details_list->isEmpty()) {
            throw new AppException("当前结算单有误，请重新结算");
        }

        $order_list = [];
        $order_trade_no_map = [];
        $settlement_trade_no_group = [];
        $company_name_list = [];
        $business_company_name_list = [];
        $pay_way_list = [];
        $cloud_account_tax_author_cover_rate_list = [];
        $com_is_extra_list = [];
        $company_tax_list = [];
        $first_live_time_list = [];
        $price_type_list = [];
        $export_anchor_log_list = [];
        $special_date = "2025-03-01";
        $all_settlement_amount = 0;
        $cloud_account_tax = 0;
        $all_order_ids = [];
        foreach ($settle_details_list as $item) {
            $order_trade_no_map["{$item->order_id}-{$item->anchor_log_id}"] = $item->trade_no;
            if (!isset($settlement_trade_no_group[$item->trade_no])) {
                $settlement_trade_no_group[$item->trade_no] = [
                    'status' => $item->status,
                    'settlement_amount' => 0
                ];
            }

            $first_live_time_list[] = date("Y-m-d", strtotime($item->first_live_time));

            if (strtotime($item->first_live_time) < strtotime($special_date)) {
                $cloud_account_tax = 1.063;
            } else {
                $cloud_account_tax = 1.064;
            }

//            // 是否承担星图提现手续费
//            $is_extra = $item->is_extra;
//            if ($item->author_mcn === '游点文化' || in_array($item->author_type, [1, 2])) {
//                $is_extra = 0;
//            }
//
//            if ($is_extra) {
//                $settlement_amount = $item->anchor_order_fee - $item->anchor_cost + $item->anchor_order_extra;
//            } else {
//                $settlement_amount = $item->anchor_order_fee - $item->anchor_cost;
//            }
//
//            // 贪玩蓝V 主播不能提现，所以没有提现金额，直接就是主播工资
//            if ($item->author_type == 2) {
//                $settlement_amount = - $item->anchor_cost;
//            }

            $settlement_amount = $item->pay_amount - $item->amount;

            $all_settlement_amount += $settlement_amount;
            $settlement_trade_no_group[$item->trade_no]['settlement_amount'] += $settlement_amount;

            $order_list[] = [
                'order_id' => $item->order_id,
                'anchor_log_id' => $item->anchor_log_id
            ];
            $company_name_list[] = $item->company_name;
            $business_company_name_list[] = $item->business_company_name;
            $pay_way_list[] = $item->pay_way;
            $cloud_account_tax_author_cover_rate_list[] = $item->cloud_account_tax_author_cover_rate;
            $com_is_extra_list[] = $item->com_is_extra;
            $company_tax_list[] = $item->company_tax;
            $price_type_list[] = $item->price_type;
            $export_anchor_log_list[] = $item->anchor_log_id;
            $all_order_ids[] = $item->order_id;
        }

        // 只允许机构与结算主体相同才能合并打印 个人机构也不能合并
        if ($export_type == self::EXPORT_SETTLE_TYPE_COMBINE) {
            $company_name_list = array_unique($company_name_list);
            $business_company_name_list = array_unique($business_company_name_list);
            $pay_way_list = array_unique($pay_way_list);
            $cloud_account_tax_author_cover_rate_list = array_unique($cloud_account_tax_author_cover_rate_list);
            $com_is_extra_list = array_unique($com_is_extra_list);
            $company_tax_list = array_unique($company_tax_list);
            $price_type_list = array_unique($price_type_list);
            $export_anchor_log_list = array_unique($export_anchor_log_list);
            if (count($company_name_list) > 1) {
                throw new AppException("存在不同机构: " . implode(",", $company_name_list));
            }
            if (count($business_company_name_list) > 1) {
                throw new AppException("存在不同收款主体: " . implode(",", $business_company_name_list));
            }
            if ($company_name_list[0] == "个人" && count($export_anchor_log_list) > 1) {
                throw new AppException("机构为个人的不同主播不允许合并");
            }
            if (count($pay_way_list) > 1) {
                throw new AppException("存在不同的主播收款方式");
            }
            // 云账户打款税点只有个人使用，个人不允许合并打印，所以不需要判断
//            if (count($cloud_account_tax_author_cover_rate_list) > 1) {
//                throw new AppException("存在不同的承担云账户打款税点");
//            }
            if (count($com_is_extra_list) > 1) {
                throw new AppException("存在不同的是否承担开票税");
            }
            if (count($company_tax_list) > 1) {
                throw new AppException("存在不同的对方开票税率");
            }

            foreach ($first_live_time_list as $item) {
                if (in_array($special_date, $first_live_time_list) && strtotime($item) < strtotime($special_date)) {
                    throw new AppException("不允许3月前与3月订单合并结算");
                }
            }

            if (in_array(5, $price_type_list) && count($price_type_list) > 1) {
                throw new AppException("存在不同的计费类型");
            }
        }

        // 补款特殊计算
        $pay_way = $pay_way_list[0];
        $cloud_account_tax_author_cover_rate = $cloud_account_tax_author_cover_rate_list[0];
        $com_is_extra = $com_is_extra_list[0];
        $company_tax = $company_tax_list[0];
        if ($all_settlement_amount >= 0) {
            $all_settlement_amount = $this->settleCalcAfterTaxAmount(
                $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $all_settlement_amount
            );
        }

        // 检查各个结算标识的状态
        $need_to_confirm = [];
        foreach ($settlement_trade_no_group as $trade_no => $item) {
            if (!in_array($item['status'], [OdsStarDemandQrcodeReceiveLogModel::STATUS_CONFIRM, OdsStarDemandQrcodeReceiveLogModel::STATUS_SUCCESS])) {
                $need_to_confirm[] = $trade_no;
            }
            if ($item['settlement_amount'] >= 0) {
                $settlement_trade_no_group[$trade_no]['settlement_amount'] = $this->settleCalcAfterTaxAmount(
                    $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $item['settlement_amount']
                );
            }
        }

        if ($need_to_confirm) {
            throw new AppException("存在未确认对账或未收款的结算: " . implode(",", $need_to_confirm));
        }

        $payee_trade_no_map = [];
        $payee_unique_map = [];
        foreach ($settle_list as $item) {
            foreach ($item['payee_list'] as $payee) {
                $payee_data = [
                    'company' => $payee['payee'],
                    'address' => $payee['address'],
                    'bank' => $payee['bank'],
                    'bank_card_no' => $payee['bank_card_no'],
                    'id_card' => $payee['id_card'] ?? '',
                    'telephone' => $payee['telephone'] ?? '',
                ];
                $payee_trade_no_map[$item['trade_no']][] = $payee_data;
                $unique_key = "{$payee['payee']}-{$payee['address']}-{$payee['bank']}-{$payee['bank_card_no']}";
                if (!isset($payee_unique_map[$unique_key])) {
                    $payee_unique_map[$unique_key] = $payee_data;
                }
            }
        }

        $all = [];
        $payway_company_list = [];
        foreach (array_chunk($order_list, 1000) as $item) {
            $order_ids = array_column($item, 'order_id');
            $anchor_log_ids = array_column($item, 'anchor_log_id');
            $anchor_order_unique_key_list = [];
            foreach ($item as $sub_item) {
                $anchor_order_unique_key_list[] = "{$sub_item['order_id']}-{$sub_item['anchor_log_id']}";
            }

            $list = (new OdsStarDemandOrderListLogModel())->getLiveCostReportData(new ADLiveCostReportFilterParam([
                "dimension" => [
                    "live_order_id", "anchor_company", "platform", "live_account_id", "live_demand_id",
                    "live_demand_name", "live_author_id", "live_author_name", "live_anchor_name",
                    "live_media_type", "dsp_order_type"
                ],
                "filter" => [
                    "live_order_id" => [
                        "column" => "live_order_id",
                        "value" => $order_ids
                    ],
                    "anchor_log_id" => [
                        "column" => "anchor_log_id",
                        "value" => $anchor_log_ids
                    ]
                ],
                "target" => [
                    "anchor_cost", "anchor_order_fee", "author_mcn", "star_aweme_account", "should_return_money",
                    "first_live_time", "anchor_log_id", "payway_company", "mcn_id", "media_type", "should_pay_money",
                    "author_mcn", "cloud_account_tax_author_cover_rate", "contract_game_name", "live_time_cost",
                    "live_price", "price_type", "amount", "is_extra", "extra_desc", "deduction_desc",
                    "tw_pay_yd", "cloud_account_fee", "yd_out_tax", "yd_company_fee",
                    "author_type", "pre_should_pay_money", "price_type"
                ],
                "search_all" => true
            ]));

            // 筛选出本次选的订单+主播ID
            $list['list'] = $list['list']->filter(function ($value) use ($anchor_order_unique_key_list) {
                return in_array("{$value->live_order_id}-{$value->anchor_log_id}", $anchor_order_unique_key_list);
            });

            // 请求的订单数对比查询的订单数是否一致
            $report_order_ids = $list['list']->pluck('live_order_id')->unique()->toArray();
            $order_diff = array_diff($order_ids, $report_order_ids);
            $report_order_diff = array_diff($report_order_ids, $order_ids);
            if ($order_diff || $report_order_diff) {
                throw new AppException("查询数据有误，请重新尝试");
            }

            foreach ($list['list'] as $settle_detail) {
                $trade_no = $order_trade_no_map["{$settle_detail->live_order_id}-{$settle_detail->anchor_log_id}"];
                $all[$trade_no][] = $settle_detail;
                $payway_company_list[] = $settle_detail->payway_company;
            }
        }

        $payway_company_list = array_unique($payway_company_list);
        $payway_company_info_list = (new OdsCompanyInvoiceInfoModel())->getListByShortNameList($payway_company_list);
        $payway_company_info_list = $payway_company_info_list->map(function ($item) {
            return [
                'short_name' => $item->short_name,
                'company' => $item->company_name,
                'address' => $item->invoicing_address,
                'bank' => $item->bank_name,
                'bank_card_no' => $item->account_number,
                'id_card' => '',
                'telephone' => '',
            ];
        });

        // 合并打印使用
        $payway_company_info_unique_list = $payway_company_info_list->unique('short_name')->toArray();

        // 分开打印使用
        $payway_company_info_list = $payway_company_info_list->groupBy('short_name')->toArray();

        // 保存文件夹
        $file_dir = EnvConfig::UPLOAD_PATH . '/star_demand_export/';
        $file_path = SRV_DIR . $file_dir;

        // 判断上传目录是否存在，不存在则创建
        if (!file_exists($file_path)) {
            mkdir($file_path, 0755, true);
        }

        // 按结算记录打印结算单
        $file_list = [];
        if ($export_type == self::EXPORT_SETTLE_TYPE_SPLIT) {
            foreach ($all as $trade_no => $settle_detail_list) {
                // 结算标识分开打印
                // 遍历生成 {{$data_list}}
                $data_list = '';
                $amount_total = 0;
                $anchor_order_fee_total = 0;
                $anchor_cost_total = 0;
                $should_amount_total = 0;
                $cloud_account_tax_author_cover_rate = '';
                $is_youdian = false;
                $payway_company = '';
                $yd_company_fee_total = 0;
                $yd_out_tax_total = 0;
                $live_time_list = [];
                $live_media_type_list = [];
                $dsp_order_type_list = [];
                $price_type_list = [];
                foreach ($settle_detail_list as $item) {
                    // 计算税前的应返或应补
                    $should_amount = $item->pre_should_pay_money - $item->should_return_money;
                    $should_amount_total += $should_amount;
                    $amount_total += $item->amount;
                    $anchor_order_fee_total += $item->anchor_order_fee;
                    $yd_company_fee_total += round($item->yd_company_fee, 2);
                    $yd_out_tax_total += round($item->yd_out_tax, 2);

                    if (!$cloud_account_tax_author_cover_rate) {
                        $cloud_account_tax_author_cover_rate = $item->cloud_account_tax_author_cover_rate;
                    }

                    if (!$payway_company) {
                        $payway_company = $item->payway_company;
                    }

                    // 游点结算单 主播工资列变更为贪玩付游点
                    if (in_array($item->anchor_company, ['广州游点文化传媒有限公司', '游点自孵化']) || $item->author_mcn === '游点文化' || in_array($item->author_type, [1])) {
                        $is_youdian = true;
                        $special_column = round($item->tw_pay_yd, 2);
                        $should_amount = 0;
                        $should_amount_total = 0;
                    } else {
                        $special_column = round($item->anchor_cost, 2);
                    }
                    $anchor_cost_total += $special_column;

                    $remark_list = [];
                    if ($item->extra_desc) {
                        $remark_list[] = $item->extra_desc;
                    }

                    if ($item->deduction_desc) {
                        $remark_list[] = $item->deduction_desc;
                    }

                    $remark = implode("|", $remark_list);

                    $live_time_list[] = strtotime($item->first_live_time);

                    $live_media_type_list[] = ADLiveMediaTypeMap::MEDIA_TYPE_MAP[$item->live_media_type ?? 0];

                    $dsp_order_type_list[] = ADFieldsENToCNMap::TOUTIAO_STAR_DSP_ORDER_TYPE[$item->dsp_order_type] ?? '';

                    $price_type_list[] = $this->formatPriceType($item->price_type);

                    // 抵扣完是补款，需要计算每一笔的税后应补款或税后应返款
                    $after_tax_deduction_amount = $should_amount;
                    if ($settlement_trade_no_group[$trade_no]['settlement_amount'] >= 0) {
                        $after_tax_deduction_amount = $this->settleCalcAfterTaxAmount(
                            $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $after_tax_deduction_amount
                        );
                    }

                    // 游点专属模板
                    if ($is_youdian) {
                        // 星图下单金额，主播提现金额，游点下发工资 都为0时就不用打印出来了
                        if ($item->amount == 0 && $item->anchor_order_fee == 0 && $item->yd_company_fee == 0) {
                            continue;
                        }

                        $data_list .= '
                            <tr class="align-center" style="height: 25px;">
                                <td>' . $item->first_live_time . '</td>
                                <td>' . $item->live_anchor_name . '</td>
                                <td>' . $item->live_author_name . '</td>
                                <td style="vnd.ms-excel.numberformat:@">' . $item->star_aweme_account . '</td>
                                <td>' . $item->contract_game_name . '</td>
                                <td>' . ADFieldsENToCNMap::YES_OR_NO[$item->is_extra] . '</td>
                                <td>' . $this->formatLiveTime($item->live_time_cost) . '</td>
                                <td>' . $item->live_price . '</td>
                                <td>' . ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$item->price_type] . '</td>
                                <td>' . $item->amount . '</td>
                                <td>' . $item->anchor_order_fee . '</td>
                                <td>' . round($item->yd_company_fee, 2) . '</td>
                                <td>' . $remark . '</td>
                            </tr>
                        ';
                    } else {
                        // 星图下单金额，主播提现金额，主播工资，应返款金额/应补款金额，实际到账金额/需补款金额 都为0时就不用打印出来了
                        if ($item->amount == 0 && $item->anchor_order_fee == 0 && $special_column == 0 && $should_amount == 0 && $after_tax_deduction_amount == 0) {
                            continue;
                        }

                        $data_list .= '
                            <tr class="align-center" style="height: 25px;">
                                <td>' . $item->first_live_time . '</td>
                                <td>' . $item->live_anchor_name . '</td>
                                <td>' . $item->live_author_name . '</td>
                                <td style="vnd.ms-excel.numberformat:@">' . $item->star_aweme_account . '</td>
                                <td>' . $item->contract_game_name . '</td>
                                <td>' . ADFieldsENToCNMap::YES_OR_NO[$item->is_extra] . '</td>
                                <td>' . $this->formatLiveTime($item->live_time_cost) . '</td>
                                <td>' . $item->live_price . '</td>
                                <td>' . ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$item->price_type] . '</td>
                                <td>' . $item->amount . '</td>
                                <td>' . $item->anchor_order_fee . '</td>
                                <td>' . $special_column . '</td>
                                <td>' . $should_amount . '</td>
                                <td>' . $after_tax_deduction_amount . '</td>
                                <td>' . $remark . '</td>
                            </tr>
                        ';
                    }
                }

                $should_amount_total_upper = Helpers::moneyToString(abs($should_amount_total));
                if ($should_amount_total < 0) {
                    $should_amount_total_upper = "负" . $should_amount_total_upper;
                }

                // 计算出来的税后金额 如果是正的则是补款，如果是负的就是返款
                $export_all_settlement_amount = $settlement_trade_no_group[$trade_no]['settlement_amount'];
                $export_all_settlement_amount_upper = Helpers::moneyToString(abs($export_all_settlement_amount));
                if ($export_all_settlement_amount < 0) {
                    $export_all_settlement_amount_upper = "负" . $export_all_settlement_amount_upper;
                }

                // 总数为正数，就是应补款，我们就作为付款方，反之为应返款+收款方
                if ($should_amount_total >= 0) {
                    $payee_info = $payee_trade_no_map[$trade_no] ?? [];
                    $pay_info = $payway_company_info_list[$payway_company] ?? [];
                } else {
                    $payee_info = $payway_company_info_list[$payway_company] ?? [];
                    $pay_info = $payee_trade_no_map[$trade_no] ?? [];
                }

                // 游点收付款方特殊处理
                if ($is_youdian) {
                    $youdian_company_info = (new OdsCompanyInvoiceInfoModel())->getDataByShortName('广州游点');
                    if ($should_amount_total >= 0) {
                        $payee_info = [[
                            'short_name' => $youdian_company_info['short_name'],
                            'company' => $youdian_company_info['company_name'],
                            'address' => $youdian_company_info['invoicing_address'],
                            'bank' => $youdian_company_info['bank_name'],
                            'bank_card_no' => $youdian_company_info['account_number'],
                        ]];
                        $pay_info = $payway_company_info_list[$payway_company] ?? [];
                    } else {
                        $payee_info = $payway_company_info_list[$payway_company] ?? [];
                        $pay_info = [[
                            'short_name' => $youdian_company_info['short_name'],
                            'company' => $youdian_company_info['company_name'],
                            'address' => $youdian_company_info['invoicing_address'],
                            'bank' => $youdian_company_info['bank_name'],
                            'bank_card_no' => $youdian_company_info['account_number'],
                        ]];
                    }
                }

                if (empty($payee_info) || empty($pay_info)) {
                    throw new AppException("结算标识：$trade_no 或 结算主体：$payway_company 没有付款人信息");
                }

                // 收付款方拼接
                $pay_colspan = 7;
                $payee_colspan = 6;
                if ($is_youdian) {
                    $pay_colspan = 7;
                    $payee_colspan = 4;
                }

                [
                    $pay_company_name,
                    $pay_company, $pay_address, $pay_bank, $pay_bank_account, $pay_operator, $pay_id_card, $pay_telephone,
                    $payee_company, $payee_address, $payee_bank, $payee_bank_account, $payee_operator, $payee_id_card, $payee_telephone,
                ] = (new StarDemandSettleTemplate())->genPayAndPayeeInfo($pay_info, $payee_info, $pay_colspan, $payee_colspan);

                // 将所有开播时间排序并转成月份
                sort($live_time_list);
                $live_time_month = [];
                foreach ($live_time_list as $live_time) {
                    $live_time_month[] = date("Ym", $live_time);
                }

                $live_time_month = implode("-", array_unique($live_time_month));

                // 主播收款方式
                if ($pay_way_list[0] == 1) {
                    $export_pay_way = "对公█&nbsp;&nbsp;个人□";
                } else {
                    $export_pay_way = "对公□&nbsp;&nbsp;个人█";
                }

                // 是否承担开票税
                if ($com_is_extra_list[0] == 0) {
                    $export_com_is_extra = "是□&nbsp;&nbsp;否█";
                } else {
                    $export_com_is_extra = "是█&nbsp;&nbsp;否□";
                }

                $export_company_tax = $company_tax_list[0];

                // 直播渠道
                $live_media_type = implode("、", array_unique($live_media_type_list));

                // 订单类型
                $dsp_order_type = implode("、", array_unique($dsp_order_type_list));

                // 价格类型
                $price_type = implode("|", array_unique($price_type_list));

                $find = [
                    '{{$date}}', '{{$trade_no}}', '{{$pay_company_name}}', '{{$cloud_account_tax_author_cover_rate}}', '{{$data_list}}',
                    '{{$amount_total}}', '{{$anchor_order_fee_total}}', '{{$anchor_cost_total}}', '{{$should_amount_total}}',
                    '{{$actual_amount_total}}', '{{$amount_total_upper}}', '{{$anchor_order_fee_total_upper}}',
                    '{{$anchor_cost_total_upper}}', '{{$should_amount_total_upper}}', '{{$actual_amount_total_upper}}',
                    '{{$pay_company}}', '{{$pay_address}}', '{{$pay_bank}}', '{{$pay_bank_account}}', '{{$pay_operator}}',
                    '{{$payee_company}}', '{{$payee_address}}', '{{$payee_bank}}', '{{$payee_bank_account}}', '{{$payee_operator}}',
                    '{{$yd_out_tax_total}}', '{{$yd_company_fee_total}}', '{{$yd_out_tax_total_upper}}', '{{$yd_company_fee_total_upper}}',
                    '{{$pay_way}}', '{{$com_is_extra}}', '{{$company_tax}}',
                    '{{$pay_id_card}}', '{{$pay_telephone}}',
                    '{{$payee_id_card}}', '{{$payee_telephone}}',
                    '{{$live_media_type}}', '{{$dsp_order_type}}', '{{$price_type}}',
                    '{{$actual_amount_round_total}}', '{{$actual_amount_round_total_upper}}',
                    '{{$yd_company_fee_round_total}}', '{{$yd_company_fee_round_total}}'
                ];

                $replace = [
                    $live_time_month, $trade_no, $pay_company_name, $cloud_account_tax_author_cover_rate, $data_list,
                    $amount_total, $anchor_order_fee_total, $anchor_cost_total, $should_amount_total,
                    $export_all_settlement_amount, Helpers::moneyToString($amount_total), Helpers::moneyToString($anchor_order_fee_total),
                    Helpers::moneyToString($anchor_cost_total), $should_amount_total_upper, $export_all_settlement_amount_upper,
                    $pay_company, $pay_address, $pay_bank, $pay_bank_account, $pay_operator,
                    $payee_company, $payee_address, $payee_bank, $payee_bank_account, $payee_operator,
                    $yd_out_tax_total, $yd_company_fee_total, Helpers::moneyToString($yd_out_tax_total), Helpers::moneyToString($yd_company_fee_total),
                    $export_pay_way, $export_com_is_extra, $export_company_tax,
                    $pay_id_card, $pay_telephone,
                    $payee_id_card, $payee_telephone,
                    $live_media_type, $dsp_order_type, $price_type,
                    round($export_all_settlement_amount, 2), $export_all_settlement_amount_upper,
                    round($yd_company_fee_total, 2), round($yd_company_fee_total, 2)
                ];

                $special_column_name = '主播工资';
                if ($is_youdian) {
                    $special_column_name = '贪玩付游点';
                    $res_str = str_replace($find, $replace, (new StarDemandSettleTemplate())->getYoudianSettlementTemplate($special_column_name));
                } else {
                    $res_str = str_replace($find, $replace, (new StarDemandSettleTemplate())->getSettlementTemplate($special_column_name));
                }
                if ($file_type === 'excel') {
                    // 生成文件名
                    $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.xlsx';
                    $file_full_path = $file_path . $filename;
                    file_put_contents($file_full_path, $res_str);
                } elseif ($file_type === 'pdf') {
                    $mpdf = new Mpdf([
                        'autoScriptToLang' => true,
                        'autoLangToFont'   => true,
                        'format' => 'A4-L',
                        'default_font_size' => 10,
                        'useSubstitutions' => true,
                        'simpleTables' => true
                    ]);
//
                    $mpdf->WriteHTML($res_str);

                    // 输出为 PDF 文件
                    $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.pdf';
                    $file_full_path = $file_path . $filename;
                    $mpdf->Output($file_full_path, Destination::FILE);
                } elseif ($file_type === 'html') {
                    // 生成文件名
                    $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.html';
                    $file_full_path = $file_path . $filename;
                    file_put_contents($file_full_path, $res_str);
                } else {
                    throw new AppException("导出文件类型错误");
                }

                $file_list[] = [
                    'file_full_path' => $file_full_path,
                    'file_path' => $file_dir . $filename
                ];

                // 更新结算收付款方信息
                if ($should_amount_total >= 0) {
                    $update_data = json_encode($payee_info, JSON_UNESCAPED_UNICODE);
                } else {
                    $update_data = json_encode($pay_info, JSON_UNESCAPED_UNICODE);
                }
                $model->updateByTradeNo($trade_no, ['payee_list' => $update_data]);
            }
        } else {
            // 结算标识合并打印
            $data_list = '';
            $amount_total = 0;
            $anchor_order_fee_total = 0;
            $anchor_cost_total = 0;
            $should_amount_total = 0;
            $cloud_account_tax_author_cover_rate = '';
            $is_youdian = false;
            $payway_company = '';
            $trade_no_list = [];
            $live_time_list = [];
            $live_media_type_list = [];
            $dsp_order_type_list = [];
            $price_type_list = [];
            $yd_company_fee_total = 0;
            $yd_out_tax_total = 0;
            foreach ($all as $trade_no => $settle_detail_list) {
                foreach ($settle_detail_list as $item) {
                    // 我们为付款方时，应返款为负数，应补款为正数
                    $should_amount = $item->pre_should_pay_money - $item->should_return_money;
                    $should_amount_total += $should_amount;
                    $amount_total += $item->amount;
                    $anchor_order_fee_total += $item->anchor_order_fee;
                    $yd_company_fee_total += round($item->yd_company_fee, 2);
                    $yd_out_tax_total += round($item->yd_out_tax, 2);

                    if (!$cloud_account_tax_author_cover_rate) {
                        $cloud_account_tax_author_cover_rate = $item->cloud_account_tax_author_cover_rate;
                    }

                    if (!$payway_company) {
                        $payway_company = $item->payway_company;
                    }

                    // 游点结算单 主播工资列变更为贪玩付游点
                    if (in_array($item->anchor_company, ['广州游点文化传媒有限公司', '游点自孵化']) || $item->author_mcn === '游点文化' || in_array($item->author_type, [1])) {
                        $is_youdian = true;
                        $special_column = round($item->tw_pay_yd, 2);
                        $should_amount = 0;
                        $should_amount_total = 0;
                    } else {
                        $special_column = round($item->anchor_cost, 2);
                    }
                    $anchor_cost_total += $special_column;

                    $remark_list = [];
                    if ($item->extra_desc) {
                        $remark_list[] = $item->extra_desc;
                    }

                    if ($item->deduction_desc) {
                        $remark_list[] = $item->deduction_desc;
                    }

                    $remark = implode("|", $remark_list);

                    $live_time_list[] = strtotime($item->first_live_time);

                    $live_media_type_list[] = ADLiveMediaTypeMap::MEDIA_TYPE_MAP[$item->live_media_type ?? 0];

                    $dsp_order_type_list[] = ADFieldsENToCNMap::TOUTIAO_STAR_DSP_ORDER_TYPE[$item->dsp_order_type] ?? '';

                    $price_type_list[] = $this->formatPriceType($item->price_type);

                    // 抵扣完是补款，需要计算每一笔的税后应补款或税后应返款
                    $after_tax_deduction_amount = $should_amount;
                    if ($all_settlement_amount >= 0) {
                        $after_tax_deduction_amount = $this->settleCalcAfterTaxAmount(
                            $cloud_account_tax, $pay_way, $cloud_account_tax_author_cover_rate, $com_is_extra, $company_tax, $after_tax_deduction_amount
                        );
                    }

                    // 游点专属模板
                    if ($is_youdian) {
                        // 星图下单金额，主播提现金额，游点下发工资 都为0时就不用打印出来了
                        if ($item->amount == 0 && $item->anchor_order_fee == 0 && $item->yd_company_fee == 0) {
                            continue;
                        }

                        $data_list .= '
                            <tr class="align-center" style="height: 25px;">
                                <td>' . $item->first_live_time . '</td>
                                <td>' . $item->live_anchor_name . '</td>
                                <td>' . $item->live_author_name . '</td>
                                <td style="vnd.ms-excel.numberformat:@">' . $item->star_aweme_account . '</td>
                                <td>' . $item->contract_game_name . '</td>
                                <td>' . ADFieldsENToCNMap::YES_OR_NO[$item->is_extra] . '</td>
                                <td>' . $this->formatLiveTime($item->live_time_cost) . '</td>
                                <td>' . $item->live_price . '</td>
                                <td>' . ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$item->price_type] . '</td>
                                <td>' . $item->amount . '</td>
                                <td>' . $item->anchor_order_fee . '</td>
                                <td>' . round($item->yd_company_fee, 2) . '</td>
                                <td>' . $remark . '</td>
                            </tr>
                        ';
                    } else {
                        // 星图下单金额，主播提现金额，主播工资，应返款金额/应补款金额，实际到账金额/需补款金额 都为0时就不用打印出来了
                        if ($item->amount == 0 && $item->anchor_order_fee == 0 && $special_column == 0 && $should_amount == 0 && $after_tax_deduction_amount == 0) {
                            continue;
                        }

                        $data_list .= '
                            <tr class="align-center" style="height: 25px;">
                                <td>' . $item->first_live_time . '</td>
                                <td>' . $item->live_anchor_name . '</td>
                                <td>' . $item->live_author_name . '</td>
                                <td style="vnd.ms-excel.numberformat:@">' . $item->star_aweme_account . '</td>
                                <td>' . $item->contract_game_name . '</td>
                                <td>' . ADFieldsENToCNMap::YES_OR_NO[$item->is_extra] . '</td>
                                <td>' . $this->formatLiveTime($item->live_time_cost) . '</td>
                                <td>' . $item->live_price . '</td>
                                <td>' . ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$item->price_type] . '</td>
                                <td>' . $item->amount . '</td>
                                <td>' . $item->anchor_order_fee . '</td>
                                <td>' . $special_column . '</td>
                                <td>' . $should_amount . '</td>
                                <td>' . $after_tax_deduction_amount . '</td>
                                <td>' . $remark . '</td>
                            </tr>'
                        ;
                    }
                }
                $trade_no_list[] = $trade_no;
            }

            $should_amount_total_upper = Helpers::moneyToString(abs($should_amount_total));
            if ($should_amount_total < 0) {
                $should_amount_total_upper = "负" . $should_amount_total_upper;
            }

            // 计算出来的税后金额 如果是正的则是补款，如果是负的就是返款
            $export_all_settlement_amount = $all_settlement_amount;
            $export_all_settlement_amount_upper = Helpers::moneyToString(abs($export_all_settlement_amount));
            if ($export_all_settlement_amount < 0) {
                $export_all_settlement_amount_upper = "负" . $export_all_settlement_amount_upper;
            }

            // 总数为正数，就是应补款，我们就作为付款方，反之为应返款+收款方
            if ($should_amount_total >= 0) {
                $payee_info = array_values($payee_unique_map);
                $pay_info = array_values($payway_company_info_unique_list);
            } else {
                $payee_info = array_values($payway_company_info_unique_list);
                $pay_info = array_values($payee_unique_map);
            }

            // 游点收付款方特殊处理
            if ($is_youdian) {
                $youdian_company_info = (new OdsCompanyInvoiceInfoModel())->getDataByShortName('广州游点');
                if ($should_amount_total >= 0) {
                    $payee_info = [[
                        'short_name' => $youdian_company_info['short_name'],
                        'company' => $youdian_company_info['company_name'],
                        'address' => $youdian_company_info['invoicing_address'],
                        'bank' => $youdian_company_info['bank_name'],
                        'bank_card_no' => $youdian_company_info['account_number'],
                    ]];
                    $pay_info = $payway_company_info_list[$payway_company] ?? [];
                } else {
                    $payee_info = $payway_company_info_list[$payway_company] ?? [];
                    $pay_info = [[
                        'short_name' => $youdian_company_info['short_name'],
                        'company' => $youdian_company_info['company_name'],
                        'address' => $youdian_company_info['invoicing_address'],
                        'bank' => $youdian_company_info['bank_name'],
                        'bank_card_no' => $youdian_company_info['account_number'],
                    ]];
                }
            }

            if (empty($payee_info) || empty($pay_info)) {
                throw new AppException("没有收款人信息，请重新选择收款人");
            }

            // 收付款方拼接
            $pay_colspan = 7;
            $payee_colspan = 6;
            if ($is_youdian) {
                $pay_colspan = 7;
                $payee_colspan = 4;
            }

            [
                $pay_company_name,
                $pay_company, $pay_address, $pay_bank, $pay_bank_account, $pay_operator, $pay_id_card, $pay_telephone,
                $payee_company, $payee_address, $payee_bank, $payee_bank_account, $payee_operator, $payee_id_card, $payee_telephone,
            ] = (new StarDemandSettleTemplate())->genPayAndPayeeInfo($pay_info, $payee_info, $pay_colspan, $payee_colspan);

            // 将所有开播时间排序并转成月份
            sort($live_time_list);
            $live_time_month = [];
            foreach ($live_time_list as $live_time) {
                $live_time_month[] = date("Ym", $live_time);
            }

            $live_time_month = implode("-", array_unique($live_time_month));

            // 主播收款方式
            if ($pay_way_list[0] == 1) {
                $export_pay_way = "对公█&nbsp;&nbsp;个人□";
            } else {
                $export_pay_way = "对公□&nbsp;&nbsp;个人█";
            }

            // 是否承担开票税
            if ($com_is_extra_list[0] == 0) {
                $export_com_is_extra = "是□&nbsp;&nbsp;否█";
            } else {
                $export_com_is_extra = "是█&nbsp;&nbsp;否□";
            }

            $export_company_tax = $company_tax_list[0];

            // 直播渠道
            $live_media_type = implode("、", array_unique($live_media_type_list));

            // 订单类型
            $dsp_order_type = implode("、", array_unique($dsp_order_type_list));

            // 价格类型
            $price_type = implode("|", array_unique($price_type_list));

            $find = [
                '{{$date}}', '{{$trade_no}}', '{{$pay_company_name}}', '{{$cloud_account_tax_author_cover_rate}}', '{{$data_list}}',
                '{{$amount_total}}', '{{$anchor_order_fee_total}}', '{{$anchor_cost_total}}', '{{$should_amount_total}}',
                '{{$actual_amount_total}}', '{{$amount_total_upper}}', '{{$anchor_order_fee_total_upper}}',
                '{{$anchor_cost_total_upper}}', '{{$should_amount_total_upper}}', '{{$actual_amount_total_upper}}',
                '{{$pay_company}}', '{{$pay_address}}', '{{$pay_bank}}', '{{$pay_bank_account}}', '{{$pay_operator}}',
                '{{$payee_company}}', '{{$payee_address}}', '{{$payee_bank}}', '{{$payee_bank_account}}', '{{$payee_operator}}',
                '{{$yd_out_tax_total}}', '{{$yd_company_fee_total}}', '{{$yd_out_tax_total_upper}}', '{{$yd_company_fee_total_upper}}',
                '{{$pay_way}}', '{{$com_is_extra}}', '{{$company_tax}}',
                '{{$pay_id_card}}', '{{$pay_telephone}}',
                '{{$payee_id_card}}', '{{$payee_telephone}}',
                '{{$live_media_type}}', '{{$dsp_order_type}}', '{{$price_type}}',
                '{{$actual_amount_round_total}}', '{{$actual_amount_round_total_upper}}',
                '{{$yd_company_fee_round_total}}', '{{$yd_company_fee_round_total}}'
            ];

            $replace = [
                $live_time_month, implode(",", $trade_no_list), $pay_company_name, $cloud_account_tax_author_cover_rate, $data_list,
                $amount_total, $anchor_order_fee_total, $anchor_cost_total, $should_amount_total,
                $export_all_settlement_amount, Helpers::moneyToString($amount_total), Helpers::moneyToString($anchor_order_fee_total),
                Helpers::moneyToString($anchor_cost_total), $should_amount_total_upper, $export_all_settlement_amount_upper,
                $pay_company, $pay_address, $pay_bank, $pay_bank_account, $pay_operator,
                $payee_company, $payee_address, $payee_bank, $payee_bank_account, $payee_operator,
                $yd_out_tax_total, $yd_company_fee_total, Helpers::moneyToString($yd_out_tax_total), Helpers::moneyToString($yd_company_fee_total),
                $export_pay_way, $export_com_is_extra, $export_company_tax,
                $pay_id_card, $pay_telephone,
                $payee_id_card, $payee_telephone,
                $live_media_type, $dsp_order_type, $price_type,
                round($export_all_settlement_amount, 2), $export_all_settlement_amount_upper,
                round($yd_company_fee_total, 2), round($yd_company_fee_total, 2)
            ];

            $special_column_name = '主播工资';
            if ($is_youdian) {
                $special_column_name = '贪玩付游点';
                $res_str = str_replace($find, $replace, (new StarDemandSettleTemplate())->getYoudianSettlementTemplate($special_column_name));
            } else {
                $res_str = str_replace($find, $replace, (new StarDemandSettleTemplate())->getSettlementTemplate($special_column_name));
            }

            if ($file_type === 'excel') {
                // 生成文件名
                $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.xlsx';
                $file_full_path = $file_path . $filename;
                file_put_contents($file_full_path, $res_str);
            } elseif ($file_type === 'pdf') {
                $mpdf = new Mpdf([
                    'autoScriptToLang' => true,
                    'autoLangToFont'   => true,
                    'format' => 'A4-L',
                    'default_font_size' => 10,
                    'useSubstitutions' => true,
                    'simpleTables' => true
                ]);

                $mpdf->WriteHTML($res_str);

                // 输出为 PDF 文件
                $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.pdf';
                $file_full_path = $file_path . $filename;
                $mpdf->Output($file_full_path, Destination::FILE);
            } elseif ($file_type === 'html') {
                // 生成文件名
                $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.html';
                $file_full_path = $file_path . $filename;
                file_put_contents($file_full_path, $res_str);
            } else {
                throw new AppException("导出文件类型错误");
            }

            $file_list[] = [
                'file_full_path' => $file_full_path,
                'file_path' => $file_dir . $filename
            ];

            // 更新结算收付款方信息
            if ($should_amount_total >= 0) {
                $update_data = json_encode($payee_info, JSON_UNESCAPED_UNICODE);
            } else {
                $update_data = json_encode($pay_info, JSON_UNESCAPED_UNICODE);
            }
            $model->updateByTradeNos($trade_no_list, ['payee_list' => $update_data]);
        }

        // 触发直播成本清洗逻辑
        (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($all_order_ids);

        // 加入zip文件
        if (count($file_list) > 1) {
            $filename = date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.zip';
            $file_full_path = $file_path . $filename;
            $zip = new ZipArchive();
            $zip->open($file_full_path, ZipArchive::CREATE);
            foreach ($file_list as $file) {
                $zip->addFile($file['file_full_path'], basename($file['file_full_path']));
            }

            $zip->close();
            // 删除生成的临时文件
            foreach ($file_list as $file) {
                unlink($file['file_full_path']);
            }

            $download_path = $file_dir . $filename;
        } elseif (count($file_list) === 1) {
            $download_path = $file_list[0]['file_path'];
        } else {
            throw new AppException("生成失败，请重新尝试");
        }

        return [
            'download_url' => EnvConfig::DOMAIN . $download_path,
            'file_list' => $file_list
        ];
    }

    /**
     * 更新结算单开票时间
     * @param $trade_no
     * @param $invoice_time
     */
    public function updateSettlementInvoiceTime($trade_no, $invoice_time)
    {
        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        $trade_order_list = $receive_model->getDetailsByTradeNo($trade_no);

        if ($trade_order_list->isEmpty()) {
            throw new AppException("该结算单不存在");
        }

        if (!in_array(Container::getSession()->name, self::FINANCE_WHITE_USER)) {
            throw new AppException("请联系财务人员确认对账");
        }

        if ($trade_order_list->whereIn('status', [$receive_model::STATUS_CANCEL])->isNotEmpty()) {
            throw new AppException("该结算单已取消，请勿重复操作");
        }

        $receive_model->updateByTradeNo($trade_no, [
            'invoice_time' => $invoice_time
        ]);

        // 触发直播成本清洗逻辑
        $order_ids = $trade_order_list->pluck("order_id")->unique()->toArray();
        (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($order_ids);
    }

    /**
     * @param $condition
     * @param $page
     * @param $rows
     * @return array
     */
    public function getAuthorList($condition, $page, $rows)
    {
        $data = (new OdsStarAuthorLogModel())->getList($condition, $page, $rows);
        return $data;
    }

    /**
     * @param $data
     * @return void
     */
    public function addAuthor($data)
    {
        $model = new OdsStarAuthorLogModel();
        $newest_data = (array)($model->getNewestByAuthorId($data['author_id']));

        $operator = Container::getSession()->name;

        $replace_data = [];
        if (!empty($newest_data)) {
            if (strtotime($newest_data['start_date']) >= strtotime($data['start_date'])) {
                throw new AppException('开始时间必须大于历史记录的开始时间');
            }
            // 修改结束时间
            $replace_data[] = [
                'author_id' => $newest_data['author_id'],
                'start_date' => $newest_data['start_date'],
                'aweme_account' => $newest_data['aweme_account'],
                'company' => $newest_data['company'],
                'author_name' => $newest_data['author_name'],
                'author_type' => $newest_data['author_type'],
                'author_mcn' => $newest_data['author_mcn'],
                'creator' => $newest_data['creator'],
                'editor' => $operator,
                'end_date' => date('Y-m-d H:i:s', strtotime($data['start_date']) - 1),
            ];
        }

        $replace_data[] = [
            'author_id' => $data['author_id'],
            'start_date' => $data['start_date'],
            'aweme_account' => $data['aweme_account'] ?? '',
            'company' => $data['company'] ?? '',
            'author_name' => $data['author_name'] ?? '',
            'author_type' => $data['author_type'] ?? 0,
            'author_mcn' => $data['author_mcn'] ?? 0,
            'editor' => '',
            'creator' => $operator,
            'end_date' => '2100-01-01 00:00:00'
        ];

        $model->replace($replace_data);
    }

    /**
     * @param $data
     * @return void
     */
    public function editAuthor($data)
    {
        $operator = Container::getSession()->name;

        $model = new OdsStarAuthorLogModel();
        $history = (array)($model->getItemByAuthorIdAndStartTime($data['author_id'], $data['start_date']));
        if (empty($history)) {
            throw new AppException('找不到当前记录');
        }

        $update_data = [
            'aweme_account' => $data['aweme_account'],
            'company' => $data['company'],
            'author_name' => $data['author_name'],
            'author_type' => $data['author_type'],
            'editor' => $operator,
        ];

        $model->update($data['author_id'], $data['start_date'], $update_data);
    }

    /**
     * 获取主播费用录入是否需要BPM审批与抄送
     * @param $extra_payment
     * @param string $first_live_time
     * @return bool[]
     */
    public function getSpecialFundApproveCopyStatus($extra_payment, $first_live_time = '')
    {
        // 需要审批
        $need_approve = false;
        // 需要抄送
        $need_copy_approve = false;

        // 7月前的订单无需审批
        if ($first_live_time && strtotime($first_live_time) < strtotime("2025-07-01 12:00:00")) {
            return [false, false];
        }

        // 1. 加班费1000以下，不需要审批
        // 2. 加班费1000-3000，需要审批，不需要抄送
        if ($extra_payment >= 1000 && $extra_payment <= 3000) {
            $need_approve = true;
        }

        if ($extra_payment > 3000) {
            $need_approve = true;
            $need_copy_approve = true;
        }

        return [$need_approve, $need_copy_approve];
    }

    /**
     * 处理主播费用录入审批通过
     * @param $fund_info_list
     * @return void
     */
    public function handleSpecialFundPass($fund_info_list)
    {
        $model = new OdsStarSpecialFundLog();
        // 校验订单结算情况
        $order_ids = $fund_info_list->pluck('order_id')->unique()->toArray();
        $receive_order_data = ((new OdsStarDemandQrcodeReceiveLogModel()))->getNormalDataByOrderId($order_ids);
        if ($receive_order_data->isNotEmpty()) {
            throw new AppException('操作失败，以下订单已结算，无法录入加班费：' . json_encode($order_ids));
        }

        // 对应订单的录入历史
        foreach ($fund_info_list as $fund_info) {
            $fund_info = (array)$fund_info;

            $history_log = $model->getDataByOrderIdAndAnchorId($fund_info['order_id'], $fund_info['anchor_log_id']);

            if ($history_log->isNotEmpty()) {
                // 删除已录入订单
                $model->removeByIds($history_log->pluck('id')->toArray());
            }

            $model->customUpdate(
                $fund_info['order_id'],
                $fund_info['anchor_log_id'],
                ['status' => ToutiaoStarLogic::APPROVE_STATUS_PASSED]
            );
        }

        (new ToutiaoTaskModel())->refreshLiveOrderCostCalc($order_ids);
    }

    /**
     * 更新备注
     * @param $trade_no
     * @param $remark
     * @return void
     */
    public function updateSettlementRemark($trade_no, $remark)
    {
        (new OdsStarDemandQrcodeReceiveLogModel())->updateByTradeNo($trade_no, ['remark' => $remark]);
    }

    /**
     * 发起用章申请
     * @param $trade_nos
     * @return string
     */
    public function approveChapter($trade_nos)
    {
        // 1. 发起用章申请
        // 1.1 走合并打印的检查
        $model = new OdsStarDemandQrcodeReceiveLogModel();
        $settle_details_list = $model->getDetailsInTradeNos($trade_nos);
        if ($settle_details_list->isEmpty()) {
            throw new AppException("当前结算单有误，请重新申请");
        }

        $settlement_trade_no_group = [];
        $company_name_list = [];
        $business_company_name_list = [];
        $pay_way_list = [];
        $com_is_extra_list = [];
        $company_tax_list = [];
        $first_live_time_list = [];
        $price_type_list = [];
        $special_date = "2025-03-01";
        $export_anchor_log_list = [];
        foreach ($settle_details_list as $item) {
            $order_trade_no_map["{$item->order_id}-{$item->anchor_log_id}"] = $item->trade_no;
            if (!isset($settlement_trade_no_group[$item->trade_no])) {
                $settlement_trade_no_group[$item->trade_no] = [
                    'status' => $item->status,
                    'settlement_amount' => 0,
                    'trade_type' => $item->trade_type
                ];
            }

            $first_live_time_list[] = date("Y-m-d", strtotime($item->first_live_time));

            $settlement_amount = $item->pay_amount - $item->amount;

            $settlement_trade_no_group[$item->trade_no]['settlement_amount'] += $settlement_amount;

            $company_name_list[] = $item->company_name;
            $business_company_name_list[] = $item->business_company_name;
            $pay_way_list[] = $item->pay_way;
            $com_is_extra_list[] = $item->com_is_extra;
            $company_tax_list[] = $item->company_tax;
            $price_type_list[] = $item->price_type;
            $export_anchor_log_list[] = $item->anchor_log_id;
        }

        // 只允许机构与结算主体相同才能合并打印 个人机构也不能合并
        $company_name_list = array_unique($company_name_list);
        $business_company_name_list = array_unique($business_company_name_list);
        $pay_way_list = array_unique($pay_way_list);
        $com_is_extra_list = array_unique($com_is_extra_list);
        $company_tax_list = array_unique($company_tax_list);
        $price_type_list = array_unique($price_type_list);
        $export_anchor_log_list = array_unique($export_anchor_log_list);
        if (count($company_name_list) > 1) {
            throw new AppException("存在不同机构: " . implode(",", $company_name_list));
        }
        if (count($business_company_name_list) > 1) {
            throw new AppException("存在不同收款主体: " . implode(",", $business_company_name_list));
        }
        if ($company_name_list[0] == "个人" && count($export_anchor_log_list) > 1) {
            throw new AppException("机构为个人的不同主播不允许合并");
        }
        if (count($pay_way_list) > 1) {
            throw new AppException("存在不同的主播收款方式");
        }
        if (count($com_is_extra_list) > 1) {
            throw new AppException("存在不同的是否承担开票税");
        }
        if (count($company_tax_list) > 1) {
            throw new AppException("存在不同的对方开票税率");
        }

        foreach ($first_live_time_list as $item) {
            if (in_array($special_date, $first_live_time_list) && strtotime($item) < strtotime($special_date)) {
                throw new AppException("不允许3月前与3月订单合并结算");
            }
        }

        if (in_array(5, $price_type_list) && count($price_type_list) > 1) {
            throw new AppException("存在不同的计费类型");
        }

        // 检查各个结算标识的状态
        $need_to_confirm = [];
        foreach ($settlement_trade_no_group as $trade_no => $item) {
            if (
                !in_array($item['status'], [OdsStarDemandQrcodeReceiveLogModel::STATUS_WAIT_SCAN, OdsStarDemandQrcodeReceiveLogModel::STATUS_CONFIRM])
                || $item['trade_type'] != OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_PAY
            ) {
                $need_to_confirm[] = $trade_no;
            }
        }

        if ($need_to_confirm) {
            throw new AppException("存在返款或已取消的结算: " . implode(",", $need_to_confirm));
        }

        // 1.2 新建一个表 用于存放一个任务ID 结算标识列表 审核状态 审核实例ID
        $approve_model = new OdsStarDemandQrcodeReceiveApproveLogModel();
        $approve_trade_nos = $approve_model->getInApproveListByTradeNos($trade_nos)->pluck('trade_no')->toArray();
        if (!empty($approve_trade_nos)) {
            throw new AppException("以下结算标识已在审核中或已审核通过：" . implode(",", $approve_trade_nos));
        }

        // 1.3 带着任务ID生成BPM新建实例链接 跳转过去
        $user_name = Container::getSession()->name;
        $approve_id = date("YmdHis") . time() . rand(100000, 999999);
        $approve_list = [];
        foreach ($trade_nos as $trade_no) {
            $approve_list[] = [
                'approve_id' => $approve_id,
                'trade_no' => $trade_no,
                'creator' => $user_name,
                'status' => ToutiaoStarLogic::APPROVE_STATUS_WAIT_INST
            ];
        }

        $approve_model->replace($approve_list);

        $url = (new ToutiaoStarLogic())->getBpmApproveInstFormUrl(30, 176);

        return $url . "&approve_id={$approve_id}";
    }

    /**
     * 用章申请更新回结算审批
     * @param $approve_id
     * @param $work_project_approve_inst_id
     * @return void
     */
    public function updateSettlementApproveInstId($approve_id, $work_project_approve_inst_id)
    {
        $model = new OdsStarDemandQrcodeReceiveApproveLogModel();

        $list = $model->getWatiInstListByApproveId($approve_id);
        if ($list->isEmpty()) {
            throw new AppException('找不到关联的结算标识');
        }

        // 检查结算标识有没有其他已在审核中的实例
        $in_approve_trade_nos = $model->checkTradeNoInOtherApprove($approve_id)->pluck('trade_no')->toArray();
        if (!empty($in_approve_trade_nos)) {
            throw new AppException('以下结算标识已有审批实例：' . implode(",", $in_approve_trade_nos));
        }

        $model->editByApproveId($approve_id, [
            'work_project_approve_inst_id' => $work_project_approve_inst_id, 'status' => ToutiaoStarLogic::APPROVE_STATUS_WAIT
        ]);
    }

    /**
     * 用章申请更新审批情况
     * @param $data
     * @return void
     */
    public function updateSettlementApproveChapter($data, $type)
    {
        $model = new OdsStarDemandQrcodeReceiveApproveLogModel();
        $trade_info_list = $model->getDataByWorkProjectApproveInstId($data['work_project_approve_inst_id']);
        if (empty($trade_info_list)) {
            throw new AppException('审批实例所关联的结算记录不存在');
        }

        // 检查结算标识有没有其他已在审核中的实例
        $in_approve_trade_nos = $model->checkTradeNoInOtherApprove($trade_info_list->first()->approve_id)->pluck('trade_no')->toArray();
        if (!empty($in_approve_trade_nos)) {
            throw new AppException('以下结算标识已有审批实例：' . implode(",", $in_approve_trade_nos));
        }

        // 只确认补款的单子
        $trade_nos = $trade_info_list->pluck('trade_no')->unique()->toArray();
        $trade_details_list = (new OdsStarDemandQrcodeReceiveLogModel())->getDetailsInTradeNos($trade_nos);
        $should_pay_trade_nos = $trade_details_list
            ->where('trade_type', OdsStarDemandQrcodeReceiveLogModel::TRADE_TYPE_PAY)
            ->pluck('trade_no')
            ->toArray();

        $trade_info_list = $trade_info_list->filter(function ($item) use ($should_pay_trade_nos) {
            return in_array($item->trade_no, $should_pay_trade_nos);
        });

        // 审批通过
        if (in_array($data['type'], [ApproveController::APPROVE_TYPE_PASSED, ApproveController::APPROVE_TYPE_SUBMIT_PROCESSING])) {
            // 财务审批节点通过 只更新结算标识为已对账
            if ($type === 'node') {
                $this->handleApproveChapterPass($trade_info_list);
            } else {
                // 如果是审批实例通过 则更新审批实例的状态
                $model->editByWorkProjectApproveInstId($data['work_project_approve_inst_id'], ['status' => ToutiaoStarLogic::APPROVE_STATUS_PASSED]);
            }
        } elseif ($data['type'] == ApproveController::APPROVE_TYPE_REJECT) {
            $model->editByWorkProjectApproveInstId($data['work_project_approve_inst_id'], ['status' => ToutiaoStarLogic::APPROVE_STATUS_REJECT]);
        } elseif ($data['type'] == ApproveController::APPROVE_TYPE_GO_BACK) {
            $model->editByWorkProjectApproveInstId($data['work_project_approve_inst_id'], ['status' => ToutiaoStarLogic::APPROVE_STATUS_RETURN]);
        } elseif (in_array($data['type'], [ApproveController::APPROVE_TYPE_SUBMIT, ApproveController::APPROVE_TYPE_SUBMIT_GO_BACK])) {
            $model->editByWorkProjectApproveInstId($data['work_project_approve_inst_id'], ['status' => ToutiaoStarLogic::APPROVE_STATUS_WAIT]);
        }
    }

    /**
     * 处理用章申请通过情况
     * @param $trade_info_list
     * @return void
     */
    public function handleApproveChapterPass($trade_info_list)
    {
        $trade_nos = $trade_info_list->pluck('trade_no')->unique()->toArray();
        if (!empty($trade_nos)) {
            $model = new OdsStarDemandQrcodeReceiveLogModel();
            $trade_details_list = $model->getDetailsInTradeNos($trade_nos);
            $model->customUpdateByTradeNos($trade_nos, ['status' => OdsStarDemandQrcodeReceiveLogModel::STATUS_CONFIRM, 'confirm_time' => date('Y-m-d H:i:s')]);

            // 触发直播成本清洗逻辑
            $order_ids = $trade_details_list->pluck("order_id")->unique()->toArray();
            (new ToutiaoTaskModel())->refreshLiveOrderCostCalc($order_ids);
        }
    }

    /**
     * 格式化价格类型
     * @param $price_type
     * @return string
     */
    public function formatPriceType($price_type)
    {
        if (in_array($price_type, [5, 6])) {
            return ADFieldsENToCNMap::TOUTIAO_STAR_LIVE_PRICE_TYPE[$price_type] ?? '';
        } else {
            return 'CPT';
        }
    }
}
