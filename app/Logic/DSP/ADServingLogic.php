<?php
/**
 * Created by <PERSON>p<PERSON><PERSON><PERSON>.
 * User: Administrator
 * Date: 2020/06/09
 * Time: 14:39
 */

namespace App\Logic\DSP;


use App\Constant\ApiAccessUserInfo;
use App\Constant\BatchAD;
use App\Constant\BatchADTaskProcess;
use App\Constant\MediaType;
use App\Container;
use App\Exception\AppException;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\RedisModel\BatchADTaskQueueTaskNumModel;
use App\Model\RedisModel\IntelligentRobotFrequencyModel;
use App\Model\SqlModel\Database\ZDBuilder;
use App\Model\SqlModel\DataMedia\ADComposeFilterModel;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorKuaishouModel;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorTencentV3Model;
use App\Model\SqlModel\DataMedia\ADIntelligentMonitorToutiaoV2Model;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\DataMedia\OdsMaterialLowEffectAttemptLog;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\ADAccountPacketModel;
use App\Model\SqlModel\Zeda\ADComposePacketModel;
use App\Model\SqlModel\Zeda\ADHotMaterialPacketModel;
use App\Model\SqlModel\Zeda\ADIntelligentComposeConditionModel;
use App\Model\SqlModel\Zeda\ADIntelligentComposeLogModel;
use App\Model\SqlModel\Zeda\ADIntelligentComposeModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorBindModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorExecBodyModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorRobotLogModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorRobotModel;
use App\Model\SqlModel\Zeda\ADLabTaskListModel;
use App\Model\SqlModel\Zeda\ADMaterialPacketModel;
use App\Model\SqlModel\Zeda\ADSettingPacketModel;
use App\Model\SqlModel\Zeda\ADStarMaterialPacketModel;
use App\Model\SqlModel\Zeda\ADTagPacketModel;
use App\Model\SqlModel\Zeda\ADTargetingPacketModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\ADTaskLogModel;
use App\Model\SqlModel\Zeda\ADTaskSolutionModel;
use App\Model\SqlModel\Zeda\ADUpdateKuaishouCreativeLogModel;
use App\Model\SqlModel\Zeda\ADWordPacketModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialModeModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\MysqlConnection;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeImageParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeListParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoCoverParam;
use App\Param\ADServing\ADComposeContent\ADComposeCreativeVideoParam;
use App\Param\ADServing\ADComposeFilterParam;
use App\Param\ADServing\ADAccountPacketParam;
use App\Param\ADServing\ADAccountPacketSearchParam;
use App\Param\ADServing\ADComposePacketParam;
use App\Param\ADServing\ADComposePacketSearchParam;
use App\Param\ADServing\ADHotMaterialPacketParam;
use App\Param\ADServing\ADIntelligentComposeConditionParam;
use App\Param\ADServing\ADIntelligentComposeConditionSearchParam;
use App\Param\ADServing\ADIntelligentComposeLogParam;
use App\Param\ADServing\ADIntelligentComposeLogSearchParam;
use App\Param\ADServing\ADIntelligentComposeParam;
use App\Param\ADServing\ADIntelligentComposeSearchParam;
use App\Param\ADServing\ADIntelligentMonitorBindLogSearchParam;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use App\Param\ADServing\ADIntelligentMonitorExecBodyParam;
use App\Param\ADServing\ADIntelligentMonitorExecBodySearchParam;
use App\Param\ADServing\ADIntelligentMonitorRobotLogSearchParam;
use App\Param\ADServing\ADIntelligentMonitorRobotParam;
use App\Param\ADServing\ADIntelligentMonitorRobotSearchParam;
use App\Param\ADServing\ADStarMaterialPacketParam;
use App\Param\ADServing\ADTaskCreateParam;
use App\Param\ADServing\ADMaterialPacketParam;
use App\Param\ADServing\ADMaterialPacketSearchParam;
use App\Param\ADServing\ADSettingPacketParam;
use App\Param\ADServing\ADSettingPacketSearchParam;
use App\Param\ADServing\ADTagPacketParam;
use App\Param\ADServing\ADTagPacketSearchParam;
use App\Param\ADServing\ADTargetingPacketParam;
use App\Param\ADServing\ADTaskMQDataParam;
use App\Param\ADServing\ADTaskSearchParam;
use App\Param\ADServing\ADTaskLogSearchParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskSolutionParam;
use App\Param\ADServing\ADWordPacketParam;
use App\Param\ADServing\ADTargetingPacketSearchParam;
use App\Param\ADServing\ADWordPacketSearchParam;
use App\Service\MaterialExpertFilterService;
use App\Service\MaterialInferiorFilterService;
use App\Service\MaterialModeService;
use App\Service\MediaAD\MediaAD;
use App\Service\MediaIntelligentMonitor\IntelligentMonitorTencentV3;
use App\Service\MediaIntelligentMonitor\IntelligentMonitorToutiaoV2;
use App\Service\PlatformAD\PlatformAD;
use App\Service\UserService;
use App\Struct\Input;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Throwable;

class ADServingLogic
{
    //----------------------------------------------定向包--------------------------------------------------------------

    /**
     * @param $media_type
     * @param $company
     * @param $md5
     * @return ZDBuilder|Model|object
     */
    public function getTargetingExitByMD5($media_type, $company, $md5)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        return $ad_targeting_packet_model->getDataByMD5($media_type, $company, $md5);
    }

    /**
     * 添加一个定向包
     * @param array $data
     * @return array
     */
    public function addTargetingPacket(array $data)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        $data['creator'] = Container::getSession()->name;
        $data['create_time'] = time();
        $data['update_time'] = time();
        $data['is_voluntary'] = ADTargetingPacketParam::CUSTOM_VOLUNTARY;
        $targeting_param = new ADTargetingPacketParam($data);
        $targeting_param->setTargetingByArray($data['targeting']);
        $validate_result = $targeting_param->targetingVerify();
        if (count($validate_result)) {
            return [
                'state' => false,
                'result' => 0,
                'error_msg' => $validate_result
            ];
        } else {
            return [
                'state' => true,
                'result' => $ad_targeting_packet_model->addTargetingPacket($targeting_param),
                'error_msg' => []
            ];
        }
    }

    /**
     * 编辑一个定向包
     * @param int $id
     * @param array $data
     * @return array
     */
    public function editTargetingPacket(int $id, array $data)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        $data['update_time'] = time();
        $targeting_param = new ADTargetingPacketParam($data);
        $targeting_param->setTargetingByArray($data['targeting']);
        $validate_result = $targeting_param->targetingVerify();
        if (count($validate_result)) {
            return [
                'state' => false,
                'result' => 0,
                'error_msg' => $validate_result
            ];
        } else {
            return [
                'state' => true,
                'result' => $ad_targeting_packet_model->editTargetingPacket($id, $targeting_param),
                'error_msg' => []
            ];
        }
    }

    /**
     * 获取定向包列表
     * @param array $data
     * @return array
     */
    public function getTargetingPacketList(array $data)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_targeting_packet_model->getList(new ADTargetingPacketSearchParam($data), $user_list);
    }

    /**
     * 获取定向包列表
     * @param array $data
     * @return array
     */
    public function getTargetingPacketListByApi(array $data)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        return $ad_targeting_packet_model->getList(new ADTargetingPacketSearchParam($data), []);
    }

    /**
     * 获取一个定向包内容
     * @param int $id
     * @return Model|Builder|null|object
     */
    public function getTargetingInfoById(int $id)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        return $ad_targeting_packet_model->getData(new ADTargetingPacketSearchParam(['id' => $id]));
    }

    /**
     * 删除定向包
     * @param int $id
     */
    public function deleteTargetingPacket(int $id)
    {
        $ad_targeting_packet_model = new ADTargetingPacketModel();
        $ad_targeting_packet_model->deleteTargetingPacket($id);
    }

    //------------------------------------------------文案包------------------------------------------------------------

    /**
     * 添加一个文案包
     * @param array $data
     */
    public function addWordPacket(array $data)
    {
        $ad_word_packet_model = new ADWordPacketModel();
        $ad_word_packet_model->addWordPacket(new ADWordPacketParam($data));
    }

    /**
     * 添加一个文案包
     * @param int $id
     * @param array $data
     */
    public function editWordPacket(int $id, array $data)
    {
        $ad_word_packet_model = new ADWordPacketModel();
        $ad_word_packet_model->updateWordPacket($id, new ADWordPacketParam($data));
    }

    /**
     * 获取文案包列表
     * @param array $data
     * @return array
     */
    public function getWordPacketList(array $data)
    {
        $ad_word_packet_model = new ADWordPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_word_packet_model->getList(new ADWordPacketSearchParam($data), $user_list);
    }

    /**
     * 删除文案包
     * @param int $id
     */
    public function deleteWordPacket(int $id)
    {
        $ad_word_packet_model = new ADWordPacketModel();
        $ad_word_packet_model->deleteWordPacket($id);
    }

    //--------------------------------------------------标签包----------------------------------------------------------

    /**
     * 创建新的标签包
     * @param array $input
     */
    public function addTagPacket(array $input)
    {

        $input['content'] = trim($input['content']);
        $input['content'] = str_replace(" ", "\n", $input['content']);
        $input['content'] = explode("\n", $input['content']);
        $atcp = new ADTagPacketParam($input);
        $ad_tag_packet_model = new ADTagPacketModel();
        $ad_tag_packet_model->addTagPacket($atcp);
    }


    /**
     * 更新标签包
     * @param int $id
     * @param array $input
     */
    public function editTagPacket(int $id, array $input)
    {
        $input['content'] = trim($input['content']);
        $input['content'] = str_replace(" ", "\n", $input['content']);
        $input['content'] = explode("\n", $input['content']);
        $atcp = new ADTagPacketParam($input);
        $ad_tag_packet_model = new ADTagPacketModel();
        $ad_tag_packet_model->editTagPacket($id, $atcp);
    }

    /**
     * 获取标签包列表
     * @param array $data
     * @return array
     */
    public function getTagPacketList(array $data): array
    {
        $ad_tag_packet_model = new ADTagPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_tag_packet_model->getList(new ADTagPacketSearchParam($data), $user_list);
    }

    /**
     * 删除标签包
     * @param int $id
     */
    public function deleteTagPacket(int $id)
    {
        $ad_tag_packet_model = new ADTagPacketModel();
        $ad_tag_packet_model->deleteTagPacket($id);
    }

    //------------------------------------------素材包------------------------------------------------------------------

    /**
     * 复制素材包
     * @param array $data
     * @return int
     */
    public function copyMaterialPacket(array $data): int
    {
        $ad_material_packet_model = new ADMaterialPacketModel();
        $packet_list = $ad_material_packet_model->getListById($data['ids']);
        foreach ($packet_list as $packet) {
            $packet = (array)$packet;
            $packet['video_list'] = json_decode($packet['video_list'], true);
            $packet['video_vertical_list'] = json_decode($packet['video_vertical_list'], true);
            $packet['group_image_list'] = json_decode($packet['group_image_list'], true);
            $packet['group_video_list'] = json_decode($packet['group_video_list'], true);
            $packet['small_image_list'] = json_decode($packet['small_image_list'], true);
            $packet['large_image_list'] = json_decode($packet['large_image_list'], true);
            $packet['large_vertical_image_list'] = json_decode($packet['large_vertical_image_list'], true);
            $packet['audio_list'] = json_decode($packet['audio_list'], true);
            $packet['media_type'] = $data['media_type'];
            $ampp = MaterialModeService::deleteErrorMaterial(new ADMaterialPacketParam($packet));
            $ad_material_packet_model->addMaterialPacket($ampp);
        }
        return 0;
    }

    /**
     * 添加素材包
     * @param array $data
     */
    public function addMaterialPacket(array $data)
    {
        $ampp = new ADMaterialPacketParam($data);
        $ad_material_packet_model = new ADMaterialPacketModel();
        $ad_material_packet_model->addMaterialPacket($ampp);
    }

    /**
     * 添加素材包for ada
     * @param array $data
     */
    public function addMaterialPacketForAdA(array $data)
    {
        $material_file_model = new MaterialFileModel();
        $list = $material_file_model->getListByIdsForAda($data['ids']);

        $video_vertical_list = [];
        $video_list = [];
        $large_image_list = [];
        $large_vertical_image_list = [];
        $group_image_list = [];

        foreach ($list as $material_file) {
            $material_file_data = (array)$material_file;
            if ($material_file->file_type == MaterialFileModel::FILE_TYPE_VIDEO) {
                $material_file_data['cover_list'] = [];
                if ($material_file->width > $material_file->height) {
                    $video_list[] = $material_file_data;
                } else {
                    $video_vertical_list[] = $material_file_data;
                }
            } else if ($material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                if ($material_file->width > $material_file->height) {
                    $large_image_list[] = $material_file_data;
                } else {
                    $large_vertical_image_list[] = $material_file_data;
                }
            } elseif (in_array($material_file->file_type, [MaterialFileModel::FILE_TYPE_COMBINE3, MaterialFileModel::FILE_TYPE_COMBINE4, MaterialFileModel::FILE_TYPE_COMBINE6, MaterialFileModel::FILE_TYPE_COMBINE9])) {
                $group_image_list[] = $material_file_data;
            }
        }

        if (empty($video_vertical_list) && empty($video_list) && empty($large_image_list) && empty($large_vertical_image_list) && empty($group_image_list)) {
            throw new AppException('空素材 或者 此次选的素材都是非法素材');
        }

        $ampp = new ADMaterialPacketParam([
            'name' => $data['name'],
            'media_type' => $data['media_type'],
            'video_vertical_list' => $video_vertical_list,
            'video_list' => $video_list,
            'group_image_list' => $group_image_list,
            'group_video_list' => [],
            'small_image_list' => [],
            'large_image_list' => $large_image_list,
            'large_vertical_image_list' => $large_vertical_image_list,
            'audio_list' => [],
        ]);

        $ad_material_packet_model = new ADMaterialPacketModel();
        $ad_material_packet_model->addMaterialPacket($ampp);
    }

    /**
     * 更新素材包
     * @param int $id
     * @param array $data
     */
    public function editMaterialPacket(int $id, array $data)
    {
        $ampp = new ADMaterialPacketParam($data);
        $ad_material_packet_model = new ADMaterialPacketModel();
        $ad_material_packet_model->editMaterialPacket($id, $ampp);
    }

    /**
     * 获取素材包列表
     * @param array $data
     * @return array
     */
    public function getMaterialPacketList(array $data)
    {
        $ad_material_packet_model = new ADMaterialPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_material_packet_model->getList(new ADMaterialPacketSearchParam($data), $user_list);
    }

    /**
     * 获取素材包info
     * @param array $data
     * @return Builder|Model|object
     */
    public function getMaterialPacketInfo(array $data)
    {
        $ad_material_packet_model = new ADMaterialPacketModel();
        return $ad_material_packet_model->getDataById($data['id']);
    }

    /**
     * 获取文案包info
     * @param array $data
     * @return Builder|Model|object
     */
    public function getWordPacketInfo(array $data)
    {
        $ad_word_packet_model = new ADWordPacketModel();
        return $ad_word_packet_model->getDataById($data['id']);
    }

    /**
     * 删除素材包
     * @param $id
     */
    public function deleteMaterialPacket(int $id)
    {
        $ad_material_packet_model = new ADMaterialPacketModel();
        $ad_material_packet_model->deleteMaterialPacket($id);
    }

    //-----------------------------------------------账号包-------------------------------------------------------------

    /**
     * 获取账号包列表
     * @param array $data
     * @return array
     */
    public function getAccountPacketList(array $data)
    {
        $ad_account_packet_model = new ADAccountPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_account_packet_model->getList(new ADAccountPacketSearchParam($data), $user_list);
    }

    /**
     * 创建新的账号包
     * @param array $data
     * @return void
     */
    public function addAccountPacket(array $data)
    {
        $aac_param = new ADAccountPacketParam($data);
        $ad_account_packet_model = new ADAccountPacketModel();
        $ad_account_packet_model->addAccountPacket($aac_param);
    }

    /**
     * 更新一个账号包
     * @param int $id
     * @param array $data
     * @return void
     */
    public function editAccountPacket(int $id, array $data)
    {
        $aac_param = new ADAccountPacketParam($data);
        $ad_account_packet_model = new ADAccountPacketModel();
        $ad_account_packet_model->editAccountPacket($id, $aac_param);
    }

    /**
     * 删除一个账号包
     * @param int $id
     * @return void
     */
    public function deleteAccountPacket(int $id)
    {
        $ad_account_packet_model = new ADAccountPacketModel();
        $ad_account_packet_model->deleteAccountPacket($id);
    }

    //------------------------------------------------参数包------------------------------------------------------------

    /**
     * 创建新的参数包
     * @param array $input
     * @return array
     */
    public function addSettingPacket(array $input)
    {
        $ad_setting_packet_model = new ADSettingPacketModel();
        $input['creator'] = Container::getSession()->name;
        $input['create_time'] = time();
        $input['update_time'] = time();
        $setting_param = new ADSettingPacketParam($input);
        $setting_param->setSettingByArray($input['setting']);
        $validate_result = $setting_param->settingVerify();
        if (count($validate_result)) {
            return [
                'state' => false,
                'result' => 0,
                'error_msg' => $validate_result
            ];
        } else {
            return [
                'state' => true,
                'result' => $ad_setting_packet_model->addSettingPacket($setting_param),
                'error_msg' => []
            ];
        }

    }

    /**
     * 更新一个参数包
     * @param int $id
     * @param array $input
     * @return array
     */
    public function editSettingPacket(int $id, array $input)
    {
        $ad_setting_packet_model = new ADSettingPacketModel();
        $input['update_time'] = time();
        $setting_param = new ADSettingPacketParam($input);
        $setting_param->setSettingByArray($input['setting']);
        $validate_result = $setting_param->settingVerify();
        if (count($validate_result)) {
            return [
                'state' => false,
                'result' => 0,
                'error_msg' => $validate_result
            ];
        } else {
            return [
                'state' => true,
                'result' => $ad_setting_packet_model->editSettingPacket($id, $setting_param),
                'error_msg' => []
            ];
        }
    }

    /**
     * 获取参数包列表
     * @param array $data
     * @return array
     */
    public function getSettingPacketList(array $data)
    {
        $ad_setting_packet_model = new ADSettingPacketModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_setting_packet_model->getList(new ADSettingPacketSearchParam($data), $user_list);
    }

    /**
     * 删除一个参数包
     * @param $id
     */
    public function deleteSettingPacket($id)
    {
        $ad_setting_packet_model = new ADSettingPacketModel();
        $ad_setting_packet_model->deleteSettingPacket($id);
    }

    //-----------------------------------------------广告组合包-----------------------------------------------------------

    /**
     * 查询广告组合包
     * @param $id
     * @return ADComposePacketParam
     */
    public function getADComposePacketInfoById($id)
    {
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $result = (new ADComposePacketModel())->getDataByIdUserList($id, $user_list);

        if ($result) {
            $result = (new ADComposePacketParam())->initBySqlData((array)$result);
        } else {
            throw new AppException("找不到id为{$id}的广告组合信息");
        }
        return $result;
    }

    /**
     * 查询广告组合包
     * @param array $input
     * @return array
     */
    public function getADComposePacketList(array $input)
    {
        $user_list = (new PermissionLogic())->getLeaderOrAgentLeaderUserList();

        $result = (new ADComposePacketModel())->getListByConditionUserList(
            (new ADComposePacketSearchParam($input)), $user_list
        );
        $list = [];
        foreach ($result['list'] as $info) {
            $list[] = (new ADComposePacketParam())->initBySqlData((array)$info);
        }
        $result['list'] = $list;
        return $result;
    }

    /**
     * 新增广告组合包
     * @param array $input
     * @return int
     */
    public function addADComposePacket(array $input)
    {
        $ad_compose_packet_param = new ADComposePacketParam($input);
        $ad_compose_packet_param->creator = Container::getSession()->name;
        $ad_compose_packet_param->creator_id = Container::getSession()->user_id;
        $ad_compose_packet_param->judgeAccount();
        $ad_compose_packet_param->judgeGame();
        $ad_compose_packet_param->judgeMaterial();
        return (new ADComposePacketModel())->addADComposePacket($ad_compose_packet_param);
    }

    /**
     * 编辑广告组合包
     * @param array $input
     * @return int
     */
    public function editADComposePacket(array $input)
    {
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $param = (new ADComposePacketParam($input));
        $param->judgeAccount();
        $param->judgeGame();
        $param->judgeMaterial();
        return (new ADComposePacketModel())->editADComposePacket(
            $param, $user_list
        );
    }

    /**
     * 删除广告组合包
     * @param array $ids
     * @return int
     */
    public function deleteADComposePacket(array $ids)
    {
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return (new ADComposePacketModel())->deleteByIdsUserList($ids, $user_list);
    }

    //-----------------------------------------------广告任务-----------------------------------------------------------

    private function makeComposeInputById($id)
    {
        $ad_compose_packet_model = new ADComposePacketModel();
        $compose_info = $ad_compose_packet_model->getDataByIdUserList($id, []);

        if (!$compose_info) {
            throw new AppException('找不到组合信息');
        }

        return json_decode(json_encode((new ADComposePacketParam([
            'creator' => $compose_info->creator,
            'creator_id' => $compose_info->creator_id
        ]))->initBySqlData((array)$compose_info)), true);
    }

    public function ADComposeToADTaskForIntelligentCompose($compose_id, $intelligent_compose_id)
    {
        $input = $this->makeComposeInputById($compose_id);
        $input['script_leader'] = $input['creator'];
        $input['script_leader_id'] = $input['creator_id'];
        $input['intelligent_compose_id'] = $intelligent_compose_id;
        return $this->ADComposeToADTask($input, true);
    }

    public function ADComposeCheck(array $input)
    {
        $input['compose_config']['ignore_warning'] = $input['ignore_warning'];
        $packet = new ADComposePacketParam($input);
        $packet->judgeAccount();
        $packet->judgeGame();
        $material_permission_validate = $packet->judgeMaterial(true);
        if ($material_permission_validate) {
            $validate_result = [
                'state' => false,
                'error_list' => $material_permission_validate
            ];
        } else {
            $compose_content = $packet->makeADComposeContent();

            if (UserService::isSuperManager()) {
                $material_validate_result = $compose_content->validateLowMaterial($input['attempt_material_ids'] ?? []);
                if (!$material_validate_result['state']) {
                    return $material_validate_result;
                }
                $ios_validate_result = $compose_content->validateIOS($input['ios_sure'] ?? 1);
                if (!$ios_validate_result['state']) {
                    return $ios_validate_result;
                }
            }

            $validate_result = $compose_content->composeAlgorithmValidate();
        }


        if ($validate_result['state']) {

            $ad_algorithm = new ADServingComposeAlgorithmLogic();
            $task_create_list = $ad_algorithm->composeAlgorithm($compose_content);

            $task_create_list = $ad_algorithm->dispatchADTaskParam($task_create_list, $compose_content->dispatch_type);

            $task_create_list->resetListForBusinessLogic();

            $task_create_list->judgeCreateTaskMaxNum();

            $compose_result = [
                'state' => true,
                'num' => count($task_create_list->getList()),
                'list' => $task_create_list->getList(),
                'error_msg' => []
            ];
        } else {
            $error_msg = [];
            foreach ($validate_result['error_list'] as $err) {
                $error_msg[$err['type']][] = $err;
            }
            $compose_result = [
                'state' => false,
                'num' => 0,
                'list' => [],
                'error_msg' => $error_msg,
            ];
        }

        if (!$compose_result['state']) {
            $compose_result['result'] = 0;
        }
        return $compose_result;
    }

    /**
     * 生成广告任务
     * @param array $input
     * @param bool $is_script
     * @return array
     */
    public function ADComposeToADTask(array $input, bool $is_script = false)
    {
        $input['compose_config']['ignore_warning'] = $input['ignore_warning'] ?? '';

        $material_permission_validate = [];
        $packet = new ADComposePacketParam($input);
        if (!$is_script) {
            $packet->judgeAccount();
            $packet->judgeGame();
            $material_permission_validate = $packet->judgeMaterial(true);
        }

        if ($material_permission_validate) {
            $validate_result = [
                'state' => false,
                'error_list' => $material_permission_validate
            ];
        } else {
            $compose_content = $packet->makeADComposeContent($input['script_leader'] ?? '', $input['script_leader_id'] ?? 0);
            $material_validate_result = $compose_content->validateLowMaterial($input['attempt_material_ids'] ?? []);
            if (!$material_validate_result['state']) {
                return $material_validate_result;
            }
            $ios_validate_result = $compose_content->validateIOS($input['ios_sure'] ?? 1);
            if (!$ios_validate_result['state']) {
                return $ios_validate_result;
            }

            $validate_result = $compose_content->composeAlgorithmValidate();
        }

        if ($validate_result['state']) {
            $ad_algorithm = new ADServingComposeAlgorithmLogic();
            $task_create_list = $ad_algorithm->composeAlgorithm(
                $compose_content
            );

            $task_create_list = $ad_algorithm->dispatchADTaskParam($task_create_list, $compose_content->dispatch_type);

            $task_create_list->resetListForBusinessLogic();

            $task_create_list->judgeCreateTaskMaxNum();

            $task_create_list->setADTaskQueueIndex();

            $task_create_list->setLowEffectMaterialAttemptExt($input['attempt_material_ids'] ?? []);

            $task_create_list->init();

            $ad_task_model = new ADTaskModel();
            $ad_task_master_mq_logic = new ADTaskMasterMQLogic();
            $task_list_num = $task_success_insert_num = $task_already_num = 0;

            foreach ($task_create_list->getList() as $task_param) {

                $task_list_num++;
                $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
                if ($input['script_leader'] ?? '') {
                    $task_param->origin_type = 4;
                    $task_param->setExt('origin_intelligent_compose_id', $input['intelligent_compose_id'] ?? 0);
                }
                $task_id = $ad_task_model->addTask($task_param);
                if ($task_id) {
                    $task_param->id = $task_id;
                    $ad_task_master_mq_logic->produceTask($task_param->toMQData());
                    $task_success_insert_num++;
                }
            }

            $compose_result = [
                'state' => true,
                'error_msg' => [],
                'ad_task_array' => [
                    'task_list_num' => $task_list_num,
                    'task_success_insert_num' => $task_success_insert_num,
                    'task_already_num' => $task_already_num,
                    'state' => $task_list_num === $task_success_insert_num + $task_already_num
                ]
            ];
        } else {
            $error_msg = [];
            foreach ($validate_result['error_list'] as $err) {
                $error_msg[$err['type']][] = $err;
            }
            $compose_result = [
                'state' => false,
                'error_msg' => $error_msg,
                'ad_task_array' => []
            ];
        }

        if ($compose_result['state']) {
            return $compose_result;
        } else {
            $compose_result['result'] = 0;
            return $compose_result;
        }
    }

    /**
     * 保存组合包并生成广告任务
     * @param array $input
     * @return array
     * @throws Exception
     */
    public function ADComposeToPacketAndADTask(array $input)
    {
        // 组合包校验
        $input['compose_config']['ignore_warning'] = $input['ignore_warning'];
        $packet = new ADComposePacketParam($input);
        $packet->judgeAccount();
        $packet->judgeGame();
        $material_permission_validate = $packet->judgeMaterial(true);

        if ($material_permission_validate) {
            $validate_result = [
                'state' => false,
                'error_list' => $material_permission_validate
            ];
        } else {
            $compose_packet = $packet->makeADComposeContent();
            $material_validate_result = $compose_packet->validateLowMaterial($input['attempt_material_ids'] ?? []);
            if (!$material_validate_result['state']) {
                return $material_validate_result;
            }
            $ios_validate_result = $compose_packet->validateIOS($input['ios_sure'] ?? 1);
            if (!$ios_validate_result['state']) {
                return $ios_validate_result;
            }
            $validate_result = $compose_packet->composeAlgorithmValidate();
        }

        if (!$validate_result['state']) {
            $error_msg = [];
            foreach ($validate_result['error_list'] as $err) {
                $error_msg[$err['type']][] = $err;
            }
            return [
                'state' => false,
                'error_msg' => $error_msg,
                'ad_task_array' => []
            ];
        }

        MysqlConnection::getConnection('default')->beginTransaction();

        try {
            $ad_compose_packet_model = new ADComposePacketModel();
            if ($packet->id) { // 更新组合包
                $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
                $compose_info = $ad_compose_packet_model->getDataByIdUserList($packet->id, $user_list);
                if (!$compose_info) {
                    throw new Exception('非法操作');
                }
                $ad_compose_packet_model->editADComposePacket((new ADComposePacketParam($input)), $user_list);
            } else { // 新增组合包
                $packet->id = $ad_compose_packet_model->addADComposePacket(new ADComposePacketParam($input));
                if (!$packet->id) {
                    throw new Exception('保存组合包失败');
                }
            }

            $compose_packet->compose_id = $packet->id;

            $compose_packet->compose_config->ignore_warning = $input['ignore_warning'];

            $ad_algorithm = new ADServingComposeAlgorithmLogic();

            $task_create_list = $ad_algorithm->composeAlgorithm(
                $compose_packet
            );

            $task_create_list = $ad_algorithm->dispatchADTaskParam($task_create_list, $compose_packet->dispatch_type);

            $task_create_list->resetListForBusinessLogic();

            $task_create_list->judgeCreateTaskMaxNum();

            $task_create_list->setADTaskQueueIndex();

            $task_create_list->setLowEffectMaterialAttemptExt($input['attempt_material_ids'] ?? []);

            $task_create_list->init();

            $ad_task_model = new ADTaskModel();
            $ad_task_master_mq_logic = new ADTaskMasterMQLogic();
            $task_list_num = $task_success_insert_num = $task_already_num = 0;

            foreach ($task_create_list->getList() as $task_param) {

                $task_list_num++;
                $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
                $task_id = $ad_task_model->addTask($task_param);
                if ($task_id) {
                    $task_param->id = $task_id;
                    $ad_task_master_mq_logic->produceTask($task_param->toMQData());
                    $task_success_insert_num++;
                }
            }

            MysqlConnection::getConnection('default')->commit();

            return [
                'state' => true,
                'error_msg' => [],
                'ad_task_array' => [
                    'task_list_num' => $task_list_num,
                    'task_success_insert_num' => $task_success_insert_num,
                    'task_already_num' => $task_already_num,
                    'state' => $task_list_num === $task_success_insert_num + $task_already_num
                ]
            ];

        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage());
        }
    }

    /**
     * api重启广告任务
     * @param $id
     * @return bool
     */
    public function restartADTaskByApi($id)
    {
        $ad_task_model = new ADTaskModel();
        $task = $ad_task_model->getDataById($id);
        if (!$task) {
            throw new AppException("找不到id是{$id}的广告任务信息");
        }
        if ($task->state_code == ADTaskModel::TASK_SUCCESS) {
            throw new AppException("执行广告任务错误:此任务已经执行完毕，如要再次投放请选择再次投放");
        }
        if ($task->is_stop == 1) {
            throw new AppException("此广告任务为暂停任务，无法重启");
        }

        $task_create_list = (new ADTaskCreateParam());

        $task_param = (new ADTaskParam())->initBySqlData((array)$task);

        $task_create_list->addADTaskParam($task_param);

        $ad_task_model = new ADTaskModel();

        $ad_task_model->updateTask($task_create_list->getList()[0], $task_param->id);

        (new ADTaskMasterMQLogic())->produceTask($task_create_list->getList()[0]->toMQData());

        return true;
    }

    /**
     * api复制广告任务
     * @param $id
     * @param array $data
     * @return int
     */
    public function copyADTaskByApi($id, array $data)
    {
        $ad_task_model = new ADTaskModel();
        $task = $ad_task_model->getDataById($id);

        if ($task->state_code != ADTaskModel::TASK_SUCCESS) {
            throw new AppException('此任务不是成功投放的任务');
        }

        $task_create_list = (new ADTaskCreateParam());

        $task_param = (new ADTaskParam())->initBySqlData((array)$task);

        $task_create_list->addADTaskParam($task_param);

        $task_create_list->init();

        $ad_task_model = new ADTaskModel();

        $task_id = $ad_task_model->addTask($task_create_list->getList()[0]);
        if ($task_id) {
            $task_param->id = $task_id;
            (new ADTaskMasterMQLogic())->produceTask($task_param->toMQData());
            return $task_id;
        } else {
            throw new AppException('复制广告任务失败');
        }
    }

    /**
     * 批量创建
     * @param array $ids
     * @return array
     */
    public function againMakeADTaskByIds(array $ids)
    {
        $task_success_digest_num = 0;
        $task_already_digest_num = 0;
        $task_list_num = 0;

        $ad_task_model = new ADTaskModel();
        $task_list = $ad_task_model->getListByIds($ids);

        if ($task_list->isEmpty()) {
            throw new AppException('此次所选广告任务中找不到数据');
        }

        $task_create_list = (new ADTaskCreateParam());

        foreach ($task_list as $key => $task) {
            $task_list_num++;
            $task_create_list->addADTaskParam((new ADTaskParam())->initBySqlData((array)$task));
        }

        $task_create_list->validateMaterial();

        $task_create_list->init();

        $task_create_list->setADTaskQueueIndex();

        $ad_task_model = new ADTaskModel();
        $ad_task_master_mq_logic = new ADTaskMasterMQLogic();

        foreach ($task_create_list->getList() as $task_param) {

            $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
            $task_id = $ad_task_model->addTask($task_param);
            if ($task_id) {
                $task_param->id = $task_id;
                $ad_task_master_mq_logic->produceTask($task_param->toMQData());
                $task_success_digest_num++;
            }
        }

        return [
            'task_list_num' => $task_list_num,
            'task_success_digest_num' => $task_success_digest_num,
            'task_already_digest_num' => $task_already_digest_num,
            'state' => $task_list_num === $task_success_digest_num + $task_already_digest_num
        ];
    }

    /**
     * 脚本强制重启
     * @param int $id
     * @throws Exception
     */
    public function restartADTaskByIdForScript(int $id)
    {
        $ad_task_model = new ADTaskModel();
        $task = $ad_task_model->getDataById($id);
        if (!$task) {
            throw new AppException("找不到id是{$id}的广告任务信息");
        }

        (new ADTaskMasterMQLogic())->produceTask((new ADTaskParam())->initBySqlData((array)$task)->toMQData());
    }

    public function jumpQueueADTaskByIds(array $ids)
    {
        $ad_task_model = new ADTaskModel();

        $task_list = $ad_task_model->getListByIds($ids);

        if ($task_list->isEmpty()) {
            throw new AppException('此次所选广告任务中找不到数据');
        }

        $task_ids = $task_list
            ->where('state_code', '!=', ADTaskModel::TASK_SUCCESS)
            ->pluck('id')
            ->toArray();
        $task_ids && $ad_task_model->updateTaskStateCodeByIds(ADTaskModel::NO_START, $task_ids);

        foreach ($task_list as $key => $task) {
            (new ADTaskMQLogic())->produceTask(new ADTaskMQDataParam([
                'id' => $task->id,
                'platform' => 'all',
                'media_type' => 'all',
                'media_type_name' => 'all',
                'queue_index' => ((int)$task->id % 10) + 1,
                'creator_id' => $task->creator_id,
            ]));
        }
    }

    /**
     * 批量重启
     * @param array $ids
     * @return array
     */
    public function restartADTaskByIds(array $ids)
    {
        $ad_task_model = new ADTaskModel();

        $task_success_digest_num = 0;
        $task_already_digest_num = 0;
        $task_list_num = 0;

        $task_list = $ad_task_model->getListByIds($ids);

        if ($task_list->isEmpty()) {
            throw new AppException('此次所选广告任务中找不到数据');
        }

        $task_create_list = (new ADTaskCreateParam());

        foreach ($task_list as $key => $task) {
            $task_list_num++;
            if ((int)$task->state_code === ADTaskModel::TASK_SUCCESS) {
                $task_success_digest_num++;
            } else {
                $task_create_list->addADTaskParam((new ADTaskParam())->initBySqlData((array)$task));
            }
        }

        $task_create_list->validateMaterial();

        $task_create_list->setADTaskQueueIndex();

        $task_ids = $task_list
            ->where('state_code', '!=', ADTaskModel::TASK_SUCCESS)
            ->pluck('id')
            ->toArray();
        $task_ids && $ad_task_model->updateTaskStateCodeByIds(ADTaskModel::WAIT_PUSH_QUEUE, $task_ids);
        foreach ($task_create_list->getAccountQueueIndexMap() as $account_id => $queue_index) {
            if ($queue_index > 0) {
                $ad_task_model->updateTaskQueueByAccountIdAndIds($queue_index, $account_id, $task_ids);
            }
        }

        $ad_task_master_mq_logic = (new ADTaskMasterMQLogic());

        foreach ($task_create_list->getList() as $task_param) {

            $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
            $ad_task_master_mq_logic->produceTask($task_param->toMQData());
//            (new ADTaskDigestLogic())->digestTask($task_create_list->getList()[0]);
            $task_success_digest_num++;
        }

        return [
            'task_list_num' => $task_list_num,
            'task_success_digest_num' => $task_success_digest_num,
            'task_already_digest_num' => $task_already_digest_num,
            'state' => $task_list_num === ($task_success_digest_num + $task_already_digest_num)
        ];
    }

    /**
     * 获取广告任务日志列表
     * @param ADTaskLogSearchParam $param
     * @return array
     */
    public function getTaskLogList(ADTaskLogSearchParam $param)
    {
        $ad_task_log_model = new ADTaskLogModel();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        return $ad_task_log_model->getList($param, $user_list);
    }

    /**
     * 获取广告任务列表
     * @param ADTaskSearchParam $param
     * @return array
     */
    public function getADTaskList(ADTaskSearchParam $param)
    {
        $ad_task_model = new ADTaskModel();

        $user_list = [];
        if (!UserService::isSuperManager()) {
            $leader_list = Container::getSession()->get('user_level.dsp.leader_list');
            $leader_agent_list = Container::getSession()->get('user_level.dsp.leader_agent_list');
            /**@var Collection $leader_list */
            /**@var Collection $leader_agent_list */
            if (!UserService::isSuperManager() && $leader_agent_list->isNotEmpty()) {
                foreach ($leader_agent_list as $item) {
                    $leader_list->add((object)[
                        'platform' => $item->platform,
                        'agent_leader' => $item->agent_leader,
                    ]);
                }
            }
            $leader = $leader_list->pluck('agent_leader')->toArray();
            $user_list = array_values(array_unique(array_merge((new UserService())->getUserOptions('dsp')->pluck('name')->toArray(), $leader)));
        }

        $result = $ad_task_model->getList($param, $user_list);
        $list = [];
        foreach ($result['list'] as $info) {
            $list[] = (new ADTaskParam())->initBySqlData((array)$info);
        }
        $result['list'] = $list;
        return $result;
    }

    /**
     * 删除广告任务
     * @param array $ids
     * @return int
     */
    public function deleteADTaskByIds(array $ids)
    {
        return (new ADTaskModel())->deleteADTaskByIds($ids);
    }

    /**
     * 暂停广告任务
     * @param array $ids
     * @return int
     */
    public function stopADTaskByIds(array $ids)
    {
        return (new ADTaskModel())->stopTaskByIds($ids);
    }

    /**
     * 启动广告任务
     * @param array $ids
     * @return array
     */
    public function startADTaskByIds(array $ids)
    {
        $ad_task_model = new ADTaskModel();

        $task_success_digest_num = 0;
        $task_already_digest_num = 0;
        $task_list_num = 0;

        $task_list = $ad_task_model->getListByIds($ids);

        $task_create_list = (new ADTaskCreateParam());

        foreach ($task_list as $key => $task) {
            $task_list_num++;
            if ((int)$task->state_code === ADTaskModel::TASK_SUCCESS) {
                $task_success_digest_num++;
            } else {
                $task_create_list->addADTaskParam((new ADTaskParam())->initBySqlData((array)$task));
            }
        }

        $task_create_list->setADTaskQueueIndex();

        if ($task_list) {
            $task_ids = $task_list->where('task_state', '=', ADTaskModel::TASK_STATE_STOP)->pluck('id')->toArray();
            $ad_task_model->startTaskByIds($task_ids);
            foreach ($task_create_list->getAccountQueueIndexMap() as $account_id => $queue_index) {
                if ($queue_index > 0) {
                    $ad_task_model->updateTaskQueueByAccountIdAndIds($queue_index, $account_id, $task_ids);
                }
            }
        }

        $ad_task_master_mq_logic = (new ADTaskMasterMQLogic());

        foreach ($task_create_list->getList() as $task_param) {
            $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
            $ad_task_master_mq_logic->produceTask($task_param->toMQData());
            $task_success_digest_num++;
        }

        return [
            'task_list_num' => $task_list_num,
            'task_success_digest_num' => $task_success_digest_num,
            'task_already_digest_num' => $task_already_digest_num,
            'state' => $task_list_num === ($task_success_digest_num + $task_already_digest_num)
        ];
    }

    /**
     * 获取队列中消息数量情况
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function getQueueTaskNum($media_type, $platform)
    {
        $queue_message_num_map = (new BatchADTaskQueueTaskNumModel)->getPlatformMediaQueueTaskNumMap($platform, $media_type);
        $queue_message_num_map = array_merge(BatchADTaskProcess::getPlatformMediaQueueNumInitMap($platform, $media_type), $queue_message_num_map);
        $data = [];
        foreach ($queue_message_num_map as $key => $value) {
            $queue_index = explode('queue_', $key);
            $data[$queue_index[1]] = $value;
        }
        return $data;
    }

    // ------------------------------------------------广告盒子----------------------------------------------------------

    /**
     * 生成广告任务
     * @param array $input
     * @return array
     */
    public function ADBoxToTask(array $input)
    {
        $check_result = $this->ADBoxCheck($input);

        if (!$check_result['state']) {
            return $check_result;
        }

        $task_create_list = (new ADTaskCreateParam());

        $task_already_digest_num = 0;
        $task_success_insert_num = 0;
        $task_list_num = 0;

        foreach ($input as $key => $box_param) {
            $task_list_num++;
            $task_create_list->addADTaskParam((new ADTaskParam())->initByFontData((array)$box_param));
        }

        $task_create_list->init();

        $task_create_list->setADTaskQueueIndex();

        $ad_task_model = new ADTaskModel();
        $ad_task_master_mq_logic = new ADTaskMasterMQLogic();

        foreach ($task_create_list->getList() as $task_param) {

            $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
            $task_id = $ad_task_model->addTask($task_param);
            if ($task_id) {
                $task_param->id = $task_id;
                $ad_task_master_mq_logic->produceTask($task_param->toMQData());
                $task_success_insert_num++;
            }
        }

        return [
            'task_list_num' => $task_list_num,
            'task_success_digest_num' => $task_success_insert_num,
            'task_already_digest_num' => $task_already_digest_num,
            'state' => $task_list_num === $task_success_insert_num + $task_already_digest_num
        ];
    }

    /**
     * 校验广告盒子
     * @param array $input
     * @return array
     */
    public function ADBoxCheck(array $input)
    {
        $compose_results = ['result' => []];

        foreach ($this->structBoxParamToCompose($input) as $compose_param) {

            $packet = new ADComposePacketParam($compose_param);

            $compose_content = $packet->makeADComposeContent();

            $validate_result = $compose_content->composeAlgorithmValidate();

            if ($validate_result['state']) {

                $compose_result = [
                    'state' => true,
                ];

            } else {

                $error_msg = [];

                foreach ($validate_result['error_list'] as $err) {
                    $error_msg[$err['type']][] = $err;
                }

                $compose_result = [
                    'state' => false,
                    'error_msg' => $error_msg,
                ];
            }

            // id为当前请求盒子临时序号，前端生成，不做保存
            $compose_results['result'][$packet->id] = $compose_result;
        }

        $compose_results['state'] = !in_array(false, array_column($compose_results['result'], 'state'));

        return $compose_results;
    }

    /**
     * 任务参数转化为组合包参数结构
     * @param array $input
     * @return array
     */
    private function structBoxParamToCompose(array $input)
    {
        $compose_param = [];

        foreach ($input as $box_param) {

            $task_param = (new ADTaskParam())->initByFontData((array)$box_param);

            if ($task_param->creative_mode === BatchAD::CREATIVE_PROGRAM_MODE) {
                $task_param->compose_config->word_num_in_ad = count($task_param->getWordList());
            } else {
                $task_param->compose_config->word_num_in_ad = count($task_param->creative_list);
            }

            $task_param->compose_config->pic_num_in_ad = $task_param->compose_config->video_num_in_ad = 0;
            if ($box_param['group_image_list'] ?? []) {
                $task_param->compose_config->pic_num_in_ad += count($box_param['group_image_list']);
            }
            if ($box_param['group_video_list'] ?? []) {
                $task_param->compose_config->video_num_in_ad += count($box_param['group_video_list']);
            }
            if ($box_param['small_image_list'] ?? []) {
                $task_param->compose_config->pic_num_in_ad += count($box_param['small_image_list']);
            }
            if ($box_param['large_image_list'] ?? []) {
                $task_param->compose_config->pic_num_in_ad += count($box_param['large_image_list']);
            }
            if ($box_param['large_vertical_image_list'] ?? []) {
                $task_param->compose_config->pic_num_in_ad += count($box_param['large_vertical_image_list']);
            }
            if ($box_param['video_list'] ?? []) {
                $task_param->compose_config->video_num_in_ad += count($box_param['video_list']);
            }
            if ($box_param['video_vertical_list'] ?? []) {
                $task_param->compose_config->video_num_in_ad += count($box_param['video_vertical_list']);
            }
            if ($box_param['audio_list'] ?? []) {
                $task_param->compose_config->video_num_in_ad += count($box_param['audio_list']);
            }
            if ($task_param->creative_list[0]['title_list'] ?? []) {
                $task_param->compose_config->creative_word_num_in_ad = count($task_param->creative_list[0]['title_list']);
            } else {
                $task_param->compose_config->creative_word_num_in_ad = 1;
            }

            $compose_param[] = array_merge($task_param->toArray(), [
                'account_list' => [$box_param['account_id']],
                'setting_compose_type' => BatchAD::SETTING_COMPOSE_TYPE_INFO,
                'targeting_compose_type' => BatchAD::TARGETING_COMPOSE_TYPE_INFO,
                'word_list' => $task_param->getWordList() ?? [],
                'group_image_list' => $box_param['group_image_list'] ?? [],
                'group_video_list' => $box_param['group_video_list'] ?? [],
                'small_image_list' => $box_param['small_image_list'] ?? [],
                'large_image_list' => $box_param['large_image_list'] ?? [],
                'large_vertical_image_list' => $box_param['large_vertical_image_list'] ?? [],
                'audio_list' => $box_param['audio_list'] ?? [],
                'video_list' => $box_param['video_list'] ?? [],
                'video_vertical_list' => $box_param['video_vertical_list'] ?? [],
            ]);
        }

        return $compose_param;
    }

    //------------------------------------------------报表筛选----------------------------------------------------------

    /**
     * 报表筛选媒体账号
     * @param array $input
     * @return array
     */
    public function getMediaAccountFilter(array $input)
    {
        $acfp = new ADComposeFilterParam($input);

        $media_account_filter_model = new ADComposeFilterModel();

        $permission_logic = new PermissionLogic();
        $agent_permission = $permission_logic->getLoginUserAgentPermission();
        $game_permission = $permission_logic->getLoginUserGamePermission();

        $result = $media_account_filter_model->getMediaAccountFilter($acfp, $agent_permission, $game_permission);

        return (new ADFilterCalcLogic())->getMediaAccountFilterSumAndFormat($result, $acfp->media_type);
    }

    /**
     * 报表筛选定向
     * @param array $input
     * @return array
     */
    public function getTargetingFilter(array $input)
    {
        $acfp = new ADComposeFilterParam($input);

        $media_targeting_filter_model = new ADComposeFilterModel();

        $permission_logic = new PermissionLogic();
        $agent_permission = $permission_logic->getLoginUserAgentPermission();
        $game_permission = $permission_logic->getLoginUserGamePermission();

        $result = $media_targeting_filter_model->getTargetingFilter($acfp, $agent_permission, $game_permission);

        return (new ADFilterCalcLogic())->getTargetingFilterSumAndFormat($result, $acfp->media_type);
    }

    /**
     * 报表筛选素材文件
     * @param array $input
     * @return array
     */
    public function getMaterialFileFilter(array $input)
    {
        $acfp = new ADComposeFilterParam($input);

        $media_targeting_filter_model = new ADComposeFilterModel();

        $permission_logic = new PermissionLogic();
        $agent_permission = $permission_logic->getLoginUserAgentPermission();
        $game_permission = $permission_logic->getLoginUserGamePermission();
        $material_permission = $permission_logic->getLoginUserMaterialPermission();
        $user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();

        if ($input['media_type']) {
            $media_width_height = (new MaterialModeModel())->getListByMediaFileType(
                $input['media_type'],
                $input['file_type'] ?? ''
            )->map(function ($element) {
                return [$element->width, $element->height];
            });
        } else {
            $media_width_height = [];
        }

        $result = $media_targeting_filter_model->getMaterialFileFilter($acfp, $user_list, $material_permission, $agent_permission, $game_permission, $media_width_height);

        return (new ADFilterCalcLogic())->getMaterialFileFilterSumAndFormat($result, $acfp->media_type);
    }

    /**
     * 报表筛选文案
     * @param array $input
     * @return array
     */
    public function getWordContentFilter(array $input)
    {
        $acfp = new ADComposeFilterParam($input);

        $media_word_filter_model = new ADComposeFilterModel();

        $permission_logic = new PermissionLogic();
        $agent_permission = $permission_logic->getLoginUserAgentPermission();
        $game_permission = $permission_logic->getLoginUserGamePermission();

        $result = $media_word_filter_model->getWordFilter($acfp, $agent_permission, $game_permission);

        return (new ADFilterCalcLogic())->getWordContentFilterSumAndFormat($result, $acfp->media_type);
    }

    /**
     * 获取包的状态
     * @param array $check_task_package_list
     * @return array
     */
    public function getADTaskPackageStatus(array $check_task_package_list)
    {
        $check_list = [];
        foreach ($check_task_package_list as $package_info) {
            if (
                !isset($package_info['platform']) ||
                !isset($package_info['site_id']) ||
                !isset($package_info['task_id']) ||
                !isset($package_info['game_type'])
            ) {
                throw new AppException('查询列表信息丢失!');
            }
            if ($package_info['site_id']) {
                $check_list[$package_info['platform']][$package_info['site_id']] = [
                    'task_id' => $package_info['task_id'],
                    'game_type' => $package_info['game_type'],
                    'state' => $package_info['game_type'] == 'IOS' ? 1 : 0
                ];
            }
        }

        foreach ($check_list as $platform => $info_list) {
            $site_id_list = array_keys($info_list);
            try {
                $result_list = PlatformAD::create($platform)->getAPKStateList($site_id_list, Container::getSession()->name);
                foreach ($result_list as $result) {
                    $check_list[$platform][$result['site_id']]['state'] = $result['state'];
                }
            } catch (Throwable $e) {
                if ($e->getMessage() != '无返回信息') {
                    throw new AppException($e->getMessage(), $e->getCode());
                }
            }
        }

        return $check_list;
    }

    //-------------------------------------------实验室广告任务------------------------------------------------------------

    /**
     * 新增一条实验室广告任务
     * @param array $input
     * @return int
     */
    public function addADLabTask(array $input)
    {
        $ad_lab_task_pa = new ADLabTaskParam($input);
        $id = (new ADLabTaskListModel())->addLabTaskList($ad_lab_task_pa);
        if ($id) {
            $ad_lab_task_pa->id = $id;
            (new ADLabTaskMQLogic())->produceTask($ad_lab_task_pa);
            return $id;
        } else {
            throw new AppException('插入数据失败');
        }
    }

    //-------------------------------------------媒体特殊代码------------------------------------------------------------

    /**
     * 根据定向id推送人群包
     * @param int $targeting_id
     * @param string $platform
     * @param array $to_account_id_list
     * @return array
     */
    public function pushAudienceForAnalysis(int $targeting_id, string $platform, array $to_account_id_list)
    {
        $targeting_data = (new ADTargetingPacketModel())->getDataById($targeting_id);
        if (!$targeting_data) {
            throw new AppException("找不到定向id为{$targeting_id}的信息");
        }
        $targeting_data = (array)$targeting_data;
        $param = new ADTargetingPacketParam([
            'id' => $targeting_data['id'],
            'media_type' => $targeting_data['media_type'],
            'company' => $targeting_data['company'],
            'name' => $targeting_data['name'],
            'md5' => $targeting_data['md5'],
            'update_time' => $targeting_data['update_time'],
            'create_time' => $targeting_data['create_time'],
            'creator' => $targeting_data['creator'],
            'state' => $targeting_data['state'],
            'is_voluntary' => $targeting_data['is_voluntary']
        ]);
        $targeting_data['targeting'] = json_decode($targeting_data['targeting'], true);
        $param->setTargetingByArray($targeting_data['targeting']);

        switch ($targeting_data['media_type']) {
            case MediaType::TENCENT:
                $outside_targeting = [
                    'expand_enabled' => $targeting_data['targeting']['expand_enabled'],
                    'expand_targeting' => $targeting_data['targeting']['expand_targeting'],
                ];
                break;
            default:
                $outside_targeting = [];
        }

        $all_audience_id_list = $param->targeting->getAllAudienceIdList();
        if ($all_audience_id_list) {
            foreach ($to_account_id_list as $account_id) {
                if ($param->media_type == MediaType::TOUTIAO) {
                    $data = (new MediaAccountModel())->getDataByAccountId($account_id, MediaType::TOUTIAO);
                    if (!$data) {
                        throw new AppException("{$account_id}账号为空");
                    }
                    $toutiao_majordomo_id = $data->toutiao_majordomo_id;
                    $media_ad_service = new MediaAD($param->media_type);
                    $need_push_audience_list_data = $media_ad_service->getNeedUploadAudienceList(
                        $all_audience_id_list,
                        $account_id,
                        $platform,
                        $toutiao_majordomo_id
                    );
                } else {
                    $media_ad_service = new MediaAD($param->media_type);
                    $need_push_audience_list_data = $media_ad_service->getNeedUploadAudienceList(
                        $all_audience_id_list,
                        $account_id,
                        $platform,
                        $param->company
                    );
                }
                if ($need_push_audience_list_data->isNotEmpty()) {
                    $audience_account_id_list = $need_push_audience_list_data->pluck('account_id')->toArray();
                    if ($audience_account_id_list) {
                        $media_ad_service->pushAudience(
                            $audience_account_id_list,
                            $need_push_audience_list_data->toArray(),
                            [$account_id]
                        );
                    } else {
                        throw new AppException("暂无账号拥有此人群包的授权权限");
                    }
                }
            }
        }
        return $outside_targeting ? array_merge([
            'targeting' => $param->targeting->getMediaFormatTargetingArray()
        ], $outside_targeting) : [
            'targeting' => $param->targeting->getMediaFormatTargetingArray()
        ];
    }

    /**
     * 获取人群包列表
     * @param array $input
     * @return array
     */
    public function getAudienceListByCondition(array $input)
    {
        $media_ad = new MediaAD($input['media_type']);
        return $media_ad->audienceList(
            $input['company'],
            $input['page'],
            $input['rows'],
            ($input['id'] ?? 0),
            $input['name'] ?? '',
            $input['tag'] ?? '',
            $input['source'] ?? '',
            $input['account_id'] ?? '',
            ['population_type' => $input['population_type'] ?? '']
        );
    }

    /**
     * 获取流量包列表
     * @param array $input
     * @return array
     */
    public function getFlowListByCondition(array $input)
    {
        $media_ad = new MediaAD($input['media_type']);
        return $media_ad->flowList(
            $input['name'] ?? '',
            $input['account_id'] ?? '',
            $input['page'],
            $input['rows']
        );
    }

    /**
     * 获取预估人数
     * @param array $data
     * @return mixed
     */
    public function getTargetAudienceEstimateCount(array $data)
    {
        $media_type = $data['media_type'] ?? 0;
        if (!in_array($data['media_type'], [MediaType::TOUTIAO, MediaType::TENCENT])) {
            throw new AppException('不支持当前媒体类型');
        }
        // 随机出一个媒体账号
        $media_account_model = new MediaAccountModel();
        $account_info = $media_account_model->getRandomDataByMediaType($media_type);
        if (!$account_info) {
            throw new AppException("找不到账号:{$account_info->account_id}的信息");
        }
        $media_ad_service = new MediaAD($media_type);
        return $media_ad_service->getTargetAudienceEstimate(
            ((new ADTargetingPacketParam($data))->setTargetingByArray($data['targeting']))->targeting->toArray(), $account_info
        );
    }


    /**
     * api添加一条快手修改程序化创意的记录
     * @param array $data
     * @return bool
     */
    public function updateKuaishouCreative(array $data)
    {
        $insert_data = [
            'advertiser_id' => $data['advertiser_id'],
            'unit_id' => $data['unit_id'],
            'photo_list' => json_encode($data['photo_list']),
            'create_time' => time()
        ];
        return (new ADUpdateKuaishouCreativeLogModel())->add($insert_data);
    }


    /**
     * 校验ROI的值是否合理
     * @param $roi_ratio
     * @param $roi_standard_value
     * @param int $day
     * @return array
     */
    public function judgeRoiValue($roi_ratio, $roi_standard_value, int $day = 1)
    {
        //如果没有设置roi则不校验
        if (empty($roi_standard_value)) {
            return [];
        }

        $error_msg_list = [];
        foreach ($roi_ratio as $roi) {
            $key = "day_{$day}_standard_value";
            $roi_standard_value = $roi_standard_value->$key;
            if ($roi < $roi_standard_value * 0.4) {
                $error_msg_list[] = [
                    'error_type' => 'warning',
                    'type' => 'warning_msg',
                    'key' => 'rai_ratio',
                    'value' => $roi,
                    'msg' => sprintf("您当前设置的roi系数为%s，比标准值%s低超60%%，建议roi系数不低于%s,请再次确认", json_encode($roi_ratio), $roi_standard_value, $roi_standard_value * 0.4)
                ];
                break;
            }
        }
        return $error_msg_list;
    }

    /**
     * 验证外部机器学习程序调用接口
     * @param Input $input
     * @param $app_secret
     * @return array
     */
    public function verifyOuterRequestForMachineLearning(Input $input, $app_secret)
    {
        if ($input['app_secret'] !== $app_secret) {
            throw new AppException('非法请求');
        }
        //目前只允许操作头条
        $input['media_type'] = MediaType::TOUTIAO;
        if (
            isset(
                ApiAccessUserInfo::USER_INFO[$input['app_secret']],
                ApiAccessUserInfo::USER_INFO[$input['app_secret']][$input['editor_id']]
            )) {
            $input['editor_name'] = ApiAccessUserInfo::USER_INFO[$input['app_secret']][$input['editor_id']];
        } else {
            throw new AppException("修改者ID: {$input['editor_id']} 不存在");
        }

        $list = (new MediaAccountModel())->getListByAgentLeader([$input['media_type']], $input['editor_name'])->keyBy('account_id');
        $account_ids = collect($input['update'])->pluck('account_id');

        if ($no_permission = $account_ids->diff($list->keys())->toArray()) {
            $no_permission = json_encode($no_permission);
            throw new AppException("账号ID: {$no_permission}, {$input['creator']} 无权限操作");
        }


        $input_data = $input->getData();
        $input_data['leader_permission'] = -1;

        return $input_data;
    }

    /**
     * 检查账号负责人权限
     * @param $media_type
     * @param Collection $account_ids
     * @param string $agent_leader_name
     */
    public function checkAgentLeaderPermission($media_type, Collection $account_ids, string $agent_leader_name)
    {
        $list = (new MediaAccountModel())->getListByAgentLeader([$media_type], $agent_leader_name)->keyBy('account_id');

        if ($no_permission = $account_ids->diff($list->keys())->toArray()) {
            $no_permission = json_encode($no_permission);
            throw new AppException("账号ID: {$no_permission}, {$agent_leader_name} 无权限操作");
        }
    }

    /**
     * 获取任务错误方案映射列表
     * @param $input
     * @return mixed
     */
    public function getTaskSolutionList($input)
    {
        return (new ADTaskSolutionModel())->getList(
            (new ADTaskSolutionParam($input)), $input['rows'], $input['page']);
    }

    /**
     * 获取任务错误方案映射数据
     * @param $input
     * @return Collection
     */
    public function getTaskSolutionData($input)
    {
        return (new ADTaskSolutionModel())->getData(
            (new ADTaskSolutionParam($input))
        );
    }

    /**
     * 新增任务错误方案映射
     * @param array $input
     * @return int
     */
    public function addTaskSolution(array $input)
    {
        unset($input['creator']);
        unset($input['creator_id']);
        return (new ADTaskSolutionModel())->add((new ADTaskSolutionParam($input)));
    }

    /**
     * 编辑任务错误方案映射
     * @param array $input
     * @return int
     */
    public function editTaskSolution(array $input)
    {
        unset($input['creator']);
        unset($input['creator_id']);
        return (new ADTaskSolutionModel())->edit((new ADTaskSolutionParam($input)));
    }

    /**
     * 删除任务错误方案映射
     * @param array $input
     * @return int
     */
    public function deleteTaskSolution(array $input)
    {
        return (new ADTaskSolutionModel())->delete($input['id']);
    }

    /**
     * @param array $input
     * @return bool
     */
    public function recordLowEffectAttemptLog(array $input)
    {
        $media_type = $input['media_type'];
        $game_id = $input['game_id'];
        $platform = $input['platform'];
        $attempt_data = $input['attempt_data'];
        $un_attempt_data = $input['un_attempt_data'];

        if (!$attempt_data && !$un_attempt_data) {
            throw new AppException("非法操作");
        }

        $material_file_md5_map = (new OdsMaterialFileLogModel())->getListByID(array_merge($attempt_data, $un_attempt_data))->keyBy('signature')->toArray();

        $material_filter = MaterialInferiorFilterService::inferiorMaterial($media_type, $platform, $game_id, array_keys($material_file_md5_map));

        $insert_data = [];
        foreach ($material_filter as $data) {
            $insert_data[] = [
                'media_type' => $media_type,
                'clique_id' => $data->clique_id,
                'clique_name' => $data->clique_name,
                'platform' => $platform,
                'game_id' => $game_id,
                'signature' => $data->signature,
                'reject_reason' => $data->reject_reason,
                'account_leader' => Container::getSession()->name,
                'is_force_reject' => $data->is_force,
                'is_attempt' => in_array($material_file_md5_map[$data->signature]->id, $attempt_data) ? 1 : 0,
                'ext' => $data->ext,
            ];
        }

        $model = new OdsMaterialLowEffectAttemptLog();
        $chunks = array_chunk($insert_data, 50);
        foreach ($chunks as $chunk) {
            $model->batchInsert($chunk);
        }

        return true;
    }

    /**
     * @param array $data
     * @return int
     */
    public function addHotMaterialPacket(array $data)
    {
        return (new ADHotMaterialPacketModel())->addHotMaterialPacket(new ADHotMaterialPacketParam($data));
    }

    public function editHotMaterialPacket($id, array $data)
    {
        return (new ADHotMaterialPacketModel())->editHotMaterialPacket($id, new ADHotMaterialPacketParam($data));
    }

    public function deleteHotMaterialPacket(array $ids)
    {
        return (new ADHotMaterialPacketModel())->deleteHotMaterialPacket($ids);
    }

    public function getHotMaterialPacketList(array $data)
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        return (new ADHotMaterialPacketModel())->getList($data, $user_list);
    }

    /**
     * @param array $data
     * @return int
     */
    public function addStarMaterialPacket(array $data): int
    {
        return (new ADStarMaterialPacketModel())->addStarMaterialPacket(new ADStarMaterialPacketParam($data));
    }

    public function editStarMaterialPacket($id, array $data): bool
    {
        return (new ADStarMaterialPacketModel())->editStarMaterialPacket($id, new ADStarMaterialPacketParam($data));
    }

    public function deleteStarMaterialPacket(array $ids): bool
    {
        return (new ADStarMaterialPacketModel())->deleteStarMaterialPacket($ids);
    }

    public function realTimeGetStarMaterial($account_id)
    {
        (new ToutiaoTaskModel())->getToutiaoStarVideo($account_id);
    }

    public function getStarMaterialPacketList(array $data): array
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        return (new ADStarMaterialPacketModel())->getList($data, $user_list);
    }

    public function getADIntelligentComposeList(ADIntelligentComposeSearchParam $param): array
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $data = (new ADIntelligentComposeModel())->getList($param, $user_list);
        foreach ($data['list'] as $value) {
            $value->relate_root_game_id_list = json_decode($value->relate_root_game_id_list, true);
            $value->relate_clique_game_id_list = json_decode($value->relate_clique_game_id_list, true);
            $value->top_material_filter = json_decode($value->top_material_filter, true);
            $value->potential_material_filter = json_decode($value->potential_material_filter, true);
            $value->new_material_filter = json_decode($value->new_material_filter, true);
            $value->rule_list = json_decode($value->rule_list, true);
            $value->data_media_type_list = json_decode($value->data_media_type_list, true);
            $value->create_week_day_list = json_decode($value->create_week_day_list, true);
            $value->custom_material_packet_list = json_decode($value->custom_material_packet_list, true);
        }
        return $data;
    }

    public function saveADIntelligentCompose($id, $name): int
    {
        $data = (new ADIntelligentComposeModel())->getDataById($id);
        if (!$data) {
            throw new AppException('找不到智创组合信息');
        }
        $param = (new ADIntelligentComposeParam())->initBySql($data);
        $param->name = $name;
        $param->creator = Container::getSession()->name;;
        return (new ADIntelligentComposeModel())->addADIntelligentCompose($param);
    }

    public function stopIntelligentCompose($id): int
    {
        return (new ADIntelligentComposeModel())->stopIntelligentCompose($id);
    }

    public function startIntelligentCompose($id): int
    {
        return (new ADIntelligentComposeModel())->startIntelligentCompose($id);
    }

    public function updateADIntelligentCompose($id, ADIntelligentComposeParam $param): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $compose_data = (new ADComposePacketModel())->getDataByIdUserList($param->compose_id, []);
        if ($compose_data) {
            $param->media_type = $compose_data->media_type;
        }
        return (new ADIntelligentComposeModel())->editADIntelligentComposer($id, $param, $user_list);
    }

    public function addADIntelligentCompose(ADIntelligentComposeParam $param): int
    {
        $compose_data = (new ADComposePacketModel())->getDataByIdUserList((int)$param->compose_id, []);
        if ($compose_data) {
            $param->media_type = $compose_data->media_type;
        }
        return (new ADIntelligentComposeModel())->addADIntelligentCompose($param);
    }

    public function deleteADIntelligentCompose($id): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        return (new ADIntelligentComposeModel())->deleteADIntelligentCompose($id, $user_list);
    }

    public function previewIntelligentComposeMaterialFilter(
        $compose_id, $filter_data_media_type, $filter_content,
        $filter_data_is_relate_root_game_id, $filter_relate_root_game_id_list,
        $filter_data_is_relate_clique_game_id, $filter_relate_clique_game_id_list
    ): array
    {
        $compose_info = (new ADComposePacketModel())->getDataByIdUserList($compose_id, []);

        $root_game_id_list = [];
        $clique_game_id_list = [];

        if ($filter_data_is_relate_root_game_id) {
            if ($filter_data_is_relate_root_game_id == 1) {
                $site_config_data = json_decode($compose_info->site_config, true);
                if ($site_config_data['game_id'] ?? 0) {

                    $root_game_info = (new V2DimGameIdModel())->getDataByGameId($compose_info->platform, $site_config_data['game_id']);
                    if ($root_game_info) {
                        $root_game_id_list = [$root_game_info->root_game_id];
                    }
                }
            }
            if ($filter_data_is_relate_root_game_id == 2) {
                $root_game_id_list = $filter_relate_root_game_id_list;
            }
        }

        if ($filter_data_is_relate_clique_game_id) {
            if ($filter_data_is_relate_clique_game_id == 1) {
                $site_config_data = json_decode($compose_info->site_config, true);
                if ($site_config_data['game_id'] ?? 0) {

                    $clique_game_info = (new V2DimGameIdModel())->getDataByGameId($compose_info->platform, $site_config_data['game_id']);
                    if ($clique_game_info) {
                        $clique_game_id_list = [$clique_game_info->clique_id];
                    }
                }
            }
            if ($filter_data_is_relate_clique_game_id == 2) {
                $clique_game_id_list = $filter_relate_clique_game_id_list;
            }
        }

        $compose_packet = (new ADComposePacketParam([
            'creator' => $compose_info->creator,
            'creator_id' => $compose_info->creator_id
        ]))->initBySqlData((array)$compose_info);

        $compose_content = $compose_packet->makeADComposeContent($compose_info->creator, $compose_info->creator_id);

        $model = new ADComposeFilterModel();

        $permission_logic = new PermissionLogic();

        $agent_game_permission_data = $permission_logic->getDataPermissionByUserId($compose_info->creator_id);
        $material_user_permission_data = $permission_logic->getMaterialPermissionAndUserOptionsByUserId($compose_info->creator_id);


        $agent_permission = $agent_game_permission_data['agent_permission'];
        $game_permission = $agent_game_permission_data['game_permission'];

        $material_permission = $material_user_permission_data['material_permission'];
        $user_list = $material_user_permission_data['user_list']->pluck('name')->toArray();

        $material_result = $model->getIntelligentComposeMaterialFilter(
            $filter_content,
            $filter_data_media_type,
            $root_game_id_list,
            $clique_game_id_list,
            $compose_info->platform,
            $user_list,
            $material_permission,
            $agent_permission,
            $game_permission,
            $compose_content->getMaterialFileNormalList(),
        );

        if ($filter_content['expand_filter'] && $material_result['data']->isNotEmpty()) {
            $material_file_ids = $material_result['data']->pluck('material_file_id')->toArray();
            foreach ($filter_content['expand_filter'] as $ef) {
                $ef['filter_file_type'] = $filter_content['filter_file_type'];
                $ef['filter_mode'] = $filter_content['filter_mode'];
                $ef['material_theme_ids'] = $filter_content['material_theme_ids'];
                $ef['back_material_ids'] = $filter_content['back_material_ids'];
                $material_result = $model->getIntelligentComposeMaterialFilter(
                    $ef,
                    $filter_data_media_type,
                    $root_game_id_list,
                    $clique_game_id_list,
                    $compose_info->platform,
                    $user_list,
                    $material_permission,
                    $agent_permission,
                    $game_permission,
                    $compose_content->getMaterialFileNormalList(),
                    $material_file_ids
                );
                $material_file_ids = $material_result['data']->pluck('material_file_id')->toArray();
            }
        }

        $material_list = $material_result['data'];
        $material_file_id_list = $material_list->pluck('material_file_id')->toArray();
        $expert_filter_result = MaterialExpertFilterService::incorrectExpert($compose_info->creator, $compose_info->media_type, $compose_info->platform, $material_file_id_list);
        $illegal_material_map = array_filter($expert_filter_result, function ($ele) {
            return $ele['status'];
        });
        if ($illegal_material_map) {
            $illegal_material_file_ids = array_keys($illegal_material_map);
            $after_filter_material_list = collect();
            foreach ($material_list as $file_info) {
                if (in_array($file_info->material_file_id, $illegal_material_file_ids)) {
                    $after_filter_material_list->push($file_info);
                }
            }
            $material_result['data'] = $after_filter_material_list;
        }

        return [
            'list' => $material_result['data'],
            'sql' => $material_result['sql'],
            'total' => count($material_result)
        ];
    }

    private function runOperator($left_value, $operator, $right_value): bool
    {
        switch ($operator) {
            case '>':
                return $left_value > $right_value;
            case '<':
                return $left_value < $right_value;
            case '>=':
                return $left_value >= $right_value;
            case '<=':
                return $left_value <= $right_value;
            case '=':
                return $left_value == $right_value;
            case '!=':
                return $left_value != $right_value;
            case 'between':
                if (is_array($right_value) && count($right_value) == 2) {
                    return ($left_value >= $right_value[0] && $left_value <= $right_value[1]);
                } else {
                    throw new AppException('error right_value');
                }
            case 'in':
                if (is_array($right_value)) {
                    return in_array($left_value, $right_value);
                } else {
                    throw new AppException('error right_value');
                }
            default:
                throw new AppException('error operator');
        }
    }

    private function intelligentComposeMaterialFilter(ADComposePacketParam $compose_param, ADIntelligentComposeParam $param, $material_normal_list): array
    {
        $model = new ADComposeFilterModel();


        $permission_logic = new PermissionLogic();

        $agent_game_permission_data = $permission_logic->getDataPermissionByUserId($compose_param->creator_id);
        $material_user_permission_data = $permission_logic->getMaterialPermissionAndUserOptionsByUserId($compose_param->creator_id);


        $agent_permission = $agent_game_permission_data['agent_permission'];
        $game_permission = $agent_game_permission_data['game_permission'];

        $material_permission = $material_user_permission_data['material_permission'];
        $user_list = $material_user_permission_data['user_list']->pluck('name')->toArray();

        $all_video = [];

        $root_game_id_list = [];
        if ($param->data_is_relate_root_game_id) {
            if ($param->data_is_relate_root_game_id == 1) {
                if ($compose_param->site_config['game_id'] ?? 0) {
                    $root_game_info = (new V2DimGameIdModel())->getDataByGameId($compose_param->platform, $compose_param->site_config['game_id']);
                    if ($root_game_info) {
                        $root_game_id_list = [$root_game_info->root_game_id];
                    }
                }
            }

            if ($param->data_is_relate_root_game_id == 2) {
                $root_game_id_list = $param->relate_root_game_id_list;
            }
        }

        $clique_game_id_list = [];
        if ($param->data_is_relate_clique_game_id) {
            if ($param->data_is_relate_clique_game_id == 1) {
                if ($compose_param->site_config['game_id'] ?? 0) {
                    $clique_game_info = (new V2DimGameIdModel())->getDataByGameId($compose_param->platform, $compose_param->site_config['game_id']);
                    if ($clique_game_info) {
                        $clique_game_id_list = [$clique_game_info->clique_id];
                    }
                }
            }

            if ($param->data_is_relate_clique_game_id == 2) {
                $clique_game_id_list = $param->relate_clique_game_id_list;
            }
        }


        switch ($param->material_compose_type) {
            case 'all':
            case 'auto':
                $is_get_top_material = true;
                $is_get_potential_material = true;
                $is_get_new_material = true;
                $is_get_custom_material = true;
                break;
            case 'top':
                $is_get_top_material = true;
                $is_get_potential_material = false;
                $is_get_new_material = false;
                $is_get_custom_material = false;
                break;
            case 'potential':
                $is_get_top_material = false;
                $is_get_potential_material = true;
                $is_get_new_material = false;
                $is_get_custom_material = false;
                break;
            case 'new':
                $is_get_top_material = false;
                $is_get_potential_material = false;
                $is_get_new_material = true;
                $is_get_custom_material = false;
                break;
            case 'custom':
                $is_get_top_material = false;
                $is_get_potential_material = false;
                $is_get_new_material = false;
                $is_get_custom_material = true;
                break;
            case 'top_potential':
                $is_get_top_material = true;
                $is_get_potential_material = true;
                $is_get_new_material = false;
                $is_get_custom_material = false;
                break;
            case 'top_new':
                $is_get_top_material = true;
                $is_get_potential_material = false;
                $is_get_new_material = true;
                $is_get_custom_material = false;
                break;
            case 'potential_new':
                $is_get_top_material = false;
                $is_get_potential_material = true;
                $is_get_new_material = true;
                $is_get_custom_material = false;
                break;
            case 'custom_top':
                $is_get_top_material = true;
                $is_get_potential_material = false;
                $is_get_new_material = false;
                $is_get_custom_material = true;
                break;
            case 'custom_potential':
                $is_get_top_material = false;
                $is_get_potential_material = true;
                $is_get_new_material = false;
                $is_get_custom_material = true;
                break;
            case 'custom_new':
                $is_get_top_material = false;
                $is_get_potential_material = false;
                $is_get_new_material = true;
                $is_get_custom_material = true;
                break;
            default:
                $is_get_top_material = false;
                $is_get_potential_material = false;
                $is_get_new_material = false;
                $is_get_custom_material = false;
                break;
        }
        $top_material_result = ['data' => [], 'sql' => ""];
        $potential_material_result = ['data' => [], 'sql' => ""];
        $new_material_result = ['data' => [], 'sql' => ""];
        $custom_material_result = ['data' => collect(), 'sql' => ""];

        if ($is_get_top_material) {
            $filter_content = $param->top_material_filter;

            $top_material_result = $model->getIntelligentComposeMaterialFilter(
                $filter_content,
                $param->data_media_type_list,
                $root_game_id_list,
                $clique_game_id_list,
                $compose_param->platform,
                $user_list,
                $material_permission,
                $agent_permission,
                $game_permission,
                $material_normal_list
            );

            if ($filter_content['expand_filter'] && $top_material_result['data']->isNotEmpty()) {
                $material_file_ids = $top_material_result['data']->pluck('material_file_id')->toArray();
                foreach ($filter_content['expand_filter'] as $ef) {
                    $ef['filter_file_type'] = $filter_content['filter_file_type'];
                    $ef['filter_mode'] = $filter_content['filter_mode'];
                    $ef['material_theme_ids'] = $filter_content['material_theme_ids'];
                    $ef['back_material_ids'] = $filter_content['back_material_ids'];

                    $top_material_result = $model->getIntelligentComposeMaterialFilter(
                        $ef,
                        $param->data_media_type_list,
                        $root_game_id_list,
                        $clique_game_id_list,
                        $compose_param->platform,
                        $user_list,
                        $material_permission,
                        $agent_permission,
                        $game_permission,
                        $material_normal_list,
                        $material_file_ids
                    );
                    $material_file_ids = $top_material_result['data']->pluck('material_file_id')->toArray();
                }
            }

            if ($param->material_compose_type == "auto") {
                if ($top_material_result['data']->isNotEmpty()) {
                    $is_get_potential_material = false;
                    $is_get_new_material = false;
                }
            }


        }

        if ($is_get_potential_material) {

            $filter_content = $param->potential_material_filter;

            $potential_material_result = $model->getIntelligentComposeMaterialFilter(
                $filter_content,
                $param->data_media_type_list,
                $root_game_id_list,
                $clique_game_id_list,
                $compose_param->platform,
                $user_list,
                $material_permission,
                $agent_permission,
                $game_permission,
                $material_normal_list
            );

            if ($filter_content['expand_filter'] && $potential_material_result['data']->isNotEmpty()) {
                $material_file_ids = $potential_material_result['data']->pluck('material_file_id')->toArray();
                foreach ($filter_content['expand_filter'] as $ef) {
                    $ef['filter_file_type'] = $filter_content['filter_file_type'];
                    $ef['filter_mode'] = $filter_content['filter_mode'];
                    $ef['material_theme_ids'] = $filter_content['material_theme_ids'];
                    $ef['back_material_ids'] = $filter_content['back_material_ids'];
                    $potential_material_result = $model->getIntelligentComposeMaterialFilter(
                        $ef,
                        $param->data_media_type_list,
                        $root_game_id_list,
                        $clique_game_id_list,
                        $compose_param->platform,
                        $user_list,
                        $material_permission,
                        $agent_permission,
                        $game_permission,
                        $material_normal_list,
                        $material_file_ids
                    );
                    $material_file_ids = $potential_material_result['data']->pluck('material_file_id')->toArray();
                }
            }

            if ($param->material_compose_type == "auto") {
                if ($potential_material_result['data']->isNotEmpty()) {
                    $is_get_new_material = false;
                }
            }

        }

        if ($is_get_new_material) {

            $filter_content = $param->new_material_filter;

            $new_material_result = $model->getIntelligentComposeMaterialFilter(
                $filter_content,
                $param->data_media_type_list,
                $root_game_id_list,
                $clique_game_id_list,
                $compose_param->platform,
                $user_list,
                $material_permission,
                $agent_permission,
                $game_permission,
                $material_normal_list
            );

            if ($filter_content['expand_filter'] && $new_material_result['data']->isNotEmpty()) {
                $material_file_ids = $new_material_result['data']->pluck('material_file_id')->toArray();
                foreach ($filter_content['expand_filter'] as $ef) {
                    $ef['filter_file_type'] = $filter_content['filter_file_type'];
                    $ef['filter_mode'] = $filter_content['filter_mode'];
                    $ef['material_theme_ids'] = $filter_content['material_theme_ids'];
                    $ef['back_material_ids'] = $filter_content['back_material_ids'];
                    $new_material_result = $model->getIntelligentComposeMaterialFilter(
                        $ef,
                        $param->data_media_type_list,
                        $root_game_id_list,
                        $clique_game_id_list,
                        $compose_param->platform,
                        $user_list,
                        $material_permission,
                        $agent_permission,
                        $game_permission,
                        $material_normal_list,
                        $material_file_ids
                    );
                    $material_file_ids = $new_material_result['data']->pluck('material_file_id')->toArray();
                }
            }

        }

        $custom_material_packet_list = $param->custom_material_packet_list;

        if ($is_get_custom_material && $custom_material_packet_list) {
            $material_file_list = collect();
            $material_packet_list = (new ADMaterialPacketModel())->getListById($custom_material_packet_list);
            if ($material_packet_list->isNotEmpty()) {
                foreach ($material_packet_list as $material_packet) {
                    $material_file_list = $material_file_list->merge(array_map(function ($ele) {
                        $ele['material_file_id'] = $ele['id'];
                        return (object)$ele;
                    }, json_decode($material_packet->video_vertical_list, true) ?: []));
                    $material_file_list = $material_file_list->merge(array_map(function ($ele) {
                        $ele['material_file_id'] = $ele['id'];
                        return (object)$ele;
                    }, json_decode($material_packet->video_list, true) ?: []));
                    $material_file_list = $material_file_list->merge(array_map(function ($ele) {
                        $ele['material_file_id'] = $ele['id'];
                        return (object)$ele;
                    }, json_decode($material_packet->large_image_list, true) ?: []));
                    $material_file_list = $material_file_list->merge(array_map(function ($ele) {
                        $ele['material_file_id'] = $ele['id'];
                        return (object)$ele;
                    }, json_decode($material_packet->large_vertical_image_list, true) ?: []));
                    $material_file_list = $material_file_list->merge(array_map(function ($ele) {
                        $ele['material_file_id'] = $ele['id'];
                        return (object)$ele;
                    }, json_decode($material_packet->group_image_list, true) ?: []));
                }

                $material_file_list = $material_file_list->unique('material_file_id');

                $material_file_list = $material_file_list->filter(function ($ele) use ($material_normal_list) {
                    foreach ($material_normal_list as $item) {
                        if ($item[0] != '_') {
                            if (isset($ele->{$item[0]})) {
                                $result = $this->runOperator($ele->{$item[0]}, $item[1], $item[2]);
                                if (!$result) {
                                    return false;
                                }
                            }
                        } else {
                            $sub_result = true;
                            foreach ($item[2] as $cond_list) {
                                foreach ($cond_list as $k => $v) {
                                    if (is_array($v)) {
                                        if (isset($ele->{$k})) {
                                            $result = $this->runOperator($ele->{$k}, 'between', $v);
                                            $sub_result = $sub_result || $result;
                                        }
                                    } else {
                                        $result = $this->runOperator($ele->{$k}, '=', $v);
                                        $sub_result = $sub_result || $result;
                                    }
                                }
                            }
                            if (!$sub_result) {
                                return false;
                            }
                        }
                    }
                    return true;
                });

                $custom_material_result['data'] = $material_file_list;
                $custom_material_result['sql'] = "素材包id : " . json_encode($custom_material_packet_list);
            }
        }

        $result = [
            'top_material_result' => $top_material_result,
            'potential_material_result' => $potential_material_result,
            'new_material_result' => $new_material_result,
            'custom_material_result' => $custom_material_result,
        ];

        foreach ($result as &$material_result) {
            if ($material_result['data']) {
                $material_list = $material_result['data'];
                $material_file_id_list = $material_list->pluck('material_file_id')->toArray();
                $expert_filter_result = MaterialExpertFilterService::incorrectExpert($compose_param->creator, $compose_param->media_type, $compose_param->platform, $material_file_id_list);
                $illegal_material_map = array_filter($expert_filter_result, function ($ele) {
                    return $ele['status'];
                });
                if ($illegal_material_map) {
                    $illegal_material_file_ids = array_keys($illegal_material_map);
                    $after_filter_material_list = collect();
                    foreach ($material_list as $file_info) {
                        if (in_array($file_info->material_file_id, $illegal_material_file_ids)) {
                            $after_filter_material_list->push($file_info);
                        }
                    }
                    $material_result['data'] = $after_filter_material_list;
                }
                $all_video = array_merge($all_video, $material_result['data']->toArray());
            }
        }

        $all_video = array_values(array_filter($all_video, function ($ele) {
            return $ele->file_type == MaterialFileModel::FILE_TYPE_VIDEO;
        }));

        $result['all_video'] = $all_video;

        return $result;
    }

    private function getIntelligentComposeVideoCover(array $video_list): array
    {
        $cover_info_map = [];
        $cover_info_name_map = [];

        foreach ($video_list as $video_info) {
            // 查找 ".mp4" 在字符串中最后出现的位置
            $pos = strrpos($video_info->url, '.mp4');
            $cover_url = substr_replace($video_info->url, "_creative_6.jpg", $pos, strlen('.mp4'));
            $cover_info_map[$video_info->material_file_id] = [
                'url' => $cover_url
            ];
            $cover_name_array = explode('/', $cover_url);
            $cover_name = array_pop($cover_name_array);
            $cover_info_name_map[$cover_name] = $video_info->material_file_id;
        }

        $material_file_info_list = (new MaterialFileModel())->getListByNameList(array_keys($cover_info_name_map));
        foreach ($material_file_info_list as $cover_info) {
            $cover_info_map[$cover_info_name_map[$cover_info->filename]] = $cover_info;
        }
        return $cover_info_map;
    }

    public function webExecIntelligentCompose($intelligentComposeId)
    {
        $intelligent_compose_model = new ADIntelligentComposeModel();

        $intelligent_compose = $intelligent_compose_model->getDataById($intelligentComposeId);

        $this->execIntelligentCompose($intelligentComposeId, (new ADIntelligentComposeParam())->initBySql($intelligent_compose));
    }

    public function previewIntelligentCompose($intelligentComposeId): array
    {
        $intelligent_compose_model = new ADIntelligentComposeModel();

        $intelligent_compose = $intelligent_compose_model->getDataById($intelligentComposeId);

        return $this->execIntelligentCompose($intelligentComposeId, (new ADIntelligentComposeParam())->initBySql($intelligent_compose), true);
    }

    public function execIntelligentCompose($intelligentComposeId, ADIntelligentComposeParam $param, bool $is_return = false): array
    {
        $log_model = new ADIntelligentComposeLogModel();

        $log_param = new ADIntelligentComposeLogParam($param->toLogData());
        $log_param->intelligent_compose_id = $intelligentComposeId;

        $compose_info = (new ADComposePacketModel())->getDataByIdUserList($param->compose_id, []);

        if (!($compose_info->targeting_compose_type == 0 && $compose_info->setting_compose_type == 0)) {
            // 查不出数据直接退出复执 也不在执行任何创建
            if (!$is_return) {
                echo date('Y-m-d H:i:s') . " 智创ID=$intelligentComposeId " . ' 广告组合中 " 参数包组合类型 " 和 " 定向包组合类型 " 不是 " 填充 "';
            }
            return [];
        }

        $compose_packet = (new ADComposePacketParam([
            'creator' => $compose_info->creator,
            'creator_id' => $compose_info->creator_id
        ]))->initBySqlData((array)$compose_info);

        $compose_content = $compose_packet->makeADComposeContent($compose_info->creator, $compose_info->creator_id);

        $return_list = [];

        for ($repeated_times = 1; $repeated_times <= $param->repeated_creation_times; $repeated_times++) {
            if (!$is_return) {
                $log_model->add($log_param->setComposeContent($compose_packet->toParam())->setProp([
                    'action' => ADIntelligentComposeLogModel::ACTION_CONFIRM_COMPOSE
                ]));
            }

            /**
             * $material_map 结构如下
             * [
             * 'top_material_result' => $top_material_result,
             * 'potential_material_result' => $potential_material_result,
             * 'new_material_result' => $new_material_result
             * ];
             */
            $material_map = $this->intelligentComposeMaterialFilter($compose_packet, $param, $compose_content->getMaterialFileNormalList());

            $task_create_list = new ADTaskCreateParam();

            $material_num_config = $param->getMaterialNumConfig();

            $creative_material_list = [];

            $mix_material_keys = [];

            switch ($param->material_compose_type) {
                case 'all':
                case 'auto':
                    foreach (['top_material_result', 'potential_material_result', 'new_material_result', 'custom_material_result'] as $key) {
                        if ($material_map[$key]['data']->isNotEmpty()) {

                            if (!$is_return) {
                                $log_model->add($log_param->setProp([
                                    'action' => ADIntelligentComposeLogModel::ACTION_MATERIAL_MAP[$key],
                                    'sql_text' => $material_map[$key]['sql'],
                                    'data' => $material_map[$key]['data'],
                                ]));
                            }

                            $material_data_array = $material_map[$key]['data']->toArray();
                            $len = 1;
                            $material_num = $param->material_num;
                            if ($material_map[$key]['data']->count() >= $param->material_num) {
                                $len = ceil($material_map[$key]['data']->count() / $param->material_num);
                            } else {
                                $material_num = $material_map[$key]['data']->count();
                            }

                            for ($i = 0; $i < $len; $i++) {
                                $creative = [];
                                for ($num = 1; $num <= $material_num; $num++) {
                                    $creative[] = current($material_data_array);
                                    if (next($material_data_array) === false) {
                                        // 如果到达数组的末尾，重置数组指针到开头
                                        reset($material_data_array);
                                    }
                                }

                                $creative_material_list[] = $creative;
                            }
                            if ($param->material_compose_type == 'auto' && $creative_material_list) {
                                break;
                            }
                        }
                    }
                    break;
                case 'top':
                case 'potential':
                case 'new':
                case 'custom':
                    $key = "{$param->material_compose_type}_material_result";
                    $material_num = $material_num_config[$param->material_compose_type];

                    if (!$is_return) {
                        $log_model->add($log_param->setProp([
                            'action' => ADIntelligentComposeLogModel::ACTION_MATERIAL_MAP[$key],
                            'sql_text' => $material_map[$key]['sql'],
                            'data' => $material_map[$key]['data'],
                        ]));
                    }

                    if ($material_map[$key]['data']->isNotEmpty()) {
                        $material_data_array = $material_map[$key]['data']->toArray();
                        $len = 1;
                        if ($material_map[$key]['data']->count() >= $material_num) {
                            $len = ceil($material_map[$key]['data']->count() / $material_num);
                        } else {
                            $material_num = $material_map[$key]['data']->count();
                        }
                        for ($i = 0; $i < $len; $i++) {
                            $creative = [];
                            for ($num = 1; $num <= $material_num; $num++) {
                                $creative[] = current($material_data_array);
                                if (next($material_data_array) === false) {
                                    // 如果到达数组的末尾，重置数组指针到开头
                                    reset($material_data_array);
                                }
                            }
                            $creative_material_list[] = $creative;
                        }
                    }
                    break;
                case 'top_potential':
                    $mix_material_keys = ["top", "potential"];
                    break;
                case 'top_new':
                    $mix_material_keys = ["top", "new"];
                    break;
                case 'potential_new':
                    $mix_material_keys = ["potential", "new"];
                    break;
                case 'custom_top':
                    $mix_material_keys = ["custom", "top"];
                    break;
                case 'custom_potential':
                    $mix_material_keys = ["custom", "potential"];
                    break;
                case 'custom_new':
                    $mix_material_keys = ["custom", "new"];
                    break;
            }

            if ($mix_material_keys) {

                $total_material_num = 0;
                $material_data_array_map = [];
                foreach ($mix_material_keys as $key) {
                    $sub_key = "{$key}_material_result";
                    $total_material_num += $material_map[$sub_key]['data']->count();
                    $material_data_array_map[$sub_key] = $material_map[$sub_key]['data']->toArray();
                    if (!$is_return) {
                        $log_model->add($log_param->setProp([
                            'action' => ADIntelligentComposeLogModel::ACTION_MATERIAL_MAP[$sub_key],
                            'sql_text' => $material_map[$sub_key]['sql'],
                            'data' => $material_map[$sub_key]['data'],
                        ]));
                    }
                }

                $len = 1;
                if ($total_material_num >= $param->material_num) {
                    $len = ceil($total_material_num / $param->material_num);
                } else {
                    $material_num_config = $param->getMaterialNumConfig($total_material_num);
                }


                for ($i = 0; $i < $len; $i++) {
                    $creative = [];
                    foreach ($mix_material_keys as $key) {
                        $sub_material_num = $material_num_config[$key];
                        $sub_key = "{$key}_material_result";
                        if ($material_map[$sub_key]['data']->isNotEmpty()) {
                            if ($material_map[$sub_key]['data']->count() < $sub_material_num) {
                                $sub_material_num = $material_map[$sub_key]['data']->count();
                            }
                            for ($sub_i = 0; $sub_i < $sub_material_num; $sub_i++) {
                                $creative[] = current($material_data_array_map[$sub_key]);
                                if (next($material_data_array_map[$sub_key]) === false) {
                                    // 如果到达数组的末尾，重置数组指针到开头
                                    reset($material_data_array_map[$sub_key]);
                                }
                            }
                        }
                    }
                    if ($creative) {
                        $creative_material_list[] = $creative;
                    }
                }
            }

            if (!$is_return) {
                $log_model->add($log_param->setProp([
                    'action' => ADIntelligentComposeLogModel::ACTION_MAKE_CREATIVE_LIST,
                    'data' => $creative_material_list,
                ]));
            }

            if (!$creative_material_list) {
                // 查不出数据直接退出复执 也不在执行任何创建
                break;
            }

            $cover_info_map = $this->getIntelligentComposeVideoCover($material_map['all_video']);

            $account_list = $compose_content->account_list->account_list;

            $word_list = [];
            if ($param->word_select_type == 0) {
                $word_list = $compose_content->word_list->getWordContent();
            } else {
                $word_packet = (new ADWordPacketModel())->getDataById($param->custom_word_packet_id);
                if ($word_packet) {
                    $word_list = json_decode($word_packet->content_list, true);
                }
            }

            if (!$word_list) {
                // 没有文案直接退出复执 也不在执行任何创建
                break;
            }

            foreach ($compose_content->account_list->account_list as $account_info) {
                foreach ($creative_material_list as $creative_data) {

                    $word_content_list = [];
                    for ($i = 0; $i < $param->word_num; $i++) {
                        $word_content_list[] = current($word_list);
                        if (next($word_list) === false) {
                            // 如果到达数组的末尾，重置数组指针到开头
                            reset($word_list);
                        }
                    }

                    $creative_list = new ADComposeCreativeListParam();
                    if ($compose_content->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
                        foreach ($creative_data as $creative) {
                            if ($creative->file_type == MaterialFileModel::FILE_TYPE_VIDEO && empty($cover_info_map[$creative->material_file_id]->id)) {
                                echo $creative->material_file_id . "找不到封面" . PHP_EOL;
                            }

                            if ($creative->file_type == MaterialFileModel::FILE_TYPE_VIDEO) {
                                $creative_list->addVideoToList(
                                    '',
                                    new ADComposeCreativeVideoCoverParam([
                                        'id' => $cover_info_map[$creative->material_file_id]->id,
                                        'url' => $cover_info_map[$creative->material_file_id]->url,
                                        'width' => $creative->width,
                                        'height' => $creative->height,
                                    ]),
                                    new ADComposeCreativeVideoParam([
                                        'id' => $creative->material_file_id,
                                        'url' => $creative->url,
                                        'width' => $creative->width,
                                        'height' => $creative->height,
                                    ])
                                );
                            } elseif ($creative->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                                $creative_list->addImageToList(
                                    '',
                                    new ADComposeCreativeImageParam([
                                        'id' => $creative->material_file_id,
                                        'url' => $creative->url,
                                        'width' => $creative->width,
                                        'height' => $creative->height,
                                        'image_list' => []
                                    ])
                                );
                            } elseif (in_array($creative->file_type, [
                                MaterialFileModel::FILE_TYPE_COMBINE3,
                                MaterialFileModel::FILE_TYPE_COMBINE4,
                                MaterialFileModel::FILE_TYPE_COMBINE6,
                                MaterialFileModel::FILE_TYPE_COMBINE9
                            ])) {
                                $creative_list->addImageToList(
                                    '',
                                    (new ADComposeCreativeImageParam([
                                        'id' => $creative->material_file_id,
                                        'url' => $creative->url,
                                        'width' => $creative->width,
                                        'height' => $creative->height,
                                        'image_list' => []
                                    ]))->initImageList()
                                );

                            }
                        }
                    } else {
                        foreach ($word_content_list as $word) {
                            foreach ($creative_data as $creative) {

                                if ($creative->file_type == MaterialFileModel::FILE_TYPE_VIDEO && empty($cover_info_map[$creative->material_file_id]->id)) {
                                    echo $creative->material_file_id . "找不到封面" . PHP_EOL;
                                }

                                if ($creative->file_type == MaterialFileModel::FILE_TYPE_VIDEO) {
                                    $creative_list->addVideoToList(
                                        $word,
                                        new ADComposeCreativeVideoCoverParam([
                                            'id' => $cover_info_map[$creative->material_file_id]->id,
                                            'url' => $cover_info_map[$creative->material_file_id]->url,
                                            'width' => $creative->width,
                                            'height' => $creative->height,
                                        ]),
                                        new ADComposeCreativeVideoParam([
                                            'id' => $creative->material_file_id,
                                            'url' => $creative->url,
                                            'width' => $creative->width,
                                            'height' => $creative->height,
                                        ])
                                    );
                                } elseif ($creative->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                                    $creative_list->addImageToList(
                                        $word,
                                        new ADComposeCreativeImageParam([
                                            'id' => $creative->material_file_id,
                                            'url' => $creative->url,
                                            'width' => $creative->width,
                                            'height' => $creative->height,
                                            'image_list' => []
                                        ])
                                    );
                                } elseif (in_array($creative->file_type, [
                                    MaterialFileModel::FILE_TYPE_COMBINE3,
                                    MaterialFileModel::FILE_TYPE_COMBINE4,
                                    MaterialFileModel::FILE_TYPE_COMBINE6,
                                    MaterialFileModel::FILE_TYPE_COMBINE9
                                ])) {
                                    $creative_list->addImageToList(
                                        $word,
                                        (new ADComposeCreativeImageParam([
                                            'id' => $creative->material_file_id,
                                            'url' => $creative->url,
                                            'width' => $creative->width,
                                            'height' => $creative->height,
                                            'image_list' => []
                                        ]))->initImageList()
                                    );

                                }


                            }
                        }
                    }


                    $compose_account_info = null;
                    if ($param->dispatch_type == 'account') {
                        $compose_account_info = current($account_list);
                        if (next($account_list) === false) {
                            // 如果到达数组的末尾，重置数组指针到开头
                            reset($account_list);
                        }
                    } else {
                        $compose_account_info = $account_info;
                    }

                    if ($compose_content->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
                        $task_create_list->addADTaskParam(
                            (new ADTaskParam($compose_content->getTaskBaseData()))->initByCompose(
                                $compose_account_info['account_id'],
                                $compose_account_info['account_name'],
                                $creative_list,
                                $word_content_list,
                                '填充',
                                $compose_content->targeting_list->targeting_info_list[0]->targeting,
                                '填充',
                                $compose_content->setting_list->setting_info_list[0]->setting,
                                $compose_content->site_config->toArray(),
                                $compose_content->compose_config,
                                $compose_content->other_setting,
                                $param->rule_list,
                                $compose_content->rta_list
                            )
                        );
                    } else {
                        $task_create_list->addADTaskParam(
                            (new ADTaskParam($compose_content->getTaskBaseData()))->initByCompose(
                                $compose_account_info['account_id'],
                                $compose_account_info['account_name'],
                                $creative_list,
                                [],
                                '填充',
                                $compose_content->targeting_list->targeting_info_list[0]->targeting,
                                '填充',
                                $compose_content->setting_list->setting_info_list[0]->setting,
                                $compose_content->site_config->toArray(),
                                $compose_content->compose_config,
                                $compose_content->other_setting,
                                $param->rule_list,
                                $compose_content->rta_list
                            )
                        );
                    }
                }

                // 如果是账号平均分配的话，在第一次循环已经分配完了
                if ($param->dispatch_type == 'account') {
                    break;
                }
            }

            $task_create_list->setADTaskQueueIndex();

            $task_create_list->init();

            $ad_task_model = new ADTaskModel();
            $ad_task_master_mq_logic = new ADTaskMasterMQLogic();

            $ad_task_id_list = [];

            foreach ($task_create_list->getList() as $key => $task_param) {

                $task_param->queue_index = ((int)($task_create_list->getAccountQueueIndexMap()[$task_param->account_id] ?? 0));
                $task_param->origin_type = 4;
                $task_param->setExt('origin_intelligent_compose_id', $intelligentComposeId);
                if (!$is_return) {
                    $task_id = $ad_task_model->addTask($task_param);
                    if ($task_id) {
                        $task_param->id = $task_id;
                        $ad_task_id_list[] = $task_param->id;
                        $ad_task_master_mq_logic->produceTask($task_param->toMQData());
                    }
                } else {
                    $task_param->id = $key + 1;
                    $return_list[] = $task_param;
                }
            }

            if (!$is_return) {
                $log_model->add($log_param->setProp([
                    'action' => ADIntelligentComposeLogModel::ACTION_MAKE_AD_TASK_LIST,
                    'data' => $ad_task_id_list,
                ]));
            }
        }
        return $return_list;
    }

    public function getIntelligentComposeLog(ADIntelligentComposeLogSearchParam $param): array
    {
        $log_model = new ADIntelligentComposeLogModel();
        return $log_model->getList($param);
    }

    public function webPreviewIntelligentMonitorExecBodyConditionSql($media_type, $target_type, $condition)
    {
        $condition_time_range = (new IntelligentMonitorToutiaoV2())->getDateRange($condition['time_range'], $condition['time_range_type'], $condition['time_range_unit']);
        $bind_target_param = new ADIntelligentMonitorBindParam([
            'target_type' => $target_type,
            'target_value' => '1',
        ]);
        switch ($media_type) {
            case MediaType::TOUTIAO:
                return (new ADIntelligentMonitorToutiaoV2Model())->getConditionSqlData($condition, $condition_time_range, [$bind_target_param], Container::getSession()->platform_list[0] ?? 'TW', [1], true)['sql'];
            case MediaType::TENCENT:
                return (new ADIntelligentMonitorTencentV3Model())->getConditionSqlData($condition, $condition_time_range, [$bind_target_param], Container::getSession()->platform_list[0] ?? 'TW', [1], true)['sql'];
            case MediaType::KUAISHOU:
                return (new ADIntelligentMonitorKuaishouModel())->getConditionSqlData($condition, $condition_time_range, [$bind_target_param], Container::getSession()->platform_list[0] ?? 'TW', [1], true)['sql'];
            default:
                return '';
        }
    }

    public function getIntelligentMonitorExecBody(ADIntelligentMonitorExecBodySearchParam $param): array
    {
        $user_list = UserService::isSuperManager() ? [] : ['智创模板', Container::getSession()->name];

        $exec_body_model = new ADIntelligentMonitorExecBodyModel();

        $data = $exec_body_model->getList($param, $user_list);

        foreach ($data['list'] as $value) {
            $value->exec_body = json_decode($value->exec_body, true);
            $value->success_action = json_decode($value->success_action, true);
            $value->fail_action = json_decode($value->fail_action, true);
        }

        return $data;
    }

    public function addIntelligentMonitorExecBody(ADIntelligentMonitorExecBodyParam $param): int
    {
        $exec_body_model = new ADIntelligentMonitorExecBodyModel();
        return $exec_body_model->addADIntelligentMonitorExecBody($param);
    }

    public function updateIntelligentMonitorExecBody($id, ADIntelligentMonitorExecBodyParam $param): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorExecBodyModel();
        return $exec_body_model->updateIntelligentMonitorExecBody($id, $param, $user_list);
    }

    public function deleteIntelligentMonitorExecBody($id): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorExecBodyModel();
        return $exec_body_model->deleteADIntelligentMonitorExecBody($id, $user_list);
    }

    public function getIntelligentMonitorRobot(ADIntelligentMonitorRobotSearchParam $param): array
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];

        $exec_body_model = new ADIntelligentMonitorRobotModel();

        $data = $exec_body_model->getList($param, $user_list);

        foreach ($data['list'] as $value) {
            $value->exec_body_list = json_decode($value->exec_body_list, true);
        }

        return $data;
    }

    public function addIntelligentMonitorRobot(ADIntelligentMonitorRobotParam $param): int
    {
        $exec_body_model = new ADIntelligentMonitorRobotModel();
        return $exec_body_model->addADIntelligentMonitorRobot($param);
    }

    public function updateIntelligentMonitorRobot($id, ADIntelligentMonitorRobotParam $param): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorRobotModel();
        return $exec_body_model->updateIntelligentMonitorRobot($id, $param, $user_list);
    }

    public function deleteIntelligentMonitorRobot($id): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorRobotModel();
        $update_result = $exec_body_model->deleteADIntelligentMonitorRobot($id, $user_list);

        if ($update_result) {
            return (new ADIntelligentMonitorBindModel())->unbindByRobotoId($id);
        } else {
            return 0;
        }
    }

    public function stopIntelligentMonitorRobot($id): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorRobotModel();
        return $exec_body_model->stopIntelligentMonitorRobot($id, $user_list);
    }

    public function startIntelligentMonitorRobot($id): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new ADIntelligentMonitorRobotModel();
        return $exec_body_model->startIntelligentMonitorRobot($id, $user_list);
    }


    public function getIntelligentComposeConditionPacket(ADIntelligentComposeConditionSearchParam $param): array
    {
        $user_list = UserService::isSuperManager() ? [] : ['智创模板', Container::getSession()->name];
        $data = (new ADIntelligentComposeConditionModel())->getList($param, $user_list);
        foreach ($data['list'] as $value) {
            $value->condition = json_decode($value->condition, true);
        }
        return $data;
    }

    public function addIntelligentComposeConditionPacket($data): int
    {
        return (new ADIntelligentComposeConditionModel())->addCondition(new ADIntelligentComposeConditionParam($data));
    }

    public function editIntelligentComposeConditionPacket($id, $data): int
    {
        return (new ADIntelligentComposeConditionModel())->editCondition($id, new ADIntelligentComposeConditionParam($data), Container::getSession()->name);
    }

    public function deleteIntelligentComposeConditionPacket($id): int
    {
        return (new ADIntelligentComposeConditionModel())->deleteCondition($id, Container::getSession()->name);
    }

    public function getIntelligentMonitorBindLog(ADIntelligentMonitorBindLogSearchParam $param)
    {
        return (new ADIntelligentMonitorBindModel())->getList($param);
    }

    public function getIntelligentMonitorRobotLog(ADIntelligentMonitorRobotLogSearchParam $param)
    {
        $data = (new ADIntelligentMonitorRobotLogModel())->getList($param);
        foreach ($data['list'] as $value) {
            $value->action = json_decode($value->action, true);
            $value->condition_body_content = json_decode($value->condition_body_content, true);
            $value->condition_logic_result = json_decode($value->condition_logic_result, true);
        }

        return $data;
    }

    /**
     * @param $robot_id
     * @param $bind_type
     * @param $bind_target_type
     * @param array $bind_target_value_list
     * @param $target_type
     * @param array $target_value_list
     * @return int
     */
    public function bindRobotByTargetValueList($robot_id, $bind_type, $bind_target_type, array $bind_target_value_list, $target_type, array $target_value_list): int
    {
        $robot_info = (new ADIntelligentMonitorRobotModel())->getDataById($robot_id);

        if (!$robot_info) {
            throw new AppException('找不到绑定机器人信息');
        } else {
            if (!UserService::isSuperManager() && $robot_info->creator != Container::getSession()->name) {
                throw new AppException('绑定机器人信息错误');
            }
        }

        $ad_intelligent_monitor_bind_model = new ADIntelligentMonitorBindModel();

        $list = [];

        if ($bind_type == 'precise') {
            $target_value_list = array_values(array_unique($target_value_list));

            $already_list = $ad_intelligent_monitor_bind_model->getAlreadyBindValueById($robot_id, $target_type, $target_value_list, 'target');

            if ($already_list->isNotEmpty()) {
                $already_list_value_array = $already_list->pluck('target_value')->toArray();
                $target_value_list = array_values(array_filter($target_value_list, function ($item) use ($already_list_value_array) {
                    return !in_array($item, $already_list_value_array);
                }));
            }

            if (!$target_value_list) {
                throw new AppException('本次去重绑定后为空');
            }

            foreach ($target_value_list as $target_value) {
                $list[] = (new ADIntelligentMonitorBindParam([
                    'media_type' => $robot_info->media_type,
                    'media_agent_type' => $robot_info->media_agent_type,
                    'bind_type' => 'precise',
                    'bind_target_type' => $target_type,
                    'bind_target_value' => $target_value,
                    'target_type' => $target_type,
                    'target_value' => $target_value,
                    'monitor_robot_id' => $robot_id,
                    'first_exec_time' => date("Y-m-d H:i:s", strtotime("+$robot_info->delay_exec_hour hours")),
                    'creator' => Container::getSession()->name
                ]))->toSqlData();
            }
        } else {
            $bind_target_value_list = array_values(array_unique($bind_target_value_list));

            $already_list = $ad_intelligent_monitor_bind_model->getAlreadyBindValueById($robot_id, $bind_target_type, $bind_target_value_list, 'bind');

            if ($already_list->isNotEmpty()) {
                $already_list_value_array = $already_list->pluck('bind_target_value')->toArray();
                $bind_target_value_list = array_values(array_filter($bind_target_value_list, function ($item) use ($already_list_value_array) {
                    return !in_array($item, $already_list_value_array);
                }));
            }

            if (!$bind_target_value_list) {
                throw new AppException('本次去重绑定后为空！');
            }

            foreach ($bind_target_value_list as $bind_target_value) {
                $list[] = (new ADIntelligentMonitorBindParam([
                    'media_type' => $robot_info->media_type,
                    'media_agent_type' => $robot_info->media_agent_type,
                    'bind_type' => 'range',
                    'bind_target_type' => $bind_target_type,
                    'bind_target_value' => $bind_target_value,
                    'target_type' => $target_type,
                    'target_value' => '',
                    'monitor_robot_id' => $robot_id,
                    'first_exec_time' => date("Y-m-d H:i:s", strtotime("+$robot_info->delay_exec_hour hours")),
                    'creator' => Container::getSession()->name
                ]))->toSqlData();
            }
        }

        if ($bind_type == 'loop') {
            // 绑定新目标后需要清楚裂变锁
            (new IntelligentRobotFrequencyModel())->delFrequencyLockNumForRobot($robot_info->id, $bind_target_type, $target_type);
        }

        return $ad_intelligent_monitor_bind_model->bindList($list);
    }

    public function bindRobotByLogId($log_id)
    {
        $ad_intelligent_monitor_bind_model = new ADIntelligentMonitorBindModel();
        $bind_data = $ad_intelligent_monitor_bind_model->getDataById($log_id);

        if (!$bind_data) {
            throw new AppException('找不到绑定记录');
        } else {
            if (!in_array($bind_data->status, [2, 3])) {
                throw new AppException('绑定记录错误');
            }

            if (!UserService::isSuperManager() && $bind_data->creator != Container::getSession()->name) {
                throw new AppException('绑定记录错误!');
            }
        }

        $robot_info = (new ADIntelligentMonitorRobotModel())->getDataById($bind_data->monitor_robot_id);

        if (!$robot_info) {
            throw new AppException('找不到绑定机器人信息');
        } else {
            if (!UserService::isSuperManager() && $robot_info->creator != Container::getSession()->name) {
                throw new AppException('绑定机器人信息错误');
            }
        }

        $bind_data->unbind_target_value_list = '[]';
        $bind_data->finish_target_value_list = '[]';
        $bind_data->first_exec_time = date("Y-m-d H:i:s", strtotime("+$robot_info->delay_exec_hour hours"));
        $bind_data->status = ADIntelligentMonitorBindModel::RUNNING_STATUS;
        $bind_data->creator = Container::getSession()->name;
        $bind_param = (new ADIntelligentMonitorBindParam())->initBySqlData($bind_data);

        $ad_intelligent_monitor_bind_model->bind($bind_param);

        if ($bind_data->bind_type == 'loop') {
            // 绑定新目标后需要清楚裂变锁
            (new IntelligentRobotFrequencyModel())->delFrequencyLockNumForRobot($robot_info->id, $bind_data->bind_target_type, $bind_data->target_type);
        }

        return $bind_data;
    }

    public function unbindRobotByLogId($log_id)
    {
        $ad_intelligent_monitor_bind_model = new ADIntelligentMonitorBindModel();
        $bind_data = $ad_intelligent_monitor_bind_model->getDataById($log_id);

        if (!$bind_data) {
            throw new AppException('找不到绑定记录');
        } else {
            if ($bind_data->status != 1) {
                throw new AppException('绑定记录错误');
            }

            if ($bind_data->creator != Container::getSession()->name) {
                throw new AppException('绑定记录错误!');
            }
        }

        $ad_intelligent_monitor_bind_model->unbind([$log_id]);

        return $bind_data;
    }

    public function batchUnbindIntelligentMonitorBind(array $ids)
    {
        $ad_intelligent_monitor_bind_model = new ADIntelligentMonitorBindModel();
        return $ad_intelligent_monitor_bind_model->unbind($ids);
    }

    public function batchUpdateIntelligentComposeCrateADWeek(array $ids, array $weeks)
    {
        return (new ADIntelligentComposeModel())->updateCreateADWeek($ids, $weeks);
    }

    public function batchUpdateIntelligentComposeCrateADTime(array $ids, $time)
    {
        return (new ADIntelligentComposeModel())->updateCreateADTime($ids, $time);
    }

    public function batchStartADIntelligentCompose(array $ids)
    {
        return (new ADIntelligentComposeModel())->batchStartADIntelligentCompose($ids);
    }

    public function batchEndADIntelligentCompose(array $ids)
    {
        return (new ADIntelligentComposeModel())->batchEndADIntelligentCompose($ids);
    }

    public function batchUpdateCustomMaterialPacket(array $ids, $custom_material_packet_list)
    {
        return (new ADIntelligentComposeModel())->batchUpdateCustomMaterialPacket($ids, json_encode($custom_material_packet_list));
    }

    public function batchUpdateIntelligentComposeMaterialComposeType(array $ids, $custom_material_packet_id)
    {
        return (new ADIntelligentComposeModel())->batchUpdateIntelligentComposeMaterialComposeType($ids, $custom_material_packet_id);
    }

    public function batchUpdateIntelligentComposeMaterialCondition(array $ids, $condition)
    {
        $model = (new ADIntelligentComposeModel());
        $list = $model->getListByIds($ids);

        $error = '';

        foreach ($list as $compose) {
            $compose->top_material_filter = json_decode($compose->top_material_filter, true);
            $compose->potential_material_filter = json_decode($compose->potential_material_filter, true);
            $compose->new_material_filter = json_decode($compose->new_material_filter, true);

            if ($condition['top']['material_theme_ids'] ?? false) {
                $compose->top_material_filter['material_theme_ids'] = $condition['top']['material_theme_ids'];
            }

            if ($condition['top']['back_material_ids'] ?? false) {
                $compose->top_material_filter['back_material_ids'] = $condition['top']['back_material_ids'];
            }

            if ($condition['potential']['material_theme_ids'] ?? false) {
                $compose->potential_material_filter['material_theme_ids'] = $condition['potential']['material_theme_ids'];
            }

            if ($condition['potential']['back_material_ids'] ?? false) {
                $compose->potential_material_filter['back_material_ids'] = $condition['potential']['back_material_ids'];
            }

            if ($condition['new']['material_theme_ids'] ?? false) {
                $compose->new_material_filter['material_theme_ids'] = $condition['new']['material_theme_ids'];
            }

            if ($condition['new']['back_material_ids'] ?? false) {
                $compose->new_material_filter['back_material_ids'] = $condition['new']['back_material_ids'];
            }

            $result = (new ADIntelligentComposeModel())->batchUpdateADIntelligentComposeMaterialFilter(
                $compose->id,
                json_encode($compose->top_material_filter, JSON_UNESCAPED_UNICODE),
                json_encode($compose->potential_material_filter, JSON_UNESCAPED_UNICODE),
                json_encode($compose->new_material_filter, JSON_UNESCAPED_UNICODE),
            );

            if (!$result) {
                $error .= ($compose->id . '更新失败;');
            }
        }

        if ($error) {
            throw new AppException($error);
        }
        return true;
    }

    public function batchDeleteADIntelligentCompose(array $ids)
    {
        return (new ADIntelligentComposeModel())->batchDeleteADIntelligentCompose($ids);
    }

    public function batchUpdateMaterialWordNum(array $ids, $material_num, $word_num)
    {
        return (new ADIntelligentComposeModel())->batchUpdateMaterialWordNum($ids, $material_num, $word_num);
    }

    public function batchUpdateDispatchType(array $ids, $dispatch_type)
    {
        return (new ADIntelligentComposeModel())->batchUpdateDispatchType($ids, $dispatch_type);
    }
}
