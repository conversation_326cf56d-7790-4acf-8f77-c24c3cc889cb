<?php

namespace App\Logic\DMS;

use App\Constant\BusinessUnitAndProjectTeamMap;
use App\Constant\ProfitStatementSqlMap;
use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Constant\RoutePermission;
use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\Database\ZDBuilder;
use App\Model\SqlModel\Tanwan\DwdGameFinancialProfitLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\CpMoneyDivideConfigDetailModel;
use App\Model\SqlModel\Zeda\CpMoneyDivideConfigModel;
use App\Model\SqlModel\Zeda\GameCpMoneyLogModel;
use App\Model\SqlModel\Zeda\V2DimGameIdModel as ZxV2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameMultipleLogModel;
use App\Model\SqlModel\Zeda\GamePayMoneyModel;
use App\Model\SqlModel\Zeda\OtherCostDetailModel;
use App\Model\SqlModel\Zeda\OtherCostModel;
use App\Model\SqlModel\Zeda\ProjectTeamModel;
use App\Model\SqlModel\Zeda\ProjectTeamRootGameModel;
use App\MysqlConnection;
use App\Param\DMS\CpDivideConfigListParam;
use App\Param\DMS\CpDivideConfigParam;
use App\Param\DMS\GameMultipleListParam;
use App\Param\DMS\OtherCostDetailListExportParam;
use App\Param\DMS\OtherCostDetailListParam;
use App\Param\DMS\OtherCostConfigExportParam;
use App\Param\DMS\ProfitStatementListParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Utils\DateTool;
use App\Utils\DimensionTool;
use App\Utils\FrontendTool;
use App\Utils\Math;
use Common\EnvConfig;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use NXP\Exception\DivisionByZeroException;
use NXP\MathExecutor;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ProfitStatementLogic
{
    /**
     * @param ProfitStatementListParam $param
     * @return array
     */
    public function profitStatementList(ProfitStatementListParam $param): array
    {
        $model = new DwdGameFinancialProfitLogModel();
        $data = $model->getProfitStatementList($param);

        // 累计字段的合计处理
//        $total_column_sum_data = $this->getTotalColumnSum($data['list'], $param);

        // 初始化合计数据
        $sum = ['tdate' => '合计', 'run_date' => '合计'];
        $keys = [];
        if ($data['list']->isNotEmpty()) {
            $keys = array_keys((array)$data['list']->first());
            foreach ($keys as $key) {
                if (!in_array($key, $param->target_raw) && !in_array($key, $param->compute_children_target) && !in_array($key, $param->customized_target_column_collect)) {
                    continue;
                }

                $sum[$key] = 0;
            }
        }

        $list = [];
        foreach ($data['list'] as $item) {
            $item = (array)$item;
            foreach ($item as $key => $value) {
                if (!in_array($key, $param->target_raw) && !in_array($key, $param->compute_children_target) && !in_array($key, $param->customized_target_column_collect)) {
                    continue;
                }

                if (strpos($key, '_rate') !== FALSE) {
                    // 比例类的都用百分比显示,且保留小数点后两位
                    $item[$key] = round($value * 100, 2) . '%';
                } elseif (in_array($key, ProfitStatementSqlMap::DECIMAL_TWO)) {
                    // 产品倍数和综合倍数保留两位,其余都用整数
                    $item[$key] = Math::decimal($value, 2);
                } else {
                    $item[$key] = round($value);
                }

            }
            // 合计
            foreach ($keys as $key) {
                if (!in_array($key, $param->target_raw) && !in_array($key, $param->compute_children_target) && !in_array($key, $param->customized_target_column_collect)) {
                    continue;
                }

                if (strpos($key, '_rate') !== FALSE) {
                    continue;
                }

                if (is_numeric($sum[$key])) {
                    $sum[$key] += $item[$key];

                    // 小数位转换
                    if (strpos($key, '_rate') !== FALSE) {
                        $sum[$key] = round($sum[$key] * 100, 2) . '%';
                    } elseif (in_array($key, ProfitStatementSqlMap::DECIMAL_TWO)) {
                        $sum[$key] = Math::decimal($sum[$key], 2);
                    }
                }
            }

            // 自定义指标
            if ($param->customized_target) {
                $this->customizedTarget($param->customized_target, $item);
            }

            $list[] = $item;
        }

        // 合计项的计算列
        isset($param->compute_target['total_multiple']) && $sum['total_multiple'] = $sum['total_first_day_pay_money'] > 0 ? Math::decimal($sum['total_pre_day_360_pay'] / $sum['total_first_day_pay_money'], 2) : 0;
        isset($param->compute_target['current_multiple']) && $sum['current_multiple'] = $sum['first_day_pay_money'] > 0 ? Math::decimal($sum['reg_total_pay_money'] / $sum['first_day_pay_money'], 2) : 0;
        isset($param->compute_target['applet_rate']) && $sum['applet_rate'] = $sum['applet_os_money'] > 0 ? Math::decimal($sum['applet_divide_money'] / $sum['applet_os_money'], 2) : 0 . '%';
        isset($param->compute_target['ios_rate']) && $sum['ios_rate'] = $sum['ios_pay_money'] > 0 ? Math::decimal($sum['apple_divide_money'] / $sum['ios_pay_money'], 2) : 0 . '%';
        isset($param->compute_target['cp_rate']) && $sum['cp_rate'] = $sum['total_pay_money'] > 0 ? Math::decimal($sum['cp_money'] / $sum['total_pay_money'], 2) : 0 . '%';
        isset($param->compute_target['dehan_rate']) && $sum['dehan_rate'] = $sum['dehan_pay_money'] > 0 ? Math::decimal($sum['dehan_divide_money'] / $sum['dehan_pay_money'], 2) : 0 . '%';
        isset($param->compute_target['total_rate']) && $sum['total_rate'] = $sum['total_pay_money'] > 0 ? Math::decimal(($sum['applet_divide_money'] + $sum['apple_divide_money'] + $sum['cp_money'] + $sum['dehan_divide_money'] + $sum['pay_way_ip_money']) / $sum['total_pay_money'], 2) : 0 . '%';
        isset($param->compute_target['share_total_rate']) && $sum['share_total_rate'] = $sum['total_pay_money'] > 0 ? Math::decimal(($sum['share_applet_divide_money'] + $sum['share_apple_divide_money'] + $sum['cp_money'] + $sum['dehan_divide_money'] + $sum['pay_way_ip_money']) / $sum['total_pay_money'], 2) : 0 . '%';
        isset($param->compute_target['total_divide_money']) && $sum['total_divide_money'] = Math::decimal(($sum['applet_divide_money'] + $sum['apple_divide_money'] + $sum['cp_money'] + $sum['dehan_divide_money'] + $sum['pay_way_ip_money']), 2);
        isset($param->compute_target['share_total_divide_money']) && $sum['share_total_divide_money'] = Math::decimal(($sum['share_applet_divide_money'] + $sum['share_apple_divide_money'] + $sum['cp_money'] + $sum['dehan_divide_money'] + $sum['pay_way_ip_money']), 2);
        isset($param->compute_target['other_cost']) && $sum['other_cost'] = Math::decimal(($sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost']), 2);
        isset($param->compute_target['profit_rate']) && $sum['profit_rate'] = $sum['cost_money'] > 0 ? Math::decimal($sum['current_month_profit'] / $sum['cost_money'], 2) : 0 . '%';
        isset($param->compute_target['multiple']) && $sum['multiple'] = $sum['first_day_pay_money'] > 0 ? Math::decimal($sum['pre_day_360_pay'] / $sum['first_day_pay_money'], 2) : 0;
        isset($param->compute_target['pre_profit_rate']) && $sum['pre_profit_rate'] = $sum['cost_money'] > 0 ? Math::decimal($sum['pre_day_360_profit'] / $sum['cost_money'], 2) : 0 . '%';
        isset($param->compute_target['pre_day_360_profit_exclude_other_cost']) && $sum['pre_day_360_profit_exclude_other_cost'] = Math::decimal(($sum['pre_day_360_profit'] + $sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost']), 0);
        isset($param->compute_target['pre_share_day_360_profit_exclude_other_cost']) && $sum['pre_share_day_360_profit_exclude_other_cost'] = Math::decimal(($sum['pre_day_360_share_profit'] + $sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost']), 0);
        isset($param->compute_target['true_divide_applet_pay_money']) && $sum['true_divide_applet_pay_money'] = Math::decimal(($sum['applet_pay_money'] * 0.4), 0);
        isset($param->compute_target['box_agent_divide_money']) && $sum['box_agent_divide_money'] = Math::decimal(($sum['box_agent_money'] * 0.4), 0);
        isset($param->compute_target['pre_box_agent_divide_money']) && $sum['pre_box_agent_divide_money'] = Math::decimal(($sum['pre_box_agent_money'] * 0.4), 0);
        isset($param->compute_target['pre_profit_share_rate']) && $sum['pre_profit_share_rate'] = Math::decimal(($sum['pre_day_360_share_profit'] / $sum['cost_money']), 4) * 100 . '%';
        isset($param->compute_target['pre_profit_share_rate_exclude_other_cost']) && $sum['pre_profit_share_rate_exclude_other_cost'] = Math::decimal(($sum['pre_day_360_share_profit'] + $sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost']) / $sum['cost_money'], 4) * 100 . '%';
        isset($param->compute_target['multiple_exclude_cps']) && $sum['multiple_exclude_cps'] = $sum['pre_day_360_pay_exclude_cps'] > 0 ? Math::decimal($sum['pre_day_360_pay_exclude_cps'] / $sum['first_day_pay_money_exclude_cps'], 2) : 0;
        isset($param->compute_target['current_month_profit_exclude_other_cost']) && $sum['current_month_profit_exclude_other_cost'] = Math::decimal($sum['current_month_profit'] + $sum['server_money'] + $sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost'] , 2);
        isset($param->compute_target['current_month_share_profit_exclude_other_cost']) && $sum['current_month_share_profit_exclude_other_cost'] = Math::decimal($sum['current_month_share_profit'] + $sum['server_money'] + $sum['plate_cost'] + $sum['endorsement_cost'] + $sum['license_cost'] + $sum['brand_promotion_cost'] + $sum['outsourcing_cost'] , 2);
        // 固定小程序分成比例
        $sum['true_divide_applet_pay_ratio'] = '0.40';
        // 自定义指标
        if ($param->customized_target) {
            $this->customizedTarget($param->customized_target, $sum);
        }

        return [
            'total' => count($list),
            'list' => $list,
            'sql' => $data['sql'],
            'sum' => $sum,
            'is_accurate' => (count($list) < $param->limit) ? 1 : 0,
        ];
    }

    /**
     * @param GameMultipleListParam $param
     * @return array
     */
    public function gameMultipleList(GameMultipleListParam $param): array
    {
        $model = new V2DimGameMultipleLogModel();
        $data = $model->gameMultipleList($param);

        return [
            'total' => $data['total'],
            'list' => $data['list'],
            'sql' => $data['sql'],
        ];
    }

    /**
     * @param $param
     * @return bool
     */
    public function addGameMultiple($param): bool
    {
        $model = new V2DimGameMultipleLogModel();
        // 添加了集团游戏，就不能再添加同一个集团游戏所属的根游戏，反之同理  -- 1.0版本  by婷总
        // 允许填了集团游戏，还允许填根游戏  -- 2.0版本  by仇哥20241111
        $data = $model->checkExist($param['platform'], $param['tdate'], $param['config_type'], $param['clique_root_id']);
        if ($data->isNotEmpty()) {
            $message = $data->first()->config_type == 1 ? "已存在按集团的配置" : "已存在按根的配置";
            throw new AppException("{$message}, 请勿重复插入");
        }

        $insert_data = [
            'platform' => $param['platform'],
            'tdate' => $param['tdate'],
            'config_type' => $param['config_type'],
            'clique_root_id' => $param['clique_root_id'],
            'multiple' => $param['multiple'],
            'creator' => Container::getSession()->name,
            'insert_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        return $model->add($insert_data);
    }

    /**
     * @param $param
     * @return int
     */
    public function editGameMultiple($param): int
    {
        $model = new V2DimGameMultipleLogModel();
        $data = $model->getData($param['platform'], $param['tdate'], $param['config_type'], $param['clique_root_id']);
        if (!$data) {
            throw new AppException("数据不存在");
        }

        $update_data = [
            'multiple' => $param['multiple'],
            'creator' => Container::getSession()->name,
            'update_time' => date('Y-m-d H:i:s')
        ];

        return $model->edit($param['platform'], $param['tdate'], $param['config_type'], $param['clique_root_id'], $update_data);
    }

    /**
     * @param $param
     * @return int
     */
    public function deleteGameMultiple($param): int
    {
        $model = new V2DimGameMultipleLogModel();
        return $model->delete($param['platform'], $param['tdate'], $param['config_type'], $param['clique_root_id']);
    }

    public function searchOptions($column, $keyword, $filter)
    {
        switch ($column) {
            case 'clique_id':
                $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
                $res = (new V2DimGameIdModel())->getListLikeClique($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission);
                return $res->map(function ($item) {
                    return [
                        'label' => "{$item->clique_id}-{$item->clique_name}",
                        'value' => $item->clique_id,
                    ];
                });
            case 'root_game':
                $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
                return (new V2DimGameIdModel())->getListLikeRootGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission)->map(function ($item) {
                    return [
                        'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                        'value' => $item->root_game_id,
                    ];
                });
            case 'main_game':
                $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
                return (new V2DimGameIdModel())->getListLikeMainGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission)->map(function ($item) {
                    return [
                        'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->main_game_id}-{$item->main_game_name}",
                        'value' => $item->main_game_id,
                    ];
                });
            case 'game':
                $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
                return (new V2DimGameIdModel())->getListLikeGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission)->map(function ($item) {
                    return [
                        'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->game_id}-{$item->game_name}",
                        'value' => $item->game_id,
                    ];
                });
            case 'business_unit':
                return collect(BusinessUnitAndProjectTeamMap::BUSINESS_UNIT)->map(function ($item) {
                    return [
                        'label' => $item,
                        'value' => $item,
                    ];
                });
            case 'project_team':
                $business_unit = $filter['business_unit']['value'] ?? "";
                if (empty($business_unit)) throw new AppException("filter的值为空");
                return collect(BusinessUnitAndProjectTeamMap::PROJECT_TEAM_MAP[$business_unit])->map(function ($item) {
                    return [
                        'label' => $item,
                        'value' => $item,
                    ];
                });
            case 'project_team_by_business_type':
                return (new ProjectTeamModel())->getListLikeName($keyword, DimensionTool::getProjectTeamFilterOptions($filter))->map(function ($item) {
                    return [
                        'label' => $item->business_unit . '-' . $item->project_team,
                        'value' => $item->id,
                    ];
                });
            case 'root_game_by_project_team':
                $project_team = $filter['project_team_id']['value'] ?? "";
                $platform = $filter['platform']['value'] ?? "";
                if (empty($project_team) || empty($platform)) throw new AppException("请选择平台或项目组");
                $game_permission = (new PermissionLogic())->getLoginUserGamePermission();

                /**
                 * 需求：财务要求如果是中台类型的就直接读取权限内全部根游戏。
                 * 原逻辑：一开始商量好的是根据项目组id来取，逻辑是 project_team_root_game里配置好的关系 + 所有游戏（排除project_team_root_game配置好的游戏）
                 * 处理：判断下选的项目组id是不是中台类型，因为是多选，就取第一个值来取business_type好了，如果是中台类型，直接走getListLikeRootGame逻辑
                 * 财务的需求老是变来变去...导致改得东西很唐突，哎，有点无语
                 */
                $project_team_data = (new ProjectTeamModel())->getListByIds($project_team);
                $business_type = $project_team_data->first()->business_type;
                if ($business_type == ProjectTeamModel::COMMON_TYPE) {
                    $list = (new V2DimGameIdModel())->getListLikeRootGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission)->map(function ($item) {
                        return [
                            'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                            'value' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                        ];
                    });
                } else {
                    $project_team_root_game_list = (new ProjectTeamRootGameModel())->getListLikeName($keyword, DimensionTool::getProjectTeamFilterOptions($filter), count($project_team), $game_permission);
                    $root_game_list_without_project_team_root = (new ZxV2DimGameIdModel())->getListWithOutProjectRootGameLikeRootGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission);
                    $list = [];
                    foreach ($project_team_root_game_list as $item) {
                        $list[] = [
                            'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                            'value' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                        ];
                    }
                    foreach ($root_game_list_without_project_team_root as $item) {
                        $list[] = [
                            'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                            'value' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                        ];
                    }
                }

                return $list;
            case 'money_type':
                return collect(OtherCostDetailModel::MONEY_TYPE)->map(function ($item, $key) {
                    return [
                        'label' => $item,
                        'value' => $key,
                    ];
                });
            default:
                return [];
        }
    }

    /**
     * 累计字段的处理
     * @param $list
     * @param ProfitStatementListParam $param
     * @return array
     */
    private function getTotalColumnSum($list, ProfitStatementListParam $param): array
    {
        if ($param->aggregation_type == '按月') {
            $total_column_sum_data = []; // 处理过后的合计累计数据
            $temp_group_data = [];
            foreach ($list as $datum) {
                $datum = (array)$datum;

                $tmp = [];
                foreach (ProfitStatementSqlMap::SUM_COLUMN as $total_target) {
                    if (!isset($datum[$total_target])) continue;

                    $tmp['tdate'] = $datum['tdate'];
                    $tmp[$total_target] = $datum[$total_target];
                }

                $unique_key_tmp = [];
                foreach (array_merge(['run_date'], $param->dimension) as $dimension_item) {
                    $unique_key_tmp[] = $datum[$dimension_item];
                }
                $unique_key = implode('-', $unique_key_tmp);
                if (!empty($tmp)) {
                    $temp_group_data[$unique_key][] = $tmp;
                }
            }

            foreach ($temp_group_data as $unique_key => $group_array) {
                // 根据tdate排序,然后需没个分组最后一条的累计数据来相加
                array_multisort(array_column($group_array, 'tdate'), SORT_ASC, $group_array);
                $end_total_data = end($group_array);
                $total_column_sum_data[$unique_key] = $end_total_data;
            }

            $return_sum_data = [];
            foreach ($total_column_sum_data as $item) {
                foreach (ProfitStatementSqlMap::SUM_COLUMN as $total_target) {
                    if (!isset($item[$total_target])) continue;

                    if (!isset($return_sum_data[$total_target])) {
                        $return_sum_data[$total_target] = $item[$total_target];
                    } else {
                        $return_sum_data[$total_target] += $item[$total_target];
                    }
                }
            }
        } else {
            $return_sum_data = [];
            foreach ($list as $item) {
                $item = (array)$item;
                foreach (ProfitStatementSqlMap::SUM_COLUMN as $total_target) {
                    if (!isset($item[$total_target])) continue;

                    if (!isset($return_sum_data[$total_target])) {
                        $return_sum_data[$total_target] = $item[$total_target];
                    } else {
                        $return_sum_data[$total_target] += $item[$total_target];
                    }
                }
            }
        }

        return $return_sum_data;
    }

    /************************************************其他成本 start****************************************************/

    /**
     * @param $money_type
     * @param $money
     * @param array $other_cost_configs
     * @return void
     * @throws Exception
     */
    public function addOtherCostConfig($money_type, $money, array $other_cost_configs)
    {
        $insert_data = $this->handleOtherConfig($money_type, $money, $other_cost_configs);
        if (empty($insert_data)) {
            throw new AppException("您选择的根游戏对应月没有流水数据，请先自查游戏为买量or发行");
        }

        // 开启事务
        MysqlConnection::getConnection('default')->beginTransaction();

        try {
            $config_id = (new OtherCostModel())->add([
                'other_cost_config' => json_encode([
                    'money_type' => $money_type,
                    'money' => $money,
                    'other_cost_config' => $other_cost_configs,
                ]),
                'creator' => Container::getSession()->name,
                'operator' => Container::getSession()->name
            ]);

            // 补充配置id
            foreach ($insert_data as &$insert_datum) {
                $insert_datum['other_cost_config_id'] = $config_id;
                $insert_datum['creator'] = Container::getSession()->name;
            }
            // TODO 校验$insert_data是否重复，产生原因：多个成本配置里，配置相同的 平台+根游戏+月份的数据，因为其他成本配置是保存的json，没法在新增/编辑的时候判断是否已经重复，所以留在这里判断了
            (new OtherCostDetailModel())->addMultiple($insert_data);

            MysqlConnection::getConnection('default')->commit();
        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }

    }

    /**
     * @param $id
     * @param $money_type
     * @param $money
     * @param array $other_cost_configs
     * @return void
     * @throws Exception
     */
    public function editOtherCostConfig($id, $money_type, $money, array $other_cost_configs)
    {
        $model = new OtherCostModel();
        $other_cost_detail_model = new OtherCostDetailModel();
        if (!$model->getData($id)) {
            throw new AppException("id有误");
        }

        // 开始事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            $model->edit($id, [
                'other_cost_config' => json_encode([
                    'money_type' => $money_type,
                    'money' => $money,
                    'other_cost_config' => $other_cost_configs,
                ]),
                'operator' => Container::getSession()->name
            ]);

            // 删除旧数据
            $other_cost_detail_model->deleteByOtherConfigId($id);

            $insert_data = $this->handleOtherConfig($money_type, $money, $other_cost_configs);
            // 补充配置id
            foreach ($insert_data as &$insert_datum) {
                $insert_datum['other_cost_config_id'] = $id;
                $insert_datum['creator'] = Container::getSession()->name;
            }
            $model = new OtherCostDetailModel();
            $model->addMultiple($insert_data);

            MysqlConnection::getConnection('default')->commit();
        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }

    }

    /**
     * @param OtherCostDetailListParam | OtherCostDetailListExportParam $param
     * @return Collection
     */
    public function otherCostDetailList($param): \Illuminate\Support\Collection
    {
        $model = new OtherCostDetailModel();
        $data = $model->getOtherCostDetailList($param);

        foreach ($data as &$item) {
            $root_game_str = $project_team_str = [];
            $month_str = '';
            $item->other_cost_config = json_decode($item->other_cost_config, true);
            foreach ($item->other_cost_config['other_cost_config'] as $config) {
                foreach ($config['root_games'] as $root_game_item) {
                    $key = $config['platform'] . '-' . $root_game_item['root_game_id'] . '-' . $root_game_item['root_game_name'];
                    if (!in_array($key, $root_game_str)) {
                        $root_game_str[] = $key;
                    }
                }

                foreach ($config['project_teams'] as $project_team_item) {
                    if (!in_array($project_team_item['project_team_name'], $project_team_str)) {
                        $project_team_str[] = $project_team_item['project_team_name'];
                    }
                }

                $month_str .= "{$config['start_month']}-{$config['end_month']}" . PHP_EOL;
            }
            $item->root_game_str = implode(PHP_EOL, $root_game_str);
            $item->project_team_str = implode(PHP_EOL, $project_team_str);
            $item->month_str = $month_str;
        }

        return $data;
    }

    /**
     * @param $config_id
     * @return ZDBuilder|Model|Builder|object|null
     */
    public function getOtherCostConfig($config_id)
    {
        $model = new OtherCostModel();
        $data = $model->getData($config_id);
        if (!$data) {
            throw new AppException("config_id 有误");
        }

        $data->other_cost_config = json_decode($data->other_cost_config, true);
        return $data;
    }

    public function importOtherCostConfig(array $import_data)
    {
        // 数据校验+格式化
        $batch_insert_data = $this->checkAndFormatImportData($import_data);

        try {
            // 开始事务
            MysqlConnection::getConnection('default')->beginTransaction();
            foreach ($batch_insert_data as $batch_id => $batch_insert_datum) {
                $insert_data = $this->handleOtherConfig($batch_insert_datum['money_type'], $batch_insert_datum['money'], $batch_insert_datum['other_cost_config']);
                if (empty($insert_data)) {
                    throw new AppException("批次id: {$batch_id}的根游戏对应月没有流水数据，请先自查游戏为买量or发行");
                }

                $config_id = (new OtherCostModel())->add([
                    'other_cost_config' => json_encode([
                        'money_type' => $batch_insert_datum['money_type'],
                        'money' => $batch_insert_datum['money'],
                        'other_cost_config' => $batch_insert_datum['other_cost_config'],
                    ]),
                    'creator' => Container::getSession()->name,
                    'operator' => Container::getSession()->name
                ]);

                // 补充配置id
                foreach ($insert_data as &$insert_datum) {
                    $insert_datum['other_cost_config_id'] = $config_id;
                    $insert_datum['creator'] = Container::getSession()->name;
                }

                (new OtherCostDetailModel())->addMultiple($insert_data);
            }

            MysqlConnection::getConnection('default')->commit();
        } catch (Exception $exception) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($exception->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param $import_data
     * @return array
     */
    private function checkAndFormatImportData($import_data): array
    {
        if (!(count($import_data) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        // 固定的表头,和模板一一对应,有改动的话,需要改模板
        $header = [
            'batch_id',
            'money_type',
            'money',
            'platform',
            'proxy_type',
            'start_month',
            'end_month',
            'business_type',
            'business_unit_and_project_team',
            'root_game_id',
            'divide_money',
            'agency',
            'remark',
        ];

        // format
        $data_list = [];
        foreach ($import_data as $index => $datum) {
            if ($index == 0) {
                continue;
            }
            $tmp = [];
            foreach ($datum as $sub_index => $item) {
                $key_name = $header[$sub_index];
                $tmp[$key_name] = $item;
            }
            $data_list[] = $tmp;
        }

        $batch_insert_data = [];
        foreach ($data_list as $index => &$item) {
            // 费用类型转英文并校验
            if (!in_array($item['money_type'], array_values(OtherCostDetailModel::MONEY_TYPE))) {
                throw new AppException("第" . ($index + 2) . "行数据的费用类型(money_type)有误，不存在该费用类型");
            }
            $item['money_type'] = array_flip(OtherCostDetailModel::MONEY_TYPE)[$item['money_type']];

            // 平台转英文并校验
            if (!in_array($item['platform'], array_values(EnvConfig::PLATFORM_MAP))) {
                throw new AppException("第" . ($index + 2) . "行数据的平台有误，不存在该平台");
            }
            $item['platform'] = array_flip(EnvConfig::PLATFORM_MAP)[$item['platform']];

            // proxy_type校验
            if (!in_array($item['proxy_type'], [1, 2, 3])) {
                throw new AppException("第" . ($index + 2) . "行数据的买量类型有误");
            }

            // 金额校验
            if ($item['money'] == 0 || $item['money'] < 0) {
                throw new AppException("第" . ($index + 2) . "行数据的费用总金额为0, 请检查");
            }

            // 金额校验
            if ($item['divide_money'] == 0 || $item['divide_money'] < 0) {
                throw new AppException("第" . ($index + 2) . "行数据的比例金额为0, 请检查");
            }

            // 项目类型转英文
            if (!in_array($item['business_type'], array_values(ProjectTeamModel::BUSINESS_TYPE_MAP))) {
                throw new AppException("第" . ($index + 2) . "行数据的项目组类型有误");
            }
            $item['business_type'] = array_flip(ProjectTeamModel::BUSINESS_TYPE_MAP)[$item['business_type']];

            // 费用类型为《宣发费》和《代言费》时，代理和备注必填
            if (in_array($item['money_type'], [OtherCostDetailModel::ENDORSEMENT_MONEY, OtherCostDetailModel::BRAND_PROMOTION_COST])) {
                if (empty($item['agency']) || empty($item['remark'])) {
                    throw new AppException("第" . ($index + 2) . "行数据的代理有误, 费用类型为《宣发费》和《代言费》时，代理和备注必填");
                }
            }

            // 校验日期格式
            $format_date = 'Y-m';
            $start_month = DateTime::createFromFormat($format_date, $item['start_month']);
            $end_month = DateTime::createFromFormat($format_date, $item['end_month']);
            if (!($start_month && $start_month->format($format_date) == $item['start_month']) || !($end_month && $end_month->format($format_date) == $item['end_month'])) {
                throw new AppException("第" . ($index + 2) . "行数据的日期格式有误, 格式为:2024-01, 请检查");
            }
            if ($start_month > $end_month) {
                throw new AppException("第" . ($index + 2) . "行数据的开始日期不能大于结束日期");
            }

            // ------------格式化为录入格式------------

            // 事业部项目组多选切割; 平台+项目组类型+事业部项目组的关系校验，通过project_team表校验
            if (empty($item['business_unit_and_project_team'])) {
                throw new AppException("第" . ($index + 2) . "行数据的事业部项目组不能为空");
            }
            $item['business_unit_and_project_team'] = explode(',', $item['business_unit_and_project_team']);
            foreach ($item['business_unit_and_project_team'] as $business_unit_and_project_team) {
                [$business_unit, $project_team] = explode('-', $business_unit_and_project_team);
                $project_team_data = (new ProjectTeamModel())->getDataByUniqueKey($business_unit, $project_team, $item['platform']);
                if (empty($project_team_data)) {
                    throw new AppException("第" . ($index + 2) . "行数据事业部项目组选项的{$business_unit}-{$project_team}不存在，请确认关系");
                }
                if ($project_team_data->business_type != $item['business_type']) {
                    throw new AppException("第" . ($index + 2) . "行数据事业部项目组选项的 {$business_unit}-{$project_team} 项的项目组类型/中台类型有误，请确认项目组类型/中台类型和项目组的关系");
                }
                $item['project_teams'][] = [
                    'project_team_id' => $project_team_data->id,
                    'project_team_name' => "{$business_unit}-{$project_team}"
                ];
            }

            // root_game_id 切割
            if (empty($item['root_game_id'])) {
                throw new AppException("第" . ($index + 2) . "行数据的根游戏项不能为空");
            }
            $item['root_game_id'] = explode(',', $item['root_game_id']);
            foreach ($item['root_game_id'] as $root_game_id) {
                $game_data = (new V2DimGameIdModel())->getDataByRootGameId($item['platform'], $root_game_id);
                if (empty($game_data)) {
                    throw new AppException("第" . ($index + 2) . "行数据,根游戏id:{$root_game_id}不存在");
                }
                $item['root_games'][] = [
                    'root_game_id' => $root_game_id,
                    'root_game_name' => $game_data->root_game_name
                ];
            }

            // 事业部项目组+根游戏id的关系校验，通过project_team_root_game表校验
            // 此处复用searchOption的接口方法，就不重新写方法去校验了，所以模仿前端请求构造个filter参数
            $keyword = '';
            $filter = [
                'platform' => [
                    'column' => 'platform',
                    'type' => 'string',
                    'value' => $item['platform'],
                    'condition' => 'eq',
                ],
                'project_team_id' => [
                    'column' => 'project_team_id',
                    'type' => 'string',
                    'value' => array_column($item['project_teams'], 'project_team_id'),
                    'condition' => 'in',
                ]
            ];
            $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
            $project_team_root_game_list = (new ProjectTeamRootGameModel())->getListLikeName($keyword, DimensionTool::getProjectTeamFilterOptions($filter), count($filter['project_team_id']['value']), $game_permission);
            $root_game_list_without_project_team_root = (new ZxV2DimGameIdModel())->getListWithOutProjectRootGameLikeRootGame($keyword, DimensionTool::getGameFilterOptions($filter, 'game'), $game_permission);
            foreach ($item['root_games'] as $root_game_item) {
                if (in_array($root_game_item['root_game_id'], array_column($project_team_root_game_list->toArray(), 'root_game_id'))) {
                    continue;
                }
                if (in_array($root_game_item['root_game_id'], array_column($root_game_list_without_project_team_root->toArray(), 'root_game_id'))) {
                    continue;
                }
                throw new AppException("第" . ($index + 2) . "行数据,根游戏id与事业部项目组关系错误，请确认项目组是否含有所填根游戏 or 当前账号是否有根游戏权限，错误的根游戏id：" . $root_game_item['root_game_id']);
            }

            // 根据batch_id分组成录入格式
            if (!isset($batch_insert_data[$item['batch_id']])) {
                $batch_insert_data[$item['batch_id']] = [
                    'platform' => $item['platform'],
                    'money' => $item['money'],
                    'money_type' => $item['money_type'],
                    'other_cost_config' => [
                        [
                            'agency' => $item['agency'],
                            'business_type' => $item['business_type'],
                            'divide' => Math::div($item['divide_money'], $item['money'], 4),
                            'divide_money' => $item['divide_money'],
                            'start_month' => $item['start_month'],
                            'end_month' => $item['end_month'],
                            'month' => [$item['start_month'], $item['end_month']],
                            'platform' => $item['platform'],
                            'project_teams' => $item['project_teams'],
                            'proxy_type' => $item['proxy_type'],
                            'remark' => $item['remark'],
                            'root_games' => $item['root_games'],
                        ]
                    ],
                ];
            } else {
                $other_cost_config = [
                    'agency' => $item['agency'],
                    'business_type' => $item['business_type'],
                    'divide' => Math::div($item['divide_money'], $item['money'], 4),
                    'divide_money' => $item['divide_money'],
                    'start_month' => $item['start_month'],
                    'end_month' => $item['end_month'],
                    'month' => [$item['start_month'], $item['end_month']],
                    'platform' => $item['platform'],
                    'project_teams' => $item['project_teams'],
                    'proxy_type' => $item['proxy_type'],
                    'remark' => $item['remark'],
                    'root_games' => $item['root_games'],
                ];
                $batch_insert_data[$item['batch_id']]['other_cost_config'][] = $other_cost_config;
            }
        }

        return $batch_insert_data;
    }

    /**
     * @param $id
     * @return void
     * @throws Exception
     */
    public function deleteOtherCostConfig($id)
    {
        $other_cost_config_model = new OtherCostModel();
        $other_cost_detail_model = new OtherCostDetailModel();
        // 开始事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            // 删除前的校验
            $this->deleteCheck($id);

            $other_cost_config_model->remove($id);
            $other_cost_detail_model->deleteByOtherConfigId($id);

            MysqlConnection::getConnection('default')->commit();
        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param $money_type
     * @param $money
     * @param $other_cost_configs
     * @return array
     */
    private function handleOtherConfig($money_type, $money, $other_cost_configs): array
    {
        $this->checkOtherConfig($other_cost_configs, $money, $money_type);
        /**
         * 把总金额按配置比例分配到$config_money
         * 在根据月份数量计算每月金额，即 $config_money/$month_count = $per_month_money
         * 排列组合 platform - root_game_id - cost_month - project_team
         * 计算当前platform - root_game_id在当月的授权金占比：
         *   1.N个根游戏当月都有流水，授权金就按 流水比例*总授权金 = 该游戏授权金总额
         *   2.两个根游戏，一个有流水，一个没有流水，那每元授权金总额都算给有流水的，没有流水的，那授权金全都归到有流水的根游戏上
         *   3.两个根游戏，假设都没有流水，那每元授权金就为0，然后再根据day * game_count 摊出 每天分摊的授权金
         */
        $game_id_model = new V2DimGameIdModel();
        $game_pay_model = new GamePayMoneyModel();
        $list = [];
        // 组合
        foreach ($other_cost_configs as $other_cost_config) {
            $cost_month_array = DateTool::getMonthRangeDataByDateRange($other_cost_config['start_month'], $other_cost_config['end_month']);// 起始日期统计
            $cost_month_count = count($cost_month_array);
            $config_money = $other_cost_config['divide_money'];
            $per_month_money = Math::div($config_money, $cost_month_count, 2);
            $root_games = $other_cost_config['root_games'];
            $project_team_ids = json_encode(array_map(function ($item) {
                return (int)$item;
            }, array_column($other_cost_config['project_teams'], 'project_team_id')));// 此项是冗余的，json格式保存是方便配置列表查询筛选用

            foreach ($cost_month_array as $cost_month) {
                $root_game_total_pay_money = [];
                $start_date = date("Y-m-01", strtotime($cost_month));
                $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));

                foreach ($root_games as $root_game) {
                    $root_game_id = $root_game['root_game_id'];
                    if ($other_cost_config['proxy_type'] == 3) {
                        $proxy_type_array = [1, 2]; // proxy_type=3时为 买量+发行，需要拆分成买量和发行
                    } else {
                        $proxy_type_array = [$other_cost_config['proxy_type']];
                    }
                    foreach ($proxy_type_array as $proxy_type) {
                        // 获取时间范围内根游戏总流水
                        $total_pay_money = $game_pay_model->getRootGameTotalPayMoney($root_game_id, $other_cost_config['platform'], [$start_date, $end_date], $proxy_type);
                        $root_game_total_pay_money[] = [
                            'platform' => $other_cost_config['platform'],
                            'proxy_type' => $proxy_type,
                            'root_game_id' => $root_game_id,
                            'cost_month' => $cost_month,
                            'current_month_total_pay_money' => $total_pay_money,
                            'mark' => $total_pay_money > 0 ? 1 : 0,
                            'money_type' => $money_type,
                            'remark' => $other_cost_config['remark'],
                            'project_team_ids' => $project_team_ids,
                        ];
                    }
                }

                // 计算各个根游戏的流水占比
                $config_root_games_total_pay_money_sum = array_sum(array_column($root_game_total_pay_money, 'current_month_total_pay_money'));
                foreach ($root_game_total_pay_money as &$root_game_total_pay_money_item) {
                    $root_game_total_pay_money_item['current_month_all_root_game_total_pay_money'] = $config_root_games_total_pay_money_sum;
                    $root_game_total_pay_money_item['rate'] = Math::div($root_game_total_pay_money_item['current_month_total_pay_money'], $config_root_games_total_pay_money_sum, 4);
                    $root_game_total_pay_money_item['per_month_money'] = $per_month_money;
                    $root_game_total_pay_money_item['divide_money'] = Math::decimal($root_game_total_pay_money_item['rate'] * $per_month_money, 2);
                }

                $list = array_merge($list, $root_game_total_pay_money);
            }


        }

        // 合并多配置里相同项（platform+proxy_type+root_game_id+cost_month）
        $merge_list = [];
        foreach ($list as $item) {
            $unique_key = "{$item['platform']}_{$item['proxy_type']}_{$item['root_game_id']}_{$item['cost_month']}";
            if (!isset($merge_list[$unique_key])) {
                $merge_list[$unique_key] = [
                    'platform' => $item['platform'],
                    'proxy_type' => $item['proxy_type'],
                    'root_game_id' => $item['root_game_id'],
                    'cost_month' => $item['cost_month'],
                    'current_month_total_pay_money' => $item['current_month_total_pay_money'],
                    'mark' => $item['mark'],
                    'divide_money' => $item['divide_money'],
                    'remark' => $item['remark'],
                    'project_team_ids' => $item['project_team_ids'],
                ];
            } else {
                $merge_list[$unique_key]['divide_money'] += $item['divide_money'];// 累加金额
            }
        }

        $insert_data = [];
        foreach ($merge_list as $item) {
            $start_date = date("Y-m-01", strtotime($item['cost_month']));
            $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
            // 需要同步到根游戏下的所有主游戏
            $main_game_list = $game_id_model->getAllMainGameByRootGameId($item['root_game_id'], $item['platform'], $item['proxy_type']);
            // 获取这个根游戏的子游戏数量
            $game_count = $game_id_model->getGameCountByRootGame($item['platform'], $item['root_game_id'], $item['proxy_type']);
            // 计算分摊
            $per_data = $this->perCostMoney($start_date,
                $end_date,
                $item['current_month_total_pay_money'],
                $item['divide_money'],
                $game_count);

            // 共享情况下 mark通过根游戏流水判断
            $mark = $item['mark'];
            foreach ($main_game_list as $main_game_item) {
                // 补充游戏数据 TODO
                (new OperationProfitLogic())->makeUpGamePayMoney($item['platform'], $item['root_game_id'], $main_game_item->main_game_id, $item['proxy_type'], $start_date, $end_date);
                $insert_data[] = [
                    'money_type' => $money_type,
                    'platform' => $item['platform'],
                    'root_game_id' => $item['root_game_id'],
                    'main_game_id' => $main_game_item->main_game_id,
                    'proxy_type' => $item['proxy_type'],
                    'cost_month' => $item['cost_month'],
                    'current_month_total_pay_money' => $item['current_month_total_pay_money'],
                    'money' => $item['divide_money'],
                    'per_money' => $per_data['per_money'],
                    'per_day_money' => $per_data['per_day_money'],
                    'mark' => $mark,
                    'remark' => $item['remark'],
                    'project_team_ids' => $item['project_team_ids'],
                ];
            }
        }

        return $insert_data;
    }

    private function checkOtherConfig(array $other_cost_configs, $money, $money_type)
    {
        // 比例金额校验, 各比例金额之和必须等于总金额
        $total_divide_money = Math::decimal(array_sum(array_column($other_cost_configs, 'divide_money')), 2);
        if ($total_divide_money != $money) {
            throw new AppException("各比例金额之和必须等于总金额");
        }

        // 先组合，后校验platform + root_game_id + 事业部 + 项目组 + cost_month 唯一
        $list = [];
        foreach ($other_cost_configs as $other_cost_config) {
            // 必填项校验
            if (in_array($money_type, [OtherCostDetailModel::ENDORSEMENT_MONEY, OtherCostDetailModel::BRAND_PROMOTION_COST])) {
                if (empty($other_cost_config['remark']) || empty($other_cost_config['agency'])) {
                    throw new AppException("费用类型为《宣发费》和《代言费》时，代理和备注必填");
                }
            }

            $cost_month_array = DateTool::getMonthRangeDataByDateRange($other_cost_config['start_month'], $other_cost_config['end_month']);// 起始日期统计
            $root_games = $other_cost_config['root_games'];
            $project_teams = $other_cost_config['project_teams'];// 格式为: 事业部-项目组

            foreach ($root_games as $root_game) {
                foreach ($project_teams as $project_team_item) {
                    foreach ($cost_month_array as $cost_month) {
//                        // 目前只允许录入2024-10月后的数据，之前的数据走老的录入入口
//                        if ($cost_month < "2024-10") {
//                            throw new AppException("只允许录入2024-10月（含）之后的数据， 老数据请到老入口录入");
//                        }
                        $project_team_id = $project_team_item['project_team_id'];
                        $unique_key = "{$other_cost_config['platform']}_{$root_game['root_game_id']}_{$other_cost_config['proxy_type']}_{$project_team_id}_{$cost_month}";
                        if (isset($list[$unique_key])) {
                            throw new AppException("配置中有冲突，请检查重复项：{$unique_key}");
                        } else {
                            $list[$unique_key] = [
                                'platform' => $other_cost_config['platform'],
                                'proxy_type' => $other_cost_config['proxy_type'],
                                'root_game_id' => $root_game['root_game_id'],
                                'business_unit' => explode('-', $project_team_item['project_team_name'])[0],
                                'project_team' => explode('-', $project_team_item['project_team_name'])[1],
                                'cost_month' => $cost_month,
                            ];
                            // TODO 校验数据库中的唯一性，因为现在1.0版本还没有项目组的概念，所以先不校验
                        }
                    }
                }
            }
        }

    }

    /**
     * 计算分摊的金额
     *
     * @param     $start_date
     * @param     $end_date
     * @param     $total_pay_money
     * @param     $money
     * @param     $count *子游戏数量
     *
     * @return array
     */
    public function perCostMoney($start_date, $end_date, $total_pay_money, $money, $count): array
    {
        // 按流水分摊三个金额
        $per_money = $total_pay_money > 0 ? bcdiv($money, $total_pay_money, 10) : 0;

        // 按月份分摊是三个金额
        $days = ceil((strtotime($end_date) - strtotime($start_date)) / 86400) + 1;
        $per_day_money = ($days * $count) > 0 ? bcdiv($money, $days * $count, 10) : 0;

        return [
            'per_money' => $per_money,
            'per_day_money' => $per_day_money,
        ];
    }

    /************************************************其他成本 end*****************************************************/

    /************************************************研发分成 start ****************************************************/

    /**
     * @param CpDivideConfigParam $param
     * @return void
     * @throws Exception
     */
    public function addCpDivideConfig(CpDivideConfigParam $param)
    {
        /**
         * 大体逻辑：
         * 总共三个表：cp_money_divide_config，cp_money_divide_config_detail，game_cp_money_log
         * cp_money_divide_config 是保存配置的，cp_money_divide_config_detail 是保存拆解维度的配置，game_cp_money_log 是保存最终的分成结果的
         * 1.校验新增的数据，detail表不能有重复的数据
         * 2.保存配置到cp_money_divide_config， 保存拆解后的维度数据到cp_money_divide_config_detail，cp_money_divide_config留个状态，异步处理任务
         */
        $param->checkCpDivideConfig();

        // 开启事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            $config_id = (new CpMoneyDivideConfigModel())->add($param->genConfigInsertData());
            $param->setConfigId($config_id);
            (new CpMoneyDivideConfigDetailModel())->add($param->genDetailInsertData());

            MysqlConnection::getConnection('default')->commit();
            // 异步通知开启计算

        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }

    }

    /**
     * @param CpDivideConfigParam $param
     * @return void
     * @throws Exception
     */
    public function editCpDivideConfig(CpDivideConfigParam $param)
    {
        $param->checkCpDivideConfig();

        // 开启事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            (new CpMoneyDivideConfigModel())->update($param->id, $param->genConfigUpdateData());

            (new CpMoneyDivideConfigDetailModel())->deleteByConfigId($param->id);
            (new CpMoneyDivideConfigDetailModel())->add($param->genDetailInsertData());

            MysqlConnection::getConnection('default')->commit();
            // 异步通知开启计算

        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param $config_id
     * @return void
     * @throws Exception
     */
    public function deleteCpDivideConfig($config_id)
    {
        // 开启事务
        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            (new CpMoneyDivideConfigModel())->delete($config_id);
            (new CpMoneyDivideConfigDetailModel())->deleteByConfigId($config_id);
            (new GameCpMoneyLogModel())->deleteByCpConfigId($config_id);

            MysqlConnection::getConnection('default')->commit();

        } catch (Exception $e) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param CpDivideConfigListParam $param
     * @return Collection
     */
    public function getCpDivideConfigList(CpDivideConfigListParam $param): Collection
    {
        $model = new CpMoneyDivideConfigModel();
        $list = $model->getCpDivideConfigList($param);
        foreach ($list as $item) {
            $formula_data = json_decode($item->formula, true);
            $item->formula = $formula_data[2] ?? $formula_data[0];
            $item->ios_divide = round($item->ios_divide * 100, 2) . '%';
            $item->yyb_divide = round($item->yyb_divide * 100, 2) . '%';
            $item->applet_divide = round($item->applet_divide * 100, 2) . '%';

            $item->root_game_ids = json_decode($item->root_game_ids, true);
            $item->main_game_ids = json_decode($item->main_game_ids, true);
            $item->game_ids = json_decode($item->game_ids, true);
            $root_game_str = $main_game_str = $game_str = '';
            foreach ($item->root_game_ids as $root_game_item) {
                $root_game_str .= "{$root_game_item['root_game_id']}-{$root_game_item['root_game_name']}" . PHP_EOL;
            }
            foreach ($item->main_game_ids as $main_game_item) {
                $main_game_str .= "{$main_game_item['main_game_id']}-{$main_game_item['main_game_name']}" . PHP_EOL;
            }
            foreach ($item->game_ids as $game_item) {
                $game_str .= "{$game_item['game_id']}-{$game_item['game_name']}" . PHP_EOL;
            }
            $item->root_game_str = $root_game_str;
            $item->main_game_str = $main_game_str;
            $item->game_str = $game_str;
        }

        return $list;
    }

    /**
     * @param $id
     * @return ZDBuilder|Model|Builder|object
     */
    public function getCpDivideConfigDetail($id)
    {
        $model = new CpMoneyDivideConfigModel();
        $data = $model->getData($id);
        if (!$data) {
            throw new AppException('id错误');
        }
        $data->formula = json_decode($data->formula, true);
        $data->root_game_ids = json_decode($data->root_game_ids, true);
        $data->main_game_ids = json_decode($data->main_game_ids, true);
        $data->game_ids = json_decode($data->game_ids, true);

        return $data;
    }

    /************************************************研发分成 end *****************************************************/

    /**
     * 背景：产品利润比较特殊，如产品利润报表总共有个《产品利润》，《产品倍数配置》, 《其他成本配置》,仇哥要求要能权限配置，但是历史架构问题，路由里面没得再配置路由权限。
     * 处理：对于产品利润，有配置《指标选择》《维度选择》《维度筛选》时，才显示《产品利润》
     * @return array
     */
    public function getTablePermission(): array
    {
        $service = new PermissionService();
        $route_id = RouteID::PROFIT_STATEMENT;
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId('dms');

        $route_permission_list = $service->getRankRoutePermission($level, $rank_id, $route_id);
        $dimension_filter_list = FrontendTool::dimensionFilterStruct($route_permission_list);
        $dimension_list = FrontendTool::dimensionStruct($route_permission_list);
        $target_list = FrontendTool::targetStruct($route_permission_list);

        $show_profit_statement_table = false;
        if (!empty($dimension_filter_list) && !empty($dimension_list) && !empty($target_list)) {
            $show_profit_statement_table = true;
        }

        $route_permission_list = $service->getRankRoutePermission($level, $rank_id, RouteID::PROFIT_STATEMENT);
        $data['table'] = $route_permission_list->where('cate', RoutePermission::INPUT_PERMISSION)->pluck('name')->toArray();

        if ($show_profit_statement_table || UserService::isSuperManager()) {
            array_unshift($data['table'], "产品利润");
        }

        return $data;
    }

    /**
     * @param $id
     * @return void
     */
    private function deleteCheck($id)
    {
        // 配置只要包含了<=当前月份-2，就不允许删除
        $config = (new OtherCostModel())->getData($id);
        if (!$config) {
            throw new AppException("配置有误");
        }

        $data = json_decode($config->other_cost_config, true);
        $other_cost_configs = $data['other_cost_config'];
        foreach ($other_cost_configs as $other_cost_config) {
            $cost_month_array = DateTool::getMonthRangeDataByDateRange($other_cost_config['start_month'], $other_cost_config['end_month']);// 起始日期统计
            foreach ($cost_month_array as $cost_month) {
//                if (strtotime($cost_month) <= strtotime("-2 months", strtotime(date('Y-m')))) {
//                    throw new AppException("配置包含2个月前数据，请联系财务确认");
//                }
            }
        }

    }

    /**
     * @param OtherCostDetailListExportParam $list_param
     * @return string
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function exportOtherCostConfig(OtherCostDetailListExportParam $list_param): string
    {
        // 初始化表格，设置名称
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', '配置ID');
        $sheet->setCellValue('B1', '费用类型');
        $sheet->setCellValue('C1', '金额');
        $sheet->setCellValue('D1', '配置');
        $sheet->setCellValue('E1', '平台');
        $sheet->setCellValue('F1', '买量/发行');
        $sheet->setCellValue('G1', '起始月份');
        $sheet->setCellValue('H1', '结束月份');
        $sheet->setCellValue('I1', '项目组/中台');
        $sheet->setCellValue('J1', '项目组'); // 非合并项, 其余都为合并项
        $sheet->setCellValue('K1', '根游戏'); // 非合并项, 其余都为合并项
        $sheet->setCellValue('L1', '比例金额');
        $sheet->setCellValue('M1', '比例');
        $sheet->setCellValue('N1', '备注');
        $sheet->setCellValue('O1', '代理');
        $sheet->setCellValue('P1', '创建人');
        $sheet->setCellValue('Q1', '操作人');
        $sheet->setCellValue('R1', '最后操作时间');
        $writer = new Xlsx($spreadsheet);
        // 单元格设置为水平+垂直居中对齐
        $style_array = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];

        // 查询数据
        $data = $this->otherCostDetailList($list_param);
        if ($data->isEmpty()) {
            throw new AppException("导出的数据为空，请检查权限或筛选条件是否正确");
        }

        // 构造表格
        $file_path = '';
        $i = 2; // 初始行数
        foreach ($data as $item) {
            $data_config = $item->other_cost_config;
            $other_cost_config_id = $item->id;
            $money = $data_config['money'];
            $money_type = $data_config['money_type'];
            $other_cost_configs = $data_config['other_cost_config'];
            $parent_config_start_i = $i; // 顶级配置的初始行数
            foreach ($other_cost_configs as $index => $other_cost_config) {
                $param = new OtherCostConfigExportParam($other_cost_config);
                $param->setOtherCostConfigId($other_cost_config_id);
                $param->setOtherMoneyType($money_type);
                $param->setOtherMoney($money);
                $param->setSubConfigIndex($index);

                $data_list = $param->getMaxCountData();
                $sub_config_start_i = $i; // 子配置初始行数，用户计算合并表格
                $sub_config_end_i = $sub_config_start_i + count($data_list) - 1; // 子配置结束行数，用户计算合并表格
                $parent_config_end_i = $sub_config_end_i; // 顶级配置的结束行数
                foreach ($data_list as $data_item) {
                    $sheet->setCellValue("A$i", $param->other_cost_config_id);
                    $sheet->setCellValue("B$i", $param->money_type_name);
                    $sheet->setCellValue("C$i", $param->money);
                    $sheet->setCellValue("D$i", "配置{$param->sub_index}");
                    $sheet->setCellValue("E$i", $param->platform_name);
                    $sheet->setCellValue("F$i", $param->proxy_type_name);
                    $sheet->setCellValue("G$i", $param->start_month);
                    $sheet->setCellValue("H$i", $param->end_month);
                    $sheet->setCellValue("I$i", $param->business_type_name);
                    $sheet->setCellValue("J$i", $data_item['project_team_item']); // 项目组-非合并项, 其余都为合并项
                    $sheet->setCellValue("K$i", $data_item['root_game_item']); // 根游戏-非合并项, 其余都为合并项
                    $sheet->setCellValue("L$i", $param->divide_money);
                    $sheet->setCellValue("M$i", $param->divide);
                    $sheet->setCellValue("N$i", $param->remark);
                    $sheet->setCellValue("O$i", $param->agency);
                    $sheet->setCellValue("P$i", $item->creator);
                    $sheet->setCellValue("Q$i", $item->operator);
                    $sheet->setCellValue("R$i", $item->update_time);

                    $i++;
                }
                // 同子配置合并单元格
                $sheet->mergeCells("D{$sub_config_start_i}:D{$sub_config_end_i}"); // 配置
                $sheet->mergeCells("E{$sub_config_start_i}:E{$sub_config_end_i}"); // 平台
                $sheet->mergeCells("F{$sub_config_start_i}:F{$sub_config_end_i}"); // 买量类型
                $sheet->mergeCells("G{$sub_config_start_i}:G{$sub_config_end_i}"); // 起始月份
                $sheet->mergeCells("H{$sub_config_start_i}:H{$sub_config_end_i}"); // 结束月份
                $sheet->mergeCells("I{$sub_config_start_i}:I{$sub_config_end_i}"); // 项目类型
                $sheet->mergeCells("L{$sub_config_start_i}:L{$sub_config_end_i}"); // 比例金额
                $sheet->mergeCells("M{$sub_config_start_i}:M{$sub_config_end_i}"); // 比例
                $sheet->mergeCells("N{$sub_config_start_i}:N{$sub_config_end_i}"); // 备注
                $sheet->mergeCells("O{$sub_config_start_i}:O{$sub_config_end_i}"); // 代理
                // 居中格式设置
                $sheet->getStyle("D{$sub_config_start_i}:D{$sub_config_end_i}")->applyFromArray($style_array); // 配置
                $sheet->getStyle("E{$sub_config_start_i}:E{$sub_config_end_i}")->applyFromArray($style_array); // 平台
                $sheet->getStyle("F{$sub_config_start_i}:F{$sub_config_end_i}")->applyFromArray($style_array); // 买量类型
                $sheet->getStyle("G{$sub_config_start_i}:G{$sub_config_end_i}")->applyFromArray($style_array); // 起始月份
                $sheet->getStyle("H{$sub_config_start_i}:H{$sub_config_end_i}")->applyFromArray($style_array); // 结束月份
                $sheet->getStyle("I{$sub_config_start_i}:I{$sub_config_end_i}")->applyFromArray($style_array); // 项目类型
                $sheet->getStyle("L{$sub_config_start_i}:L{$sub_config_end_i}")->applyFromArray($style_array); // 比例金额
                $sheet->getStyle("M{$sub_config_start_i}:M{$sub_config_end_i}")->applyFromArray($style_array); // 比例
                $sheet->getStyle("N{$sub_config_start_i}:N{$sub_config_end_i}")->applyFromArray($style_array); // 备注
                $sheet->getStyle("O{$sub_config_start_i}:O{$sub_config_end_i}")->applyFromArray($style_array); // 代理

                $file_path = $param->getSaveFilePath();
            }

            // 同配置id合并
            $sheet->mergeCells("A{$parent_config_start_i}:A{$parent_config_end_i}"); // 配置id
            $sheet->mergeCells("B{$parent_config_start_i}:B{$parent_config_end_i}"); // 费用类型
            $sheet->mergeCells("C{$parent_config_start_i}:C{$parent_config_end_i}"); // 金额
            $sheet->mergeCells("P{$parent_config_start_i}:P{$parent_config_end_i}"); // 创建人
            $sheet->mergeCells("Q{$parent_config_start_i}:Q{$parent_config_end_i}"); // 操作人
            $sheet->mergeCells("R{$parent_config_start_i}:R{$parent_config_end_i}"); // 最后操作时间
            // 居中格式设置
            $sheet->getStyle("A{$parent_config_start_i}:A{$parent_config_end_i}")->applyFromArray($style_array); // 配置id
            $sheet->getStyle("B{$parent_config_start_i}:B{$parent_config_end_i}")->applyFromArray($style_array); // 费用类型
            $sheet->getStyle("C{$parent_config_start_i}:C{$parent_config_end_i}")->applyFromArray($style_array); // 金额
            $sheet->getStyle("P{$parent_config_start_i}:P{$parent_config_end_i}")->applyFromArray($style_array); // 创建人
            $sheet->getStyle("Q{$parent_config_start_i}:Q{$parent_config_end_i}")->applyFromArray($style_array); // 操作人
            $sheet->getStyle("R{$parent_config_start_i}:R{$parent_config_end_i}")->applyFromArray($style_array); // 最后操作时间
        }

        $writer->save($file_path);

        return $file_path;
    }

    /**
     * @param array $input
     * @return Collection
     */
    public function projectTeamRootGameList(array $input): Collection
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }

        $model = new ProjectTeamRootGameModel();
        return $model->getList($input);
    }

    /**
     * @param array $input
     * @return void
     */
    public function addProjectTeamRootGame(array $input)
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }

        $model = new ProjectTeamRootGameModel();
        $is_exist = $model->getData($input['project_team_id'], $input['platform'], $input['root_game_id']);
        if ($is_exist) {
            throw new AppException("该关系已存在，请勿重复添加");
        }

        $model->add([
            'project_team_id' => $input['project_team_id'],
            'platform' => $input['platform'],
            'root_game_id' => $input['root_game_id'],
            'root_game_name' => $input['root_game_name'],
            'is_exclusive' => $input['is_exclusive'],
            'type' => $input['type'],
        ]);
    }

    /**
     * @param array $import_data
     * @return bool
     */
    public function importProjectTeamRootGame(array $import_data): bool
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }

        if (!(count($import_data) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        // 固定的表头,和模板一一对应,有改动的话,需要改模板
        $header = [
            'project_team_id',
            'platform',
            'root_game_id',
            'is_exclusive',
            'type',
        ];

        // format
        $data_list = [];
        foreach ($import_data as $index => $datum) {
            if ($index == 0) {
                continue;
            }
            $tmp = [];
            foreach ($datum as $sub_index => $item) {
                $key_name = $header[$sub_index];
                $tmp[$key_name] = $item;
            }
            $data_list[] = $tmp;
        }

        $dim_game_id_model = new V2DimGameIdModel();
        $all_root_game_data = $dim_game_id_model->getAllRootGame();
        $model = new ProjectTeamRootGameModel();
        foreach ($data_list as &$item) {
            $is_exist = $model->getData($item['project_team_id'], $item['platform'], $item['root_game_id']);
            if ($is_exist) {
                throw new AppException("{$item['project_team_id']}-{$item['platform']}-{$item['root_game_id']}关系已存在，请勿重复添加，如果是修改专属游戏，请通过编辑来操作");
            }

            $item['root_game_name'] = $all_root_game_data->where('platform', $item['platform'])->where('root_game_id', $item['root_game_id'])->first()->root_game_name ?? '';
        }

        return $model->addMultiple($data_list);
    }

    /**
     * @param array $input
     * @return void
     */
    public function editProjectTeamRootGame(array $input)
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }

        $model = new ProjectTeamRootGameModel();
        $is_exist = $model->getData($input['project_team_id'], $input['platform'], $input['root_game_id']);
        if (!$is_exist) {
            throw new AppException("数据不存在");
        }

        $model->edit(
            $input['project_team_id'],
            $input['platform'],
            $input['root_game_id'],
            [
                'is_exclusive' => $input['is_exclusive'],
                'type' => $input['type'],
            ]
        );
    }

    /**
     * @param array $input
     * @return void
     */
    public function deleteProjectTeamRootGame(array $input)
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }

        $model = new ProjectTeamRootGameModel();
        $is_exist = $model->getData($input['project_team_id'], $input['platform'], $input['root_game_id']);
        if (!$is_exist) {
            throw new AppException("数据不存在");
        }

        $model->delete(
            $input['project_team_id'],
            $input['platform'],
            $input['root_game_id']
        );
    }

    /**
     * @return string
     */
    public function exportProjectTeamRootGame(): string
    {
        if (!UserService::isSuperManager()) {
            throw new AppException("仅限超管能操作");
        }
        $model = new ProjectTeamRootGameModel();
        $list = $model->getList([]);

        $header = ['business_unit', 'project_team', 'project_team_id', 'platform', 'clique_id', 'clique_name', 'root_game_id', 'root_game_name', 'is_exclusive', 'type', 'insert_time'];
        $content = '';
        foreach ($list as $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                $tmp[] = $item[$column] ?? ' ';
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        $abs_path = SRV_DIR . '/market/' . date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.csv';

        // csv头部
        $data = implode(',', $header) . PHP_EOL;
        $header_str = mb_convert_encoding($data, 'GB18030', 'utf-8');
        file_put_contents($abs_path, $header_str, LOCK_EX);
        // 内容
        $content = mb_convert_encoding($content, 'GB18030', 'utf-8');
        file_put_contents($abs_path, $content, FILE_APPEND | LOCK_EX);
        return $abs_path;
    }

    /**
     * @param $customized_target
     * @param $data
     * @return void
     */
    protected function customizedTarget($customized_target, &$data)
    {
        // 这些指标要特殊处理 要除100
        $target_list = [];

        try {
            foreach ($customized_target as $target_item) {
                $executor = new MathExecutor();
                $column_name = $target_item->name;
                try {
                    foreach ($target_item->formula['column'] as $column) {
                        $column_value = is_array($data) ? $data[$column] : $data->$column;
                        if (in_array($column, $target_list)) {
                            $executor->setVar($column, rtrim($column_value, '%') / 100);
                        } else {
                            $executor->setVar($column, $column_value);
                        }
                    }
                    $res_value = $executor->execute($target_item->formula['formula']);

                } catch (DivisionByZeroException $e) {
                    $res_value = 0;
                }

                if ($target_item->format === 2) {
                    $res_value = Math::decimal($res_value * 100, $target_item->precision);
                    $res_value .= '%';
                } else {
                    $res_value = Math::decimal($res_value, $target_item->precision);
                }
                if (is_array($data)) {
                    $data[$column_name] = $res_value;
                } else {
                    $data->$column_name = $res_value;
                }
            }
        } catch (\Throwable $exception) {
            throw new AppException($exception->getMessage());
        }

    }
}