<?php

namespace App\Logic;

use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\PermissionLogic as DMSPermissionLogic;
use App\Model\RedisModel\DataSumModel;
use App\Model\SqlModel\Tanwan\WechatPushConfigModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Model\SqlModel\Zeda\WechatPushConfigNewModel;

//use App\Model\SqlModel\Zeda\WechatPushDataDmsModel;
//use App\Model\SqlModel\Zeda\WechatPushDataLyModel;
use App\Model\SqlModel\Tanwan\WechatPushDataDmsModel;
use App\Model\SqlModel\DatahubLY\WechatPushDataLyModel;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;


class WechatDataLogic
{
    const BOLD_COLUMNS = [
        'first_pay_back_roi',
        'total_pay_money'
    ];

    public function getWechatPushUrl($start_time, $theme, $user_id)
    {
        $redirect_uri = EnvConfig::WX_API_DOMAIN . "/dataSum/dataSum.html";
        $redirect_uri = urlencode($redirect_uri);
        $state = "{$start_time}_{$theme}_{$user_id}";
        $state = urlencode($state);
        $app_id = EnvConfig::WECHAT['qingbaoju']['appid'];
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$app_id}&redirect_uri={$redirect_uri}&response_type=code&scope=snsapi_base&state={$state}#wechat_redirect";
        return $url;
    }

    public function getZXWechatPushUrl($start_time, $theme, $user_id)
    {
        $redirect_uri = EnvConfig::WX_API_DOMAIN . "/dataSum/dataSumQbj.html";
        $redirect_uri = urlencode($redirect_uri);
        $state = "{$start_time}_{$theme}_{$user_id}";
        $state = urlencode($state);
        $app_id = EnvConfig::WECHAT['zx_qbj']['appid'];
        $url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$app_id}&redirect_uri={$redirect_uri}&response_type=code&scope=snsapi_base&state={$state}#wechat_redirect";
        return $url;
    }

    public function getFeishuPushUrl($start_time, $theme, $user_id)
    {
        $theme = urlencode($theme);
        return EnvConfig::WX_API_DOMAIN . "/dataSum.html?type=feishu&user_id={$user_id}&theme={$theme}&start_time={$start_time}";
    }

    public function getOpenId($code, $config_name = 'qingbaoju')
    {
        $app_id = EnvConfig::WECHAT[$config_name]['appid'];
        $secret = EnvConfig::WECHAT[$config_name]['secret'];
        $grant_type = 'authorization_code';
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$app_id}&secret=${secret}&code={$code}&grant_type={$grant_type}";
        $response = Helpers::getCurl($url);
        $response_array = json_decode($response, true);
        if (empty($response_array)) {
            return false;
        }
        return $response_array['openid'];
    }

    public function bindCode($open_id, $start_time, $theme, $user_id)
    {
        $model = new DataSumModel();
        $code = Container::getUUID();
        $model->set($code, $open_id, $start_time, $theme, $user_id);
        return $code;
    }

    public function getDataSum($input)
    {
        $type = $input['type'] ?? 'wechat';

        switch ($type) {
            case "wechat" :
                $code = $input['code'];
                $value = $this->getConfig($code);

                // TODO 测试数据
//                $value['start_time'] = 1709481600;
//                $value['user_id'] = 176;
//                $value['theme'] = '斗罗';
//                $value['open_id'] = 'oVv9P1ov0YrwSbp1DYiUMvhxeLcA';


//                // TODO 测试数据
//                $value['start_time'] = 1695052800;
//                $value['user_id'] = 30;
//                $value['theme'] = '传世5';
//                $value['open_id'] = 'oVv9P1pJoSAzjOmkiw_Qovz7cyqg';

                // TODO 测试数据
//                $value['start_time'] = 1690992000;
//                $value['user_id'] = 30;
//                $value['theme'] = '全民江湖';
//                $value['open_id'] = 'oVv9P1pJoSAzjOmkiw_Qovz7cyqg';
                if (!$value) {
                    return false;
                }
                $time_benchmark = $value['start_time'];
                $open_id = $value['open_id'];
                $theme = $value['theme'];
                $user_id = $value['user_id'];
                $user_model = new UserModel();
                $user_info = $user_model->getDataByOpenId($open_id);
                if (!$user_info) {
                    throw new AppException("找不到该用户");
                }
                break;
            case "feishu" :
                $time_benchmark = $input['start_time'];
                $theme = urldecode($input['theme']);
                $user_id = $input['user_id'];
                break;
            default:
                throw new AppException('参数异常');
        }

        //兼容旧数据加一天
        if ($time_benchmark <= 1628611200) {
            $time_benchmark = $time_benchmark + 86400;
        }
//         $time_benchmark = 1656778200+86400;
//         $open_id = 'oVv9P1ov0YrwSbp1DYiUMvhxeLcA';
//         $theme = '牛逼传奇测试';

        $sum_date = date("Y-m-d", $time_benchmark);
        $sum_hour = date("G", $time_benchmark);
        $last_sum_date = date("Y-m-d", $time_benchmark - (24 * 60 * 60));
        if ($sum_hour == 0) {
            $show_date = date("m月d日", $time_benchmark - 86400);
            $show_week = Helpers::getWeekCN($time_benchmark - 86400);
            $show_hour = '24:00';
        } else {
            $show_date = date("m月d日", $time_benchmark);
            $show_week = Helpers::getWeekCN($time_benchmark);
            $show_hour = $sum_hour . ':00';
        }

        //$user_id = $user_info->id;

        $config_model = new WechatPushConfigNewModel();
        $config_dms = $config_model->getModuleConfig($user_id, 'dms', $theme);
        $config_ly = $config_model->getModuleConfig($user_id, 'ly', $theme);
        if (!$config_dms && !$config_ly) {
            throw new AppException("找不到该用户配置");
        }

        $both = false;
        $sub_themes = [];
        if ($config_dms) {
            $permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
            $configs = json_decode($config_dms->config, true);

            // 合并万紫的数据
            $configs = $this->mergeThemeConfig($configs, $theme, 'dms', $config_dms->account);
            //每一个子配置
            foreach ($configs as $sub_config) {
                $sub_themes[] = $this->getDmsData($sub_config, $sum_date, $last_sum_date, $sum_hour, $permission);
            }
            $both = true;
        }
        if ($config_ly) {
            $configs = json_decode($config_ly->config, true);
            // 合并万紫的数据
            $configs = $this->mergeThemeConfig($configs, $theme, 'ly', $config_ly->account);
            //每一个子配置
            foreach ($configs as $sub_config) {
                $ly_data = $this->getLyData($sub_config, $sum_date, $last_sum_date, $sum_hour);
                if ($both === false) {
                    $sub_themes[] = $ly_data;
                } else {
                    foreach ($sub_themes as &$sub_theme) {
                        if ($sub_theme['theme_name'] == $ly_data['theme_name']) {
                            if ($sub_theme['data']->where('type', '合计汇总') && $ly_data['data']->where('type', '合计汇总')) {
                                $item1 = $sub_theme['data']->where('type', '合计汇总')->first();
                                $item2 = $ly_data['data']->where('type', '合计汇总')->first();
                                $item1 = $this->sumTotal($item1, $item2);
                                $sub_theme['data']->merge([$item1]);
                                $ly_data['data'] = $ly_data['data']->filter(function ($value) {
                                    return $value->type !== '合计汇总';
                                });

                                $item1 = $sub_theme['last_data']->where('type', '合计汇总')->first();
                                $item2 = $ly_data['last_data']->where('type', '合计汇总')->first();
                                $item1 = $this->sumTotal($item1, $item2);
                                $sub_theme['last_data']->merge([$item1]);
                                $ly_data['last_data'] = $ly_data['last_data']->filter(function ($value) {
                                    return $value->type !== '合计汇总';
                                });
                            }
                            $sub_theme['data'] = $sub_theme['data']->merge($ly_data['data']);
                            $sub_theme['last_data'] = $sub_theme['last_data']->merge($ly_data['last_data']);
                        }
                    }
                }
            }
        }

        if (count($sub_themes) === 1 && $sub_themes[0]['theme_name'] === '子主题1') {
            $sub_themes[0]['theme_name'] = ' ';
        }
        $target = $config_dms ? json_decode($config_dms->target, true) : json_decode($config_ly->target, true);
        return [
            'sub_theme'    => $sub_themes,
            'target'       => $target,
            'theme'        => $theme,
            'bold_columns' => self::BOLD_COLUMNS,
            'sum_date'     => "{$show_date} {$show_week}",
            'sum_hour'     => "{$show_hour} "
        ];
    }


    public function getDataSumPush($time_benchmark, $user_id, $theme)
    {
        $sum_date = date("Y-m-d", $time_benchmark);
        $sum_hour = date("G", $time_benchmark);
        $last_sum_date = date("Y-m-d", $time_benchmark - (24 * 60 * 60));
        if ($sum_hour == 0) {
            $show_date = date("m月d日", $time_benchmark - 86400);
            $show_week = Helpers::getWeekCN($time_benchmark - 86400);
            $show_hour = '24:00';
        } else {
            $show_date = date("m月d日", $time_benchmark);
            $show_week = Helpers::getWeekCN($time_benchmark);
            $show_hour = $sum_hour . ':00';
        }


        $config_model = new WechatPushConfigNewModel();
        $config_dms = $config_model->getModuleConfig($user_id, 'dms', $theme);
        $config_ly = $config_model->getModuleConfig($user_id, 'ly', $theme);
        if (!$config_dms && !$config_ly) {
            throw new AppException("找不到该用户配置");
        }

        $both = false;
        $sub_themes = [];
        if ($config_dms) {
            $permission = (new DMSPermissionLogic())->getDataPermissionByUserId($user_id);
            $configs = json_decode($config_dms->config, true);

            // 合并万紫的数据
            $configs = $this->mergeThemeConfig($configs, $theme, 'dms', $config_dms->account);
            //每一个子配置
            foreach ($configs as $sub_config) {
                $sub_themes[] = $this->getDmsData($sub_config, $sum_date, $last_sum_date, $sum_hour, $permission);
            }
            $both = true;
        }
        if ($config_ly) {
            $configs = json_decode($config_ly->config, true);
            // 合并万紫的数据
            $configs = $this->mergeThemeConfig($configs, $theme, 'ly', $config_ly->account);
            //每一个子配置
            foreach ($configs as $sub_config) {
                $ly_data = $this->getLyData($sub_config, $sum_date, $last_sum_date, $sum_hour);
                if ($both === false) {
                    $sub_themes[] = $ly_data;
                } else {
                    foreach ($sub_themes as &$sub_theme) {
                        if ($sub_theme['theme_name'] == $ly_data['theme_name']) {
                            if ($sub_theme['data']->where('type', '合计汇总') && $ly_data['data']->where('type', '合计汇总')) {
                                $item1 = $sub_theme['data']->where('type', '合计汇总')->first();
                                $item2 = $ly_data['data']->where('type', '合计汇总')->first();
                                $item1 = $this->sumTotal($item1, $item2);
                                $sub_theme['data']->merge([$item1]);
                                $ly_data['data'] = $ly_data['data']->filter(function ($value) {
                                    return $value->type !== '合计汇总';
                                });

                                $item1 = $sub_theme['last_data']->where('type', '合计汇总')->first();
                                $item2 = $ly_data['last_data']->where('type', '合计汇总')->first();
                                $item1 = $this->sumTotal($item1, $item2);
                                $sub_theme['last_data']->merge([$item1]);
                                $ly_data['last_data'] = $ly_data['last_data']->filter(function ($value) {
                                    return $value->type !== '合计汇总';
                                });
                            }
                            $sub_theme['data'] = $sub_theme['data']->merge($ly_data['data']);
                            $sub_theme['last_data'] = $sub_theme['last_data']->merge($ly_data['last_data']);
                        }
                    }
                }
            }
        }

        if (count($sub_themes) === 1 && $sub_themes[0]['theme_name'] === '子主题1') {
            $sub_themes[0]['theme_name'] = ' ';
        }
        $target = $config_dms ? json_decode($config_dms->target, true) : json_decode($config_ly->target, true);
        return [
            'sub_theme'    => $sub_themes,
            'target'       => $target,
            'theme'        => $theme,
            'bold_columns' => self::BOLD_COLUMNS,
            'sum_date'     => "{$show_date} {$show_week}",
            'sum_hour'     => "{$show_hour} "
        ];
    }


    /**
     * 合并同主题名的配置
     *
     * @param        $config
     * @param        $theme
     * @param        $model
     * @param        $account
     *
     * @return mixed
     */
    public function mergeThemeConfig($config, $theme, $model, $account)
    {
        // 首先去找一下 有没有同名的主题
        $same_data = (new WechatPushConfigModel())->getConfigByTheme($theme, $account);
        // TODO 测试数据
//        $config_json = '[{"sub_theme":"就是游戏日报","sub_config":[{"sum":1,"info":[{"os":[],"ag_name":[],"alg_name":[],"platform":"TW","main_game_id":["122","194","375","124","391","403","521","298","541","142"],"root_game_id":["28"]}],"type":"TW-555","month_total_pay_money_goal":"100000"},{"sum":1,"info":[{"os":[],"ag_name":[],"alg_name":[],"platform":"TW","main_game_id":["505","483","463","513"],"root_game_id":["351"]}],"type":"TW-神器"}]}]';
//        $json = '[{"sub_theme":"就是游戏日报","sub_config":[{"sum":1,"info":[{"os":["安卓"],"ag_name":["194"],"alg_name":["1-头条-俊雯组"],"platform":"wanzi","main_game_id":["34","140"],"root_game_id":["2","3"]}],"type":"TW-555","month_total_pay_money_goal":"100000"}]}]';
//        $config = json_decode($config_json, true);
//        $same_data = true;
//        $same_config = json_decode($json, true);


        // 存在相同的主题，需要合并里面的数据
        if ($same_data) {
            // 还需要判断子主题是否相同
            $same_config = json_decode($same_data->config, true);

            // 用sub_theme子主题做索引
            $same_config = array_column($same_config, null, 'sub_theme');
            foreach ($config as &$sub_config) {
                // 如果不存在相同子主题的配置，则直接跳过
                if (!isset($same_config[$sub_config['sub_theme']])) {
                    continue;
                }

                // 存在 则合并
                $this->mergeInfoData($sub_config, $same_config[$sub_config['sub_theme']], $model);
            }

        }

        return $config;
    }

    function mergeInfoData(array &$arr1, array &$arr2, $model)
    {
        foreach ($arr2['sub_config'] as $item2) {
            $type_found = false;
            foreach ($arr1['sub_config'] as &$item1) {
                if ($item1['type'] === $item2['type']) {
                    $type_found = true;
                    $this->mergeInfo($item1['info'], $item2['info'], $model);
                    break;
                }
            }
            if (!$type_found) {
                // 插到前面来
                array_unshift($arr1['sub_config'], $item2);
                // $arr1['sub_config'][] = $item2;
            }
        }
    }

    private function mergeInfo(array &$info1, array $info2, $model)
    {
        foreach ($info2 as $item2) {
            $platformFound = false;
            foreach ($info1 as &$item1) {
                if ($item1['platform'] === $item2['platform']) {
                    $platformFound = true;
                    $this->mergePlatformData($item1, $item2, $model);
                    break;
                }
            }
            if (!$platformFound) {
                $info1[] = $item2;
            }
        }
    }

    private function mergePlatformData(array &$platform1, array $platform2, $model)
    {
        if ($model === 'dms') {
            $platform1['os'] = array_unique(array_merge($platform1['os'], $platform2['os']));
            $platform1['ag_name'] = array_unique(array_merge($platform1['ag_name'], $platform2['ag_name']));
            $platform1['alg_name'] = array_unique(array_merge($platform1['alg_name'], $platform2['alg_name']));
            $platform1['main_game_id'] = array_unique(array_merge($platform1['main_game_id'], $platform2['main_game_id']));
            $platform1['root_game_id'] = array_unique(array_merge($platform1['root_game_id'], $platform2['root_game_id']));
        } else {
            $platform1['channel_id'] = array_unique(array_merge($platform1['os'], $platform2['os']));
            $platform1['ag_name'] = array_unique(array_merge($platform1['ag_name'], $platform2['ag_name']));
            $platform1['game_id'] = array_unique(array_merge($platform1['alg_name'], $platform2['alg_name']));
            $platform1['main_game_id'] = array_unique(array_merge($platform1['main_game_id'], $platform2['main_game_id']));
        }

    }

    private function sumTotal($item1, $item2)
    {
        $keys = ["ori_money", "money", "reg_uid_count", "dby_second_login_rate", "dby_second_login_count", "month_reg_uid_count", "reg_uid_new_pay_count",
                 "uid_count", "old_uid_count", "total_pay_money", "first_day_pay_money", "cost_per_reg_uid_new_pay", "open_service",
                 "month_ori_money", "month_money", "month_total_pay_money", "day_pay_count"];
        foreach ($keys as $key) {
            if (isset($item1->$key) && isset($item2->$key)) {
                if ($key === 'dby_second_login_rate') {
                    $item1->$key = Math::div($item1->dby_second_login_count + $item2->dby_second_login_count, $item1->dby_reg_uid_count + $item2->dby_reg_uid_count);
                } elseif ($key === 'cost_per_reg_uid_new_pay') {
                    $item1->$key = Math::div($item1->money + $item2->money, $item1->reg_uid_new_pay_count + $item2->reg_uid_new_pay_count);
                } else {
                    $item1->$key = $item1->$key + $item2->$key;
                }
            }
        }
        return $item1;
    }

    private function getDmsData($config, $sum_date, $last_sum_date, $sum_hour, $permission)
    {
        $data_model = new WechatPushDataDmsModel();
        $datas = $last_datas = collect();
        $type_info_sum = [];
        $total_goal = 0;
        $sub_theme = $config['sub_theme'];
        //每一个游戏组的
        foreach ($config['sub_config'] as $c) {
            $type = $c['type'];

            $sum = $c['sum'] ?? 0;
            $is_apportioned = $c['is_apportioned'] ?? 1;
            $month_total_pay_money_goal = isset($c['month_total_pay_money_goal']) && is_numeric($c['month_total_pay_money_goal']) ? $c['month_total_pay_money_goal'] : 0;
            $total_goal += $month_total_pay_money_goal;
            $type_info = $alg_names = [];
            foreach ($c['info'] as $data) {
                $platform = $data['platform'];
                $main_game_id = $data['main_game_id'];
                $alg = $data['alg_name'] ?? [];
                $alg_names = array_merge($alg_names, $alg);
                $ag = $data['ag_name'] ?? [];
                $os = $data['os'] ?? [];
                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['main_game_id'   => $main_game_id,
                                             'agent_group_id' => $ag,
                                             'os'             => $os];
                } else {
                    $type_info[$platform]['main_game_id'] = array_merge($type_info[$platform]['main_game_id'], $main_game_id);
                    $type_info[$platform]['agent_group_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                    $type_info[$platform]['os'] = array_merge($type_info[$platform]['os'], $os);
                }
            }
            $alg_names = array_unique($alg_names);
            if ($sum == 1) {
                $type_info_sum[$sub_theme][$type] = [
                    'game'           => $type_info,
                    'alg'            => $alg_names,
                    'is_apportioned' => $is_apportioned,
                ];
            }

            $data = $last_data = [];
            $one_data = $data_model->getTypeReport($c, $type_info, $alg_names, $sum_date, $last_sum_date, $sum_hour, $permission, $is_apportioned);
            if ($one_data->where('tdate', $sum_date)->isNotEmpty()) {
                $data[] = $one_data->where('tdate', $sum_date)->first();
            } else {
                $data[] = collect([['type'                       => $c['type'], "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                    "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                    "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                    'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            if ($one_data->where('tdate', $last_sum_date)->isNotEmpty()) {
                $last_data[] = $one_data->where('tdate', $last_sum_date)->first();
            } else {
                $last_data[] = collect([['type'                       => $c['type'], "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                         "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                         "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                         'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            $datas = $datas->merge($data);
            $last_datas = $last_datas->merge($last_data);
        }

        //合计
        if (count($type_info_sum) > 0) {
            $sum_data = $data_model->getDataSum($type_info_sum, $total_goal, $sum_date, $last_sum_date, $sum_hour, $permission);
            if ($sum_data->where('tdate', $sum_date)->isNotEmpty()) {
                $first_sum_data[] = $sum_data->where('tdate', $sum_date)->first();
            } else {
                $first_sum_data[] = collect([['type'                       => '合计汇总', "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                              "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                              "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                              'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            if ($sum_data->where('tdate', $last_sum_date)->isNotEmpty()) {
                $last_sum_data[] = $sum_data->where('tdate', $last_sum_date)->first();
            } else {
                $last_sum_data[] = collect([['type'                       => '合计汇总', "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                             "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                             "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                             'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            $datas = $datas->merge($first_sum_data);
            $last_datas = $last_datas->merge($last_sum_data);
        }

        return [
            'theme_name' => $config['sub_theme'],
            'data'       => $datas,
            'last_data'  => $last_datas
        ];
    }

    private function getLyData($config, $sum_date, $last_sum_date, $sum_hour)
    {
        $data_model = new WechatPushDataLyModel();
        $datas = $last_datas = collect();
        $type_info_sum = [];
        $total_goal = 0;
        foreach ($config['sub_config'] as $c) {
            $sum = $c['sum'] ?? 0;
            $month_total_pay_money_goal = isset($c['month_total_pay_money_goal']) && is_numeric($c['month_total_pay_money_goal']) ? $c['month_total_pay_money_goal'] : 0;
            $total_goal += $month_total_pay_money_goal;

            $type_info = [];
            foreach ($c['info'] as $data) {
                $platform = $data['platform'];
                $game_id = $data['game_id'];
                $channel_id = $data['channel_id'] ?? [];
                $ag = $data['ag_name'] ?? [];
                if (!isset($type_info[$platform])) {
                    $type_info[$platform] = ['game_id'        => $game_id,
                                             'channel_id'     => $channel_id,
                                             'agent_group_id' => $ag];
                } else {
                    $type_info[$platform]['game_id'] = array_merge($type_info[$platform]['game_id'], $game_id);
                    $type_info[$platform]['channel_id'] = array_merge($type_info[$platform]['channel_id'], $channel_id);
                    $type_info[$platform]['agent_group_id'] = array_merge($type_info[$platform]['agent_group_id'], $ag);
                }
            }
            if ($sum == 1) {
                $type_info_sum[] = $type_info;
            }

            $data = $last_data = [];
            $one_data = $data_model->getTypeReport($c, $type_info, $sum_date, $last_sum_date, $sum_hour);
            if ($one_data->where('tdate', $sum_date)->isNotEmpty()) {
                $data[] = $one_data->where('tdate', $sum_date)->first();
            } else {
                $data[] = collect([['type'                       => $c['type'], "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                    "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                    "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                    'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            if ($one_data->where('tdate', $last_sum_date)->isNotEmpty()) {
                $last_data[] = $one_data->where('tdate', $last_sum_date)->first();
            } else {
                $last_data[] = collect([['type'                       => $c['type'], "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                         "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                         "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                         'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            $datas = $datas->merge($data);
            $last_datas = $last_datas->merge($last_data);
        }

        if (count($type_info_sum) > 0) {
            $first_sum_data = $last_sum_data = [];
            $sum_data = $data_model->getDataSum($type_info_sum, $total_goal, $sum_date, $last_sum_date, $sum_hour);
            if ($sum_data->where('tdate', $sum_date)->isNotEmpty()) {
                $first_sum_data[] = $sum_data->where('tdate', $sum_date)->first();
            } else {
                $first_sum_data[] = collect([['type'                       => '合计汇总', "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                              "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                              "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                              'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            if ($sum_data->where('tdate', $last_sum_date)->isNotEmpty()) {
                $last_sum_data[] = $sum_data->where('tdate', $last_sum_date)->first();
            } else {
                $last_sum_data[] = collect([['type'                       => '合计汇总', "ori_money" => 0, "money" => 0, "reg_uid_count" => 0, "dby_second_login_rate" => 0.00, "dby_second_login_count" => 0, "month_reg_uid_count" => 0,
                                             "reg_uid_new_pay_count"      => 0, "uid_count" => 0, "old_uid_count" => 0, "total_pay_money" => 0, "first_day_pay_money" => 0, "cost_per_reg_uid_new_pay" => 0,
                                             "open_service"               => 0, "month_ori_money" => 0, "month_money" => 0, "month_total_pay_money" => 0, "day_pay_count" => 0,
                                             'month_total_pay_money_goal' => 0, 'month_total_pay_money_progress' => 0]])
                    ->first();
            }
            $datas = $datas->merge($first_sum_data);
            $last_datas = $last_datas->merge($last_sum_data);
        }

        return [
            'theme_name' => $config['sub_theme'],
            'data'       => $datas,
            'last_data'  => $last_datas
        ];
    }

    private function getConfig($code)
    {
        $data_sum_model = new DataSumModel();
        $value = $data_sum_model->get($code);
        if (!$value) {
            return false;
        }
        $data_sum_model->delete($code);
        return $value;
    }
}
