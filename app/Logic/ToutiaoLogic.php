<?php


namespace App\Logic;

use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\HttpModel\Toutiao\CustomerCenter\AdvertiserModel as CustomerAdvertiserModel;
use App\Model\HttpModel\Toutiao\Advertiser\AdvertiserModel;
use App\Model\HttpModel\Toutiao\Majordomo\MajordomoModel;
use App\Model\HttpModel\Toutiao\Oauth2Model;
use App\Model\HttpModel\Toutiao\Subscribe\SubscribeModel;
use App\Model\HttpModel\Toutiao\User\UserModel as AuthUserModel;
use App\Model\RedisModel\AudienceChangeLogRedisModel;
use App\Model\SqlModel\DataMedia\OdsAccountAuthorizationLogModel;
use App\Model\SqlModel\Zeda\AudienceChangeLogModel;
use App\Model\SqlModel\Zeda\AudienceModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Model\SqlModel\Zeda\ToutiaoAudienceChangeLogModel;
use App\Model\SqlModel\Zeda\ToutiaoRdsSubscribeLogModel;
use App\Model\SqlModel\Zeda\ToutiaoRdsTaskModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\MysqlConnection;
use App\Param\MediaAccountInfoParam;
use App\Param\MediaMajordomoAccountInfoParam;
use App\Service\MediaService;
use App\Utils\Helpers;
use Exception;
use Illuminate\Support\Collection;

class ToutiaoLogic
{
    const RDS_TASK_LIST = [
        [
            "app_name" => "adsGroup",
            "app_id" => ****************,
            "description" => "adsGroup_2.0广告小时报表",
            "id" => ****************,
            "subscribe_id" => ****************
        ],
        [
            "app_name" => "adsGroup",
            "app_id" => ****************,
            "description" => "adsGroup_2.0素材小时报表",
            "id" => ****************,
            "subscribe_id" => ****************
        ],
        [
            "app_name" => "adsGroup01",
            "app_id" => ****************,
            "description" => "adsGroup01_2.0广告小时报表",
            "id" => ****************,
            "subscribe_id" => ****************
        ],
        [
            "app_name" => "adsGroup01",
            "app_id" => ****************,
            "description" => "adsGroup01_2.0素材小时报表",
            "id" => 1836414734459912,
            "subscribe_id" => ****************
        ],
        [
            "app_name" => "adsGroup02",
            "app_id" => 1694447725468692,
            "description" => "adsGroup02_2.0广告小时报表",
            "id" => 1836415200905731,
            "subscribe_id" => 1694447725468692
        ],
        [
            "app_name" => "adsGroup02",
            "app_id" => 1694447725468692,
            "description" => "adsGroup02_2.0素材小时报表",
            "id" => 1836414698990601,
            "subscribe_id" => 1694447725468692
        ],
        [
            "app_name" => "adsGroup03",
            "app_id" => 1694460456435715,
            "description" => "adsGroup03_2.0广告小时报表",
            "id" => 1836415228466247,
            "subscribe_id" => 1694460456435715
        ],
        [
            "app_name" => "adsGroup03",
            "app_id" => 1694460456435715,
            "description" => "adsGroup03_2.0素材小时报表",
            "id" => 1836414623983753,
            "subscribe_id" => 1694460456435715
        ],
        [
            "app_name" => "adstanwan",
            "app_id" => 1598615457838094,
            "description" => "adstanwan_2.0广告小时报表",
            "id" => 1836415036630683,
            "subscribe_id" => 1598615457838094
        ],
        [
            "app_name" => "adstanwan",
            "app_id" => 1598615457838094,
            "description" => "adstanwan_2.0素材小时报表",
            "id" => 1836414956492427,
            "subscribe_id" => 1598615457838094
        ],
        [
            "app_name" => "adstanwan2",
            "app_id" => 1626046377857053,
            "description" => "adstanwan2_2.0广告小时报表",
            "id" => 1836415069958602,
            "subscribe_id" => 1626046377857053
        ],
        [
            "app_name" => "adstanwan2",
            "app_id" => 1626046377857053,
            "description" => "adstanwan2_2.0素材小时报表",
            "id" => 1836414925417924,
            "subscribe_id" => 1626046377857053
        ]
    ];

    /**
     * 头条授权
     *
     * @param $state
     * @param $auth_code
     * @return array
     * @throws Exception
     */
    public function saveToken($state, $auth_code)
    {
        $platform = $state['platform'];
        $company = $state['company'];
        $oauth2_model = new Oauth2Model();
        $user_model = new UserModel();
        $user_info = $user_model->getData($state['creator_id']);
        if (!$user_info) {
            $msg = "找不到该用户：creator_id: {$state['creator_id']}";
            Helpers::getLogger('toutiao')->error($msg);
            throw new AppException($msg);
        }
        $creator_name = $user_info->name;

        $developer_info = (new MediaDeveloperAccountModel())->getDataByPlatformMediaCompany($platform, MediaType::TOUTIAO, $company);
        if (!$developer_info) {
            throw new AppException("找不到头条开发者，平台: $platform");
        }

        $access_token_info = $oauth2_model->accessToken($developer_info->app_id, $developer_info->app_secret, $auth_code);
        $oauth2_advertisers = $oauth2_model->advertiserGet($access_token_info['access_token']);
        if (empty($oauth2_advertisers['list'])) {
            $msg = "找不到该广告主信息, 平台: $platform, access_token: {$access_token_info['access_token']}";
            Helpers::getLogger('toutiao')->error($msg, $access_token_info);
            throw new AppException($msg);
        }

        $majordomos = [];
        foreach ($oauth2_advertisers['list'] as $oauth2_advertiser) {
            if ($oauth2_advertiser['account_role'] === 'CUSTOMER_ADMIN') {
                $majordomos[] = $oauth2_advertiser;
            }
        }
        if (empty($majordomos)) {
            $msg = "只支持（管家-管理员）授权";
            Helpers::getLogger('toutiao')->error($msg, $oauth2_advertisers);
            throw new AppException($msg);
        }

        // 订阅RDS
        $rds_account_ids = [];

        $request_time = date("Y-m-d H:i:s");
        $message = '';
        $advertiser_model = new AdvertiserModel();
        $media_service = new MediaService();
        $majordomo_ids = array_column($majordomos, 'advertiser_id');
        $script_auth_account_list = (new OdsAccountAuthorizationLogModel())
            ->getListByAccountIds(MediaType::TOUTIAO, $majordomo_ids)
            ->keyBy('account_id')
            ->toArray();

        $now = time();
        $majordomo_model = new MajordomoModel();
        foreach ($majordomos as $majordomo) {
            $advertiser_info_list = [];
            $add_account_ids = [];
            $need_update_budget_ids = [];
            if (in_array($majordomo['advertiser_id'], [****************, ****************])) {
                $advertisers = $this->getSubAccountList($majordomo['advertiser_id'], $access_token_info['access_token']);
                // 媒体上有的账号id
                $account_ids = array_column($advertisers, 'advertiser_id');
            } else {
                $advertisers = $majordomo_model->select($majordomo['advertiser_id'], $access_token_info['access_token']);
                // 媒体上有的账号id
                $account_ids = array_column($advertisers['list'], 'advertiser_id');
            }
            if (!empty($account_ids)) {
                // 数据库纪录的账号id
                $advertiser_list = (new MediaAccountModel())->getAllByMajordomoId($majordomo['advertiser_id']);
                $advertiser_account_ids = $advertiser_list->pluck('account_id')->toArray();

                // 对比后没有在数据库的账号访问接口 获取company
                $add_account_ids = array_diff($account_ids, $advertiser_account_ids);
                foreach (array_chunk($add_account_ids, 100) as $ids) {
                    $tmp_ids = array_values($ids);
                    $tmp_advertiser_info_list = $advertiser_model->info(
                        $tmp_ids,
                        $access_token_info['access_token'],
                        ['id', 'name', 'company']);
                    $advertiser_info_list = array_merge($advertiser_info_list, $tmp_advertiser_info_list);

                    // 获取日预算为无限的账号，然后设置10w预算
                    $budget_list = $advertiser_model->getBudget($tmp_ids, $access_token_info['access_token']);
                    foreach ($budget_list['list'] as $budget) {
                        if ($budget['budget_mode'] === 'BUDGET_MODE_INFINITE') {
                            $need_update_budget_ids[] = $budget['advertiser_id'];
                        }
                    }
                }

                // 整合数据
                foreach ($advertiser_list as $item) {
                    if (in_array($item->account_id, $account_ids)) {
                        $advertiser_info_list[] = [
                            'id' => $item->account_id,
                            'name' => $item->account_name,
                            'company' => $item->company
                        ];
                    }
                }
            }

            $script_auth_account_info = $script_auth_account_list[$majordomo['advertiser_id']] ?? [];
            try {
                MysqlConnection::getConnection('default')->beginTransaction();
                $media_service->saveAuthMajordomoAccount(
                    new MediaMajordomoAccountInfoParam([
                        'platform' => $platform,
                        'media_type' => MediaType::TOUTIAO,
                        'access_token' => $access_token_info['access_token'],
                        'access_token_expires' => $now + $access_token_info['expires_in'],
                        'refresh_token' => $access_token_info['refresh_token'],
                        'refresh_token_expires' => $now + $access_token_info['refresh_token_expires_in'],
                        'account_id' => $majordomo['advertiser_id'],
                        'account' => empty($script_auth_account_info) ? '' : $script_auth_account_info->account,
                        'password' => empty($script_auth_account_info) ? '' : $script_auth_account_info->password,
                        'name' => $majordomo['advertiser_name'],
                        'company' => $company,
                        'developer_app_id' => $developer_info->app_id
                    ]), $state['creator_id'], $creator_name
                );
                if (!empty($advertiser_info_list)) {
                    foreach ($advertiser_info_list as $advertiser) {
                        $media_account_param = new MediaAccountInfoParam([
                            'media_type' => MediaType::TOUTIAO,
                            'platform' => $platform,
                            'account_id' => $advertiser['id'],
                            'account_name' => $advertiser['name'],
                            'access_token' => $access_token_info['access_token'],
                            'access_token_expires' => 0,
                            'refresh_token' => '',
                            'refresh_token_expires' => 0,
                            'company' => $advertiser['company'],
                            'toutiao_majordomo_id' => $majordomo['advertiser_id'],
                            'create_time' => time(),
                            'agent' => $developer_info->app_id
                        ]);
                        $tmp_message = $media_service->saveAuthAccount($media_account_param, $state['creator_id'], $creator_name);
                        $message .= "{$advertiser['name']}-$tmp_message;";

                        // 需要订阅rds的账号
                        if (strpos($tmp_message, '不允许授权') === false && in_array($advertiser['id'], $add_account_ids)) {
                            $rds_account_ids[] = (int)$advertiser['id'];
                        }

                        // 更新新账号预算 防止单账号跑多消耗
                        if (
                            strpos($tmp_message, '不允许授权') !== false
                            || !in_array($advertiser['id'], $add_account_ids)
                            || !in_array($advertiser['id'], $need_update_budget_ids)
                        ) {
                            continue;
                        }
                        $advertiser_model->updateAdvertiserBudget([
                            'advertiser_id' => (int)$advertiser['id'],
                            'budget_mode' => 'BUDGET_MODE_DAY',
                            'budget' => 100000
                        ], $access_token_info['access_token']);
                    }
                }
                MysqlConnection::getConnection('default')->commit();
            } catch (Exception $e) {
                MysqlConnection::getConnection('default')->rollBack();
                throw $e;
            }
        }

        // 订阅rds
        if (!empty($rds_account_ids)) {
            $this->addRdsSubscribeLog($developer_info->app_id, $rds_account_ids);
        }

        $log_majordomo_ids = implode(',', $majordomo_ids);
        Helpers::getLogger('toutiao')->info("管家账号：{$log_majordomo_ids} 授权成功", [
            'creator' => $creator_name,
            'creator_id' => $state['creator_id'],
            'majordomo_account_id' => $majordomo_ids,
            'request_time' => $request_time,
            'response_time' => date("Y-m-d H:i:s"),
            'access_token_info' => $access_token_info
        ]);

        return [
            'access_token_info' => $access_token_info,
            'account_info' => $oauth2_advertisers,
            'message' => $message,
        ];
    }

    public function updateDailyAudience($day, $auto_clear_cache = true)
    {
        if ($auto_clear_cache) {
            // 清理前一天的文件缓存
            $audience_change_log_redis_model = new AudienceChangeLogRedisModel();
            $audience_change_log_redis_model->delToutiaoUploadFile();
            $audience_change_log_redis_model->delToutiaoFileZip();
        }

        $audience_change_log_model = new AudienceChangeLogModel();
        $audience_model = new AudienceModel();
        $audience_change_list = $audience_change_log_model->getAllByMediaTypeAndChangeTimeAndState(
            MediaType::TOUTIAO,
            strtotime($day),
            AudienceChangeLogModel::NOT_START
        );
        $audience_change_group_list = $audience_change_list->groupBy('audience_id');
        $toutiao_audience_change_log_model = new ToutiaoAudienceChangeLogModel();
        /** @var Collection $audience_id_change_list */
        foreach ($audience_change_group_list as $audience_id => $audience_id_change_list) {
            $audience_info = $audience_model->getData($audience_id);
            if (empty($audience_info)) {
                Helpers::getLogger('dmp')->error("自动更新头条人群包获取audience信息失败", [
                    'audience_id' => $audience_id,
                ]);
                continue;
            }

            // 入表
            $toutiao_audience_change_log_id = $toutiao_audience_change_log_model->add(
                $audience_info->account_id,
                $audience_id,
                $audience_info->audience_id,
                $audience_info->data_source_id,
                strtotime($day),
                $audience_info->publish_time
            );
            if (!($toutiao_audience_change_log_id > 0)) {
                Helpers::getLogger('dmp')->error("自动更新时添加toutiao_audience_change_log失败", [
                    'audience_id' => $audience_id,
                    'external_audience_id' => $audience_info->audience_id,
                    'data_source_id' => $audience_info->data_source_id,
                    'change_time' => strtotime($day),
                ]);
                continue;
            }
            $audience_change_log_model->updateStateByAudienceIdAndChangeTime($audience_id, strtotime($day), AudienceChangeLogModel::PROCESSING);
        }
    }

    /**
     * 获取工作台下的子账号列表
     * @param $majordomo_id
     * @param $access_token
     * @return array
     * @throws Exception
     */
    public function getSubAccountList($majordomo_id, $access_token)
    {
        $page = 1;
        $page_size = 100;
        $account_list = [];
        $advertiser_model = new CustomerAdvertiserModel();
        while (true) {
            $data = $advertiser_model->list($majordomo_id, $access_token, $page, $page_size);
            $list = $data['list'];

            foreach ($list as $item) {
                // 只同步广告主账号
                if ($item['advertiser_type'] !== 'NORMAL') {
                    continue;
                }
                $account_id = $item['advertiser_id'];
                $account_list[$account_id] = $item;
            }

            $page_info = $data['page_info'];
            $total_page = $page_info['total_page'] ?? 0;
            $page++;
            if ($page > $total_page) {
                break;
            }
        }

        return $account_list;
    }

    /**
     * 添加订阅任务
     * @param $app_id
     * @param $rds_account_ids
     * @return void
     */
    public function addRdsSubscribeLog($app_id, $rds_account_ids)
    {
        $list = [];
        $task_list = (new ToutiaoRdsTaskModel())->getAll();
        foreach ($task_list as $task) {
            if ($task->app_id == $app_id) {
                foreach ($rds_account_ids as $rds_account_id) {
                    $list[] = [
                        'app_id' => $app_id,
                        'account_id' => $rds_account_id,
                        'subscribe_task_id' => $task->subscribe_task_id,
                    ];
                }
            }
        }

        (new ToutiaoRdsSubscribeLogModel())->addMultiple($list);
    }

    /**
     * 订阅RDS
     * @param $rds_data
     * @param $rds_account_ids
     * @return void
     */
    public function subscribeRDS($rds_data, $rds_account_ids)
    {
        $subscribe_model = new SubscribeModel();
        // 接口一次最多500个账号
        $account_chunk = array_chunk($rds_account_ids, 500);
        foreach ($account_chunk as $chunk) {
            foreach (self::RDS_TASK_LIST as $task) {
                if ($task['app_id'] == $rds_data['app_id']) {
                    $data = [
                        'app_id' => (int)$rds_data['app_id'],
                        'subscribe_task_id' => (int)$task['id'],
                        'core_user_id' => (int)$rds_data['core_user_id'] ?? 0,
                        'advertiser_ids' => $chunk
                    ];

                    try {
                        $subscribe_model->subscribeRDS($rds_data['app_access_token'], $data);
                    } catch (Exception $e) {
                        Helpers::getLogger('toutiao')->error('订阅RDS失败', [
                            'msg' => $e->getMessage(),
                            'data' => $data
                        ]);
                        continue;
                    }
                }
            }
        }
    }
}
