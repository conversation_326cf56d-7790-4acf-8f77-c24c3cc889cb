<?php
/**
 * 贪玩数据库管理
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;


use App\Constant\ResponseCode;
use App\Struct\Input;
use App\Logic\TWDBLogLogic;

class TwdblogController extends Controller
{
    /**
     * 全部图表
     * @param Input $input
     * @return array
     * <AUTHOR>
     */
    public function chartData(Input $input)
    {
        $input->verify(['start_time', 'end_time']);
        $logic = new TWDBLogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getChartData($input['start_time'], $input['end_time'])
        ];
    }

    /**
     * 图标
     * @param Input $input
     * @return array
     * <AUTHOR>
     */
    public function pieData(Input $input)
    {
        $input->verify(['start_time', 'end_time', 'table']);
        $logic = new TWDBLogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getPieData($input['table'], $input['platform'], $input['start_time'], $input['end_time'])
        ];
    }
}