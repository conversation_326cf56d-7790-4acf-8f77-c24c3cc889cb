<?php


namespace App\Controller;


use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\OrderPayRateDataLogic;
use App\Response\Redirect;
use App\Logic\DMS\ReportLogic;
use App\Logic\WechatDataLogic;
use App\Struct\Input;
use Common\EnvConfig;

class DataSumController extends Controller
{
    protected $pass_method = [
        'dataSum',
        'dataSumQbj',
        'getDataSumReport',
        'getDataSumPushReport',
        'orderPayRateDataSum',
        'orderPayRateDataSumQbj',
        'getOrderPayRateDataSumReport'
    ];

    public function dataSum(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) != 3) {
            throw new AppException("参数错误");
        }
        [$start_time, $theme, $user_id] = $params;

        $logic = new WechatDataLogic();
        $open_id = $logic->getOpenId($code);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time, $theme, $user_id);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/dataSum.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function dataSumQbj(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $config_name = 'zx_qbj';
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) != 3) {
            throw new AppException("参数错误");
        }
        [$start_time, $theme, $user_id] = $params;

        $logic = new WechatDataLogic();
        $open_id = $logic->getOpenId($code, $config_name);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time, $theme, $user_id);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/dataSum.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function getDataSumReport(Input $input)
    {

        $type = $input['type'] ?? 'wechat';
        switch ($type) {
            case "wechat" :
                $input->verify(['code']);
                break;
            case "feishu" :
                $input->verify(['start_time', 'user_id', 'theme']);
                break;
            default:
                break;
        }
        $logic = new WechatDataLogic();
        $list = $logic->getDataSum($input);

        if ($list === false) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        } else {
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '',
                'data'    => $list
            ];
        }

        return $response;
    }

    public function getDataSumPushReport(Input $input)
    {
        $input->verify(['start_time', 'user_id', 'theme', 'code']);

        $start_time = $input['start_time'];
        $user_id = $input['user_id'];
        $theme = urldecode($input['theme']);
        $secret = '66Nhd&&(';

//        dd(md5($start_time . $user_id . $theme . $secret) );
        if (md5($start_time . $user_id . $theme . $secret) !== $input['code']) {
            return [
                'code'    => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        }

        $logic = new WechatDataLogic();
        $list = $logic->getDataSumPush($start_time, $user_id, $theme);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '',
            'data'    => $list
        ];

    }

    public function orderPayRateDataSum(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) != 3) {
            throw new AppException("参数错误");
        }
        [$start_time, $theme, $user_id] = $params;

        $logic = new OrderPayRateDataLogic();
        $open_id = $logic->getOpenId($code);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time, $theme, $user_id);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/orderDataSum.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function orderPayRateDataSumQbj(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) != 3) {
            throw new AppException("参数错误");
        }
        [$start_time, $theme, $user_id] = $params;

        $config_name = 'zx_qbj';
        $logic = new OrderPayRateDataLogic();
        $open_id = $logic->getOpenId($code, $config_name);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time, $theme, $user_id);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/orderDataSum.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function getOrderPayRateDataSumReport(Input $input)
    {
        $input->verify(['code']);
        $logic = new OrderPayRateDataLogic();
        $list = $logic->getDataSum($input['code']);

        if ($list === false) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        } else {
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '',
                'data'    => $list
            ];
        }

        return $response;
    }
}
