<?php
/**
 * 后台更新日志管理
 * User: lzh
 * Date: 2022/08/10
 * Time: 16:22
 */

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DMS\ChangeLogLogic;
use App\Param\ChangeLogParam;
use App\Struct\Input;

class ChangeLogController extends Controller
{
    /**
     *
     * @param Input $input
     * @return array
     */
    public function getList(Input $input): array
    {
        $change_log_logic = new ChangeLogLogic();
        $list_data = $change_log_logic->getList(new ChangeLogParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input): array
    {
        $input->verify(['publish_date', 'title', 'details', 'state']);
        try {
            $change_log_logic = new ChangeLogLogic();
            $change_log_logic->add(new ChangeLogParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input): array
    {
        $input->verify(['publish_date', 'title', 'details', 'id', 'state']);
        try {
            $change_log_logic = new ChangeLogLogic();
            $change_log_logic->edit($input['id'], new ChangeLogParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input): array
    {
        $input->verify(['id']);
        try {
            $change_log_logic = new ChangeLogLogic();
            $change_log_logic->delete($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getDetail(Input $input): array
    {
        $input->verify(['id']);
        $change_log_logic = new ChangeLogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $change_log_logic->getDetail($input['id'])
        ];
    }

    /**
     * @return array
     */
    public function getPublishDateList(): array
    {
        $change_log_logic = new ChangeLogLogic();
        $list_data = $change_log_logic->getAll();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }
}