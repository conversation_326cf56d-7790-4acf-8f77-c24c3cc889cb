<?php


namespace App\Controller;


use App\Constant\EnterpriseDiDiEmployeeConfig;
use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\CommonLogic;
use App\Logic\DMS\CustomizedTargetLogic;
use App\Logic\DMS\OuterLogic;
use App\Logic\FeishuLogic;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\DiDi\MemberModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\Aily\ChatModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\BotModel;
use App\Model\HttpModel\Feishu\Calendar\CalendarsModel;
use App\Model\HttpModel\Feishu\Contact\ContactModel;
use App\Model\HttpModel\Feishu\Docs\ContentModel;
use App\Model\HttpModel\Feishu\Docs\FileModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Volcengine\Knowledge\AbstractKnowledgeModel;
use App\Model\HttpModel\Volcengine\Knowledge\CollectionModel;
use App\Model\HttpModel\Volcengine\Knowledge\DocModel;
use App\Model\HttpModel\Volcengine\Knowledge\PointModel;
use App\Model\SqlModel\Agency\RankAgentModel;
use App\Model\SqlModel\Agency\UserModel;
use App\Model\SqlModel\DatahubLY\ViewV2DimSiteIdModel;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\DiDiTravelPartnerModel;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantBotChatModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantUserMessageTimeModel;
use App\Model\SqlModel\Zeda\FeiShuGroupAssistantRemindModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\Model\SqlModel\Zeda\FeishuUATModel;
use App\MysqlConnection;
use App\Param\ExportFileRecodeParam;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Param\Knowledge\VikingDBParam;
use App\Response\CSV;
use App\Response\File;
use App\Response\Plain;
use App\Response\Response;
use App\Service\DataBot\AilyService;
use App\Service\DataBot\DataBotConfig;
use App\Service\DataBot\DataBotPy;
use App\Service\DataBot\DataBotService;
use App\Service\DataBot\DataBotSession;
use App\Service\EnterpriseDiDiService;
use App\Service\FeiShuService;
use App\Service\FileToMarkdown;
use App\Service\GroupAssistant\FileDeletionManager;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\MessageFormat;
use App\Service\GroupAssistant\MessageSummary;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\Input;
use App\Struct\RedisCache;
use App\Task\DataBotTask;
use App\Task\ExportFileTask;
use App\Task\FeishuTask;
use App\Utils\Helpers;
use Common\EnvConfig;
use Exception;
use Illuminate\Support\Collection;
use RedisException;
use ZipArchive;

class CommonController extends Controller
{



    /**
     * 获取所有的平台列表
     *
     * @return array
     */
    public function allPlatform()
    {
        $service = new PermissionService();
        $data = $service->getPlatform(UserService::LEVEL_SUPER, 0);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    public function readRedisKey(Input $input)
    {
        $ret = RedisCache::getInstance()->get($input['key']);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $ret
        ];
    }


    /**
     * 获取某个登录用户的dms的平台列表
     *
     * @return array
     */
    public function dmsPlatform()
    {
        $service = new PermissionService();
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId('dms');
        $data = $service->getPlatform($level, $rank_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 根据平台搜索根游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function rootGameListByPlatform(Input $input)
    {
        $input->verify(['platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [['game.platform = ?', $input['platform']]];
        $res = (new V2DimGameIdModel())->getListLikeRootGame($keyword, $option);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->root_game_id}-{$item->root_game_name}",
                'value' => $item->root_game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    public function mainGameListByRootGame(Input $input)
    {
        $input->verify(['root_game_id', 'platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [['game.platform = ?', $input['platform']], ['game.root_game_id = ?', $input['root_game_id']]];
        $res = (new V2DimGameIdModel())->getListLikeMainGame($keyword, $option);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->main_game_id}-{$item->main_game_name}",
                'value' => $item->main_game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * @param Input $input
     *
     * @return array
     */
    public function lyMainGameListByRootGame(Input $input)
    {
        $input->verify(['platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [['platform = ?', $input['platform']]];
        $res = (new ViewV2DimSiteIdModel())->getListLikeMainGame($keyword, $option);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->main_game_id}-{$item->main_game_name}",
                'value' => $item->main_game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 根据平台搜索主游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function mainGameListByPlatform(Input $input)
    {
        $input->verify(['platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [['game.platform = ?', $input['platform']]];
        $res = (new V2DimGameIdModel())->getListLikeMainGame($keyword, $option);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->main_game_id}-{$item->main_game_name}",
                'value' => $item->main_game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 根据平台搜索主游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function gameListByPlatform(Input $input)
    {
        $input->verify(['platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [['game.platform = ?', $input['platform']]];
        $res = (new V2DimGameIdModel())->getListLikeGame($keyword, $option);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->game_id}-{$item->game_name}",
                'value' => $item->game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 导出文件（下载）
     *
     * @CtrlAnnotation(log_type='export')
     * @param Input $input
     *
     * @return CSV
     */
    public function exportFile(Input $input)
    {
        $input->verify(['id']);
        $model = new ExportFileTaskModel();
        $row_data = $model->getData($input['id']);
        // 验证id
        if (!$row_data) {
            throw new AppException('id非法');
        }
        // 验证user_id
        if (Container::getSession()->get('user_id') != $row_data->user_id) {
            throw new AppException('非法访问');
        }

        // 验证状态
        if ($row_data->state != 1) {
            throw new AppException('非法访问。文件未完成');
        }

        $download_filename = str_replace(' ', '', $row_data->download_filename);
        return new CSV($download_filename, $row_data->source_filename);
    }

    /**
     * 添加导出任务
     * @CtrlAnnotation(log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addExportTask(Input $input)
    {
        $input->verify(['download_filename']);
        $route = $input['router'] ?? $this->request->header['router'] ?? '';
        $route_id = RouteID::ROUTE_NAME_MAP[$route];

        //检查导出频率
        if (isset($input['check_frequency']) && 1 === (int)$input['check_frequency']) {
            $user_id = Container::getSession()->get('user_id');
            (new CommonLogic())->checkExportTaskFrequency($route_id, $user_id);
        }

        if ($route_id === RouteID::DSP_FINANCE_SCREENSHOT_MISSION) {
            // 下载的文件名
            $download_filename = $input['download_filename'] . '.zip';
            // 源文件名称
            $source_filename = 'upload/finance/' . date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.zip';
        } else {
            // 下载的文件名
            $download_filename = $input['download_filename'] . '.csv';
            // 源文件名称
            $source_filename = 'market/' . date('Ymd') . '-' . md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.csv';
        }

        // 入库的数据
        $data = [
            'param'             => json_encode($input->getData()),
            'user_id'           => Container::getSession()->get('user_id'),
            'route_id'          => $route_id,
            'download_filename' => $download_filename,
            'source_filename'   => $source_filename,
        ];
        $input->verify(['param', 'user_id', 'route_id', 'download_filename', 'source_filename'], false, $data);
        $file_param = new ExportFileRecodeParam($data);

        $model = new ExportFileTaskModel();
        $id = $model->add($file_param);

        if ($id > 0) {
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '添加成功',
            ];
        } else {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '添加失败',
            ];
        }

        return $response;
    }

    /**
     * 检查频次导出限制
     *
     * @return array
     */
    public function checkExportTaskFrequency()
    {
        $route = $input['router'] ?? $this->request->header['router'] ?? '';
        $route_id = RouteID::ROUTE_NAME_MAP[$route];
        $user_id = Container::getSession()->get('user_id');
        try {
            (new CommonLogic())->checkExportTaskFrequency($route_id, $user_id);
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '符合频次限制',
            ];
        } catch (AppException $e) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    public function searchExportFileList(Input $input)
    {
        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 20;
        $route = $input['route'] ?? '';
        $user_id = Container::getSession()->get('user_id');

        $model = new ExportFileTaskModel();
        $route_id = 0;
        if ($route) {
            $route_id = RouteID::ROUTE_NAME_MAP[$route] ?? -1; // 不存在则返回一个查不到的route_id
        }

        $data = $model->getList($user_id, $page, $rows, $route_id);
        foreach ($data['list'] as $item) {
            $item->route = array_search($item->route_id, RouteID::ROUTE_NAME_MAP);
            $item->create_time = date("Y-m-d H:i:s", $item->create_time);
        }

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data,
        ];
    }

    /**
     * 获取登录用户所有的路由权限
     *
     * @return array
     * @throws Exception
     */
    public function getAllRoutePermission()
    {
        $service = new PermissionService();

        $data = $service->getAllDimRoutePermissionByLoginUser();
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data,
        ];
    }


    /**
     * 获取登录用户的所有路由，组装成cascader
     *
     * @return array
     */
    public function allRouterCascader()
    {
        $logic = new PermissionService();

        $cascader = $logic->getLoginUserAllRouterCascader();

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $cascader,
        ];
    }

    /**
     * 前端上报错误信息
     *
     * @param Input $input
     *
     * @return array
     */
    public function reportError(Input $input)
    {
        $navigator = $input['navigator'];
        $error_message = $input['error_message'];
        $router_name = $input['router_name'];

        $session = Container::getSession();
        $name = $session->get('name');
        $now = date('Y-m-d H:i:s');

        $data = [
            'name'          => $name,
            'time'          => $now,
            'navigator'     => $navigator,
            'error_message' => $error_message,
            'router_name'   => $router_name
        ];

        Helpers::getLogger('web')->error('frontend exception: ', $data);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '记录成功'
        ];
    }

    /**
     * 下载日志文件
     *
     * @param Input $input
     *
     * @return File|array
     */
    public function downloadLog(Input $input)
    {
        $input->verify(['start_date', 'end_date']);
        $start_timestamp = strtotime($input['start_date']);
        $end_timestamp = strtotime($input['end_date']);
        $now = strtotime(date("Ymd"));

        if ($end_timestamp < $start_timestamp) {
            throw new AppException('开始日期不能大于结束日期');
        }

        if ($end_timestamp > $now) {
            throw new AppException('结束日期不能大于今天');
        }

        if (($end_timestamp - $start_timestamp) > 864000) {
            throw new AppException('时间跨度不能超过10天');
        }


        $dir = TMP_DIR . '/log_download';
        if (!file_exists($dir)) {
            mkdir($dir, 0744, true);
        }

        // 生成zip文件
        $zip_filename = $dir . '/' . md5(Container::getUUID()) . '.zip';
        $zip = new ZipArchive();
        $zip->open($zip_filename, ZipArchive::CREATE);

        for ($date_timestamp = $start_timestamp; $date_timestamp <= $end_timestamp; $date_timestamp += 86400) {
            $filename = LOG_DIR . '/' . date('Ymd', $date_timestamp) . '.log';
            if (file_exists($filename)) $zip->addFile($filename, basename($filename));
        }
        if ($zip->count() === 0) {
            $res = [
                'code'    => ResponseCode::FAILURE,
                'message' => '找不到日志文件',
            ];
        } else {
            $res = new File($zip_filename);
            $res->downloadName('log.zip');
        }
        $zip->close();
        return $res;
    }

    /**
     * @CtrlAnnotation(log_type='export')
     * @param Input $input
     *
     * @return Plain
     */
    public function downloadFile(Input $input)
    {
        $input->verify(['url', 'name']);
        $response = Response::Plain(file_get_contents($input['url']));
        $response->addHeaders([
            ['Content-Disposition', "attachment;filename={$input['name']}"]
        ]);
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='export')
     * @param Input $input
     *
     * @return File
     */
    public function downloadPackFile(Input $input)
    {
        $input->verify(['files', 'zip_name']);
        $files = json_decode($input['files'], true);
        $dir = TMP_DIR . "/zip/{$input['zip_name']}/";
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        $zip = new ZipArchive();
        $zip_filename = $dir . $input['zip_name'] . '.zip';
        $zip->open($zip_filename, ZipArchive::CREATE);

        foreach ($files as $file) {
            file_put_contents($dir . $file['name'], file_get_contents($file['url']));
            $zip->addFile($dir . $file['name'], $file['name']);
        }

        $res = new File($zip_filename);
        $res->downloadName($input['zip_name'] . '.zip');
        $zip->close();
        return $res;
    }


    /**
     * 获取某个用户的权限详情
     *
     * @return array
     */
    public function getOuterAllPermissionDetail()
    {
        $logic = new OuterLogic();
        $data = $logic->allPermissionDetail();
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    public function addAgencyUserPermission(Input $input)
    {
        if (!UserService::isSuperManager()) {
            throw new AppException('没有操作权限');
        }
        $input->verify(['user', 'agent_list', 'platform']);
        $user = $input['user'] ?? '';
        $agent_list = $input['agent_list'] ?? '';
        $platform = $input['platform'] ?? '';

        $user_model = new UserModel();
        // 判断账号是否存在
        $user_info = $user_model->getDataByAccount($user);
        if (!$user_info) {
            throw new AppException ('对外账号不存在');
        }

        try {
            MysqlConnection::getConnection('agency')->beginTransaction();
        } catch (Exception $e) {
            throw new AppException ('事务开启异常');
        }

        try {

            // 2025年02月12日，需要查看页游的数据，所以这里改为从ADB取数
            $agent_id_list = explode(',', $agent_list);
            $agent_info_list = (new V2DimAgentIdModel())->getDataByPlatformAgentIdList($platform, $agent_id_list);

            if ($agent_info_list->isEmpty()) {
                throw new AppException('渠道id不存在，退出执行');
            }
            if (count($agent_id_list) !== $agent_info_list->count()) {
                throw new AppException('部分渠道id不存在，退出执行');
            }
            if ($intersect = array_intersect($agent_id_list, explode(',', $user_info['agent_id_list']))) {
                throw new AppException('渠道id: ' . implode(',', $intersect) . '已存在，退出执行');
            }

            $add_agent_list = [];
            foreach ($agent_info_list as $item) {
                $add_agent_list[] = [
                    'platform'       => $platform,
                    'agent_group_id' => $item->agent_group_id,
                    'agent_id'       => $item->agent_id,
                ];
            }
            // 处理渠道权限
            $agent_model = new RankAgentModel();

            $agent_add_result = $agent_model->addMultiple($user_info['id'], $add_agent_list);
            if (!$agent_add_result) {
                MysqlConnection::getConnection('agency')->rollBack();
                throw new AppException ('添加失败！失败原因：添加渠道权限失败');
            }
            // 更新用户的渠道列表
            $new_agent_id_list = $user_info['agent_id_list'] . ',' . $agent_list;
            $user_model->updateAgentIdList($user_info['id'], $new_agent_id_list);
            MysqlConnection::getConnection('agency')->commit();
        } catch (\Throwable $e) {
            throw new AppException ($e->getMessage());
        }
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '渠道添加成功,用户ID:' . $user_info['id'] . ' 添加的渠道ID:' . $agent_list,
            'data'    => []
        ];
    }
}
