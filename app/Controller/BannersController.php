<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2021/11/9
 * Time: 9:49 上午
 */

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Logic\BannersLogic;
use App\Service\UserService;
use App\Struct\Input;

class BannersController extends Controller
{

//    public function __construct($request, $response = null)
//    {
//        parent::__construct($request, $response);
//    }


    public function getList(Input $input): array
    {
        // 非超管无法访问
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $list = (new BannersLogic())->getList();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }


    public function delete(Input $input): array
    {
        // 非超管无法访问
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['id']);
        (new BannersLogic())->delete($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     *
     * @return array
     */
    public function addMessage(Input $input): array
    {
        // 非超管无法访问
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['message', 'type', 'modules']);
        (new BannersLogic())->addMessage($input['message'], $input['type'], $input['modules'], Container::getSession()->name);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * @param Input $input
     *
     * @return array
     */
    public function updateMessage(Input $input): array
    {
        // 非超管无法访问
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['id', 'state', 'message', 'type', 'modules']);
        (new BannersLogic())->updateMessage($input['id'], $input['state'], $input['message'], $input['type'], $input['modules'], Container::getSession()->name);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    public function getBanners(): array
    {
        $banners = (new BannersLogic())->getBanners();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $banners,
        ];
    }
}
