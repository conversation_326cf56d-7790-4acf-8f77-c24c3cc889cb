<?php

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Container;

class WebsocketController extends Controller
{
    public function join()
    {
        $user_id = Container::getSession()->get('user_id');
        Container::getWSManager()->connect($user_id, $this->request->fd);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '连接成功',
            'data' => [
                'notice_type' => 0
            ]
        ];
    }
}
