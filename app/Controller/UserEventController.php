<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2022/1/20
 * Time: 10:28 上午
 */

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Container;
use App\Model\SqlModel\Tanwan\V2ODSUserEventLog;
use App\Struct\Input;
use App\Utils\Helpers;
use UAParser\Parser;

class UserEventController extends Controller
{


    /**
     * 添加埋点
     *
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     *
     * @return array
     * @throws \UAParser\Exception\FileNotFoundException
     */
    public function addEvent(Input $input)
    {
        $input->verify(['event_id']);
        $user_agent = $this->request->header['user-agent'];
        $parser = Parser::create();
        $ua_result = $parser->parse($user_agent);

        $data = [
            'user_id' => Container::getSession()->user_id,
            'user_name' => Container::getSession()->name,
            'ip' => Helpers::getClientIP($this->request->header),
            'ua_client' => $ua_result->ua->toString(),
            'ua_os' => $ua_result->os->toString(),
            'ua_device' => $ua_result->device->toString(),
            'event_id' => intval($input['event_id']),
            'request_time' => date("Y-m-d H:i:s", request_time()),
            'router' => $this->request->header['router'] ?? $this->request->get['router'] ?? '',
        ];
        (new V2ODSUserEventLog())->addOne($data);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => 'success',
        ];

    }

}
