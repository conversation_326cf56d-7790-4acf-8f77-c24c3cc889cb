<?php

/**
 * 标签
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;


use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MediaLogic;
use App\Service\MediaService;
use App\Struct\Input;
use Exception;

class MediaController extends Controller
{
    protected $pass_method = ['getAccountList'];

    const PLATFORM_MAP = [
        '6ywRvLy3' => 'TW',
        'fnJ8bVUm' => 'xinxin',
        'tGKcMBZP' => 'wanzi',
        '0iRNj3Uw' => '915',
    ];

    public function getAccountList(Input $input)
    {
        $input->verify(['key', 'media_type', 'page', 'rows']);
        if (!isset(self::PLATFORM_MAP[$input['key']])) {
            throw new AppException('校验失败');
        }

        if (!in_array($input['media_type'], [MediaType::BAIDU])) {
            throw new AppException('不支持的媒体类型');
        }
        $service = new MediaService();
        $list = $service->getAccountList(self::PLATFORM_MAP[$input['key']], (int)$input['media_type'], $input['page'], $input['rows']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $list
        ];
    }

    /**
     * @return array
     */
    public function getMediaTypeList()
    {
        $data = (new MediaService())->getMediaTypeOptions();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data
            ]
        ];
    }

    /**
     * @return array
     * @throws Exception
     */
    public function genMediaTypeConstFile()
    {
        $message = (new MediaService())->genMediaTypeConstFile();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => $message,
        ];
    }

    /**
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function addMediaType(Input $input)
    {
        $input->verify(['name', 'const_name']);
        $logic = new MediaLogic();

        $logic->addMediaType($input['name'], $input['const_name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];

    }

    /**
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function editMediaType(Input $input)
    {
        $input->verify(['id', 'name']);
        $logic = new MediaLogic();
        $logic->editMediaType($input['id'], $input['name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * @return array
     */
    public function getDeveloperList()
    {
        $data = (new MediaLogic())->getDeveloperList()->transform(function ($item) {
            $item->app_secret = '';
            $item->media_type_name = MediaType::MEDIA_TYPE_MAP[$item->media_type];
            return $item;
        });
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data
            ]
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function addDeveloper(Input $input)
    {
        $input->verify(['platform', 'media_type', 'app_id']);
        $logic = new MediaLogic();
        $logic->addDeveloper($input['platform'], $input['media_type'], $input['company'] ?? '', $input['app_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }
}
