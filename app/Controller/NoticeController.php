<?php
/**
 * 消息推送
 * User: zhejs
 * Date: 2019/11/12
 * Time: 11:34
 */

namespace App\Controller;


use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\TrueNameWarningLogic;
use App\Model\SqlModel\Zeda\AlertPayDataModel;
use App\Model\SqlModel\Zeda\NewServerWarningModel;
use App\Service\PermissionService;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Logic\NoticeLogic;
use Common\EnvConfig;

class NoticeController extends Controller
{
    public function unReadList()
    {
        $logic = new NoticeLogic();
        $data = $logic->getUnReadList();
        $data['version'] = EnvConfig::APP_VERSION;

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new NoticeLogic();
        $data = $logic->getList($input['page'], $input['rows']);
        $logic->setRead();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function info(Input $input)
    {
        $input->verify(['id']);
        $logic = new NoticeLogic();
        $data = $logic->info($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function dispatch(Input $input)
    {
        $input->verify(['text']);
        $logic = new NoticeLogic();
        $logic->dispatch($input['text'], $input['users'], $input['level'] ?? 1);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => ''
        ];
    }

    public function getAlertPayData(Input $input)
    {
        $input->verify(['game_reg_date']);

        $model = new AlertPayDataModel();

        $data_permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $list = $model->getList($data_permission['agent_permission'], $data_permission['game_permission'], $input['game_reg_date']);

        $data = [
            'message' => [
                'list' => $list,
            ],
            'title' => 'payment_alert',
        ];
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    public function getNewServerWarming(Input $input)
    {
        $input->verify(['start_time', 'end_time']);
        $data_permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $list = (new NewServerWarningModel())->getAllByPermission($data_permission['game_permission'], [$input['start_time'], $input['end_time']]);
        $data = [
            'message' => [
                'list' => $list,
            ],
            'title' => 'new_server_warming',
        ];
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    public function getTrueNameWarning(Input $input)
    {
        $input->verify(['start_time', 'end_time']);

        $uid = Container::getSession()->user_id;
        if (!$uid) {
            throw new AppException('请先登录');
        }
        $logic = new TrueNameWarningLogic();
        $all_data = $logic->getTrueNameWarningData($input['start_time'], $input['end_time']);
        $warning_data = $logic->getUserSelfWarningData($all_data, $uid);
        $data = [
            'message' => [
                'list' => $warning_data,
            ],
            'title' => 'true_name_warning',
        ];
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => 'eee',
            'data' => $data,
        ];
    }
}
