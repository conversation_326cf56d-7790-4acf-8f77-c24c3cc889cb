<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MonitorAppStoreRuleLogic;
use App\Logic\FeishuLogic;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\Response\Redirect;
use App\Response\Response;
use App\Service\EnterpriseDiDiService;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Struct\Input;
use App\Struct\RedisCache;
use App\Task\EnterpriseDiDiTask;
use App\Task\GroupAssistantTask;
use App\Utils\Helpers;
use Common\EnvConfig;
use RedisException;

class FeishuController extends Controller
{
    protected $pass_method = [
        'notify', 'eventSubscription', 'testFunction', 'handleMonitorAppStoreCard', 'loginNotify',
        'taskNotify', 'handleEvent', 'testEvent', 'cardEvent', 'taskNotifyAily', 'groupAssistantEvent', 'saveGroupAssistantUAT',
        'enterpriseDiDiEmployee', 'grayCardEvent',
    ];

    /**
     * @param Input $input
     * @return array
     */
    public function notify(Input $input): array
    {
        $input->verify(['code', 'state']);
        $feishu_logic = new FeishuLogic();
        try {
            $data = $feishu_logic->saveToken($input['code'], $input['state']);
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * @param Input $input
     * @return \App\Response\Html
     */
    public function loginNotify(Input $input)
    {
        $input->verify(['code', 'state']);
        $feishu_logic = new FeishuLogic();
        try {

            $feishu_logic->loginNotify($input['code'], $input['state']);
            return Response::Html("<script>window.close();</script>");

        } catch (AppException $e) {

            $message = $e->getMessage();
            return Response::Html("
                <!DOCTYPE html>
                    <html lang='en'>
                    <head>
                        <meta charset='UTF-8'>
                        <title>登录结果</title>
                    </head>
                    <body>
                        <h1>{$message}</h1>
                    </body>
                </html>
            ");

        }
    }

    /**
     * 飞书任务功能的回调(仅供内部开发人员使用，用来拉取任务列表)
     * @param Input $input
     * @return array
     */
    public function taskNotify(Input $input)
    {
        $input->verify(['code', 'state']);
        $feishu_logic = new FeishuLogic();
        try {
            $data = $feishu_logic->saveTaskToken($input['code'], $input['state']);
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        return $response;
    }

    public function taskNotifyAily(Input $input)
    {
        $input->verify(['code']);
        $feishu_logic = new FeishuLogic();
        try {
            $response = $feishu_logic->saveAilyTaskToken($input['code']);
        } catch (AppException $e) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 群助手的用户授权回调接口
     *
     * @param Input $input
     * @return mixed
     * @throws RedisException
     */
    public function saveGroupAssistantUAT(Input $input)
    {
        $input->verify(['code', 'state']);
        $feishu_logic = new FeishuLogic();
        $state = json_decode($input['state'], true);
        $used_flag_key = $state['used_flag'];
        $schedule_data = $state['schedule_data'] ?? [];
        $user_flag = RedisCache::getInstance()->get($used_flag_key);
        if (!$user_flag) {
            // 授权失败，重定向到失败页面
            $redirect_url = EnvConfig::DOMAIN . '/upload/GrantFailure.html?msg=授权链接已过期';
            return new Redirect($redirect_url);
        }

        $logger = Helpers::getLogger('group_assistant_grant');
        $logger->info('授权回调', $state);

        try {
            $feishu_logic->saveGroupAssistantTaskToken($input['code'], $state['union_id'], $state['used_flag'], $state['redirect_url']);

            // 如果有$schedule_data，则要继续未完成的流程
            if ($schedule_data) {
                $data = [
                    'union_id'      => $state['union_id'],
                    'schedule_data' => $schedule_data,
                ];

                RedisCache::getInstance()->lPush(GroupAssistantTask::GRANT_QUEUE_KEY, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
                $logger->info('授权成功，入授权事件队列', $data);
            }


            // 授权成功，重定向到成功页面
            $redirect_url = EnvConfig::DOMAIN . '/upload/GrantSuccess.html';

            return new Redirect($redirect_url);
        } catch (AppException $e) {
            // 授权失败，重定向到失败页面
            $redirect_url = EnvConfig::DOMAIN . '/upload/GrantFailure.html?msg=' . urlencode($e->getMessage());

            return new Redirect($redirect_url);

        }

    }

    public function eventSubscription(Input $input): array
    {
        Helpers::getLogger('feishu')->info('eventSubscription', ['request_data' => $input->getData()]);
        //未设置Encrypt Key
        if (isset($input['challenge']) && isset($input['token']) && isset($input['type'])) {
            return ['challenge' => $input['challenge']];
        }

        //设置了Encrypt Key
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('feishu')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }

        if (!isset($input['app_id'])) {
            Helpers::getLogger('feishu')->info('app_id column no exist', $input->getData());
            throw new AppException("请求出错 app_id column no exist");
        }

        $media_developer_account_model = new MediaDeveloperAccountModel();
        $appinfo = $media_developer_account_model->getAppInfo(MediaType::FEISHU, $input['app_id']);
        $ext_info = json_decode($appinfo->ext, true);
        $encrypt_key = $ext_info['encrypt_key'] ?? '';
        if (empty($encrypt_key)) {
            Helpers::getLogger('feishu')->error("{$input['app_id']} encrypt_key no exist", $input->getData());
        }

        $encrypt = base64_decode($input['encrypt']);

        $iv = substr($encrypt, 0, 16);
        $encrypted_event = substr($encrypt, 16);

        $decrypted_json = openssl_decrypt($encrypted_event, 'AES-256-CBC', hash('sha256', $encrypt_key, true), OPENSSL_RAW_DATA, $iv);
        $data = json_decode($decrypted_json, true);

        //todo 对事件做处理

        if (!isset($data['challenge'])) throw new AppException("解析错误");

        return ['challenge' => $data['challenge']];
    }

    /**
     * 处理飞书卡片点击相应及卡片更新
     * @param Input $input
     * @return array|mixed
     */
    public function handleMonitorAppStoreCard(Input $input)
    {
        Helpers::getLogger('feishu')->info('handleMonitorAppStoreCard', ['request_data' => $input->getData()]);
        //未设置Encrypt Key
        if (isset($input['challenge']) && isset($input['token']) && isset($input['type'])) {
            return ['challenge' => $input['challenge']];
        }

        $data = $input['action']['value'];

        if (isset($data['confirm_opt']) && 1 === (int)$data['confirm_opt']) {
            (new MonitorAppStoreRuleLogic())->confirmOperation($data['game_id'], $data['platform']);
            $content = '手动确认成功, 下一次检测到下架将会立刻执行关停广告操作';
        } elseif (isset($data['terminate_opt']) && 1 === (int)$data['terminate_opt']) {
            (new MonitorAppStoreRuleLogic())->terminateOperate($data['game_id'], $data['platform']);
            $content = '终止操作成功, 此游戏的下架检测已停止';
        } else {
            throw new AppException('参数错误');
        }

        $update_card = '{
  "config": {
    "wide_screen_mode": true
  },
  "header": {
    "title": {
      "tag": "plain_text",
      "content": "您所创建的iOS监控规则"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**平台**\nTW"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**游戏ID：**\n5371"
          }
        },
        {
          "is_short": false,
          "text": {
            "tag": "lark_md",
            "content": ""
          }
        },
        {
          "is_short": false,
          "text": {
            "tag": "lark_md",
            "content": "**' . $content . '**"
          }
        },
        {
          "is_short": false,
          "text": {
            "tag": "lark_md",
            "content": ""
          }
        }
      ]
    }
  ]
}';
        return json_decode($update_card, true);
    }


    /**
     * 情报局机器人的事件处理
     * 只处理消息事件
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function handleEvent(Input $input): array
    {
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('feishu')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }

        Helpers::getLogger('feishu')->info('接受到事件', ['request_data' => $input->getData()]);

        $encrypt_data = $input['encrypt']; // 待解密的信息
        $encrypt_key = EnvConfig::FEISHU['qingbaoju']['encrypt_key'];
        $base64_decode_message = base64_decode($encrypt_data);
        $iv = substr($base64_decode_message, 0, 16);
        $encrypted_event = substr($base64_decode_message, 16);
        $decrypt = openssl_decrypt($encrypted_event, 'AES-256-CBC', hash('sha256', $encrypt_key, true), OPENSSL_RAW_DATA, $iv);
        $decrypt = json_decode($decrypt, true);
        Helpers::getLogger('feishu')->info('decrypt', ['decrypt' => $decrypt]);

        // 只处理消息事件
        if ($decrypt['header']['event_type'] === 'im.message.receive_v1') {
            $logic = new FeishuLogic();
            if ($decrypt['event']['message']['message_type'] === 'text') {
                $logic->handleText($decrypt);
            } elseif ($decrypt['event']['message']['message_type'] === 'audio') {
                $logic->handleVoice($decrypt);
            }
        }

        // 第一次订阅的时候，需要返回这个
//        return ['challenge' => $decrypt['challenge']];

        // 否则直接随便响应就行了
        return [
            'code' => ResponseCode::SUCCESS
        ];
    }

    /**
     * 开发测试的回调
     * 只处理消息事件
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function testEvent(Input $input): array
    {
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('feishu_test')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }

        Helpers::getLogger('feishu_test')->info('接受到事件', ['request_data' => $input->getData()]);

        $encrypt_key = 'sgTbNj8DytCZVzLFYNDSgcPh2RZiG4B8';
        $decrypt = FeiShuService::decryptUsingEncryptKey($encrypt_key, $input['encrypt']);

        Helpers::getLogger('feishu_test')->info('decrypt', ['decrypt' => $decrypt]);


        // 第一次订阅的时候，需要返回这个
//        return ['challenge' => $decrypt['challenge']];


        // 消息事件
        if ($decrypt['header']['event_type'] === 'im.message.receive_v1') {
            $logic = new FeishuLogic();
            if ($decrypt['event']['message']['message_type'] === 'text') {
                $logic->handleText($decrypt);
            } elseif ($decrypt['event']['message']['message_type'] === 'audio') {
                $logic->handleVoice($decrypt);
            }

            return [
                'code' => ResponseCode::SUCCESS
            ];
        }

        // 选择场景事件
        switch ($decrypt['event']['event_key']) {
            case 'data_query':
            case 'xhs':
            case 'matching':
            case 'qa':
            case 'deepseek':
                (new FeishuLogic())->handleSelectScene($decrypt['event']['operator']['operator_id']['union_id'], $decrypt['event']['event_key']);
                break;
        }

        // 兜底随便返回一点东西
        return [
            'code' => ResponseCode::SUCCESS
        ];
    }

    /**
     * 卡片的回调
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function cardEvent(Input $input): array
    {
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('group_assistant_card')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }
        $encrypt_key = GroupAssistantService::ENCRYPT_KEY;
        $decrypt = FeiShuService::decryptUsingEncryptKey($encrypt_key, $input['encrypt']);

        Helpers::getLogger('group_assistant_card')->info('接受到卡片回调事件', ['decrypt' => $decrypt]);

//        return ['challenge' => $decrypt['challenge']];
        // 处理卡片回调
        (new GroupAssistantService())->handleGroupAssistantCardEvent($decrypt['event']['operator']['union_id'], $decrypt['event']['action']['value'], $decrypt['event']['context']['open_message_id']);

        // 卡片回调给飞书服务器
        return [
            'type' => '2.0',
            'data' => ['template_id' => GroupAssistantService::CARD_TEMPLATE],
        ];

    }

    /**
     * 卡片的回调(灰度测试)
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function grayCardEvent(Input $input): array
    {
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('group_assistant_card')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }

        $encrypt_key = 'sgTbNj8DytCZVzLFYNDSgcPh2RZiG4B8';
        $decrypt = FeiShuService::decryptUsingEncryptKey($encrypt_key, $input['encrypt']);

        Helpers::getLogger('group_assistant_card')->info('接受到卡片回调事件', ['decrypt' => $decrypt]);
//        return ['challenge' => $decrypt['challenge']];
        // 处理卡片回调
        (new GroupAssistantService())->handleGroupAssistantCardEvent($decrypt['event']['operator']['union_id'], $decrypt['event']['action']['value'], $decrypt['event']['context']['open_message_id']);

        // 卡片回调给飞书服务器
        return [
            'type' => '2.0',
            'data' => ['template_id' => GroupAssistantService::CARD_TEMPLATE],
        ];

    }

    /**
     * 群助手的回调处理
     * 只处理消息事件
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function groupAssistantEvent(Input $input): array
    {
        if (!isset($input['encrypt'])) {
            Helpers::getLogger('group_assistant')->info('encrypt no exist', $input->getData());
            throw new AppException("请求出错 encrypt no exist");
        }

        $decrypt = FeiShuService::decryptUsingEncryptKey(GroupAssistantService::ENCRYPT_KEY, $input['encrypt']);
        Helpers::getLogger('group_assistant_event')->info('事件回调：', $decrypt);

//       // 第一次订阅的时候，需要返回这个
//        return ['challenge' => $decrypt['challenge']];
        $logic = new FeishuLogic();
        switch ($decrypt['header']['event_type']) {
            // 接受消息
            case 'im.message.receive_v1':
                $logic->handleGroupAssistantMessage($decrypt);
                break;
            // 撤回消息
            case 'im.message.recalled_v1':
                $logic->handleGroupAssistantMessageRecalled($decrypt);
                break;
            // 表情回复
            case 'im.message.reaction.created_v1':
                $logic->handleGroupAssistantReaction($decrypt);
                break;
            case 'im.message.reaction.deleted_v1':
                $logic->handleGroupAssistantReactionDeleted($decrypt);
                break;
            // 机器人进群
            case 'im.chat.member.bot.added_v1':
                $logic->handlerGroupAssistantBotAdded($decrypt);
                break;
            // 机器人出群事件
            case 'im.chat.member.bot.deleted_v1':
                $logic->handlerGroupAssistantBotDeleted($decrypt);
                break;
            // 用户退群事件
            case 'im.chat.member.user.deleted_v1':
                $logic->handleGroupAssistantUserDeleted($decrypt);
                break;
            // 用户进群事件
            case 'im.chat.member.user.added_v1':
                $logic->handleGroupAssistantUserAdded($decrypt);
                break;
            // 菜单事件
            case 'application.bot.menu_v6':
                $logic->handlerGroupAssistantMenu($decrypt);
                break;
            // 用户打卡下班
            case 'attendance.user_flow.created_v1':
                (new EnterpriseDiDiService())->userClockOut($decrypt);
                break;
        }

        // 外出审批事件
        if ($decrypt['event']['type'] === 'out_approval') {
            (new EnterpriseDiDiService())->outApproval($decrypt);
        }
        // 外出审批撤销事件
        if ($decrypt['event']['type'] === 'out_approval_revert') {
            (new EnterpriseDiDiService())->outApprovalRevert($decrypt);
        }
        // 普通审批事件
        if ($decrypt['event']['type'] === 'approval_instance') {
            $logic->handleClockOutApproval($decrypt);
        }
        return [
            'code' => ResponseCode::SUCCESS
        ];


    }

    /**
     * 滴滴企业版基础人员信息更新事件
     * （飞书多维表格的自动化流程）
     *
     * @param Input $input
     * @return array
     * @throws RedisException
     */
    public function enterpriseDiDiEmployee(Input $input): array
    {
        Helpers::getLogger('didi-employee')->info('接收到事件', $input->getData());

        $input->verify(['employee_no', 'name', 'phone', 'company', 'out_regulation_id']);


        // 入队
        RedisCache::getInstance()->lPush(EnterpriseDiDiTask::SYNC_QUEUE_KEY, json_encode($input->getData(), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => 'success'
        ];


    }


}