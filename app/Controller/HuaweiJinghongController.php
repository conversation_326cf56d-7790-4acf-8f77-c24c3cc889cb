<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\HuaweiJinghongLogic;
use App\Model\RedisModel\MediaAccountAuthModel;
use App\Struct\Input;

class <PERSON><PERSON><PERSON><PERSON>inghongController extends Controller
{
    protected $pass_method = ['notify'];

    public function notify(Input $input)
    {
        $input->verify(['authorization_code', 'state']);

        $state_list = explode('.', $input['state']);
        if (count($state_list) !== 4) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败。返回参数错误'
            ];
        }
        $state = [
            'platform' => $state_list[0],
            'creator_id' => $state_list[1],
            'app_id' => $state_list[2],
            'manager_id' => $state_list[3]
        ];

        $media_account_auth_model = new MediaAccountAuthModel();
        if (!$media_account_auth_model->lockAuth($state['platform'], MediaType::HUAWEI_JINGHONG, $state['creator_id'])) {
            throw new AppException("请勿重复点击授权按钮，请到集团后台查看账号授权结果");
        }

        $logic = new HuaweiJinghongLogic();
        try {
            $data = $logic->saveToken($input['authorization_code'], $state);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        $media_account_auth_model->unLockAuth($state['platform'], MediaType::HUAWEI_JINGHONG, $state['creator_id']);

        return $response;
    }
}
