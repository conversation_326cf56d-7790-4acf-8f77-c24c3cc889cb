<?php


namespace App\Controller;


use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Response\Redirect;
use App\Logic\TeamDataPushLogic;
use App\Struct\Input;
use Common\EnvConfig;

class TeamDataController extends Controller
{
    protected $pass_method = ['teamData', 'getDataSumReport'];

    /**
     * 微信二次跳转请求
     * @param Input $input
     * @return Redirect
     */
    public function teamData(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $hour = $input['hour'] ?? 0;
        $start_time = urldecode($state);

        $logic = new TeamDataPushLogic();
        $open_id = $logic->getOpenId($code);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/teamData.html?code=' . $code . '&hour=' . $hour. '&type=wechat';

        return new Redirect($redirect_url);
    }

    public function getDataSumReport(Input $input)
    {
        $type = $input['type'] ?? 'wechat';
        switch ($type) {
            case "wechat" :
                $input->verify(['code']);
                break;
            case "feishu" :
                $input->verify(['start_time', 'user_id']);
                break;
            default:
                break;
        }
        $logic = new TeamDataPushLogic();
        $list = $logic->getDataSum($input);

        if ($list === false) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        } else {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '',
                'data' => $list
            ];
        }

        return $response;
    }
}
