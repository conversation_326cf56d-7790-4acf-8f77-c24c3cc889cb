<?php

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Service\UserService;
use Swoole\Http\Request;
use Swoole\Http\Response;

class Controller
{
    const MODULE = '';

    protected $pass_method = [];

    /**
     * @var Request $request
     */
    protected $request;

    /**
     * @var Response
     */
    protected $response;

    public function __construct($request, $response = null)
    {
        $this->request = $request;
        $this->response = $response;
    }


    public function verify($method_name)
    {
        if (!in_array($method_name, $this->pass_method)) {
            $this->checkLogin();
            $this->authentication($method_name);
        }
    }

    /**
     * 登录验证
     */
    protected function checkLogin()
    {
        if (!(Container::getSession()->user_id > 0)) {
            Container::getSession()->destroy();
            // 清除cookie
            // $this->response->cookie('x-token', '', 0, '/', '', false, false);
            throw new AppException('登录已过期，请重新登录', ResponseCode::LOGOUT);
        }

    }

    /**
     * 权限验证
     *
     * @param $method_name
     */
    protected function authentication($method_name)
    {
        $controller_annotation = Container::getCtrlAnnotation();
        if (isset($controller_annotation[static::class], $controller_annotation[static::class][$method_name])) {
            $permissions = $controller_annotation[static::class][$method_name]['permissions'];
            if (!empty($permissions)) {
                if (in_array('/admin', $permissions) && UserService::isSuperManager()) {
                    return;
                }
                if (empty(array_intersect($permissions, Container::getSession()->permission_uri))) {
                    throw new AppException('访问被拒绝', ResponseCode::ACCESS_DENY);
                }
            }
        }
    }


    protected function checkModule()
    {
        if (UserService::isSuperManager()) return;

        // 判断模块是否符合
        $user_level = Container::getSession()->get('user_level');
        $modules = array_keys($user_level);
        if (!in_array(static::MODULE, $modules)) {
            throw new AppException('请刷新页面', ResponseCode::ACCESS_DENY);
        }

    }


    /**
     * 获取请求页面的route_id
     *
     * @return int
     */
    protected function getRouteId(): int
    {
        $route_name = $this->request->header['router'] ?? $this->request->get['router'];
        if (key_exists($route_name, RouteID::ROUTE_NAME_MAP)) {
            return RouteID::ROUTE_NAME_MAP[$route_name];
        }

        return 0;
    }
}
