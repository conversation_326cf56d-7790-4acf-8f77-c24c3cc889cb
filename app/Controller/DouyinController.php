<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DouyinLogic;
use App\Model\RedisModel\MediaAccountAuthModel;
use App\Struct\Input;

class <PERSON>uyinController extends Controller
{
    protected $pass_method = ['notify'];

    public function notify(Input $input)
    {
        $input->verify(['code', 'state']);

        $state_list = explode(',', $input['state']);
        if (count($state_list) !== 3) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败。返回参数错误'
            ];
        }
        $state = [
            'platform' => $state_list[0],
            'creator_id' => $state_list[1],
            'company' => $state_list[2]
        ];

        $media_account_auth_model = new MediaAccountAuthModel();
        if (!$media_account_auth_model->lockAuth($state['platform'], MediaType::DOUYIN, $state['creator_id'])) {
            throw new AppException("请勿重复点击授权按钮，请到集团后台查看账号授权结果");
        }

        $logic = new DouyinLogic();
        try {
            $data = $logic->saveToken($input['code'], $state);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        $media_account_auth_model->unLockAuth($state['platform'], MediaType::DOUYIN, $state['creator_id']);

        return $response;
    }
}
