<?php

namespace App\Controller;


use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DMS\TrueNameWarningLogic;
use App\Response\Redirect;
use App\Struct\Input;
use Common\EnvConfig;

class TrueNameWarningController extends Controller
{
    protected $pass_method = ['report', 'reportQbj', 'getWeChatReport', 'testPush'];

    public function report(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) !== 3) {
            throw new AppException("参数错误");
        }
        [$theme, $start_time, $end_time] = $params;

        $logic = new TrueNameWarningLogic();
        $open_id = $logic->getOpenId($code);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $theme, $start_time, $end_time);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/realNameWarning.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function reportQbj(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $state = $input['state'];
        $state = urldecode($state);
        $params = explode('_', $state);
        if (count($params) !== 3) {
            throw new AppException("参数错误");
        }
        [$theme, $start_time, $end_time] = $params;

        $config_name = 'zx_qbj';
        $logic = new TrueNameWarningLogic();
        $open_id = $logic->getOpenId($code, $config_name);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $theme, $start_time, $end_time);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/realNameWarning.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function getWeChatReport(Input $input)
    {
        $input->verify(['code']);
        $logic = new TrueNameWarningLogic();
        $list = $logic->getWeChatReport($input['code']);

        if ($list === false) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        } else {
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '',
                'data'    => $list
            ];
        }

        return $response;
    }

    public function testPush(Input $input)
    {
        $input->verify(['type']);
        (new \App\Task\TrueNameWarningTask())->toRun($input['type']);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '',
            'data'    => ''
        ];
    }


}
