<?php
/**
 * Es日志管理
 */

namespace App\Controller;

use App\Container;
use App\Logic\LogEsLogic;
use App\Param\LogSearchParam;
use App\Struct\Input;
use App\Constant\ResponseCode;

class LogEsController extends Controller
{
    /**
     * 日志列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function list(Input $input): array
    {
        $input->verify(['start_date', 'end_date', 'page', 'rows']);
        $param = new LogSearchParam($input->getData());
        $logic = new LogEsLogic();

        return ['code' => ResponseCode::SUCCESS, 'message' => '获取成功', 'data' => ['list' => $logic->getList($param)]];
    }



    /**
     * 日志列表总数
     *
     * @param Input $input
     *
     * @return array
     */
    public function total(Input $input): array
    {
        $input->verify(['start_date', 'end_date', 'page', 'rows']);
        $param = new LogSearchParam($input->getData());
        $logic = new LogEsLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getTotal($param)
        ];
    }

    /**
     * 获取返回结果详情
     *
     * @param Input $input
     *
     * @return array
     */
    public function getLogResponseData(Input $input): array
    {
        $input->verify(['log_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'detail' => (new LogEsLogic())->getLogResponseData($input['log_id']),
            ]
        ];
    }


    /**
     * 获取请求内容详情
     *
     * @param Input $input
     *
     * @return array
     */
    public function getLogRequestMessage(Input $input): array
    {
        $input->verify(['log_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'detail' => (new LogEsLogic())->getLogRequestMessage($input['log_id']),
            ]
        ];
    }

    /**
     * 获取请求和返回结果详情
     *
     * @param Input $input
     *
     * @return array
     */
    public function getLogRequestAndResponseData(Input $input): array
    {
        $input->verify(['log_id']);
        [$request_message, $detail] = (new LogEsLogic())->getLogRequestAndResponseData($input['log_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'request_message' => $request_message,
                'detail' => $detail,
            ]
        ];
    }
}
