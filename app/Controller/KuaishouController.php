<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\KuaishouLogic;
use App\Model\RedisModel\MediaAccountAuthModel;
use App\Struct\Input;
use App\Utils\Helpers;

class <PERSON><PERSON>houController extends Controller
{
    protected $pass_method = ['notify'];

    public function notify(Input $input)
    {
        $input->verify(['auth_code', 'state']);
        $logic = new KuaishouLogic();
        $state = json_decode($input['state'], true);

        $media_account_auth_model = new MediaAccountAuthModel();
        if (!$media_account_auth_model->lockAuth($state['platform'], MediaType::KUAISHOU, $state['creator_id'])) {
            throw new AppException("请勿重复点击授权按钮，请到集团后台查看账号授权结果");
        }

        Helpers::getLogger('kuaishou')->info("授权参数", [
            'input' => $input->getData()
        ]);

        try {
            $data = $logic->saveToken($state, $input['auth_code']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        $media_account_auth_model->unLockAuth($state['platform'], MediaType::KUAISHOU, $state['creator_id']);

        return $response;
    }
}
