<?php


namespace App\Controller;


use App\Constant\ResponseCode;
use App\Logic\LeaderGroupLogic;
use App\Model\SqlModel\Zeda\LeaderGroupModel;
use App\Struct\Input;

class LeaderGroupController extends Controller
{


    /**
     * 添加负责人分组
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addLeaderGroup(Input $input)
    {
        $input->verify(['name', 'leaders']);

        $id = (new LeaderGroupLogic())->addLeaderGroup($input['name'], $input['leaders'], $input['project_team_id'] ?: 0, $input['project_team_name'] ?? '');

        if ($id > 0) {
            return [
                'code'    => ResponseCode::SUCCESS,
                'message' => '添加成功',
            ];
        } else {
            return [
                'code'    => ResponseCode::FAILURE,
                'message' => '添加失败',
            ];
        }
    }


    /**
     * 修改负责人分组
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function editLeaderGroup(Input $input)
    {
        $input->verify(['id', 'name', 'leaders']);

        (new LeaderGroupLogic())->editLeaderGroup($input['id'], $input['name'], $input['leaders'], $input['project_team_id'] ?: 0, $input['project_team_name'] ?? '');


        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @return array
     */
    public function getLeaderGroupList(Input $input)
    {
        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 20;
        $data = (new LeaderGroupModel())->getList($page, $rows);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }


    /**
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     * @param Input $input
     *
     * @return array
     */
    public function deleteLeaderGroup(Input $input)
    {
        $input->verify(['id']);

        (new LeaderGroupLogic())->deleteLeaderGroup($input['id']);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }


    /**
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getLeaderGroupDetail(Input $input)
    {
        $input->verify(['id']);

        $data = (new LeaderGroupLogic())->getLeaderGroupDetail($input['id']);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => (array)$data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @return array
     */
    public function getAllLeaderList()
    {
        $data = (new LeaderGroupLogic())->getAllLeaderList();

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data,
        ];
    }

    public function getAllPlatformList()
    {
        $data = (new LeaderGroupLogic())->getPlatformList();

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data,
        ];
    }

    public function editPlatformList(Input $input)
    {
        $input->verify(['id', 'platform_list']);

        (new LeaderGroupLogic())->editPlatformList($input['id'], $input['platform_list']);


        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }


    public function getProjectTeamList(Input $input)
    {
        $data = (new LeaderGroupLogic())->getProjectTeamList();


        return [
            'code'    => ResponseCode::SUCCESS,
            'data'    => $data,
            'message' => 'success',
        ];
    }
}