<?php

namespace App\Controller\ERP;

use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;

class Controller extends \App\Controller\Controller
{

    const MODULE = 'erp';

    /**
     * 权限验证
     *
     * @param $method_name
     */
    protected function authentication($method_name)
    {
        $this->checkModule();

        $controller_annotation = Container::getCtrlAnnotation();
        if (isset($controller_annotation[static::class], $controller_annotation[static::class][$method_name])) {
            $permissions = $controller_annotation[static::class][$method_name]['permissions'];
            if (!empty($permissions)) {
                if (empty(array_intersect($permissions, Container::getSession()->permission_uri['erp']))) {
                    throw new AppException('访问被拒绝', ResponseCode::ACCESS_DENY);
                }
            }
        }
    }
}