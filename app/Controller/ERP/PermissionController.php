<?php
/**
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-09
 * Time: 11:42
 */

namespace App\Controller\ERP;



use App\Constant\ResponseCode;
use App\Container;
use App\Logic\ERP\PermissionLogic;
use App\Param\RankPermissionParam;
use App\Param\SyncPositionPermissionParam;
use App\Param\UserListParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\Input;

class PermissionController extends Controller
{

//    /**
//     * 维度筛选搜索
//     *
//     * @param Input $input
//     *
//     * @return array
//     */
//    public function searchOptions(Input $input)
//    {
//        $input->verify(['column']);
//
//        $logic = new PermissionLogic();
//        $list = $logic->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '查询成功',
//            'data' => [
//                'list' => $list
//            ]
//        ];
//    }
//
//    public function searchOptionsAccurate(Input $input)
//    {
//        $input->verify(['column']);
//
//        $logic = new PermissionLogic();
//        $list = $logic->searchOptionsAccurate($input['column'], $input['keyword'] ?? '');
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '查询成功',
//            'data' => [
//                'list' => $list
//            ]
//        ];
//    }


    public function breadcrumbs()
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $breadcrumbs = $service->getBreadcrumbs($my_level, self::MODULE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['breadcrumbs' => $breadcrumbs]
        ];
    }

    public function subordinate(Input $input)
    {
        $input->verify(['page', 'rows']);
        $param = new UserListParam($input->getData());
        $logic = new PermissionService();
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $param->module = self::MODULE;
        $data = $logic->getSubordinateInfo($param, $level, $rank_id);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取某个等级下的权限列表（路由、数据权限）
     *
     *
     * @param Input $input
     *
     * @return array
     */
    public function rankPermissionDetail(Input $input)
    {
        $input->verify(['level', 'id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);

        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getRankPermissionDetail($level, $input['id'], self::MODULE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取某个等级的平台列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function platform(Input $input)
    {
        $input->verify(['level', 'rank_id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getPlatform($level, $input['rank_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }


//    /**
//     * 获取某个等级的某个平台下面的 game_list
//     *
//     * @param Input $input
//     *
//     * @return array
//     */
//    public function platformPermission(Input $input)
//    {
//        $input->verify(['platform', 'level', 'id']);
//        $level = intval($input['level']);
//        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
//        if ($my_level > $level) {
//            return [
//                'code' => ResponseCode::ACCESS_DENY,
//                'message' => '无权限访问',
//            ];
//        }
//
//        $logic = new PermissionLogic();
//        $data = $logic->getPlatformPermission($level, $input['id'], $input['platform']);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '获取成功',
//            'data' => $data,
//        ];
//    }
//
//    /**
//     * 根据游戏id获取渠道列表
//     *
//     * @param Input $input
//     *
//     * @return array
//     */
//    public function getAgentByGameId(Input $input)
//    {
//        $input->verify(['level', 'id', 'game_id', 'platform']);
//
//        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
//        $level = intval($input['level']);
//        if ($my_level > $level) {
//            return [
//                'code' => ResponseCode::ACCESS_DENY,
//                'message' => '无权限访问',
//            ];
//        }
//
//        $logic = new PermissionLogic();
//        $data = $logic->getAgentByGameId($level, $input['id'], $input['game_id'], $input['platform']);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '获取成功',
//            'data' => [
//                'list' => $data,
//            ]
//        ];
//    }

    /**
     * @param Input $input
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @return array
     * @throws \Exception
     */
    public function addRankPermission(Input $input)
    {
        $input->verify(['name', 'route_list', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $param = new RankPermissionParam($input->getData());
        $param->module = self::MODULE;

        $logic->addRankPermission($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * 编辑
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='edit')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editRankPermission(Input $input)
    {
        $input->verify(['name', 'route_list', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $param = new RankPermissionParam($input->getData());

        $logic->editRankPermission($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * 获取用户的全部权限 编辑时用到
     *
     * @param Input $input
     *
     * @return array
     */
    public function rankPlatformList(Input $input)
    {
        $input->verify(['level', 'id']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level > $input['level']) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $logic = new PermissionLogic();
        $service = new PermissionService();

        $route_permission_list = $service->getRankRoutePermissionList($input['level'], $input['id']);
        $platform_list = $logic->getRankPlatformList($input['level'], $input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'platform_list' => $platform_list,
                'route_permission_list' => $route_permission_list,
            ],
        ];
    }

    /**
     * 删除
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function delRankPermission(Input $input)
    {
        $input->verify(['id', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $logic->delRankPermission($input['id'], $input['level']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 获取平台层的 "卡片" 和用户列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function platformList(Input $input)
    {
        if (!UserService::isSuperManager()) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }
        $keyword = $input['keyword'] ?? '';

        $service = new PermissionService();
        $card_list = $service->getPlatformList($keyword, self::MODULE);
        $user_list = $service->getUserList(UserService::LEVEL_SUPER, 0);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ],
        ];
    }

    /**
     * 部门列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于平台  直接拿登录用户的platform_id
        if ($my_level === UserService::LEVEL_PLATFORM) {
            $platform_id = Container::getSession()->getUserDetail(self::MODULE)['platform_id'];
        } else {
            // 否则需要前端传入
            $input->verify(['platform_id']);
            $platform_id = $input['platform_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentList($platform_id);
        $user_list = $service->getUserList(UserService::LEVEL_PLATFORM, $platform_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }

    /**
     * 部门分组列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentGroupList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于部门 则不需要传入部门id 直接拿登录用户的department_id
        if ($my_level === UserService::LEVEL_DEPARTMENT) {
            $department_id = Container::getSession()->getUserDetail(self::MODULE)['department_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_id']);
            $department_id = $input['department_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentGroupList($department_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT, $department_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }


    /**
     * 部门岗位列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentGroupPositionList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于部门分组 则不需要传入部门分组id 直接拿登录用户的department_group_id
        if ($my_level === UserService::LEVEL_DEPARTMENT_GROUP) {
            $department_group_id = Container::getSession()->getUserDetail(self::MODULE)['department_group_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_group_id']);
            $department_group_id = $input['department_group_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentGroupPositionList($department_group_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT_GROUP, $department_group_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }


    /**
     * 获取岗位下面的用户列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function userList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['department_group_position_id']);

        $service = new PermissionService();
        $list = $service->getUserList(UserService::LEVEL_DEPARTMENT_GROUP_POSITION, $input['department_group_position_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list]
        ];

    }

    /**
     * 同步各级部门的权限
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function addSyncRankPermission(Input $input)
    {
        $input->verify(['route_ids', 'position_list']);
        $data = $input->getData();
        $param = new SyncPositionPermissionParam($data);
        $param->setPositionListToFormat(self::MODULE);
        $logic = new PermissionService();
        $logic->syncPositionPermission($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];
    }

//    /**
//     * 根据平台获取游戏列表
//     *
//     * @param Input $input
//     * @return array
//     */
//    public function getGameListByPlatform(Input $input)
//    {
//        $input->verify(['platform']);
//        $logic = new PermissionLogic();
//        $list = $logic->getGameListByPlatform($input['platform'], $input['keyword']);
//
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $list
//        ];
//    }
}