<?php
/**
 * 用户管理接口
 * User: Melody
 * Date: 2020/07/15
 * Time: 9:46
 */

namespace App\Controller\ERP;

use App\Constant\ResponseCode;
use App\Container;
use App\Struct\Input;
use App\Param\UserParam;
use App\Logic\UserLogic;
use App\Service\UserService;

class UserController extends Controller
{

    /**
     * 初始化
     *
     * @return array
     */
    public function positionCascader()
    {
        $service = new UserService();
        $data = $service->getPositionCascader(self::MODULE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取level下用户列表(包括自己和下一级用户)
     *
     * @return array
     */
    public function userOptions()
    {
        $service = new UserService();
        $list = $service->getUserOptions(self::MODULE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list]
        ];

    }

    /**
     * 添加用户
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function addUser(Input $input)
    {
        $input->verify(['level', 'account', 'department_group_id', 'department_id', 'department_group_position_id',
            'platform_id', 'password', 'name']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $param = new UserParam($input->getData());
        $param->creator = Container::getSession()->name;
        $param->module = self::MODULE;

        // 添加用户账号
        $logic = new UserLogic();
        $logic->addUser($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='edit')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editUser(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['id', 'department_group_id', 'department_id', 'department_group_position_id', 'platform_id']);
        $logic = new UserLogic();

        $param = new UserParam($input->getData());
        $param->editor = Container::getSession()->name;
        $param->module = self::MODULE;

        $logic->editUser($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='edit')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editUserName(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['id', 'name']);

        $param = new UserParam($input->getData());
        $param->editor = Container::getSession()->name;
        $param->module = self::MODULE;

        (new UserLogic())->editUserName($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }


    /**
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='delete')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function deleteUser(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['user_id']);
        $editor = Container::getSession()->name;

        (new UserLogic())->deleteUser($input['user_id'], $editor);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 判断是否可以提示绑定其他模块
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function canLink(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['account']);

        $data = (new UserLogic())->canLink($input['account'], self::MODULE);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }


    /**
     * 绑定账号到fa（财务）模块
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function linkUser(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code' => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['level', 'account', 'department_group_id', 'department_id', 'department_group_position_id',
            'platform_id']);
        $param = new UserParam($input->getData());
        $param->module = self::MODULE;
        (new UserLogic())->linkUser($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }
}
