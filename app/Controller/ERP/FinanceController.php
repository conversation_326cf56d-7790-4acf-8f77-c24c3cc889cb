<?php
/**
 * 进销管理
 * User: gzz
 * Date: 2020/04/13
 * Time: 11:22
 */

namespace App\Controller\ERP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Constant\RoutePermission;
use App\Exception\AppException;
use App\Logic\ERP\FinanceLogic;
use App\Logic\ERP\PermissionLogic;
use App\Param\Erp\CombinationAddParam;
use App\Param\Erp\CombinationListParam;
use App\Param\Erp\ConsumablesAddParam;
use App\Param\Erp\ConsumablesListParam;
use App\Param\Erp\ProductAddParam;
use App\Param\Erp\ProductListParam;
use App\Param\Erp\CustomerAddParam;
use App\Param\Erp\CustomerListParam;
use App\Param\Erp\StorageAddParam;
use App\Param\Erp\StorageListParam;
use App\Param\Erp\VendorAddParam;
use App\Param\Erp\VendorListParam;
use App\Param\Erp\StorehouseAddParam;
use App\Param\Erp\StorehouseListParam;
use App\Param\Erp\WarehouseAddParam;
use App\Param\Erp\WarehouseListParam;
use App\Response\CSV;
use App\Struct\Input;
use App\Utils\UploadTool;
use Common\EnvConfig;


class FinanceController extends Controller
{
    public function searchOptions(Input $input)
    {
        $input->verify(['column']);
        $list = (new FinanceLogic())->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $list
            ]
        ];
        return $response;
    }

    /**
     * 产品记录
     * @CtrlAnnotation(permissions=['/finance/product'])
     * @param Input $input
     * @return array
     */
    public function getProductList(Input $input)
    {

        $list = (new FinanceLogic())->getProductList(new ProductListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加产品
     * @CtrlAnnotation(permissions=['/finance/product'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addProduct(Input $input)
    {
        $input->verify(['product_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addProduct(new ProductAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑产品
     * @CtrlAnnotation(permissions=['/finance/product'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateProduct(Input $input)
    {
        $input->verify(['product_id', 'product_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateProduct(new ProductAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除产品
     * @CtrlAnnotation(permissions=['/finance/product'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeProduct(Input $input)
    {
        $input->verify(['product_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeProduct($input['product_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 客户记录
     * @CtrlAnnotation(permissions=['/finance/customer'])
     * @param Input $input
     * @return array
     */
    public function getCustomerList(Input $input)
    {

        $list = (new FinanceLogic())->getCustomerList(new CustomerListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加客户
     * @CtrlAnnotation(permissions=['/finance/customer'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addCustomer(Input $input)
    {
        $input->verify(['customer_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addCustomer(new CustomerAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑客户
     * @CtrlAnnotation(permissions=['/finance/customer'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateCustomer(Input $input)
    {
        $input->verify(['customer_id', 'customer_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateCustomer(new CustomerAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除客户
     * @CtrlAnnotation(permissions=['/finance/customer'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeCustomer(Input $input)
    {
        $input->verify(['customer_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeCustomer($input['customer_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }



    /**
     * 供应商记录
     * @CtrlAnnotation(permissions=['/finance/supplier'])
     * @param Input $input
     * @return array
     */
    public function getVendorList(Input $input)
    {

        $list = (new FinanceLogic())->getVendorList(new VendorListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加供应商
     * @CtrlAnnotation(permissions=['/finance/supplier'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addVendor(Input $input)
    {
        $input->verify(['vendor_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addVendor(new VendorAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑供应商
     * @CtrlAnnotation(permissions=['/finance/supplier'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateVendor(Input $input)
    {
        $input->verify(['vendor_id', 'vendor_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateVendor(new VendorAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除供应商
     * @CtrlAnnotation(permissions=['/finance/supplier'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeVendor(Input $input)
    {
        $input->verify(['vendor_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeVendor($input['vendor_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }



    /**
     * 仓库记录
     * @CtrlAnnotation(permissions=['/finance/stockAddress'])
     * @param Input $input
     * @return array
     */
    public function getStorehouseList(Input $input)
    {

        $list = (new FinanceLogic())->getStorehouseList(new StorehouseListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加仓库
     * @CtrlAnnotation(permissions=['/finance/stockAddress'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addStorehouse(Input $input)
    {
        $input->verify(['storehouse_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addStorehouse(new StorehouseAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑仓库
     * @CtrlAnnotation(permissions=['/finance/stockAddress'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateStorehouse(Input $input)
    {
        $input->verify(['storehouse_id', 'storehouse_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateStorehouse(new StorehouseAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除创库
     * @CtrlAnnotation(permissions=['/finance/stockAddress'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeStorehouse(Input $input)
    {
        $input->verify(['storehouse_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeStorehouse($input['storehouse_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 组合记录
     * @CtrlAnnotation(permissions=['/finance/combination'])
     * @param Input $input
     * @return array
     */
    public function getCombinationList(Input $input)
    {

        $list = (new FinanceLogic())->getCombinationList(new CombinationListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加组合
     * @CtrlAnnotation(permissions=['/finance/combination'], log_type='add')
     * @param Input $input
     * @return array
     */
        public function addCombination(Input $input)
    {
        $input->verify(['combination_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addCombination(new CombinationAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑组合
     * @CtrlAnnotation(permissions=['/finance/combination'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateCombination(Input $input)
    {
        $input->verify(['combination_id', 'combination_name']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateCombination(new CombinationAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除组合
     * @CtrlAnnotation(permissions=['/finance/combination'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeCombination(Input $input)
    {
        $input->verify(['combination_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeCombination($input['combination_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }





    /**
     * 入库记录列表
     * @CtrlAnnotation(permissions=['/finance/stock'])
     * @param Input $input
     * @return array
     */
    public function getStorageList(Input $input)
    {
        $list = (new FinanceLogic())->getStorageList(new StorageListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加入库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addStorage(Input $input)
    {
        $input->verify(['other_id', 'product_id', 'combination_id', 'storehouse_id', 'number', 'time', 'combination_id', 'combination_num']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addStorage(new StorageAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑入库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateStorage(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateStorage(new StorageAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 冻结/解冻
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function frozenStorage(Input $input)
    {
        $input->verify(['id', 'is_frozen']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->frozenStorage($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除入库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeStorage(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeStorage($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 获取出入库的权限
     * @CtrlAnnotation(permissions=['/finance/stock'])
     *
     * @return array
     */
    public function getStoragePermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::ERP_FINANCE_STOCK);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }


    /**
     * 出库记录列表
     * @CtrlAnnotation(permissions=['/finance/stock'])
     * @param Input $input
     * @return array
     */
    public function getWarehouseList(Input $input)
    {
        $list = (new FinanceLogic())->getWarehouseList(new WarehouseListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加出库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addWarehouse(Input $input)
    {
        $input->verify(['other_id', 'combination_id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addWarehouse(new WarehouseAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑出库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateWarehouse(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateWarehouse(new WarehouseAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 冻结/解冻
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function frozenWarehouse(Input $input)
    {
        $input->verify(['id', 'is_frozen']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->frozenWarehouse($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 上传图片
     * @method POST
     * @return array
     */
    public function uploadImage()
    {
        try {
            $file_info = UploadTool::image($this->request->files, EnvConfig::ERP_FINANCE_DIR_NAME);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }


    /**
     * 编辑收款
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editCollection(Input $input)
    {
        $input->verify(['id', 'status', 'collection_money', 'collection_date', 'collection_image']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->editCollection($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '编辑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除出库
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeWarehouse(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeWarehouse($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 耗材记录
     * @CtrlAnnotation(permissions=['/finance/consumables'])
     * @param Input $input
     * @return array
     */
    public function getConsumablesList(Input $input)
    {

        $list = (new FinanceLogic())->getConsumablesList(new ConsumablesListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 增加耗材
     * @CtrlAnnotation(permissions=['/finance/consumables'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addConsumables(Input $input)
    {
        $input->verify(['consumables']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->addConsumables(new ConsumablesAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑耗材
     * @CtrlAnnotation(permissions=['/finance/consumables'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateConsumables(Input $input)
    {
        $input->verify(['id', 'consumables']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->updateConsumables(new ConsumablesAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除耗材
     * @CtrlAnnotation(permissions=['/finance/consumables'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeConsumables(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_logic = new FinanceLogic();
            $material_logic->removeConsumables($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 导入产品信息
     * @CtrlAnnotation(permissions=['/finance/product'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importProduct(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importProduct($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadProductSample()
    {
        return new CSV('产品导入sample.xlsx', 'sample/product_template.xlsx');
    }

    /**
     * 导入客户信息
     * @CtrlAnnotation(permissions=['/finance/customer'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importCustomer(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importCustomer($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadCustomerSample()
    {
        return new CSV('客户导入sample.xlsx', 'sample/customer_template.xlsx');
    }

    /**
     * 导入供应商信息
     * @CtrlAnnotation(permissions=['/finance/supplier'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importVendor(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importVendor($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadVendorSample()
    {
        return new CSV('供应商导入sample.xlsx', 'sample/vendor_template.xlsx');
    }

    /**
     * 导入仓库信息
     * @CtrlAnnotation(permissions=['/finance/stockAddress'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importStorehouse(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importStorehouse($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadStorehouseSample()
    {
        return new CSV('仓库导入sample.xlsx', 'sample/storehouse_template.xlsx');
    }

    /**
     * 导入组合信息
     * @CtrlAnnotation(permissions=['/finance/combination'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importCombination(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importCombination($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadCombinationSample()
    {
        return new CSV('组合导入sample.xlsx', 'sample/combination_template.xlsx');
    }

    /**
     * 导入耗材信息
     * @CtrlAnnotation(permissions=['/finance/consumables'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importConsumables(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importConsumables($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadConsumablesSample()
    {
        return new CSV('耗材导入sample.xlsx', 'sample/consumables_template.xlsx');
    }


    /**
     * 导入入库信息
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importStorage(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importStorage($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadStorageSample()
    {
        return new CSV('入库导入sample.xlsx', 'sample/storage_template.xlsx');
    }
    /**
     * 导入出库信息
     * @CtrlAnnotation(permissions=['/finance/stock'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importWarehouse(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importWarehouse($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadWarehouseSample()
    {
        return new CSV('出库导入sample.xlsx', 'sample/warehouse_template.xlsx');
    }
}
