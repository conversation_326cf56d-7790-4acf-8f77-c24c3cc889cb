<?php
/**
 * 操作指南管理
 * User: gzz
 * Date: 2020/03/23
 * Time: 11:22
 */

namespace App\Controller;




use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\OperationGuideLogic;
use App\Param\OperationCategoryListParam;
use App\Param\OperationGuideListParam;
use App\Struct\Input;
use App\Utils\UploadTool;
use Common\EnvConfig;

class OperationGuideController extends Controller
{

    /**
     * @param Input $input
     * @return array
     */
    public function searchOptions(Input $input)
    {
        $input->verify(['column']);
        $list = (new OperationGuideLogic())->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $list
            ]
        ];
        return $response;
    }


    /**
     *
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getList(new OperationGuideListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }


    /**
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['title', 'details']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->add($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify(['title', 'details', 'id']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->edit($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['id']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->remove($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     *
     * @param Input $input
     * @return array
     */
    public function getCategoryList(Input $input)
    {
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getCategoryList(new OperationCategoryListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;

    }

    /**
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     * @return array
     */
    public function addCategory(Input $input)
    {
        $input->verify(['second_category', 'first_category', 'association_route']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->addCategory($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editCategory(Input $input)
    {
        $input->verify(['second_category', 'first_category', 'association_route', 'id']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->editCategory($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteCategory(Input $input)
    {
        $input->verify(['id']);
        try {
            $operation_guide_logic = new OperationGuideLogic();
            $operation_guide_logic->removeCategory($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @return array
     */
    public function getCategoryAllList()
    {
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getCategoryAllList();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;

    }


    /**
     * @return array
     */
    public function getAll()
    {
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getAll();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     *
     * @param Input $input
     * @return array
     */
    public function getListLike(Input $input)
    {
        $input->verify(['keyword']);
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getListLike(new OperationGuideListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getOne(Input $input)
    {
        $input->verify(['id']);
        $operation_guide_logic = new OperationGuideLogic();
        $list_data = $operation_guide_logic->getOne($input['id']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}