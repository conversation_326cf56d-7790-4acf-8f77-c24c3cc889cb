<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\TencentLogic;
use App\Model\RedisModel\MediaAccountAuthModel;
use App\Struct\Input;

class TencentController extends Controller
{
    protected $pass_method = ['notify', 'updateChannelPackage'];

    public function notify(Input $input)
    {
        $input->verify(['authorization_code']);

        $state = (array)json_decode($input['state']);
        $logic = new TencentLogic();

        $media_account_auth_model = new MediaAccountAuthModel();
        if (!$media_account_auth_model->lockAuth($state['platform'], MediaType::TENCENT, $state['creator_id'])) {
            throw new AppException("请勿重复点击授权按钮，请到集团后台查看账号授权结果");
        }

        $is_clue = ($state['auth_version'] ?? '') === "clue";

        try {
            if ($is_clue) {
                $data = $logic->saveClueToken($input['authorization_code'], $state);
            } else {
                $data = $logic->saveToken($input['authorization_code'], $state);
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        $media_account_auth_model->unLockAuth($state['platform'], MediaType::TENCENT, $state['creator_id']);

        return $response;
    }
}
