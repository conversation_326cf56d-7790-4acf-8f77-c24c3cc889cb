<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2023/2/16
 * Time: 10:38
 */

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Model\RedisModel\CKSwitchModel;
use App\Service\UserService;
use App\Struct\Input;

class CKSwitchController extends Controller
{
    public function __construct($request, $response = null)
    {
        parent::__construct($request, $response);
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
    }


    public function changeStatus(Input $input)
    {
        $input->verify(['status']);
        $status = intval($input['status']);


        (new CKSwitchModel())->setStatus($status);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => [],
        ];
    }

    public function getStatus()
    {
        $status = (new CKSwitchModel())->getStatus();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => [
                'status' => $status
            ],
        ];
    }
}
