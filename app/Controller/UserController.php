<?php
/**
 * 用户管理数据接口
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;

use App\Constant\Environment;
use App\Constant\PlatId;
use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Logic\FeishuLogic;
use App\Model\SqlModel\Zeda\TencentUserTokenModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Response\Image;
use App\Logic\WechatLogic;
use App\Response\Redirect;
use App\Service\DataBot\DataBotConfig;
use App\Service\DataBot\DataBotSession;
use App\Service\UserService;
use App\Struct\Input;
use App\Response\Response;
use App\Logic\UserLogic;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use App\Utils\UploadTool;
use Common\EnvConfig;
use Greg<PERSON>\Captcha\CaptchaBuilder;

class UserController extends Controller
{
    protected $pass_method = [
        'login', 'logout', 'phrase', 'isShowPhrase', 'sendBindMobileCode',
        'sendCheckIPCode', 'loginNotInWhiteList', 'loginBindMobile', 'loginPasswordExpire',
        'sendKeepLoginCode', 'getLoginScanUrl', 'checkScanLogin', 'loginByScan', 'closeScanQrCode',
        'removeSuperManager', 'recoverSuperManager', 'test'
    ];

    // 技术的超管账号
    const SUPER_MANAGER_LIST = [
        'zhangzhen', // 张臻
        'daihuanqi', // 戴焕其
        'linshengji', // 林盛及
        'admin_qiujunwen', // 仇俊文_管理员
        'admin_mouxiuping', // 牟秀平_管理员
        'admin_lvwanting', // 吕婉婷_管理员
        'admin_liangjiayu', // 中旭未来
        'zhangzhonghao', // 张中昊
        'panxiang', // 潘翔
        'admin_zhongjiekun', // 钟杰堃_管理员
        'admin_chenjiatong', // 陈嘉彤_管理员
    ];

    /**
     * 解除所有技术的超管权限
     *
     * @param Input $input
     *
     * @return array
     */
    public function removeSuperManager(Input $input)
    {
        $input->verify(['secret']);
        if ($input['secret'] !== 'UMH*&(*JH') {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '密钥错误',
            ];
        }

        (new UserModel())->removeUserByAccount(self::SUPER_MANAGER_LIST);

        // 退出登录
        $token_list = RedisCache::getInstance()->hGetAll('session_token_bind');
        foreach (self::SUPER_MANAGER_LIST as $account) {
            if (isset($token_list[$account])) {
                RedisCache::getInstance()->del($token_list[$account]);
            }
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];

    }


    /**
     * 恢复所有技术的超管权限
     *
     * @param Input $input
     *
     * @return array
     */
    public function recoverSuperManager(Input $input)
    {
        $input->verify(['secret']);
        if ($input['secret'] !== 'UMH*&(*JH') {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '密钥错误',
            ];
        }
        (new UserModel())->recoverUserByAccount(self::SUPER_MANAGER_LIST);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];
    }

    /**
     * 判断是否需要显示验证码
     *
     * @return array
     */
    public function isShowPhrase()
    {
        $show = Container::getSession()->show_phrase ? true : false;

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => 'success',
            'data' => ['show' => $show]
        ];

    }

    /**
     * 生成前端验证码
     * @CtrlAnnotation(log_type='login')
     *
     * @return Image
     * <AUTHOR>
     *
     */
    public function phrase()
    {
        $builder = new CaptchaBuilder();
        $builder->build();
        Container::getSession()->phrase = $builder->getPhrase();
        $image_data = $builder->get();
        return Response::Image($image_data, 'image/jpeg');
    }

    /**
     * 登录接口
     * @CtrlAnnotation(log_type='login')
     * @method POST
     *
     * @param Input $input
     *
     * @return array
     * @see string  phrase    验证码 | 选填
     * @see string  account   用户名 | 必填
     * @see string  password  密码   | 必填
     */
    public function login(Input $input)
    {
        $input->verify(['account', 'password']);

        // 获取用户信息并验证
        $logic = new UserLogic();
        $account = $input['account'];
        $password = $input['password'];
        $phrase = $input['phrase'] ?? '';
        $user_info = $logic->checkLoginInfo($account, $password, $phrase);

        // 检验密码有效期
        $logic->checkPwdExpire($user_info);

        $data = $logic->login($user_info, $this->response);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '登录成功',
            'data' => $data,
        ];
    }

    /**
     * 已登录用户发送验证码
     * @CtrlAnnotation(log_type='other')
     *
     * @param Input $input
     *
     * @return array
     */
    public function sendCode(Input $input)
    {
        $input->verify(['mobile']);

        // 发送验证码
        $logic = new UserLogic();
        try {
            $logic->sendCode($input['mobile'], Container::getSession()->user_id);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '发送成功',
                'data' => [],
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * 未登录用户绑定手机号的验证码
     * @CtrlAnnotation(log_type='login')
     *
     * @param Input $input
     *
     * @return array
     */
    public function sendBindMobileCode(Input $input)
    {
        $input->verify(['mobile']);

        // 发送验证码
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_BIND);
            $logic->sendCode($input['mobile'], $user_info['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '发送成功',
                'data' => [],
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }


    /**
     * 发送验证IP的验证码
     * @CtrlAnnotation(log_type='login')
     *
     * @param Input $input
     *
     * @return array
     */
    public function sendCheckIPCode(Input $input)
    {
        // 发送验证码
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);
            $logic->sendCode($user_info['mobile'], $user_info['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '发送成功',
                'data' => [],
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * 生成飞书or微信登录验证二维码URL
     * @CtrlAnnotation(log_type='other')
     *
     * @return array
     */
    public function getLoginScanUrl(Input $input)
    {
        $input->verify(['media']); // wechat 和 feishu
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);
            // 此处待优化，将判断media 改为抽成一个抽象类
            if ($input['media'] == 'feishu') {
                $feishu_service = new FeishuLogic();
                $data = $feishu_service->createLoginUrl($user_info['id']);
            } else {
                $wechat_service = new WechatLogic();
                $data = $wechat_service->createLoginUrl($user_info['id']);
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
                'data' => $data,
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * 未登录用户绑定手机号
     * @CtrlAnnotation(log_type='login')
     *
     * @param Input $input
     *
     * @return array
     */
    public function loginBindMobile(Input $input)
    {
        $input->verify(['mobile', 'code']);

        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_BIND);

            // 先校验
            $logic->checkCode($input['mobile'], $input['code']);

            // 校验成功后 登录
            $data = $logic->login($user_info, $this->response);

            // 绑定手机号
            $logic->changeMobile(Container::getSession()->user_id, $input['mobile']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '验证通过',
                'data' => $data,
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }


    /**
     * 已经登录的用户修改绑定手机号
     * @CtrlAnnotation(log_type='edit')
     *
     * @param Input $input
     *
     * @return array
     */
    public function editBindMobile(Input $input)
    {
        $input->verify(['mobile', 'code']);

        $logic = new UserLogic();
        try {
            // 先校验
            $logic->checkCode($input['mobile'], $input['code']);

            // 绑定手机号
            $logic->changeMobile(Container::getSession()->user_id, $input['mobile']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '绑定成功',
                'data' => [],
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='login')
     * @param Input $input
     *
     * @return array
     */
    public function loginNotInWhiteList(Input $input)
    {
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);

            // 校验用户状态是否有效
            $logic->checkUserState($user_info['account']);

            // 校验
            $logic->checkCode($user_info['mobile'], $input['code']);

            // 检验密码有效期
            $logic->checkPwdExpire($user_info);

            // 校验成功后 登录
            $data = $logic->login($user_info, $this->response);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '验证通过',
                'data' => $data,
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='login')
     * @param Input $input
     *
     * @return array
     */
    public function loginByScan(Input $input)
    {
        $input->verify(['media', 'scan_code']); // wechat 和 feishu
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);

            // 校验用户状态是否有效
            $logic->checkUserState($user_info['account']);

            // 校验微信验证码
            // 背景：原计划是让前端轮询本接口，检查缓存如果有扫码成功标识，即可通过校验，但是由于本方法的log_type=login,如果前端轮询请求登录状态，
            //      在缓存没有标识之前，轮询会产生很多不符合登录逻辑的日志
            // 解决：前端轮询checkWechatLogin()接口检查微信登录状态，如果校验通过，接口返回一个随机数wechat_code，在请求本接口loginByWechatScan()时带上作为校验标识
            $logic->checkScanCode($input['media'], $user_info['id'], $input['scan_code']);

            // 检验密码有效期
            $logic->checkPwdExpire($user_info);

            // 校验成功后 登录
            $data = $logic->login($user_info, $this->response);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '验证通过',
                'data' => $data,
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='login')
     * @param Input $input
     *
     * @return array
     */
    public function checkScanLogin(Input $input)
    {
        $input->verify(['media']); // wechat 和 feishu
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);
            $res = $logic->checkScanLoginStatus($user_info['id'], $input['media']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取状态成功',
                'data' => $res
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }

        return $response;
    }

    public function closeScanQrCode(Input $input)
    {
        $input->verify(['media']); // wechat 和 feishu
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_IN_WHITE_LIST);
            $logic->closeScanQrCode($user_info['id'], $input['media']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取状态成功',
                'data' => []
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }

        return $response;
    }

    /**
     * 密码过期，重新修改密码
     *
     * @CtrlAnnotation(log_type='login')
     * @param Input $input
     *
     * @return array
     */
    public function loginPasswordExpire(Input $input)
    {
        $logic = new UserLogic();
        try {
            $user_info = $logic->getUnFinishLoginInfo(Container::getSession()->getToken(), UserLogic::UN_FINISH_LOGIN_NO_PWD_EXPIRE);

            // 参数验证
            $input->verify(['password']);
            $logic = new UserLogic();
            $change_res = $logic->changePasswordNoOldPwd($user_info['account'], $input['password']);

            if ($change_res) {
                // 校验成功后 登录
                $data = $logic->login($user_info, $this->response);
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => '修改成功',
                    'data' => $data,
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => '修改失败，请重试',
                ];
            }

        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }

    /**
     * 获取用户信息接口
     * @method GET
     *
     * @return array
     * @see string  token  必填
     *
     */
    public function info()
    {
        $user_level = Container::getSession()->get('user_level');
        $permission = Container::getSession()->permission;
        $permission_uri = Container::getSession()->permission_uri;
        if ($user_level !== -1) {
            foreach ($user_level as $module => $item) {
                $res_user_level[$module] = [
                    'leader' => $item['leader'],
                    'level' => $item['level'],
                    'rank_id' => $item['rank_id'],
                    'type' => $item['type'],
                    'platform_id' => $item['platform_id'],
                    'platform_name' => $item['platform_name'],
                    'department_id' => $item['department_id'],
                    'department_name' => $item['department_name'],
                    'department_group_id' => $item['department_group_id'],
                    'department_group_name' => $item['department_group_name'],
                    'department_group_position_id' => $item['department_group_position_id'],
                    'department_group_position_name' => $item['department_group_position_name'],
                    'department_group_position_worker_id' => $item['department_group_position_worker_id'],
                    'department_group_position_worker_name' => $item['department_group_position_worker_name'],
                ];
            }
        }

        $module_data = [];
        foreach (['dms', 'dsp', 'ly', 'fa', 'erp'] as $module) {
            $module_data[$module] = [
                'level_info' => $res_user_level[$module] ?? [],
                'permission' => $permission[$module] ?? [],
                'permission_uri' => $permission_uri[$module] ?? [],
            ];
        }

        // 判断是否可以登录下级
        $is_login = false;
        if (UserService::isSuperManager() || in_array(Container::getSession()->get('user_id'), EnvConfig::LOGIN_UID, true)) {
            $is_login = true;
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                    'user_info' => Container::getSession()->getUserInfo(),
                    'permission_all' => Container::getSession()->permission_all_list,
                    'platform_map' => EnvConfig::APP_VERSION . base64_encode(json_encode(EnvConfig::PLATFORM_MAP)),
                    'plat_map' => EnvConfig::APP_VERSION . base64_encode(json_encode(PlatId::MAP)),
                    'app_version' => EnvConfig::APP_VERSION,
                    'is_login' => $is_login,
                    'is_other' => Container::getSession()->get('is_other'),
                ] + $module_data
        ];
    }

    /**
     * 获取绑定微信的url
     * @CtrlAnnotation(log_type='get')
     * @method POST
     *
     * @return array
     */
    public function getBindingWechatUrl()
    {
        $wechat_service = new WechatLogic();
        $data = $wechat_service->createBindingUrl(Container::getSession()->user_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 删除绑定微信
     * @CtrlAnnotation(log_type='delete')
     * @method POST
     *
     * @return array
     */
    public function deleteOpenID()
    {
        $wechat_service = new UserLogic();
        $data = $wechat_service->deleteOpenID(Container::getSession()->user_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
            'data' => $data
        ];
    }

    public function checkBindingWechat()
    {
        $logic = new UserLogic();
        $res = $logic->checkBindingWechat(Container::getSession()->user_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'bind' => $res,
            ],
        ];
    }

    /**
     * 修改用户密码接口
     * @method POST
     *
     * @param Input $input_param
     *
     * @return array
     */
    public function changePassword(Input $input_param)
    {
        // 参数验证
        $need_param = ['oldPwd', 'newPwd'];
        $input_param->verify($need_param);
        $logic = new UserLogic();
        $change_res = $logic->changePassword(Container::getSession()->account, $input_param['oldPwd'], $input_param['newPwd']);

        if ($change_res) {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功',
            ];
        } else {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '旧密码错误',
            ];
        }
        return $response;
    }

    /**
     * 修改用户密码接口
     * @CtrlAnnotation(log_type='edit')
     * @method POST
     *
     * @param Input $input
     *
     * @return array
     * @see string  name  新密码 | 必填
     *
     */
    public function editInfo(Input $input)
    {

        $logic = new UserLogic();
        $change_res = $logic->changeInfo(Container::getSession()->account, $input['avatar']);

        if ($change_res) {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功',
            ];
        } else {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败',
            ];
        }
        return $response;
    }

    /**
     * 上传头像
     * @method POST
     *
     * @return array
     */
    public function uploadAvatar()
    {
        try {
            $file_path = UploadTool::image($this->request->files, EnvConfig::AVATAR_DIR_NAME);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => [
                    'file_path' => $file_path
                ]
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 退出登录
     * @CtrlAnnotation(log_type='login')
     */
    public function logout()
    {
        $logic = new UserLogic();
        // 清除session
        $logic->logout();
        // 清除cookie
        $this->response->cookie('x-token', '', 0, '/', '', false, false);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '退出登录成功',
        ];

        return $response;
    }


    /**
     * @CtrlAnnotation(log_type='login')
     * @param Input $input
     *
     * @return array
     */
    public function loginUser(Input $input)
    {
        $input->verify(['id']);

        $logic = new UserLogic();
        try {
            $token = $logic->loginUser($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '登录成功',
                'data' => $token
            ];
        } catch (\Exception $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 修改微信日报推送维度
     *
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     *
     * @return array
     */
    public function changeDailyReportDim(Input $input)
    {
        $input->verify(['state']);

        $logic = new UserLogic();
        $logic->changeDailyReportDim($input['state']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => ''
        ];
    }

    public function getUserList()
    {
        $logic = new UserLogic();
        $data = $logic->getUserList();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }


    /**
     * 发送续登录的验证码
     * @CtrlAnnotation(log_type='login')
     *
     * @param Input $input
     *
     * @return array
     */
    public function sendKeepLoginCode(Input $input)
    {
        $input->verify(['user_id', 'refresh_token']);
        // 发送验证码
        $logic = new UserLogic();

        try {
            $logic->sendKeepLoginCode($input['user_id'], $input['refresh_token']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '发送成功',
                'data' => [],
            ];
        } catch (AppException $exception) {
            $response = [
                'code' => $exception->getCode(),
                'message' => $exception->getMessage(),
                'data' => []
            ];
        }
        return $response;
    }


    /**
     * @CtrlAnnotation(log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getExpireUserList(Input $input): array
    {
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $id = $input['user_id'] ?? 0;
        $name = $input['name'] ?? '';
        $account = $input['account'] ?? '';
        $state = $input['state'] ?? '';

        $data = (new UserLogic())->getExpireUserList($id, $name, $account, $state);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addExpireUser(Input $input): array
    {
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['expire_time', 'user_id']);

        (new UserLogic())->addExpireUser($input['user_id'], $input['expire_time'], Container::getSession()->name);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    public function changeMergeDimension(Input $input): array
    {
        $input->verify(['merge_dimension']);
        $merge_dimension = intval($input['merge_dimension']);

        (new UserModel())->updateMergeDimension(Container::getSession()->user_id, $merge_dimension);

        Container::getSession()->set('merge_dimension', $merge_dimension);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 修改默认的统计口径
     *
     * @param Input $input
     * @return array
     */
    public function changeDimensionType(Input $input): array
    {
        $input->verify(['dimension_type']);
        $dimension_type = intval($input['dimension_type']);


        (new UserModel())->updateDimensionType(Container::getSession()->user_id, $dimension_type);

        Container::getSession()->set('dimension_type', $dimension_type);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }


    /**
     * @CtrlAnnotation(log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     */
    public function deleteExpireUser(Input $input): array
    {
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['user_id']);

        (new UserLogic())->deleteExpireUser($input['user_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }


    /**
     * @CtrlAnnotation(log_type='edit')
     *
     * @param Input $input
     *
     * @return array
     */
    public function changeExpireUser(Input $input): array
    {
        if (!UserService::isSuperManager()) {
            throw new AppException('无权限访问');
        }
        $input->verify(['user_id', 'expire_time']);

        (new UserLogic())->editExpireUser($input['user_id'], $input['expire_time'], Container::getSession()->name);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 跳转到BI登录
     *
     * @return Redirect
     */
    public function loginBI()
    {
        $account = Container::getSession()->account;
        [$timestamp, $redirect_token, $domain] = $this->getRedirectParam();

        $redirect_url = "$domain/user/loginByXToken?redirect_token=$redirect_token&account=$account&tt=$timestamp";

        return new Redirect($redirect_url);
    }


    /**
     * 跳转到BI看板
     *
     * @param Input $input
     *
     * @return Redirect
     */
    public function toBIDashboard(Input $input)
    {
        $input->verify(['dashboard_id', 'jump_data']);
        $dashboard_id = $input['dashboard_id'];
        $jump_data = $input['jump_data'];
        $account = Container::getSession()->account;
        [$timestamp, $redirect_token, $domain] = $this->getRedirectParam();

        $redirect_url = "$domain/normal/dashboard/toBIDashboard?redirect_token=$redirect_token&account=$account&tt=$timestamp&dashboard_id=$dashboard_id&jump_data=$jump_data";

        return new Redirect($redirect_url);
    }


    private function getRedirectParam()
    {
        $account = Container::getSession()->account;
        $timestamp = time();
        $key = 'ZD_GROUP_@)@)';

        $redirect_token = md5($account . $key . $timestamp);

        //重定向浏览器
        switch (EnvConfig::ENV) {
            case Environment::LOCAL:
                $domain = 'http://localhost:8888/api';
                break;
            case Environment::DEV;
                $domain = 'http://bi-dev.zx.com';
                break;
            default:
                $domain = 'https://bi.zx.com';
        }
        return [$timestamp, $redirect_token, $domain];
    }

    public function setTencentToken(Input $input)
    {
        $input->verify(['user_status', 'user_token', 'expire_time']);

        $model = (new TencentUserTokenModel());

        $is_own = $model->getUserTokenInfoByUserId(Container::getSession()->user_id);

        if ($is_own) {
            $model->updateUserToekn(
                Container::getSession()->user_id,
                $input['user_status'],
                $input['user_token'],
                $input['expire_time'],
                $input['wx_id'] ?? '',
            );
        } else {
            $model->addUserToekn(
                Container::getSession()->user_id,
                Container::getSession()->name,
                $input['user_status'],
                $input['user_token'],
                $input['expire_time'],
                $input['wx_id'] ?? '',
            );
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }
}
