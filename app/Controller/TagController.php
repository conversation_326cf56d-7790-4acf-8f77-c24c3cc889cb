<?php
/**
 * 标签
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Struct\Input;
use App\Logic\TagLogic;

class TagController extends Controller
{
    public function tagOptions()
    {
        $route_id = $this->getRouteId();
        if ($route_id === 0) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => 'route_name参数错误'
            ];
        }
        $logic = new TagLogic();
        $list = $logic->getTagOptionsByUserId(Container::getSession()->user_id, $route_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
            ]
        ];
    }

    /**
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     *
     * @return array
     */
    public function addTag(Input $input)
    {
        $input->verify(['name']);
        if (empty($input['tag'])) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '参数不完整',
                'data' => []
            ];
        }

        $route_name = $this->request->header['router'] ?? $this->request->get['router'];
        if (key_exists($route_name, RouteID::ROUTE_NAME_MAP)) {
            $route_id = RouteID::ROUTE_NAME_MAP[$route_name];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => 'route_name参数错误'
            ];
        }
        $logic = new TagLogic();

        if (empty($input['tag'])) {
            $input['tag'] = new \ArrayObject();
        }
        $sort = $input['sort'] ?? 0;
        $cate = $input['cate'] ?? '';
        $id = $logic->saveTag($route_id, Container::getSession()->user_id, $input['name'], $input['tag'], $sort, $cate);
        if ($id > 0) {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功',
                'data' => [
                    'id' => $id,
                ]
            ];
        } else {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '添加失败',
                'data' => [
                ]
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(log_type='delete')
     * @param Input $input
     *
     * @return array
     */
    public function deleteTag(Input $input)
    {
        $input->verify(['dmp_tag_id']);
        $logic = new TagLogic();
        $result = $logic->deleteTag($input['dmp_tag_id']);
        if ($result) {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
                'data' => [
                    'id' => $result,
                ]
            ];
        } else {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '删除失败',
                'data' => [
                ]
            ];
        }
        return $response;
    }


    /**
     * 添加标签默认值
     * @CtrlAnnotation(log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addDefault(Input $input)
    {
        $input->verify(['id']);
        $logic = new TagLogic();
        $route_name = $this->request->header['router'] ?? $this->request->get['router'];
        $multiple = $input['multiple'] ?? false;
        if (key_exists($route_name, RouteID::ROUTE_NAME_MAP)) {
            $route_id = RouteID::ROUTE_NAME_MAP[$route_name];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => 'route_name参数错误'
            ];
        }

        $logic->addDefault($input['id'], Container::getSession()->user_id, $route_id, $multiple);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => []
        ];
    }


    /**
     * 移除标签默认值
     * @CtrlAnnotation(log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     */
    public function removeDefault(Input $input)
    {
        $logic = new TagLogic();
        $route_name = $this->request->header['router'] ?? $this->request->get['router'];
        if (key_exists($route_name, RouteID::ROUTE_NAME_MAP)) {
            $route_id = RouteID::ROUTE_NAME_MAP[$route_name];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => 'route_name参数错误'
            ];
        }

        $tag_id = $input['tag_id'] ?? 0;

        $logic->removeDefault(Container::getSession()->user_id, $route_id, $tag_id);

        return  [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => []
        ];
    }


    /**
     * 标签排序
     *
     * @param Input $input
     *
     * @return array
     */
    public function tagSort(Input $input)
    {
        $input->verify(['id_list']);

        $id_list = $input['id_list'];
        if (!is_array($id_list)) {
            return  [
                'code' => ResponseCode::PARAM_ERROR,
                'message' => '参数错误'
            ];
        }
        $logic = new TagLogic();
        $logic->tagSort($id_list);

        return  [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

}
