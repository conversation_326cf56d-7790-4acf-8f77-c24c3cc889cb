<?php

namespace App\Controller;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\ToutiaoLogic;
use App\Logic\XingtuLogic;
use App\Model\RedisModel\MediaAccountAuthModel;
use App\Struct\Input;

class ToutiaoController extends Controller
{
    protected $pass_method = ['notify'];

    public function notify(Input $input)
    {
        $input->verify(['auth_code', 'state']);
        $toutiao_logic = new ToutiaoLogic();
        $xingtu_logic = new XingtuLogic();
        $state = json_decode($input['state'], true);

        if (!in_array($state['media_type'], [MediaType::TOUTIAO, MediaType::XINGTU])) {
            throw new AppException("不合法的媒体类型");
        }

        $media_account_auth_model = new MediaAccountAuthModel();
        if (!$media_account_auth_model->lockAuth($state['platform'], $state['media_type'], $state['creator_id'])) {
            throw new AppException("请勿重复点击授权按钮，请到集团后台查看账号授权结果");
        }

        try {
            // 星图账号授权
            if ((int)$state['media_type'] === MediaType::XINGTU) {
                $data = $xingtu_logic->saveToken($state, $input['auth_code']);
            } else {
                $data = $toutiao_logic->saveToken($state, $input['auth_code']);
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $data['message'],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '授权失败, err message: ' . $e->getMessage(),
            ];
        }

        $media_account_auth_model->unLockAuth($state['platform'], $state['media_type'], $state['creator_id']);

        return $response;
    }
}
