<?php
/**
 * 素材管理
 * User: gzz
 * Date: 2020/03/23
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Constant\RouteID;
use App\Logic\API\MaterialStoreLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\RabbitModel\MaterialFeiShuSyncTaskMQModel;
use App\Model\SqlModel\DataMedia\OdsMaterialCombinedTagsLogModel;
use App\Model\SqlModel\DataMedia\OdsMaterialFileLogModel;
use App\Model\SqlModel\Zeda\MaterialFeiShuSyncModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\Material\MaterialAddParam;
use App\Param\Material\MaterialAprovedRateListParam;
use App\Param\Material\MaterialAuthorWeightParam;
use App\Param\Material\MaterialEffectGradeListParam;
use App\Param\Material\MaterialFeiShuSyncSearchParam;
use App\Param\MediaAccountListParam;
use App\Response\Response;
use App\Service\NoticeService;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialLogic;
use App\Param\Material\MaterialStoreListParam;
use App\Utils\UploadTool;
use Common\EnvConfig;


class MaterialStoreController extends Controller
{
    protected $pass_method = ['message'];

    /**
     * @param Input $input
     * @return array
     */
    public function message(Input $input)
    {
        (new NoticeService())->broadcast(NoticeService::NOTICE_MATERIAL_TASK, $input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发送成功',
        ];
    }

    /**
     * @return array
     */
    public function getPermission()
    {
        $permission_logic = new PermissionLogic();
        $data = $permission_logic->getMaterialStoreRoutePermission();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取素材列表
     * @CtrlAnnotation(permissions=['/material/store'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $material_logic = new MaterialLogic();
        $size = explode('*', $input['size']);
        if (is_array($size) && count($size) > 1) {
            $input['width'] = $size[0];
            $input['height'] = $size[1];
        }

        $material_file_model = new OdsMaterialFileLogModel();

        $material_file_id_list = [];
        if ($input['material_file_id'] ?? false) {
            $material_file_id_list = explode(',', $input['material_file_id']);
        }
        $material_file_md5_list = [];
        if ($input['material_file_md5'] ?? false) {
            $material_file_md5_list = explode(',', $input['material_file_md5']);
        }

        if ($material_file_id_list || $material_file_md5_list) {

            $material_id_list = $material_file_model->getListByIdOrMD5($material_file_id_list, $material_file_md5_list)->pluck('material_id')->toArray();
            $material_id_list = array_unique($material_id_list);
            if ($material_id_list) {
                $input['material_id'] .= implode(',', $material_id_list);
            }
        }


        $list_data = $material_logic->getList(new MaterialStoreListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材用户列表
     * @CtrlAnnotation(permissions=['/material/store'])
     * @return array
     */
    public function userList()
    {
        $material_logic = new MaterialLogic();
        $data = $material_logic->getUserList();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
        return $response;
    }

    /**
     * 获取可以操作的媒体账户
     * @CtrlAnnotation(permissions=['/material/store'])
     * @param Input $input
     * @return array
     */
    public function mediaAccountOptions(Input $input)
    {
        $mal = new MediaAccountListParam($input);
        $list = (new PermissionLogic())->getMediaAccountOptions($mal);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list]
        ];

        return $response;
    }

    /**
     * 创建素材
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['name', 'platform']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->add($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 上传图片
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadImage()
    {
        try {
            $file_info = UploadTool::materialImage($this->request->files, EnvConfig::MATERIAL_IMG_DIR_NAME);
            $file = (new MaterialLogic())->md5IsAlreadyUpload($file_info['md5']);
            $file_info['remark'] = '';
            if ($file) {
                $file_info['remark'] = "你已经在素材ID{$file->material_id}上传过此文件";
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传视频
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadVideo()
    {
        try {
            $file_info = UploadTool::materialVideo($this->request->files, EnvConfig::MATERIAL_VIDEO_DIR_NAME);
            $file = (new MaterialLogic())->md5IsAlreadyUpload($file_info['md5']);
            $file_info['remark'] = '';
            if ($file) {
                $file_info['remark'] = "你已经在素材ID{$file->material_id}上传过此文件";
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传音频
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadAudio()
    {
        try {
            $file_info = UploadTool::materialAudio($this->request->files, EnvConfig::MATERIAL_AUDIO_DIR_NAME);
            $file = (new MaterialLogic())->md5IsAlreadyUpload($file_info['md5']);
            $file_info['remark'] = '';
            if ($file) {
                $file_info['remark'] = "你已经在素材ID{$file->material_id}上传过此文件";
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传Icon
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadIcon()
    {
        try {
            $file_info = UploadTool::materialImage($this->request->files, EnvConfig::MATERIAL_ICON_DIR_NAME);
            $file = (new MaterialLogic())->md5IsAlreadyUpload($file_info['md5']);
            $file_info['remark'] = '';
            if ($file) {
                $file_info['remark'] = "你已经在素材ID{$file->material_id}上传过此文件";
            }
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传zip
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadZip()
    {
        try {
            $file_info = UploadTool::materialZip($this->request->files, EnvConfig::MATERIAL_ZIP_DIR_NAME);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传aep
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadAep()
    {
        try {
            $file_info = UploadTool::materialAep($this->request->files, EnvConfig::MATERIAL_AEP_DIR_NAME);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 上传txt
     * @CtrlAnnotation(permissions=['/material/add'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadTxt()
    {
        try {
            $file_info = UploadTool::materialTxt($this->request->files, EnvConfig::MATERIAL_TXT_DIR_NAME);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 更新素材
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function update(Input $input)
    {
        $input->verify(['name', 'platform']);
        try {
            $material_logic = new MaterialLogic();
            $input['media_type'] = (int)$input['general'] === 0 ? 0 : $input['media_type'];
            $material_logic->update(new MaterialAddParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新素材标签
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateMaterialLabelForBatch(Input $input)
    {
        $input->verify(['platform', 'ids', 'label']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->updateMaterialLabelForBatch($input['platform'], $input['ids'], $input['label']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 批量更新素材优先测试
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateMaterialPriority(Input $input)
    {
        $input->verify(['platform', 'material_ids', 'is_priority']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->updateMaterialPriority($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateMaterialIsPublic(Input $input)
    {
        $input->verify(['platform', 'material_ids', 'is_public']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->updateMaterialIsPublic($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 移除素材
     * @CtrlAnnotation(permissions=['/material/store'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function remove(Input $input)
    {
        $input->verify(['material_id', 'platform']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->remove($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 批量移除素材
     * @CtrlAnnotation(permissions=['/material/store'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function removeMulti(Input $input)
    {
        $input->verify(['ids']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->removeMulti($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 下载素材文件
     * @CtrlAnnotation(permissions=['/material/store'], log_type='export')
     * @param Input $input
     * @return \App\Response\File|array
     */
    public function download(Input $input)
    {
        $input->verify(['material_id', 'platform', 'file_type']);
        // 用于前端iframe判断开始下载
        if (isset($input['uuid'])) {
            $this->response->cookie($input['uuid'], 'ok', 0, '/', '', false, false);
        }
        $material_logic = new MaterialLogic();
        [$file_path, $file_name, $download_file_name] = $material_logic->download($input);

        $response = Response::File($file_path . '/' . $file_name);
        $response->downloadName($download_file_name);
        return $response;
    }

    /**
     * 补充上传图片
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @method POST
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function uploadImageAdd(Input $input)
    {
        try {
            $file_info = UploadTool::materialImage($this->request->files, EnvConfig::MATERIAL_IMG_DIR_NAME);
            $material_logic = new MaterialLogic();
            $material_logic->uploadAdd($input, $file_info);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 补充上传视频
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @method POST
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function uploadVideoAdd(Input $input)
    {
        try {
            $file_info = UploadTool::materialVideo($this->request->files, EnvConfig::MATERIAL_VIDEO_DIR_NAME);
            $material_logic = new MaterialLogic();
            $material_logic->uploadAdd($input, $file_info);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 补充上传Icon
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @method POST
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function uploadIConAdd(Input $input)
    {
        try {
            $file_info = UploadTool::materialImage($this->request->files, EnvConfig::MATERIAL_ICON_DIR_NAME);
            $material_logic = new MaterialLogic();
            $material_logic->uploadAdd($input, $file_info);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 补充上传Zip
     * @CtrlAnnotation(permissions=['/material/store'], log_type='edit')
     * @method POST
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function uploadZipAdd(Input $input)
    {
        try {
            $file_info = UploadTool::materialZip($this->request->files, EnvConfig::MATERIAL_ZIP_DIR_NAME);
            $material_logic = new MaterialLogic();
            $material_logic->uploadAdd($input, $file_info);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }


    /**
     * 获取要上传媒体的素材文件列表
     * @param $input
     * @return array
     *
     */
    public function getUploadMediaFiles(Input $input)
    {
        $input->verify(['platform', 'material_ids', 'media_type']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getUploadMediaFiles($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材评分详细系信息
     * @param Input $input
     * @return array
     */
    public function getMaterialEffectGrade(Input $input)
    {
        $input->verify(['platform', 'material_id', 'cost_days']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialEffectGrade(new MaterialEffectGradeListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材评分负责人消耗
     * @param Input $input
     * @return array
     */
    public function getMaterialEffectAgentLeader(Input $input)
    {
        $input->verify(['platform', 'material_id', 'cost_date', 'create_date']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialEffectAgentLeader($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材过审详细信息
     * @param Input $input
     * @return array
     */
    public function getMaterialAprovedRate(Input $input)
    {
        $input->verify(['platform', 'material_id']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialAprovedRate(new MaterialAprovedRateListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材作者分配
     * @param Input $input
     * @return array
     */
    public function getMaterialAuthorWeight(Input $input)
    {
        $input->verify(['platform', 'material_id']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialAuthorWeight($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 更新素材作者分配百分比
     * @param Input $input
     * @return array
     */
    public function updateMaterialAuthorWeight(Input $input)
    {
        $input->verify(['platform', 'material_id', 'weights']);
        try {
            $material_logic = new MaterialLogic();
            $material_logic->updateMaterialAuthorWeight(new MaterialAuthorWeightParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/material/store'], log_type='export')
     * @param Input $input
     * @return \App\Response\File
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function exportMaterial(Input $input)
    {
        $size = explode('*', $input['size']);
        if (is_array($size) && count($size) > 1) {
            $input['width'] = $size[0];
            $input['height'] = $size[1];
        }
        $material_logic = new MaterialLogic();
        $file = $material_logic->exportMaterial(new MaterialStoreListParam($input));
        $response = Response::File($file);
        $response->downloadName('素材.xlsx');
        return $response;
    }


    /**
     * @param Input $input
     * @return array
     */
    public function getBlackWords(Input $input)
    {
        $input->verify(['industry']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getBlackWords($input['industry']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 获取素材的解构信息
     * @param Input $input
     * @return array
     */
    public function getDeconstructBySignature(Input $input)
    {
        $input->verify(['signature']);

        $material_logic = new MaterialLogic();
        $data = $material_logic->getDeconstructBySignature($input['signature']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 锁定素材分析结果
     * @CtrlAnnotation(permissions=['/material/store'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function lockStoryAnalyse(Input $input)
    {
        $input->verify(['signature', 'analyse_type', 'story_request_id']);

        $material_logic = new MaterialLogic();
        $material_logic->lockStoryAnalyse(
            $input['signature'],
            $input['analyse_type'],
            $input['story_request_id'],
            $input['summary_request_id'] ?? "",
            $input['breakdown_request_id'] ?? ""
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '锁定成功',
        ];
    }

    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_MATERIAL_STORE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 重新分析素材
     * @CtrlAnnotation(permissions=['/material/store'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function reanalyseMaterial(Input $input)
    {
        $input->verify(['signature', 'analyse_types']);

        $material_logic = new MaterialLogic();
        $material_logic->reanalyseMaterial(
            $input['signature'],
            $input['analyse_types'],
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    public function feishuMaterialSyncList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MaterialFeiShuSyncModel())->getList(new MaterialFeiShuSyncSearchParam($input)),
            'message' => '操作成功',
        ];
    }

    public function redoFeishuMaterialSyncTask(Input $input)
    {
        $input->verify(['id']);
        $data = (new MaterialFeiShuSyncModel())->getDataById($input['id']);
        if ($data && $data->status == 2) {
            (new MaterialFeiShuSyncModel())->update($input['id'], ['status' => 1]);
            (new MaterialFeiShuSyncTaskMQModel())->produce(['id' => $input['id']]);
//            (new MaterialStoreLogic())->feiShuAdd(['id' => $input['id']]);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功',
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '操作失败',
            ];
        }
    }
}
