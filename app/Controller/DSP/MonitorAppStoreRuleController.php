<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MonitorAppStoreRuleLogic;
use App\Struct\Input;

class MonitorAppStoreRuleController extends Controller
{
    /**
     * 获取监测列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new MonitorAppStoreRuleLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 管理后台 新增游戏监控规则
     * @CtrlAnnotation(permissions=['/delivery/ios-monitor-rule'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        try {
            (new MonitorAppStoreRuleLogic())->add($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 管理后台 新增游戏监控规则
     * @CtrlAnnotation(permissions=['/delivery/ios-monitor-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify([
            'platform' => [
                'required'
            ],
            'game_id' => [
                'required'
            ],
            'frequency' => [
                'required',
                ['max', 255],
                ['min', 2]
            ],
            'monitor_limit' => [
                'required',
                ['max', 255]
            ]
        ]);
        try {
            (new MonitorAppStoreRuleLogic())->edit($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '编辑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除监控规则
     * @CtrlAnnotation(permissions=['/delivery/ios-monitor-rule'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['platform', 'game_id']);
        try {
            $material_share_rule_logic = new MonitorAppStoreRuleLogic();
            $material_share_rule_logic->delete($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 确认操作
     * @CtrlAnnotation(permissions=['/delivery/ios-monitor-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function confirmOperation(Input $input)
    {
        $input->verify(['game_id', 'platform']);
        try {
            (new MonitorAppStoreRuleLogic())->confirmOperation($input['game_id'], $input['platform']);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '确认操作成功，规则将在下一次检测时执行操作'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }

    }

    /**
     * 终止操作
     * @CtrlAnnotation(permissions=['/delivery/ios-monitor-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function terminateOperate(Input $input)
    {
        $input->verify(['game_id', 'platform']);

        try {
            (new MonitorAppStoreRuleLogic())->terminateOperate($input['game_id'], $input['platform']);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '终止操作成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 产品标准参考列表
     * @CtrlAnnotation(permissions=['/delivery/standard-reference', log_type='get'])
     * @param Input $input
     * @return array
     */
    public function getStandardReferenceList(Input $input)
    {
        $input->verify(['page','rows']);
        $data = (new MonitorAppStoreRuleLogic())->getStandardReferenceList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 产品标准参考新增
     * @CtrlAnnotation(permissions=['/delivery/standard-reference', log_type='add'])
     * @param Input $input
     * @return array
     */
    public function addStandardReference(Input $input)
    {
        $input->verify(['platform', 'type']);
        try {
            (new MonitorAppStoreRuleLogic())->addStandardReference($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 产品标准参考编辑
     * @CtrlAnnotation(permissions=['/delivery/standard-reference', log_type='add'])
     * @param Input $input
     * @return array
     */
    public function editStandardReference(Input $input)
    {
        $input->verify(['platform','type']);
        try {
            (new MonitorAppStoreRuleLogic())->editStandardReference($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 产品标准参考删除
     * @CtrlAnnotation(permissions=['/delivery/standard-reference', log_type='remove'])
     * @param Input $input
     * @return array
     */
    public function removeStandardReference(Input $input)
    {
        $input->verify(['platform','type']);
        try {
            (new MonitorAppStoreRuleLogic())->removeStandardReference($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 产品标准参考修改日志
     * @CtrlAnnotation(permissions=['/delivery/standard-reference', log_type='get'])
     * @param Input $input
     * @return array
     */
    public function getStandardReferenceChangeLog(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new MonitorAppStoreRuleLogic())->getStandardReferenceChangeLog($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}