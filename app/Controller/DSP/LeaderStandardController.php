<?php
/**
 * 标准管理
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Param\LeaderStandardParam;
use App\Param\LeaderStandardSearchParam;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\LeaderStandardLogic;
use Exception;

class LeaderStandardController extends Controller
{
    /**
     * 获取标准列表
     * @CtrlAnnotation(permissions=['/delivery/leader-standard'])
     * @param Input $input
     * @return array
     */
    public function getStandardList(Input $input): array
    {

        $label_logic = new LeaderStandardLogic();
        $list_data = $label_logic->getList(new LeaderStandardSearchParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取标准记录
     * @CtrlAnnotation(permissions=['/delivery/leader-standard'])
     * @param Input $input
     * @return array
     */
    public function getStandardLog(Input $input): array
    {
        $input->verify(['root_game_id', 'agent_group_id', 'type', 'platform', 'agent_leader', 'main_game_id']);
        $input['rows'] = 500;
        $label_logic = new LeaderStandardLogic();
        $list_data = $label_logic->getLog(new LeaderStandardSearchParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 添加一个标准
     * @CtrlAnnotation(permissions=['/delivery/leader-standard'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function addStandard(Input $input): array
    {
        $input->verify(['root_game_name', 'agent_group_name', 'type', 'platform', 'agent_leader', 'budget_cost']);
        if ($input['type'] == 2 && !($input['main_game_id'] ?? '')) {
            throw new AppException('缺少必要参数：main_game_id');
        }
        try {
            $label_logic = new LeaderStandardLogic();
            $label_logic->addStandard(new LeaderStandardParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 修改一个标准
     * @CtrlAnnotation(permissions=['/delivery/label'], log_type='edit')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function updateStandard(Input $input): array
    {
        $input->verify(['root_game_name', 'agent_group_name', 'type', 'platform', 'agent_leader', 'budget_cost']);
        try {
            $label_logic = new LeaderStandardLogic();
            $label_logic->updateStandard(new LeaderStandardParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除一个标准
     * @CtrlAnnotation(permissions=['/delivery/leader-standard'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function deleteStandard(Input $input): array
    {
        $input->verify(['root_game_id', 'agent_group_id', 'main_game_id', 'platform', 'agent_leader']);
        try {
            $label_logic = new LeaderStandardLogic();
            $result = $label_logic->deleteStandard(
                $input['platform'],
                $input['root_game_id'],
                $input['main_game_id'],
                $input['agent_group_id'],
                $input['agent_leader']
            );
            $response = [
                'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
                'message' => $result ? '删除成功' : '删除失败'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}