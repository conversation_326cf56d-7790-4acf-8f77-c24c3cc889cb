<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2022/4/20
 * Time: 14:43
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Container;
use App\Logic\DSP\CustomizedTargetLogic;
use App\Param\DSP\CustomizedTargetParam;
use App\Struct\Input;

class CustomizedTargetController extends Controller
{

    /**
     * 添加自定义指标
     *
     * @return array
     */
    public function addTarget(Input $input)
    {
        $route_id = $this->getRouteId();
        if ($route_id === 0) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => 'route_name参数错误'
            ];
        }

        $input->verify(['name', 'formula', 'format', 'precision']);
        $param = new CustomizedTargetParam($input->getData());
        $param->user_id = Container::getSession()->get('user_id');
        $param->username = Container::getSession()->get('name');
        $param->route_id = $route_id;

        $logic = new CustomizedTargetLogic();
        $data = $logic->addTarget($param);

        return [
            'data' => $data,
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];

    }

    /**
     * 编辑自定义指标
     *
     * @return array
     */
    public function batchUpdateTarget(Input $input)
    {
        $input_data = $input->getData();

        unset($input_data['request_id']);
        // 字段校验
        foreach ($input_data as $data) {
            $input->verify(['id', 'name', 'formula', 'format', 'precision'], false, $data);
        }

        $logic = new CustomizedTargetLogic();
        $return = [];
        // 循环更新
        foreach ($input_data as $data) {
            $param = new CustomizedTargetParam($data);
            $param->user_id = Container::getSession()->get('user_id');
            $param->username = Container::getSession()->get('name');
            $return[] = $logic->updateTarget($param);
        }

        return [
            'data' => $return,
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];

    }


    /**
     * 删除自定义指标
     *
     * @return array
     */
    public function delTarget(Input $input)
    {
        $input->verify(['id']);
        $param = new CustomizedTargetParam($input->getData());
        $param->user_id = Container::getSession()->get('user_id');

        $logic = new CustomizedTargetLogic();
        $logic->deleteTarget($input['id'], Container::getSession()->get('user_id'));

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];
    }

}
