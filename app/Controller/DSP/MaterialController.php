<?php
/**
 * 素材
 * User: zzh
 * Date: 2020/02/21
 * Time: 15:45
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialLogic;
use App\Model\SqlModel\DataMedia\OdsMaterialCombinedTagsLogModel;
use App\Param\Material\MaterialExpertParam;
use App\Param\Material\MaterialExpertSearchParam;
use App\Param\Material\MaterialFileListParam;
use App\Struct\Input;
use App\Utils\UploadTool;

/**
 * 素材控制器
 * Class MaterialController
 * @package App\Controller
 */
class MaterialController extends Controller
{
    /**
     * @param Input $input
     * @return array
     */
    public function getMaterialCombinedTagsOption(Input $input)
    {
        $input->verify(['type', 'platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new OdsMaterialCombinedTagsLogModel())->getOption($input['platform'], $input['type'])
        ];
    }

    /**
     * 获取素材文件列表
     * @param Input $input
     * @return array
     */
    public function getMaterialFileList(Input $input)
    {
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialFileList(new MaterialFileListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    public function getMaterialExpertList(Input $input)
    {
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->getMaterialExpertList(new MaterialExpertSearchParam($input->getData()));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(permissions=['/material/expert'], log_type='edit')
     * @return array
     */
    public function updateMaterialStatus(Input $input)
    {
        $input->verify(['platform', 'name', 'status']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->updateMaterialStatus($input->platform, $input->name, $input->status);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 新增达人信息
     * @param Input $input
     * @CtrlAnnotation(permissions=['/material/expert'], log_type='add')
     * @return array
     */
    public function addMaterialExpert(Input $input)
    {
        $input->verify(['platform']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->addMaterialExpert(new MaterialExpertParam($input->getData()));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '新建成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 修改达人信息
     * @param Input $input
     * @CtrlAnnotation(permissions=['/material/expert'], log_type='edit')
     * @return array
     */
    public function editMaterialExpert(Input $input)
    {
        $input->verify(['id']);
        $material_logic = new MaterialLogic();
        $list_data = $material_logic->editMaterialExpert(new MaterialExpertParam($input->getData()));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 上传达人图片
     * @param Input $input
     * @return array
     */
    public function uploadMaterialExpertImg(Input $input)
    {
        $input->verify(['expert_name', 'expert_type']);
        try {
            $file_info = (new MaterialLogic())->uploadMaterialExpertImg($this->request->files, $input['expert_name'], $input['expert_type']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => $file_info
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function addFrameAITagTask(Input $input): array
    {
        $input->verify(['type', 'ids', 'platform']);
        try {
            (new MaterialLogic())->addFrameAITagTask($input['type'], $input['ids'], $input['platform']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }
        return $response;
    }
}
