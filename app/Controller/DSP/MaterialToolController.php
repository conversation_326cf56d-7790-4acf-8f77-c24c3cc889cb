<?php

namespace App\Controller\DSP;

use App\Constant\MIMEType;
use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\MaterialToolLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\Material\MaterialFileListParam;
use App\Param\Material\StoryboardListParam;
use App\Param\Material\VideoClipComposeParam;
use App\Param\Material\VideoScriptParam;
use App\Service\NoticeService;
use App\Struct\Input;
use App\Utils\UploadTool;


class MaterialToolController extends Controller
{
    protected $pass_method = ["message"];

    /**
     * 发送混剪任务状态
     * @param Input $input
     * @return array
     */
    public function message(Input $input)
    {
        $notice_service = new NoticeService();
        $message_type = $input['message_type'] ?? 'task';
        $notice_type = $message_type === 'task' ? NoticeService::NOTICE_VIDEO_CLIP_TASK_UPDATE : NoticeService::NOTICE_VIDEO_CLIP_COMPOSE_UPDATE;

        foreach ([$input['creator_id'], 326, 261, 27, 152, 315] as $user_id) {
            $notice_service->unicast($user_id, $notice_type, $input->getData());
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发送成功',
        ];
    }

    /**
     * 混剪组合列表
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getVideoClipComposeList(Input $input): array
    {
        $logic = new MaterialToolLogic();
        $data = $logic->getVideoClipComposeList($input['page'] ?? 1, $input['rows'] ?? 100);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 混剪任务列表
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getVideoClipTaskList(Input $input): array
    {
        $input->verify(['compose_id']);

        $logic = new MaterialToolLogic();
        $data = $logic->getVideoClipTaskList($input, $input['page'] ?? 1, $input['rows'] ?? 100);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 文本转语音
     * @CtrlAnnotation(permissions=['/material-tool/text-to-audio'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function textToAudio(Input $input)
    {
        $input->verify(['tts_service', 'voice_type', 'text', 'speed_ratio']);

        $logic = new MaterialToolLogic();
        $data = $logic->textToAudio(
            $input['tts_service'],
            $input['voice_type'],
            $input['style'],
            $input['text'],
            $input['speed_ratio'],
            $input['volume_ratio'],
            $input['pitch_ratio'],
            Container::getUUID(),
            Container::getSession()->user_id
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 声音复刻合成语音
     * @CtrlAnnotation(permissions=['/material-tool/text-to-audio'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function cloneVoiceType(Input $input)
    {
        $input->verify(['tts_service', 'audio', 'text', 'speed_ratio']);

        $logic = new MaterialToolLogic();
        $data = $logic->cloneVoiceType($input['tts_service'], $input['audio'], $input['text'], $input['speed_ratio'], $input['speaker_id'] ?? "");

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 保存音色
     * @CtrlAnnotation(permissions=['/material-tool/text-to-audio'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function saveVoiceType(Input $input)
    {
        $input->verify(['tts_service', 'speaker_id', 'speaker_name', 'gender']);

        $logic = new MaterialToolLogic();
        $logic->saveVoiceType($input['tts_service'], $input['speaker_id'], $input['speaker_name'], $input['gender']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '保存成功',
        ];
    }

    /**
     * 获取音色列表
     * @CtrlAnnotation(permissions=['/material-tool/text-to-audio'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getVoiceTypeList(Input $input)
    {
        $logic = new MaterialToolLogic();
        $data = $logic->getVoiceTypeList();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 创建视频混剪任务
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addClipCompose(Input $input)
    {
        $input->verify(['clique_id', 'clique_name', 'selling_points']);

        $logic = new MaterialToolLogic();
        $param = new VideoClipComposeParam($input->getData());
        $data = $logic->addClipComposeNew($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 重启视频混剪任务
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function restartClipCompose(Input $input)
    {
        $input->verify(['id']);

        $logic = new MaterialToolLogic();
        $logic->restartClipCompose($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '重启成功',
        ];
    }

    /**
     * 上传音频
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadVoiceAudio()
    {
        $file_info = UploadTool::materialAudio($this->request->files, MaterialToolLogic::VIDEO_CLIP_AUDIO_PATH);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => $file_info
        ];
    }

    /**
     * 上传被复刻的音频
     * @CtrlAnnotation(permissions=['/material-tool/text-to-audio'], log_type='add')
     * @return array
     */
    public function uploadCloneAudio()
    {
        $file_info = UploadTool::materialAudio($this->request->files, MaterialToolLogic::TTS_PATH, [
            MIMEType::AUDIO_MPEG, MIMEType::AUDIO_WAV, MIMEType::AUDIO_M4A, MIMEType::APPLICATION_OCTET_STREAM
        ]);

        if ($file_info['file_size'] > 10 * 1024 * 1024) {
            throw new AppException('上传音频已超过大小限制，请缩小到10MB内');
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => $file_info
        ];
    }

    /**
     * 上传封面
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadCoverImage()
    {
        $file_info = UploadTool::materialImage($this->request->files, MaterialToolLogic::VIDEO_CLIP_COVER_PATH);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => $file_info
        ];
    }

    /**
     * 上传封面
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @method POST
     *
     * @return array
     */
    public function uploadBgm()
    {
        $file_info = UploadTool::materialAudio($this->request->files, MaterialToolLogic::VIDEO_CLIP_BGM_PATH);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => $file_info
        ];
    }

    /**
     * 获取分镜列表
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getStoryboardList(Input $input): array
    {
        $logic = new MaterialToolLogic();
        $param = new StoryboardListParam($input->getData());
        $data = $logic->getStoryboardList($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 保存混剪视频到素材库
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function saveVideoToMaterial(Input $input)
    {
        $input->verify(['platform', 'theme_id', 'compose_id', 'ids', 'material_name']);

        $logic = new MaterialToolLogic();
        $logic->saveVideoToMaterial(
            $input['platform'],
            $input['theme_id'],
            $input['compose_id'],
            $input['ids'],
            $input['material_name'],
            $input['start_number'] ?? 0,
            $input['increment'] ?? 0,
            $input['number_length'] ?? 0,
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '保存成功',
        ];
    }

    /**
     * 生成视频脚本-新
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function generateVideoScriptNew(Input $input)
    {
        $input->verify(['clique_id']);

        $logic = new MaterialToolLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->generateVideoScriptInVector($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '保存成功',
            'data' => $data
        ];
    }

    /**
     * 获取集团游戏列表
     * @param Input $input
     * @return array
     */
    public function getCliqueGameList(Input $input): array
    {
        $logic = new MaterialToolLogic();
        $data = $logic->getCliqueGameList();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取集团游戏列表
     * @param Input $input
     * @return array
     */
    public function getRecommendWordsList(Input $input): array
    {
        $input->verify(['clique_id']);

        $logic = new MaterialToolLogic();
        $data = $logic->getLabelListByCliqueId($input['clique_id'], $input['label_type'] ?? "");

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取已分析的素材文件列表
     * @param Input $input
     * @return array
     */
    public function getAnalyseFileList(Input $input): array
    {
        $logic = new MaterialToolLogic();
        $param = new MaterialFileListParam($input->getData());
        $data = $logic->getAnalyseFileList($param, $input["clique_id"] ?? 0, $input['all_file'] ?? true);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 创建视频混剪任务-V2
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addClipComposeV2(Input $input)
    {
        $input->verify(['clique_id', 'clique_name', 'selling_points']);

        $logic = new MaterialToolLogic();
        $param = new VideoClipComposeParam($input->getData());
        $data = $logic->addClipComposeV2($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 混剪工程列表
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getVideoClipProjectList(Input $input): array
    {
        $input->verify(['compose_id']);

        $logic = new MaterialToolLogic();
        $data = $logic->getVideoClipProjectList($input, $input['page'] ?? 1, $input['rows'] ?? 100);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 混剪工程信息
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getVideoClipProjectInfo(Input $input): array
    {
        $input->verify(['project_id']);

        $logic = new MaterialToolLogic();
        $data = $logic->getVideoClipProjectInfo($input['project_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 保存混剪工程
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateVideoClipProject(Input $input)
    {
        $input->verify(['project_id', 'track_list']);

        $logic = new MaterialToolLogic();
        $logic->updateVideoClipProject($input['project_id'], $input['track_list']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '保存成功',
        ];
    }

    /**
     * 检查工程更新状态
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function checkVideoClipProjectState(Input $input)
    {
        $input->verify(['project_id', 'update_time']);

        $logic = new MaterialToolLogic();
        $overwrite = $logic->checkVideoClipProjectState($input['project_id'], $input['update_time']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '检查成功',
            'overwrite' => $overwrite
        ];
    }

    /**
     * 保存混剪视频到素材库
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function saveClipVideoToMaterial(Input $input)
    {
        $input->verify(['create_mode', 'platform', 'theme_id', 'project_ids', 'material_name']);

        $logic = new MaterialToolLogic();
        $data = $logic->saveClipVideoToMaterial(
            $input['create_mode'] ?? 0,
            $input['platform'],
            $input['theme_id'],
            $input['project_ids'],
            $input['material_name'],
            $input['start_number'] ?? 0,
            $input['increment'] ?? 0,
            $input['number_length'] ?? 0,
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '保存成功，请耐心等候',
            'data' => $data,
        ];
    }

    /**
     * 混剪组合信息
     * @CtrlAnnotation(permissions=['/material-tool/video-clip'])
     * @param Input $input
     * @return array
     */
    public function getVideoClipComposeInfo(Input $input): array
    {
        $input->verify(['compose_id']);

        $logic = new MaterialToolLogic();
        $data = $logic->getVideoClipComposeInfo($input['compose_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取文本转语音按钮权限
     * @return array
     */
    public function getTextToAudioButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_MATERIAL_TOOL_TEXT_TO_AUDIO);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}
