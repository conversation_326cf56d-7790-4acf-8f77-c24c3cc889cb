<?php

/**
 * 按游戏扣量配置
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\GameCallBackStrategyLogic;
use App\Model\SqlModel\Tanwan\V2DimCallbackStrategyLogModel;
use App\Param\GameCallbackStrategyParam;
use App\Struct\Input;
use App\Utils\Helpers;
use Common\EnvConfig;

class GameCallbackStrategyController extends Controller
{
    protected $pass_method = ['notify'];

    /**
     * 子平台更新成功回调
     * @param Input $input
     * @return array
     */
    public function notify(Input $input)
    {
        $input->verify(['state']);
        $state = json_decode($input['state'], true);

        Helpers::getLogger('site_strategy')->info("回调参数", [
            'input' => $input->getData()
        ]);

        if (count($state) !== 4) {
            throw new AppException('返回参数错误');
        }

        if (!in_array($state['platform'], ['TW'])) {
            throw new AppException('不支持的平台');
        }

        $model = new V2DimCallbackStrategyLogModel();
        $strategy_info = $model->getData($state['platform'], $state['media_type'], $state['level'], $state['target_id']);
        if (empty($strategy_info)) {
            throw new AppException('该扣量配置不存在');
        }

        $result = $model->updateState($state['platform'], $state['media_type'], $state['level'], $state['target_id'], $model::STATE_UPDATE_SITE_SUCCESS);
        if (!$result) {
            throw new AppException('更新扣量配置状态失败');
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
        ];
    }

    /**
     * 获取列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $data = (new GameCallBackStrategyLogic())->getList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-callback-strategy'], log_type='add')
     * 添加游戏上报策略
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['platform', 'media_type', 'level', 'target_id', 'deduction_type', 'deduction_value', 'cover_strategy', 'cover_site']);

        $param = new GameCallbackStrategyParam($input->getData());
        (new GameCallBackStrategyLogic())->add($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-callback-strategy'], log_type='edit')
     * 编辑游戏上报策略
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify(['platform', 'media_type', 'level', 'target_id', 'deduction_type', 'deduction_value', 'cover_strategy', 'cover_site']);

        $param = new GameCallbackStrategyParam($input->getData());
        (new GameCallBackStrategyLogic())->edit($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '编辑成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-callback-strategy'], log_type='delete')
     * 删除游戏上报策略
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['platform', 'media_type', 'level', 'target_id']);
        (new GameCallBackStrategyLogic())->delete($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-callback-strategy'], log_type='add')
     * 添加配置权限
     * @param Input $input
     * @return array
     */
    public function addStrategyPermission(Input $input)
    {
        $input->verify(['platform', 'root_game_id', 'user_id', 'user_name']);

        (new GameCallBackStrategyLogic())->addStrategyPermission($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 获取列表
     * @param Input $input
     * @return array
     */
    public function getStrategyPermissionList(Input $input)
    {
        $data = (new GameCallBackStrategyLogic())->getStrategyPermissionList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-callback-strategy'], log_type='delete')
     * 删除游戏上报策略
     * @param Input $input
     * @return array
     */
    public function deleteStrategyPermission(Input $input)
    {
        $input->verify(['platform', 'root_game_id', 'user_id']);
        (new GameCallBackStrategyLogic())->deleteStrategyPermission($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }
}
