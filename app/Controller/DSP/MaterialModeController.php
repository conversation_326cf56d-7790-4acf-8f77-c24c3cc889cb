<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialModeLogic;
use App\Param\Material\MaterialModeParam;
use App\Struct\Input;

class MaterialModeController extends Controller
{
    /**
     * 获取素材尺寸列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $param = new MaterialModeParam($input->getData());
        $logic = new MaterialModeLogic();
        $data = $logic->getList($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 添加素材尺寸
     * @CtrlAnnotation(permissions=['/material/mode'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addMode(Input $input)
    {
        $verify_fields = ['name', 'media_type', 'file_type', 'width', 'height', 'formats', 'max_file_size'];
        $input->verify($verify_fields);
        try {
            $mode_logic = new MaterialModeLogic();
            $mode_logic->addMode($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑素材尺寸
     * @CtrlAnnotation(permissions=['/material/mode'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editMode(Input $input)
    {
        $verify_fields = ['name', 'media_type', 'file_type', 'width', 'height', 'formats', 'max_file_size'];
        $input->verify($verify_fields, true);
        try {
            $material_mode_logic = new MaterialModeLogic();
            $material_mode_logic->editMode($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '编辑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }

        return $response;
    }

    /**
     * 删除素材尺寸
     * @CtrlAnnotation(permissions=['/material/mode'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteMode(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_mode_logic = new MaterialModeLogic();
            $material_mode_logic->deleteMode($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取题材及子题材列表
     * @return array
     */
    public function getAll()
    {
        $material_mode_logic = new MaterialModeLogic();
        $list_data = $material_mode_logic->getAll();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}
