<?php

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Logic\DSP\MediaLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\AgentParam;
use App\Param\MajordomoTodoListParam;
use App\Param\MediaAccountListParam;
use App\Param\MediaMajordomoAccountListParam;
use App\Response\CSV;
use App\Response\Response;
use App\Service\MediaService;
use App\Struct\Input;
use App\Container;
use Common\EnvConfig;
use App\Exception\AppException;
use Exception;

class MediaController extends Controller
{

    /**
     * @return array
     */
    public function getPermission()
    {
        $permission_logic = new PermissionLogic();
        $data = $permission_logic->getMediaAccountRoutePermission();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }


    /**
     * @return array
     */
    public function mediaTypeOptions()
    {
        $media_service = new MediaService();
        $data = $media_service->getMediaTypeOptions();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 根据media_type获取媒体已经绑定过的公司主体
     * @param Input $input
     * @return array
     */
    public function getCompanyList(Input $input)
    {
        $input->verify(['media_type']);
        $logic = new MediaLogic();
        $data = $logic->getCompanyList($input['media_type'], $input['platform'] ?? '');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data
            ]
        ];
    }

    /**
     * 获取媒体账号列表
     * @param Input $input
     * @return array
     */
    public function getMediaAccountList(Input $input)
    {
        $input->verify(['media_type']);
        $logic = new MediaLogic();
        $mal = new MediaAccountListParam($input);
        $list_data = $logic->getMediaAccountList($mal);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data
        ];
    }

    /**
     * 获取媒体多账号列表搜索
     * @param Input $input
     * @return array
     */
    public function getMediaAccountMoreQueryList(Input $input)
    {
        $input->verify(['media_type']);
        $logic = new MediaLogic();
        $mal = new MediaAccountListParam($input);
        $list_data = $logic->getMediaAccountMoreQueryList($mal);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data
        ];
    }

    /**
     * 获取媒体账号列表根据账号id
     * @param Input $input
     * @return array
     */
    public function getMediaAccountOptionsByAccountId(Input $input)
    {
        $input->verify(['media_type', 'account_id']);
        $logic = new MediaLogic();
        $list_data = $logic->getMediaAccountOptionsByAccountId(
            (int)$input['media_type'],
            (string)($input['platform'] ?? ''),
            (int)$input['account_id']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list_data]
        ];
    }

    /**
     * 获取媒体账号列表
     * @param Input $input
     * @return array
     */
    public function getMajordomoAccountList(Input $input)
    {
        $input->verify(['media_type']);
        $logic = new MediaLogic();
        $list_data = $logic->getMajordomoAccountList($input['media_type'], $input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list_data]
        ];
    }

    /**
     * @return array
     */
    public function accountOptions()
    {
        $logic = new MediaLogic();
        $data = $logic->getAccountOptions();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * dmp推送账号列表专供接口
     * @param Input $input
     * @return array
     */
    public function getPushaccountList(Input $input)
    {
        $logic = new MediaLogic();
        $data = $logic->getPushaccountList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAuthCompanyList(Input $input)
    {
        $input->verify([
            'media_type' => [
                'integer',
                ['in', [MediaType::TOUTIAO, MediaType::TENCENT, MediaType::KUAISHOU,
                    MediaType::IQIYI, MediaType::WEIBO, MediaType::XINGTU,
                    MediaType::YOUTUBE, MediaType::DOUYIN, MediaType::QIANCHUAN,
                    MediaType::TIKTOK, MediaType::HUAWEI, MediaType::HUAWEI_JINGHONG,
                    MediaType::VIVO, MediaType::HONOR, MediaType::BILIBILI
                ]]
            ],
            'platform' => ['required']
        ]);
        $company_list = (new MediaService())->getAuthCompanyList($input['platform'], $input['media_type']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'list' => $company_list,
            ],
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthUrl(Input $input)
    {
        $input->verify(['media_type', 'platform']);
        $url = (new MediaService())->getAuthURL(
            $input['platform'], $input['media_type'], $input['company'] ?? '',
            Container::getSession()->user_id, $input['auth_version'] ?? '',
            $input['majordomo_account_id'] ?? '', $input['account_type'] ?? '', $input['port_version'] ?? ''
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'url' => $url
            ],
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthLog(Input $input)
    {
        $input->verify(['account' => ['optional'], 'state' => ['optional']]);
        $data = (new MediaLogic())->getAuthLog($input['account'], $input['state']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function batchAuthAccount(Input $input)
    {
        $input->verify(['media_type', 'platform', 'account_content']);
        $url = (new MediaService())->getAuthURL($input['platform'], $input['media_type'], $input['company'], Container::getSession()->user_id);
        (new MediaLogic())->batchAuthAccount($input['platform'], $input['media_type'], $input['company'], $input['account_content'], $url);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function retryAuth(Input $input)
    {
        $input->verify(['id' => ['integer']]);
        $data = (new MediaLogic())->retryAuth($input['id']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getUserCreateList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new MediaLogic();
        $mal = new MediaAccountListParam($input);
        $data = $logic->getUserCreateList($mal);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 导出媒体账号列表
     * @param Input $input
     * @return CSV
     */
    public function exportUserCreateList(Input $input)
    {
        $logic = new MediaLogic();
        $mal = new MediaAccountListParam($input);
        $mal->page = 1;
        $mal->rows = 99999;
        $data = $logic->getUserCreateList($mal);
        $content = '平台,媒体,账户id,账户名,管家id,公司,负责人,授权者,授权时间' . PHP_EOL;
        foreach ($data['list'] as $item) {
            $platform = EnvConfig::PLATFORM_MAP[$item->platform];
            $media_type = MediaType::MEDIA_TYPE_MAP[$item->media_type];
            $create_time = date('Y-m-d H:i:s');
            $content .= "{$platform},{$media_type},=\"{$item->account_id}\",=\"{$item->account_name}\",=\"{$item->toutiao_majordomo_id}\",{$item->company},{$item->agent_leader},{$item->creator},=\"{$create_time}\"" . PHP_EOL;
        }
        // 源文件名称
        $str = mb_convert_encoding($content, 'GB18030', 'utf-8');
        // 下载的文件名
        $download_filename = 'media_account.csv';
        $source_filename = md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.csv';
        $csv = Response::CSV($download_filename, $source_filename);
        $csv->setPath($csv->path . '/media_account/');
        file_put_contents($csv->path . $source_filename, $str, FILE_APPEND);
        return $csv;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'])
     * @param Input $input
     * @return array
     */
    public function getLabel(Input $input)
    {
        $input->verify(['platform']);

        $logic = new MediaLogic();
        $data = $logic->getLabel($input['platform']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addLabel(Input $input)
    {
        $input->verify(['id', 'labels'], true);

        $logic = new MediaLogic();
        try {
            $logic->addLabel($input['id'], $input['labels']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ];
        }
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAccount(Input $input)
    {
        $input->verify(['platform', 'media_type']);
        $logic = new MediaLogic();
        $message = $logic->addAccount($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => $message,
        ];
    }

    /**
     * 获取标签绑定的用户
     *
     * @return array
     */
    public function tagAccounts()
    {
        $logic = new MediaLogic();
        $data = $logic->getAllAccountTag();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 补充管家账号
     *
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function fillMajordomoName(Input $input)
    {
        $input->verify(['media_type', 'majordomo_id', 'majordomo_name']);

        $logic = new MediaLogic();
        $logic->fillMajordomoName($input['media_type'], $input['majordomo_id'], $input['majordomo_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 切换负责人
     *
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function switchAgentLeader(Input $input)
    {
        $input->verify(['ids', 'agent_leader']);

        $logic = new MediaLogic();
        $logic->switchAgentLeader($input['ids'], $input['agent_leader']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 创建渠道
     *
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function createAgent(Input $input)
    {
        $input->verify(['platform', 'media_type', 'account_id', 'agent_group_id', 'agent_group_name']);
        $input['agent_group'] = $input['agent_group_id'];
        $param = new AgentParam($input->getData());
        $logic = new MediaLogic();
        $logic->createAgent($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/majordomo-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editMajordomoAccount(Input $input)
    {
        $input->verify(['id', 'media_type', 'account_id', 'account', 'password']);
        $logic = new MediaLogic();
        $logic->editMajordomoAccount($input['id'], $input['media_type'], $input['account_id'], $input['account'], $input['password']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/majordomo-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function disableMajordomoAccountList(Input $input)
    {
        $input->verify(['ids']);
        $logic = new MediaLogic();
        $logic->disableMajordomoAccountList($input['ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }


    /**
     * 管家待办事项列表
     * @CtrlAnnotation(permissions=['/advertising/majordomo-todo'])
     * @param Input $input
     * @return array
     */
    public function getMajordomoTodoList(Input $input)
    {
        $logic = new MediaLogic();
        $list_data = $logic->getMajordomoTodoList(new MajordomoTodoListParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list_data]
        ];
    }

    /**
     * 管家待办事项增加
     * @CtrlAnnotation(permissions=['/advertising/majordomo-todo'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addMajordomoTodoList(Input $input)
    {
        $input->verify(['media_type', 'platform', 'account', 'password', 'type']);
        $logic = new MediaLogic();
        $logic->addMajordomoTodoList($input['media_type'], $input['platform'], $input['account'], $input['password'], $input['type']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 管家待办事项删除
     * @CtrlAnnotation(permissions=['/advertising/majordomo-todo'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteMajordomoTodoList(Input $input)
    {
        $input->verify(['id']);
        $logic = new MediaLogic();
        $logic->deleteMajordomoTodoList($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 修改账号自动转账每日限额
     * @param Input $input
     * @return array
     */
    public function editAccountDailyTransferLimit(Input $input)
    {
        $input->verify(['edit_field', 'account_ids', 'limit_value']);
        $logic = new MediaLogic();
        try {
            if (!in_array($input['edit_field'], [MediaAccountModel::DAILY_TRANSFER_IN_LIMIT_FIELD]) ||
                !is_numeric($input['limit_value'])) {
                throw new AppException('非法请求');
            }
            $logic->editAccountDailyTransferLimit($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 切换管家账户授权者
     * @CtrlAnnotation(permissions=['/advertising/majordomo-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function switchMajordomoAccountCreator(Input $input)
    {
        $input->verify(['ids', 'creator_id']);
        $logic = new MediaLogic();
        $logic->switchMajordomoAccountCreator($input['ids'], $input['creator_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
        ];
    }

    /**
     * 更新子账号信息
     * @CtrlAnnotation(permissions=['/advertising/majordomo-account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateSubAccountInfo(Input $input)
    {
        $input->verify(['majordomo_ids']);
        $logic = new MediaLogic();
        $data = $logic->updateAccountInfoByMajordomo($input['majordomo_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }

    /**
     * 获取所有管家媒体账户
     * @param Input $input
     * @return array
     */
    public function mediaMajordomoAccountOptions(Input $input)
    {
        $param = new MediaMajordomoAccountListParam($input);
        $list = (new MediaLogic())->getMediaMajordomoAccountOptions($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['list' => $list]
        ];
    }

    /**
     * 批量创建渠道
     *
     * @CtrlAnnotation(permissions=['/advertising/media-account'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function createAgentMultiple(Input $input)
    {
        $input->verify(['platform', 'media_type', 'account_ids', 'agent_group_id', 'agent_group_name', 'agent_name_prefix']);
        $input['agent_group'] = $input['agent_group_id'];

        if (empty($input['account_ids'])) {
            throw new AppException("请选择账号");
        }

        $msg = '';
        foreach ($input['account_ids'] as $account_id) {
            try {
                $agent_data = $input->getData();
                $agent_data['account_id'] = $account_id;
                $agent_data['agent_name'] = $input['agent_name_prefix'] . '-' . $account_id;
                $agent_data['user_name'] = $input['user_name_prefix'] . '-' . $account_id;
                $param = new AgentParam($agent_data);
                $logic = new MediaLogic();
                $logic->createAgent($param);
                $msg .= "$account_id 创建成功;";
            } catch (Exception $e) {
                $msg .= "$account_id 创建失败：{$e->getMessage()};";
                continue;
            }
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => $msg
        ];
    }
}
