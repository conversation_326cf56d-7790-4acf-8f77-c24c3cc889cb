<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\AwemeAccountInterfaceLogic;
use App\Param\AwemeAccountInterfaceParam;
use App\Struct\Input;

class AwemeAccountInterfaceController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new AwemeAccountInterfaceLogic();
        $param = new AwemeAccountInterfaceParam($input->getData());

        $data = $logic->getList($param, $input['keyword'] ?? '');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/aweme-account-interface'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['platform', 'media_type', 'type', 'aweme_account', 'aweme_name', 'start_time', 'end_time']);
        $logic = new AwemeAccountInterfaceLogic();
        $param = new AwemeAccountInterfaceParam($input->getData());

        $logic->add($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/aweme-account-interface'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify(['platform', 'media_type', 'type', 'aweme_account', 'aweme_name', 'interface_person', 'start_time']);
        $logic = new AwemeAccountInterfaceLogic();
        $param = new AwemeAccountInterfaceParam($input->getData());

        $ret = $logic->edit($param);

        if ($ret) {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败'
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/aweme-account-interface'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['platform', 'media_type', 'type', 'aweme_account', 'start_time']);
        $logic = new AwemeAccountInterfaceLogic();
        $param = new AwemeAccountInterfaceParam($input->getData());

        $logic->delete($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }
}
