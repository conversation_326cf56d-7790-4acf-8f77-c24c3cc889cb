<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MonitorADCalcRuleLogic;
use App\Param\MonitorADCalcRuleParam;
use App\Struct\Input;

class MonitorADCalcRuleController extends Controller
{
    /**
     * 获取规则列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new MonitorADCalcRuleLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 管理后台 新增数值条件监控规则
     * @CtrlAnnotation(log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        try {
            (new MonitorADCalcRuleLogic())->add((new MonitorADCalcRuleParam($input)));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 管理后台 新增数值条件监控规则
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        try {
            (new MonitorADCalcRuleLogic())->edit($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除监控规则
     * @CtrlAnnotation(log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_share_rule_logic = new MonitorADCalcRuleLogic();
            $material_share_rule_logic->delete($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取监控列表
     * @param Input $input
     * @return array
     */
    public function getCalcBindList(Input $input)
    {
        $input->verify(['calc_rule_id', 'rule_type']);
        $data = (new MonitorADCalcRuleLogic())->getCalcBindList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据规则ID解绑一个规则下所有广告
     * @param Input $input
     * @return array
     */
    public function unbindCalcRuleByRuleId(Input $input)
    {
        $input->verify(['calc_rule_ids']);
        (new MonitorADCalcRuleLogic())->unbindCalcRuleByRuleId($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '解绑成功',
        ];
    }

    /**
     * 获取不同媒体的账号状态
     * @return array
     */
    public function getDiffMediaAccountStatus()
    {
        $data = (new MonitorADCalcRuleLogic())->getDiffMediaAccountStatus();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据规则ID获取规则详情
     * @param Input $input
     * @return array
     */
    public function getRuleDetailByRuleId(Input $input)
    {
        $input->verify(['rule_id']);
        $data = $input->getData();
        $data = (new MonitorADCalcRuleLogic())->getRuleDetailByRuleId($data['rule_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}