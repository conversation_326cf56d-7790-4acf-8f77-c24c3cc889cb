<?php

/**
 * 渠道控制器
 * User: hejs
 * Date: 2019/12/05
 */

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Container;
use App\Logic\DSP\AgentLogic;
use App\Model\SqlModel\Agency\UserModel;
use App\Param\AgentGroupParam;
use App\Response\Response;
use App\Service\AgentService;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Param\AgentListParam;
use App\Param\AgentParam;
use App\Utils\UploadTool;
use Common\EnvConfig;

class AgentController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);

        $param = new AgentListParam($input->getData());
        $logic = new AgentLogic();
        $data = $logic->getList($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function export(Input $input)
    {
        $param = new AgentListParam($input->getData());
        $param->page = 1;
        $param->rows = 99999;
        $logic = new AgentLogic();
        $data = $logic->getList($param);

        // 下载的文件名
        $download_filename = 'agent.csv';
        // 源文件名称
        $source_filename = md5(microtime(true) . 'user_id:' . Container::getSession()->get('user_id')) . '.csv';
        $csv = Response::CSV($download_filename, $source_filename);
        $content = '账户id,渠道id,渠道名,媒体,主体,负责人,注册时间' . PHP_EOL;
        foreach ($data['list'] as $item) {
            $media_type = MediaType::MEDIA_TYPE_MAP[$item->media_type];
            $create_time = date("Y-m-d H:i:s", $item->create_time);
            $content .= "=\"{$item->account_id}\",{$item->agent_id},{$item->agent_name},{$media_type},{$item->company},{$item->agent_leader},{$create_time}" . PHP_EOL;
        }
        $str = mb_convert_encoding($content, 'GB18030', 'utf-8');
        file_put_contents($csv->path . '/' . $source_filename, $str, FILE_APPEND);
        return $csv;
    }

    public function getAgentGroupList(Input $input)
    {
        $input->verify(['page', 'rows']);

        $logic = new AgentLogic();
        $data = $logic->getAgentGroupList($input['page'], $input['rows'], $input['media_type']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function getAgentGroupUserList(Input $input)
    {
        $input->verify(['page', 'rows']);

        $logic = new AgentLogic();
        $data = $logic->getAgentGroupUserList($input['page'], $input['rows'], $input['agent_group_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function getAgentLeaderChangeLog(Input $input)
    {
        $input->verify(['platform', 'agent_id']);

        $logic = new AgentLogic();
        $data = $logic->getAgentLeaderChangeLog($input['platform'], $input['agent_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent'], log_type='add')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function addAgent(Input $input)
    {
        $input->verify(['media_type', 'platform', 'user_name', 'user_pwd', 'agent_group', 'agent_leader', 'account_type',
            'pay_type']);
        $param = new AgentParam($input->getData());
        $logic = new AgentLogic();

        $logic->addAgent($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];

    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent'], log_type='edit')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editAgent(Input $input)
    {
        $input->verify(['platform', 'agent_id', 'media_type', 'agent_leader', 'account_type']);
        $param = new AgentParam($input->getData());
        $logic = new AgentLogic();
        // 前端有update_time 要置0
        $param->update_time = 0;
        $logic->editAgent($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    public function existUser(Input $input)
    {
        $input->verify(['user_name']);
        // 判断账号是否存在
        $user_info = (new UserModel())->getDataByAccount($input['user_name']);
        if ($user_info) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '账号已存在',
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '账号可用',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent'], log_type='edit')
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editAgentField(Input $input)
    {
        $input->verify(['platform', 'agent_id', 'field', 'value']);
        $logic = new AgentLogic();
        $logic->editAgentField($input['platform'], $input['agent_id'], $input['field'], $input['value']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }


    /**
     * @CtrlAnnotation(permissions=['/advertising/agent-group'], log_type='add')
     * @param Input $input
     *
     * @return array
     */
    public function addAgentGroup(Input $input)
    {
        $input->verify(['media_type', 'platform_list', 'name']);
        $logic = new AgentLogic();
        $logic->addAgentGroup($input['media_type'], $input['platform_list'], $input['name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];

    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent-group'], log_type='add')
     * @param Input $input
     *
     * @return array
     */
    public function bindAgentGroup(Input $input)
    {
        $input->verify(['type', 'user_list', 'agent_group_list']);
        $logic = new AgentLogic();
        $logic->bindAgentGroup($input['type'], $input['user_list'], $input['agent_group_list']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
        ];

    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent-group'], log_type='edit')
     * @param Input $input
     *
     * @return array
     */
    public function editAgentGroupPlatformList(Input $input)
    {
        $input->verify(['id', 'platform_list']);

        $param = new AgentGroupParam($input->getData());
        $logic = new AgentLogic();
        $logic->editAgentGroupPlatformList($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功'
        ];
    }

    /**
     * @CtrlAnnotation(log_type='edit')
     * @param Input $input
     *
     * @return array
     */
    public function editBatchAgentLeader(Input $input)
    {
        $input->verify(['platform', 'agent_ids', 'agent_leader']);

        $logic = new AgentLogic();
        $logic->batchModifyLeader($input['platform'], $input['agent_ids'], $input['agent_leader']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];

    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/agent'], log_type='other')
     * @return array
     */
    public function uploadIdCard()
    {
        $file_path = UploadTool::image($this->request->files, EnvConfig::IDCARD_DIR_NAME);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => [
                'file_path' => $file_path
            ]
        ];

        return $response;
    }

    public function getOptions(Input $input)
    {
        $logic = new AgentLogic();
        $list = $logic->getOptions($input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list
        ];
    }

    /**
     * @return array
     */
    public function agentTypeOptions()
    {
        $media_service = new AgentService();
        $data = $media_service->getAgentTypeOptions();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * @return array
     */
    public function agentGroupOptions()
    {
        $media_service = new AgentService();
        $data = $media_service->getAgentGroupOptions();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }
}
