<?php
/**
 * 题材管理
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialThemeLogic;
use App\Param\Material\MaterialThemeListParam;
use App\Param\Material\MaterialThemeParam;

class MaterialThemeController extends Controller
{

    /**
     * 获取题材列表
     * @CtrlAnnotation(permissions=['/material/theme'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {

        $material_theme_logic = new MaterialThemeLogic();
        $list_data = $material_theme_logic->getList(new MaterialThemeListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加一个题材
     * @CtrlAnnotation(permissions=['/material/theme'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function addTheme(Input $input)
    {
        $input->verify(['name', 'platform']);
        try {
            $material_theme_logic = new MaterialThemeLogic();
            $material_theme_logic->addTheme(new MaterialThemeParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新一个题材
     * @CtrlAnnotation(permissions=['/material/theme'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateTheme(Input $input)
    {
        $input->verify(['name']);
        try {
            $material_theme_logic = new MaterialThemeLogic();
            $material_theme_logic->updateTheme($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除一个题材
     * @CtrlAnnotation(permissions=['/material/theme'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function deleteTheme(Input $input)
    {
        $input->verify(['theme_id']);
        try {
            $material_theme_logic = new MaterialThemeLogic();
            $material_theme_logic->deleteTheme($input['theme_id'], $input['platform']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取题材及子题材列表
     * @return array
     */
    public function getAll()
    {
        $material_theme_logic = new MaterialThemeLogic();
        $list_data = $material_theme_logic->getAll();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}