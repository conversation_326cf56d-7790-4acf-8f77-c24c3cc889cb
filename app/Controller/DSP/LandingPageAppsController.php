<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\LandingPageAppsLogic;
use App\Struct\Input;

class LandingPageAppsController extends Controller
{
    /**
     * 获取列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new LandingPageAppsLogic();
        $data = $logic->getList(
            $input['platform'] ?? '',
            $input['media_page_id'] ?? '',
            $input['page'],
            $input['rows']
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 添加
     * @CtrlAnnotation(permissions=['/advertising/landing-page-apps'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addLandingPageApps(Input $input)
    {
        $input->verify(['platform', 'page_id', 'page_name', 'site_ids', 'media_page_id']);
        $logic = new LandingPageAppsLogic();
        $logic->addLandingPageApps(
            $input['platform'],
            $input['page_id'],
            $input['page_name'],
            $input['site_ids'],
            $input['media_page_id']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 修改
     * @CtrlAnnotation(permissions=['/advertising/landing-page-apps'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editLandingPageApps(Input $input)
    {
        $input->verify(['platform', 'page_id', 'page_name', 'site_ids', 'media_page_id']);
        $logic = new LandingPageAppsLogic();
        $logic->editLandingPageApps(
            $input['platform'],
            $input['page_id'],
            $input['page_name'],
            $input['site_ids'],
            $input['media_page_id']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功'
        ];
    }

    /**
     * 删除
     * @CtrlAnnotation(permissions=['/advertising/landing-page-apps'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteLandingPageApps(Input $input)
    {
        $input->verify(['platform', 'page_id']);
        $logic = new LandingPageAppsLogic();
        $logic->deleteLandingPageApps(
            $input['platform'],
            $input['page_id']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 获取腾讯落地页列表
     * @param Input $input
     * @return array
     */
    public function getTencentLandingPageList(Input $input)
    {
        $input->verify(['platform']);
        $logic = new LandingPageAppsLogic();
        $data = $logic->getPageListByPlatform(
            $input['platform'],
            $input['keywords'] ?? '',
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }
}
