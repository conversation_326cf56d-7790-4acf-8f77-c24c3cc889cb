<?php
/**
 * 标签管理
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\LabelLogic;
use App\Param\Material\LabelListParam;
use App\Param\Material\LabelParam;

class LabelController extends Controller
{

    /**
     * 获取标签列表
     * @CtrlAnnotation(permissions=['/material/label'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {

        $label_logic = new LabelLogic();
        $list_data = $label_logic->getList(new LabelListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加一个标签
     * @CtrlAnnotation(permissions=['/material/label'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function addLabel(Input $input)
    {
        $input->verify(['name', 'theme_ids', 'pid']);
        try {
            $label_logic = new LabelLogic();
            $label_logic->addLabel(new LabelParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新一个标签
     * @CtrlAnnotation(permissions=['/material/label'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateLabel(Input $input)
    {
        $input->verify(['name', 'theme_ids', 'pid']);
        try {
            $label_logic = new LabelLogic();
            $label_logic->updateLabel($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除一个标签
     * @CtrlAnnotation(permissions=['/material/label'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function deleteLabel(Input $input)
    {
        $input->verify(['id', 'platform']);
        try {
            $label_logic = new LabelLogic();
            $label_logic->deleteLabel($input['platform'],$input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取标签及子标签列表
     * @param Input $input
     * @return array
     */
    public function getAll(Input $input)
    {
        $label_logic = new LabelLogic();
        $list_data = $label_logic->getAll($input['platform'], $input['theme_id']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}