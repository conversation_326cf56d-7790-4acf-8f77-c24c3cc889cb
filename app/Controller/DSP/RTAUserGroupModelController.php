<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\RTAUserGroupModelLogic;
use App\Struct\Input;

class RTAUserGroupModelController extends Controller
{
    /**
     * 获取分群模型列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new RTAUserGroupModelLogic())->getList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 新增用户分群模型
     * @param Input $input
     * @return array
     */
    public function addUserGroupModel(Input $input)
    {
        try {
            (new RTAUserGroupModelLogic())->addUserGroupModel($input);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '用户分群模型创建成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '创建失败 ' . $e->getMessage()
            ];
        }
    }

    /**
     * 修改用户分群模型
     * @param Input $input
     * @return array
     */
    public function editUserGroupModel(Input $input)
    {
        try {
            (new RTAUserGroupModelLogic())->editUserGroupModel($input);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '用户分群模型修改成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败 ' . $e->getMessage()
            ];
        }
    }
}
