<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\LiveUserInterfacePersonLogic;
use App\Param\LiveUserInterfacePersonParam;
use App\Response\CSV;
use App\Struct\Input;
use App\Utils\UploadTool;

class LiveUserInterfacePersonController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new LiveUserInterfacePersonLogic();
        $param = new LiveUserInterfacePersonParam($input->getData());

        $data = $logic->getList($param, $input['keyword'] ?? '');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user-interface'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['platform', 'game_id', 'media_type', 'live_user', 'live_user_name', 'interface_start_time', 'interface_end_time']);
        $logic = new LiveUserInterfacePersonLogic();
        $param = new LiveUserInterfacePersonParam($input->getData());

        $logic->add($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user-interface'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify(['platform', 'game_id', 'media_type', 'live_user', 'live_user_name', 'interface_person', 'interface_start_time']);
        $logic = new LiveUserInterfacePersonLogic();
        $param = new LiveUserInterfacePersonParam($input->getData());

        $ret = $logic->edit($param);

        if ($ret) {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败'
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user-interface'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['platform', 'game_id', 'media_type', 'live_user', 'interface_start_time']);
        $logic = new LiveUserInterfacePersonLogic();
        $param = new LiveUserInterfacePersonParam($input->getData());

        $logic->delete($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 导入广告位视频对接人和媒介对接人
     * @CtrlAnnotation(permissions=['/advertising/live-user-interface'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importSiteInterfaceAndIntermediaryPerson(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new LiveUserInterfacePersonLogic())->importSiteInterfaceAndIntermediaryPerson($data_list, $this->request->files['file']['tmp_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user-interface'], log_type='export')
     * @return CSV
     */
    public function siteInterfaceTemplate()
    {
        return new CSV('广告位导入媒介编导sample.xlsx', 'sample/site_interface_template.xlsx');
    }
}
