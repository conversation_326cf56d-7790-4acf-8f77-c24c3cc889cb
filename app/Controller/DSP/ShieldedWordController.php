<?php

/**
 * 屏蔽词管理
 * https://djy36bihoz.feishu.cn/docs/doccnvtuT16HQXV9om2MjFqb7we#oN7j0c
 */


namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\ShieldedLogic;
use App\Struct\Input;


class ShieldedWordController extends Controller
{

    /**
     * 屏蔽关键词列表
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-word'], log_type='get')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function list(Input $input)
    {
        $input->verify(['page', 'rows']);

        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 50;
        $logic = new ShieldedLogic();
        $data = $logic->getWordList($page, $rows);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 添加屏蔽关键词
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-word'], log_type='add')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function add(Input $input)
    {
        $input->verify(['create_type', 'terms']);

        $create_type = $input['create_type'];
        $str_terms = $input['terms'];

        $logic = new ShieldedLogic();
        $logic->addWord($create_type, $str_terms);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 修改屏蔽关键词
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-word'], log_type='edit')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function edit(Input $input)
    {
        $input->verify(['create_type', 'old_term', 'new_term']);

        $create_type = $input['create_type'];
        $old_term = $input['old_term'];
        $new_term = $input['new_term'];

        $logic = new ShieldedLogic();
        $logic->editWord($create_type, $old_term, $new_term);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功'
        ];
    }

    /**
     * 删除屏蔽关键词
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-word'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function delete(Input $input)
    {
        $input->verify(['term']);

        $term = $input['term'];

        $logic = new ShieldedLogic();
        $logic->deleteWord($term);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 获取屏蔽词权限
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-word'])
     * @return array
     * @throws AppException
     */
    public function permission()
    {
        $logic = new PermissionLogic();
        $route_id = RouteID::DSP_SHIELDED_WORD;

        $data = $logic->getShieldedRoutePermission($route_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }
}
