<?php
/**
 * 广告变现
 * Created by PhpStorm.
 * User: hsiao
 * Date: 2023/3/23
 * Time: 11:55
 * Desc:
 */

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\ADMonetizationLogic;
use App\Param\ADMonetization\ADMonetizationGroupListParam;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanListParam;
use App\Struct\Input;
use Exception;

class ADMonetizationController extends Controller
{
    /**
     * 广告变现组列表
     * @param  Input  $input
     * @return array
     */
    public function getGroupList(Input $input)
    {
        $input->verify(['page', 'rows']);

        $param = new ADMonetizationGroupListParam($input->getData());
        $logic = new ADMonetizationLogic();
        $data = $logic->getGroupList($param, $input['keyword'] ?? '');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 添加广告变现组
     * @param  Input  $input
     * @return array
     * @throws Exception
     */
    public function addADGroup(Input $input)
    {
        $input->verify(['setting_name', 'platform', 'game_id', 'proportion', 'state']);

        $param = new ADMonetizationGroupParam($input->getData());
        $logic = new ADMonetizationLogic();
        $logic->addADGroup($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * 编辑广告变现组
     * @param  Input  $input
     * @return array
     * @throws Exception
     */
    public function editADGroup(Input $input)
    {
        $input->verify(['platform', 'setting_id']);

        $param = new ADMonetizationGroupParam($input->getData());
        $logic = new ADMonetizationLogic();
        $logic->editADGroup($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
        ];
    }

    /**
     * 广告变现计划列表
     * @param  Input  $input
     * @return array
     */
    public function getPlanList(Input $input)
    {
        $setting_keyword = $input['setting_keyword'] ?? '';
        $plan_keyword = $input['plan_keyword'] ?? '';
        $logic = new ADMonetizationLogic();
        $list_data = $logic->getPlanList(new ADMonetizationPlanListParam($input), $setting_keyword, $plan_keyword);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加广告变现计划
     * @param  Input  $input
     * @return array
     * @throws Exception
     */
    public function addADPlan(Input $input)
    {
        $input->verify([
            'platform', 'setting_id', 'plan_name', 'click_url', 'monitor_url', 'card_icon', 'card_title',
            'card_describe', 'button_name', 'video', 'show_time', 'state', 'type', 'package_name',
            'monitor_game_info', 'show_url'
        ]);

        try {
            $ad_logic = new ADMonetizationLogic();
            $ad_logic->addADPlan($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑广告变现计划
     * @param  Input  $input
     * @return array
     * @throws Exception
     */
    public function editADPlan(Input $input)
    {
        $input->verify(['platform', 'plan_id']);
        try {
            $ad_logic = new ADMonetizationLogic();
            $ad_logic->editADPlan($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 批量上架下架
     * @param  Input  $input
     * @return array
     * @throws Exception
     */
    public function batchUPADState(Input $input)
    {
        $input->verify(['platform','plan_id','state']);
        try {
            $ad_logic = new ADMonetizationLogic();
            $ad_logic->upADState($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


}
