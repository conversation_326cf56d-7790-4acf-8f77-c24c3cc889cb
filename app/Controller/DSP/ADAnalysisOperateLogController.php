<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\ADAnalysisOperateLogLogic;
use App\Struct\Input;

class ADAnalysisOperateLogController extends Controller
{
    /**
     * 获取操作记录列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new ADAnalysisOperateLogLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}