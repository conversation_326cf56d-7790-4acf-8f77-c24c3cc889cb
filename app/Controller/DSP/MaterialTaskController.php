<?php
/**
 * 素材上传任务
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Param\Material\MaterialPushMediaTaskListParam;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialTaskLogic;
use App\Param\Material\MaterialUploadMediaTaskListParam;

class MaterialTaskController extends Controller
{
    /**
     * 获取上传任务列表
     * @CtrlAnnotation(permissions=['/material/task'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $material_task_logic = new MaterialTaskLogic();
        $list_data = $material_task_logic->getList(new MaterialUploadMediaTaskListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加上传任务
     * @CtrlAnnotation(permissions=['/material/task', '/material/store'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function add(Input $input)
    {
        $input->verify(['media_account_id', 'platform', 'media_type', 'file_ids']);
        try {
            $material_task_logic = new MaterialTaskLogic();
            $material_task_logic->add($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 重新上传
     * @CtrlAnnotation(permissions=['/material/task'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function againUpload(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_task_logic = new MaterialTaskLogic();
            $material_task_logic->againUpload($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '已经重新上传'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 重新上传
     * @CtrlAnnotation(permissions=['/material/task'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function multipleReUpload(Input $input)
    {
        $input->verify(['ids']);
        try {
            $material_task_logic = new MaterialTaskLogic();
            $material_task_logic->multipleReUpload($input['ids']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '已经重新上传'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 添加授权推送素材任务
     * @CtrlAnnotation(permissions=['/material/task'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addPushTask(Input $input)
    {
        $input->verify(['upload_file_log_id', 'object_account_id_list']);
        try {
            $material_task_logic = new MaterialTaskLogic();
            $material_task_logic->addPushTask(
                $input['upload_file_log_id'],
                $input['object_account_id_list']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取授权推送素材任务
     * @param Input $input
     * @return array
     */
    public function getPushTaskList(Input $input)
    {
        try {
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
                'data' => (new MaterialTaskLogic())->getPushTaskList(new MaterialPushMediaTaskListParam($input))
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}