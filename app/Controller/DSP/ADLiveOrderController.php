<?php
namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\ADLiveOrderLogic;
use App\Param\ADServing\ADLiveOrderComposeParam;
use App\Struct\Input;

class ADLiveOrderController extends Controller
{

    /**
     * 获取内部订单组合
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getComposeList(Input $input)
    {
        $data = (new ADLiveOrderLogic())->getComposeList($input->getData(),$input['page'] ?: 1,$input['rows'] ?: 20);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '获取数据成功',
        ];
    }

    /**
     * 保存内部订单组合
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function operateCompose(Input $input)
    {
        $input->verify(['name','live_media_type']);
        $result = (new ADLiveOrderLogic())->operateCompose(new ADLiveOrderComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result,
            'message' => '操作成功',
        ];
    }

    /**
     * 保存内部订单组合并创建订单
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function operateComposeAndPublish(Input $input)
    {
        $input->verify(['name','live_media_type']);
        $result = (new ADLiveOrderLogic())->operateComposeAndPublish(new ADLiveOrderComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result,
            'message' => '操作成功',
        ];
    }

    /**
     * 删除
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function remove(Input $input)
    {
        $input->verify(['id']);
        (new ADLiveOrderLogic())->remove($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

}