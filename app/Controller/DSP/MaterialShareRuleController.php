<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialShareRuleLogic;
use App\Param\Material\MaterialShareRuleSearchParam;
use App\Struct\Input;

class MaterialShareRuleController extends Controller
{
    /**
     * 获取共享规则列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $param = new MaterialShareRuleSearchParam($input->getData());
        $logic = new MaterialShareRuleLogic();
        $data = $logic->getList($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 添加共享规则
     * @CtrlAnnotation(permissions=['/material/share-rule'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addShareRule(Input $input)
    {
        $input->verify(['name', 'rule_type', 'share_type', 'author', 'm_group', 'material_create_time']);
        try {
            $share_rule_logic = new MaterialShareRuleLogic();
            $share_rule_logic->addShareRule($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑共享规则
     * @CtrlAnnotation(permissions=['/material/share-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editShareRule(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_share_rule_logic = new MaterialShareRuleLogic();
            $material_share_rule_logic->editShareRule($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '编辑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除共享规则
     * @CtrlAnnotation(permissions=['/material/share-rule'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteShareRule(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_share_rule_logic = new MaterialShareRuleLogic();
            $material_share_rule_logic->deleteShareRule($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

}
