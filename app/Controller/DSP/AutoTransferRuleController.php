<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\AutoTransferRuleLogic;
use App\Param\AutoTransferRuleParam;
use App\Struct\Input;

class AutoTransferRuleController extends Controller
{
    /**
     * 获取自动转账规则列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $data = (new AutoTransferRuleLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 管理后台 新增自动转账监控规则
     * @CtrlAnnotation(permissions=['/delivery/auto-transfer-rule'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        try {
            $input->verify(['name', 'media_type', 'transfer_type', 'execution_account', 'balance_condition', 'calc_condition',
                'frequency', 'start_time', 'end_time'], true);
            (new AutoTransferRuleLogic())->add(new AutoTransferRuleParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 管理后台 新增自动转账监控规则
     * @CtrlAnnotation(permissions=['/delivery/auto-transfer-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        try {
            $input->verify(['id']);
            (new AutoTransferRuleLogic())->edit($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除监控规则
     * @CtrlAnnotation(permissions=['/delivery/auto-transfer-rule'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['id']);
        try {
            $auto_transfer_rule_logic = new AutoTransferRuleLogic();
            $auto_transfer_rule_logic->delete($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '禁用成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 根据可转帐key获取账号列表
     * @CtrlAnnotation(permissions=['/delivery/auto-transfer-rule'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTransferableAccountList(Input $input)
    {
        $input->verify(['transferable_key', 'account_ids', 'media_type'], true);
        $data = (new AutoTransferRuleLogic())->getTransferableAccountList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据规则ID获取规则详情
     * @param Input $input
     * @return array
     */
    public function getAutoTransferRuleDetailById(Input $input)
    {
        $input->verify(['id']);
        $data = (new AutoTransferRuleLogic())->getAutoTransferRuleDetailById($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function getAccountNameInAccountIds(Input $input)
    {
        $input->verify(['account_ids']);
        $data = (new AutoTransferRuleLogic())->getAccountNameInAccountIds($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

}