<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Constant\RoutePermission;
use App\Exception\AppException;
use App\Logic\DSP\LiveUserLogic;
use App\Model\SqlModel\DataMedia\OdsLiveUserCompanyLogModel;
use App\Param\LiveUserParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\Input;

class LiveUserController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new LiveUserLogic();
        $param = new LiveUserParam($input->getData());

        $data = $logic->getList($param, $input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addLiveUser(Input $input)
    {
        $input->verify(['media_type', 'live_user', 'live_user_name', 'name_start_time', 'name_end_time', 'type']);
        $logic = new LiveUserLogic();
        $param = new LiveUserParam($input->getData());

        $logic->addLiveUser($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editLiveUser(Input $input)
    {
        $input->verify(['media_type', 'live_user', 'live_user_name', 'name_start_time', 'type']);
        $logic = new LiveUserLogic();
        $param = new LiveUserParam($input->getData());

        $ret = $logic->editLiveUser($param);
        if ($ret) {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败'
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteLiveUser(Input $input)
    {
        $input->verify(['media_type', 'live_user', 'name_start_time', 'type']);
        $logic = new LiveUserLogic();
        $param = new LiveUserParam($input->getData());

        $logic->deleteLiveUser($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 获取直播机构列表
     * @param Input $input
     * @return array
     */
    public function getLiveCompanyList(Input $input)
    {
        $keyword = $input['keyword'] ?? '';
        $media_type = $input['media_type'] ?? '';
        if (empty($media_type)) {
            throw new AppException("功能已更新，请刷新页面重新操作");
        }

        $data = (new OdsLiveUserCompanyLogModel())->getCompanyList($media_type, $keyword)->pluck('name')->unique()->toArray();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function getCompanyList(Input $input)
    {
        $input->verify(['page', 'rows']);

        $logic = new LiveUserLogic();
        $data = $logic->getCompanyList(
            $input['platform'] ?? '', $input['media_type'] ?? '', $input['keyword'] ?? '',
            $input['type'] ?? '', $input['page'], $input['rows']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addCompany(Input $input)
    {
        $input->verify(['platform', 'media_types', 'type', 'names']);

        $logic = new LiveUserLogic();
        $logic->addCompany($input['platform'], $input['media_types'], $input['type'], $input['names']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/live-user'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteCompany(Input $input)
    {
        $input->verify(['platform', 'media_type', 'type', 'name']);

        $logic = new LiveUserLogic();
        $logic->deleteCompany($input['platform'], $input['media_type'], $input['type'], $input['name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * @return array
     */
    public function getPermission()
    {
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId('dsp');
        $permission_service = new PermissionService();
        $route_permission_list = $permission_service->getRankRoutePermission($level, $rank_id, RouteID::DSP_LIVE_USER);
        $data = [
            'button' => $route_permission_list->where('cate', RoutePermission::BUTTON)->pluck('name')
        ];

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}
