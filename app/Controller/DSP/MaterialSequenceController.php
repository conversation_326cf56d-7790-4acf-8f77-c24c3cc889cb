<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\MaterialSequenceLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\Material\MaterialSequenceFileListParam;
use App\Param\Material\MaterialSequenceListParam;
use App\Struct\Input;


class MaterialSequenceController extends Controller
{
    /**
     * @return array
     */
    public function getPermission()
    {
        $permission_logic = new PermissionLogic();
        $data = $permission_logic->getMaterialStoreRoutePermission();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取序列帧文件夹列表
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $list_data = (new MaterialSequenceLogic())->getList(new MaterialSequenceListParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取序列帧文件列表
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getFileList(Input $input): array
    {
        $input->verify(['server_id', 'folder_path']);
        $list_data = (new MaterialSequenceLogic())->getFileList(new MaterialSequenceFileListParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    public function deleteFile(Input $input): array
    {
        $input->verify(['file_id']);
        (new MaterialSequenceLogic())->deleteFile($input['file_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
            'data' => [],
        ];
    }

    /**
     * 获取题材
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getTheme(Input $input): array
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new MaterialSequenceLogic())->getTheme(),
        ];
    }

    /**
     * 获取文件夹标签
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getFolderLabel(Input $input): array
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new MaterialSequenceLogic())->getFolderLabel($input['keyword'] ?? ""),
        ];
    }

    /**
     * 文件夹打标签
     * @CtrlAnnotation(permissions=['/material/sequence-store'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addFolderLabel(Input $input)
    {
        $input->verify(['server_id', 'folder_path', 'label_name']);

        $material_logic = new MaterialSequenceLogic();
        $material_logic->addFolderLabel($input['server_id'], $input['folder_path'], $input['label_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 获取序列帧文件夹标签列表
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getLabelList(Input $input)
    {
        $list_data = (new MaterialSequenceLogic())->getLabelList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 添加标签
     * @CtrlAnnotation(permissions=['/material/sequence-label'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addLabel(Input $input)
    {
        $input->verify(['label_name', 'primary_theme', 'secondary_theme', 'description']);

        $material_logic = new MaterialSequenceLogic();
        $material_logic->addLabel($input['label_name'], $input['primary_theme'], $input['secondary_theme'], $input['description']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 编辑标签
     * @CtrlAnnotation(permissions=['/material/sequence-label'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editLabel(Input $input)
    {
        $input->verify(['label_name', 'primary_theme', 'secondary_theme', 'description']);

        $material_logic = new MaterialSequenceLogic();
        $material_logic->editLabel($input['label_name'], $input['primary_theme'], $input['secondary_theme'], $input['description']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '编辑成功'
        ];
    }

    /**
     * 编辑标签
     * @CtrlAnnotation(permissions=['/material/sequence-label'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteLabel(Input $input)
    {
        $input->verify(['label_name']);

        $material_logic = new MaterialSequenceLogic();
        $material_logic->deleteLabel($input['label_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 获取标签特征列表
     * @CtrlAnnotation(permissions=['/material/sequence-store'])
     * @param Input $input
     * @return array
     */
    public function getLabelDescription(Input $input): array
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new MaterialSequenceLogic())->getLabelDescription($input['keyword'] ?? ""),
        ];
    }
}
