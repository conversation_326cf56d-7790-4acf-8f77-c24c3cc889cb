<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\ToutiaoTransferOperateLogLogic;
use App\Struct\Input;

class ToutiaoTransferOperateLogController extends Controller
{
    /**
     * 获取操作记录列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $data = (new ToutiaoTransferOperateLogLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}