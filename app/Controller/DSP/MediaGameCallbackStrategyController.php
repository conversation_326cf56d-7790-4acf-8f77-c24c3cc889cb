<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\MediaGameCallBackStrategyLogic;
use App\Struct\Input;

class MediaGameCallbackStrategyController extends Controller
{
    /**
     * 获取列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $data = (new MediaGameCallbackStrategyLogic())->getList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-game-callback-strategy'], log_type='add')
     * 添加媒体上报策略
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['media_type', 'game_ids', 'platform']);
        (new MediaGameCallbackStrategyLogic())->add($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-game-callback-strategy'], log_type='delete')
     * 删除媒体上报策略
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['platform', 'media_type', 'level', 'game_id']);
        (new MediaGameCallbackStrategyLogic())->delete($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }
}
