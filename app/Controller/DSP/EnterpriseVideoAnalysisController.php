<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\EnterpriseVideoAnalysisLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\EnterpriseVideoAnalysisReportFilterParam;
use App\Struct\Input;
use Exception;

class EnterpriseVideoAnalysisController extends Controller
{
    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_ENTERPRISE_VIDEO_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取企业视频号分析数据
     * @CtrlAnnotation(permissions=['/adanalysis/enterprise-video-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new EnterpriseVideoAnalysisReportFilterParam($input->getData());
        $list = (new EnterpriseVideoAnalysisLogic())->getReport($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_ENTERPRISE_VIDEO_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

}
