<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: Administrator
 * Date: 2020/07/24
 * Time: 14:04
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\ADAssetLogic;
use App\Param\CampaignCreateMediaLogSearchParam;
use App\Param\TencentMaterialFileBindLogSearchParam;
use App\Param\TencentMaterialLabelParam;
use App\Param\TencentMaterialLabelSearchParam;
use App\Param\ToutiaoCreativeComponentTaskParam;
use App\Struct\Input;
use Throwable;

class ADAssetController extends Controller
{
    /**
     * 获取微信朋友圈头像
     * @param Input $input
     * @return array
     */
    public function getTencentWeChatProfileList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new ADAssetLogic)->getTencentWechatProfileList($input->getData()),
        ];
    }

    /**
     * 删除账号下头像
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/tencent-wechat-profile'], log_type='delete')
     * @return array
     */
    public function deleteTencentWeChatProfile(Input $input)
    {
        $input->verify(['account_id', 'profile_id']);
        (new ADAssetLogic)->deleteTencentWeChatProfile($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 获取微信朋友圈头像上传任务列表
     * @param Input $input
     * @return array
     */
    public function getTencentWeChatProfileTaskList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new ADAssetLogic)->getTencentWeChatProfileTaskList($input->getData()),
        ];
    }

    /**
     * 新增腾讯朋友圈头像上传任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/tencent-wechat-profile'], log_type='add')
     * @return array
     */
    public function addTencentWeChatProfileTask(Input $input)
    {
        $input->verify(['account_id_list', 'game_id', 'game_type', 'platform', 'profile_type']);
        if (!in_array($input['game_type'], ['IOS', '安卓'])) {
            throw new AppException('游戏类型不支持');
        }
        (new ADAssetLogic)->addTencentWeChatProfileTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加头像上传成功',
        ];
    }

    /**
     * 重启任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/tencent-wechat-profile'])
     * @return array
     */
    public function restartTencentWeChatProfileTask(Input $input)
    {
        $input->verify(['id']);
        (new ADAssetLogic)->restartTencentWeChatProfileTask((int)$input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加头像上传成功',
        ];
    }

    /**
     * 获取百度推广计划新建记录
     * @param Input $input
     * @return array
     */
    public function getBaiduCampaignCreateLogList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => (new ADAssetLogic)->getBaiduCampaignCreateLogList(new CampaignCreateMediaLogSearchParam($input->getData()))
        ];
    }

    /**
     * 删除百度推广计划新建记录
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/baidu-campaign-create-log'])
     * @return array
     */
    public function deleteBaiduCampaignCreateLog(Input $input)
    {
        $input->verify(['id']);
        if ((new ADAssetLogic)->deleteBaiduCampaignCreateLog((int)$input['id'])) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '删除失败',
            ];
        } else {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        }
    }

    /**
     * 获取头条青鸟线索资产任务列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoBirdClueProfileTaskList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new ADAssetLogic)->getToutiaoBirdClueProfileTaskList($input->getData()),
        ];
    }

    /**
     * 新增头条青鸟线索任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-bird-clue-profile'], log_type='add')
     * @return array
     */
    public function addToutiaoBirdClueProfileTask(Input $input)
    {
        $input->verify(['account_id_list', 'game_id', 'platform']);

        (new ADAssetLogic)->addToutiaoBirdClueProfileTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加头条青鸟线索成功',
        ];
    }


    /**
     * 重启任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-bird-clue-profile'])
     * @return array
     */
    public function restartToutiaoBirdClueProfile(Input $input)
    {
        $input->verify(['id']);
        (new ADAssetLogic)->restartToutiaoBirdClueProfile((int)$input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '重启头条青鸟线索成功',
        ];
    }

    /**
     * 获取头条原生锚点列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoNativeAnchorList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new ADAssetLogic)->getToutiaoNativeAnchorList($input->getData()),
        ];
    }

    /**
     * 新增头条原生锚点
     * @param Input $input
     * @return array
     */
    public function addToutiaoNativeAnchor(Input $input)
    {
        $input->verify(['account_id_list']);

        (new ADAssetLogic)->addToutiaoNativeAnchor($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加头条原生锚点成功',
        ];
    }


    /**
     * 重启原生锚点
     * @param Input $input
     * @return array
     */
    public function restartToutiaoNativeAnchor(Input $input)
    {
        $input->verify(['id']);
        (new ADAssetLogic)->restartToutiaoNativeAnchor((int)$input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '重启头条原生锚点成功',
        ];
    }

    /**
     * 获取头条橙子建站赠送任务列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoOrangeStationPushTaskList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取橙子建站赠送成功',
            'data' => (new ADAssetLogic)->getToutiaoOrangeStationPushTaskList($input->getData()),
        ];
    }

    /**
     * 新增头条橙子建站赠送任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-orange-station-push'], log_type='add')
     * @return array
     */
    public function addToutiaoOrangeStationPushTask(Input $input)
    {
        $input->verify(['account_id_list', 'orange_station_list', 'platform', 'account_id']);

        (new ADAssetLogic)->addToutiaoOrangeStationPushTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加头条橙子建站赠送成功',
        ];
    }


    /**
     * 重启橙子建站赠送任务
     * @param Input $input
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-bird-clue-profile'])
     * @return array
     */
    public function restartToutiaoOrangeStationPush(Input $input)
    {
        $input->verify(['id']);
        (new ADAssetLogic)->restartToutiaoOrangeStationPush((int)$input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '重启头条橙子建站赠送成功',
        ];
    }

    public function getToutiaoAvatarList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new ADAssetLogic)->getToutiaoAvatarList($input['account_id_list'] ?? [], $input['page'], $input['rows']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    public function addUpdateToutiaoAvatarTask(Input $input)
    {
        $input->verify(['account_id_list', 'material_url']);
        (new ADAssetLogic)->addUpdateToutiaoAvatarTask($input['account_id_list'] ?? '', $input['material_url']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
        ];
    }

    public function getToutiaoAvatarTaskList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new ADAssetLogic)->getToutiaoAvatarTaskList($input['$account_id_list'] ?? [], $input['page'], $input['rows']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    public function getToutiaoCreativeComponentTaskList(Input $input)
    {
        $data = (new ADAssetLogic)->getToutiaoCreativeComponentTaskList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }


    /**
     * @param Input $input
     * @return array
     */
    public function addToutiaoCreativeComponentTask(Input $input)
    {
        $input->verify(['platform','account_id_list','component_type','component_name','component_data']);
        $result = (new ADAssetLogic())->addToutiaoCreativeComponentTask(new ToutiaoCreativeComponentTaskParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result ?? [],
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function restartToutiaoCreativeComponentTask(Input $input)
    {
        $input->verify(['id']);
        $result = (new ADAssetLogic())->restartToutiaoCreativeComponentTask($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result ?? [],
            'message' => '操作成功',
        ];
    }

    public function getTencentMaterialLabelList(Input $input): array
    {
        try {
            $result = (new ADAssetLogic())->getTencentMaterialLabelList(
                new TencentMaterialLabelSearchParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getTencentFileBindLog(Input $input): array
    {
        try {
            $result = (new ADAssetLogic())->getTencentFileBindLog(
                new TencentMaterialFileBindLogSearchParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addTencentMaterialLabel(Input $input): array
    {
        (new ADAssetLogic())->addTencentMaterialLabel(
            new TencentMaterialLabelParam($input)
        );
        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }

    public function createTencentMaterialLabelTask(Input $input): array
    {
        $input->verify(['id']);
        (new ADAssetLogic())->createTencentMaterialLabelTask(
            $input['id']
        );
        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }
}
