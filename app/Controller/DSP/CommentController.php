<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Container;
use App\Logic\DSP\CommentLogic;
use App\Struct\Input;

class CommentController extends Controller
{
    /**
     * 获取评论列表
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCommentsList(Input $input)
    {
        $logic = new CommentLogic();
        $data = $logic->getCommentsList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取评论回复列表
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCommentReplyList(Input $input)
    {
        $input->verify(['comment_id']);
        $logic = new CommentLogic();
        $data = $logic->getCommentReplyList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 置顶评论
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editCommentStick(Input $input)
    {
        $input->verify(['operate_type', 'comment_info']);
        $data = $input->getData();
        $data['editor'] = Container::getSession()->get('name');
        $result = (new CommentLogic())->editCommentStick($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result
        ];
    }

    /**
     * 回复评论的回复
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function replyToReply(Input $input)
    {
        $input->verify(['operate_type', 'comment_info', 'comment_content']);
        $data = $input->getData();
        $data['editor'] = Container::getSession()->get('name');
        $result = (new CommentLogic())->replyToReply($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result
        ];
    }

    /**
     * 回复评论
     * @param Input $input
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='edit')
     * @return array
     */
    public function replyComment(Input $input)
    {
        $input->verify(['operate_type', 'comment_info', 'comment_content']);
        $data = $input->getData();
        $data['editor'] = Container::getSession()->get('name');
        $result = (new CommentLogic())->operateCommentByBulkRequest($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result
        ];
    }

    /**
     * 隐藏评论
     * @param Input $input
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='edit')
     * @return array
     */
    public function hideComment(Input $input)
    {
        $input->verify(['operate_type', 'comment_info']);
        $data = $input->getData();
        $data['editor'] = Container::getSession()->get('name');
        $result = (new CommentLogic())->operateCommentByBulkRequest($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result
        ];
    }

    /**
     * 屏蔽评论
     * @param Input $input
     * @CtrlAnnotation(permissions=['/comment/list'], log_type='edit')
     * @return array
     */
    public function blockCommentUsers(Input $input)
    {
        $input->verify(['operate_type', 'comment_info']);
        $data = $input->getData();
        $data['editor'] = Container::getSession()->get('name');
        $result = (new CommentLogic())->operateCommentByBulkRequest($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result
        ];
    }

}
