<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\PutTheBidRuleLogic;
use App\Param\PutTheBidRuleParam;
use App\Struct\Input;

class PutTheBidRuleController extends Controller
{
    /**
     * 获取规则列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new PutTheBidRuleLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取规则列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getEditPutTheBidRuleList(Input $input){
        $input->verify(['media_type', 'game_type', 'target_id']);
        $data = (new PutTheBidRuleLogic())->getEditPutTheBidRuleList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 管理后台 新增投放范围规则
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input)
    {
        $input->verify(['game_type', 'target_id', 'execution_account', 'feishu_webhook_url', 'media_type']);
        try {
            (new PutTheBidRuleLogic())->add((new PutTheBidRuleParam($input)));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 管理后台 新增投放范围规则
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input)
    {
        $input->verify(['target_id', 'game_type', 'execution_account', 'feishu_webhook_url', 'media_type']);
        try {
            (new PutTheBidRuleLogic())->edit((new PutTheBidRuleParam($input)));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除投放范围规则
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='delete')
     * @param Input $input
     * @return array
     */
    public function delete(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_share_rule_logic = new PutTheBidRuleLogic();
            $material_share_rule_logic->delete($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取投放范围价格渠道组列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getPutTheBidAgentGroupList(Input $input)
    {
        $input->verify(['media_type', 'game_type', 'target_id']);
        $data = (new PutTheBidRuleLogic())->getPutTheBidAgentGroupList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取直播间规则列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAwemePriceRangeRuleList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $data = (new PutTheBidRuleLogic())->getAwemePriceRangeRuleList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取直播间规则列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAwemeEditPutTheBidRuleList(Input $input){
        $input->verify(['media_type', 'game_type', 'target_id', 'aweme_account']);
        $data = (new PutTheBidRuleLogic())->getAwemeEditPutTheBidRuleList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 新增直播间投放范围规则
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAwemeRule(Input $input)
    {
        $input->verify(['game_type', 'target_id', 'aweme_account', 'execution_account', 'feishu_webhook_url', 'media_type']);
        try {
            (new PutTheBidRuleLogic())->addAwemeRule((new PutTheBidRuleParam($input)));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑直播间投放范围规则
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editAwemeRule(Input $input)
    {
        $input->verify(['target_id', 'game_type', 'aweme_account', 'execution_account', 'feishu_webhook_url', 'media_type']);
        try {
            (new PutTheBidRuleLogic())->editAwemeRule((new PutTheBidRuleParam($input)));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取直播间投放范围价格渠道组列表
     * @CtrlAnnotation(permissions=['/delivery/put-the-bid-rules'],log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAwemePutTheBidAgentGroupList(Input $input)
    {
        $input->verify(['media_type', 'game_type', 'target_id', 'aweme_account']);
        $data = (new PutTheBidRuleLogic())->getAwemePutTheBidAgentGroupList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}
