<?php

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\ADLabLogic;
use App\Logic\DSP\SiteLogic;
use App\Param\ADLab\ADLabParam;
use App\Param\ADLab\ADLabSearchParam;
use App\Param\ADLabServing\ADLabGroupAccountSearchParam;
use App\Param\ADLabServing\ADLabGroupParam;
use App\Param\ADLabServing\ADLabGroupSearchParam;
use App\Param\SiteOptionParam;
use App\Service\UserService;
use App\Struct\Input;
use Exception;

class ADLabController extends Controller
{
    /**
     * @param Input $input
     */
    public function getAccountList(Input $input)
    {
        $input->verify(['platform', 'media_type', 'root_game_id_list', 'os', 'page', 'rows']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getAccountList($input->getData())
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getCliqueIdList(Input $input)
    {
        $input->verify(['os', 'platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getCliqueIdList($input['os'], $input['platform'])
        ];
    }

    /**
     * 获取根游戏
     * @param Input $input
     * @return array
     */
    public function getRootGameList(Input $input)
    {
        $input->verify(['platform', 'clique_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getRooGameList($input['platform'], $input['clique_id'])
        ];
    }

    /**
     * 获取主游戏的OS
     * @param Input $input
     * @return array
     */
    public function getGameOS(Input $input)
    {
        $input->verify(['platform', 'root_game_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getGameOS($input['platform'], (int)$input['root_game_id'])
        ];
    }

    /**
     * 获取主游戏
     * @param Input $input
     * @return array
     */
    public function getMainGameList(Input $input)
    {
        $input->verify(['platform', 'root_game_id_list', 'os']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getMainGameList($input['platform'], $input['root_game_id_list'], $input['os'])
        ];
    }

    /**
     * 获取子游戏
     * @param Input $input
     * @return array
     */
    public function getGameList(Input $input)
    {
        $input->verify(['media_type', 'platform', 'os', 'root_game_id_list', 'main_game_id_list', 'page']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getGameList(
                $input['media_type'] ?? 1,
                $input['platform'],
                $input['os'],
                $input['root_game_id_list'],
                $input['main_game_id_list'] ?? [],
                $input['game_id'] ?? '',
                (int)$input['page']
            )
        ];
    }

    /**
     * 获取实验室项目
     * @param Input $input
     * @return array
     */
    public function getADLabGroupList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => (new ADLabLogic())->getADLabGroupList(new ADLabSearchParam($input->getData()))
        ];
    }

    /**
     * 添加实验室项目
     * @param Input $input
     * @CtrlAnnotation(log_type='add', throttle=600)
     * @return array
     */
    public function addADLabGroup(Input $input)
    {
        $input->verify(['platform', 'clique_id', 'os', 'other_setting']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '创建成功',
            'data' => (new ADLabLogic())->addADLabGroup($input->getData())
        ];
    }

    /**
     * 编辑实验室项目
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function editADLabGroup(Input $input)
    {
        $input->verify(['id', 'other_setting']);
        $result = (new ADLabLogic())->editADLabGroup($input->getData());
        return [
            'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
            'message' => $result ? '修改成功' : '修改失败',
            'data' => []
        ];
    }

    /**
     * 编辑实验室项目状态
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @param Input $input
     * @return array
     */
    public function editADLabGroupStatus(Input $input)
    {
        $input->verify(['id', 'status_code']);
        if ($input['status_code'] != 1) {
            $input['status_code'] = 0;
        }
        $result = (new ADLabLogic())->editADLabGroupStatus($input['id'], $input['status_code']);
        return [
            'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
            'message' => $result ? '修改成功' : '修改失败',
            'data' => []
        ];
    }

    /**
     * 删除实验室项目
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @param Input $input
     * @return array
     */
    public function deleteADLabGroup(Input $input)
    {
        $input->verify(['id']);
        $result = (new ADLabLogic())->deleteADLabGroup($input['id']);
        return [
            'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
            'message' => $result ? '删除成功' : '删除失败',
            'data' => []
        ];
    }

    public function getOperationLogList(Input $input)
    {
        try {
            $result_data = (new ADLabLogic())->getOperationLogList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     */
    public function getExternalAction(Input $input)
    {
        $input->verify(['platform', 'media_type']);
        try {
            $result_data = (new ADLabLogic())->getExternalAction($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @return array
     */
    public function operationADLab(Input $input)
    {
        $input->verify(['media_type', 'action', 'id']);
        try {
            $result_data = (new ADLabLogic())->operationADLab($input['media_type'], $input['action'], $input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $result_data
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getDepositLog(Input $input): array
    {
        $input->verify(['media_type', 'id']);
        try {
            $result_data = (new ADLabLogic())->getDepositLog($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result_data
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @return array
     */
    public function updateAD2Release(Input $input)
    {
        $input->verify(['ad_id', 'action', 'media_type']);
        try {
            $result = (new ADLabLogic())->updateAD2Release($input['ad_id'], $input['action'], $input['media_type'] ?? '');
            $response = [
                'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
                'message' => $result ? '修改成功' : '修改失败'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(log_type='edit', throttle=600)
     * @return array
     */
    public function batchUpdateAD2Release(Input $input)
    {
        $input->verify(['action', 'media_type']);
        try {
            $result = (new ADLabLogic())->batchUpdateAD2Release(
                $input['ad_ids'] ?? [],
                $input['tuoguan_ids'] ?? [],
                $input['jieguanuan_ids'] ?? [],
                $input['action'],
                $input['media_type'] ?? ''
            );
            $response = [
                'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
                'message' => $result ? '修改成功' : '修改失败'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}
