<?php
/**
 * 素材文件拓展任务
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Exception\AppException;
use App\Logic\DSP\MaterialFileExpandTaskLogic;
use App\Service\NoticeService;
use App\Struct\Input;
use App\Constant\ResponseCode;

class MaterialFileExpandTaskController extends Controller
{
    /**
     * 实时控制素材文件扩展任务状态
     * @param Input $input
     * @return array
     */
    public function message(Input $input)
    {
        (new NoticeService())->broadcast(NoticeService::NOTICE_MATERIAL_FILE_EXPAND_TASK_UPDATE, $input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发送成功',
        ];
    }

    /**
     * 获取素材文件拓展任务列表
     * @CtrlAnnotation(permissions=['/material/store'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $material_file_expand_task_logic = new MaterialFileExpandTaskLogic();
        $list_data = $material_file_expand_task_logic->getMaterialFileExpandTaskList($input->getData());
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加素材文件拓展任务
     * @param Input $input
     * @return array
     */
    public function addMaterialFileExpandTask(Input $input)
    {
        $input->verify([
            'platform', 'material_id', 'material_file_id', 'material_file_name',
            'material_file_type', 'type', 'expand_time', 'expand_width', 'expand_height'
        ]);
        $material_file_expand_task_logic = new MaterialFileExpandTaskLogic();
        $material_file_expand_task_logic->addMaterialFileExpandTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '新建成功',
        ];
    }

    /**
     * 批量添加素材文件拓展任务
     * @param Input $input
     * @return array
     */
    public function batchAddMaterialFileExpandTask(Input $input)
    {
        $input->verify([
            'material_file_id', 'op_type', 'type'
        ]);

        if ($input['op_type'] == 1 && !$input['expand_time']) {
            throw new AppException('时长扩展,一定要选择扩展时间');
        }

        if ($input['op_type'] == 2 && (!$input['expand_width'] || !$input['expand_height'])) {
            throw new AppException('尺寸扩展,一定要选择扩展尺寸');
        }

        $input['expand_time'] = ($input['expand_time'] ?? 0) ? $input['expand_time'] : 0;
        $input['expand_width'] = ($input['expand_width'] ?? 0) ? $input['expand_width'] : 0;
        $input['expand_height'] = ($input['expand_height'] ?? 0) ? $input['expand_height'] : 0;

        if (count($input['material_file_id']) > 50) {
            throw new AppException('一次不能选超过50个素材文件，请分批扩展');
        }
        $material_file_expand_task_logic = new MaterialFileExpandTaskLogic();
        $material_file_expand_task_logic->batchAddMaterialFileExpandTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '新建成功',
        ];
    }

    /**
     * 重启素材文件拓展任务
     * @param Input $input
     * @return array
     */
    public function manualMaterialFileExpandTask(Input $input)
    {
        $input->verify(['id']);
        $material_file_expand_task_logic = new MaterialFileExpandTaskLogic();
        $material_file_expand_task_logic->manualMaterialFileExpandTask((int)$input['id']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '重启成功',
        ];
        return $response;
    }

}