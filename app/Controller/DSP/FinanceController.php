<?php
/**
 * 财务对账
 * User: gzz
 * Date: 2020/04/13
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\FinanceLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\RedisModel\FinanceReportModel;
use App\Param\FinanceAccountRechargeParam;
use App\Param\FinanceCheckRecodeParam;
use App\Param\FinanceListParam;
use App\Param\FinanceMailListParam;
use App\Param\FinanceStandingBookListParam;
use App\Param\FinanceStandingBookPermissionListParam;
use App\Param\FundBalanceLogParam;
use App\Response\CSV;
use App\Response\Response;
use App\Struct\Input;
use App\Utils\Helpers;
use App\Utils\UploadTool;
use PhpOffice\PhpSpreadsheet\Exception;

class FinanceController extends Controller
{
    public function searchOptions(Input $input)
    {
        $input->verify(['column']);
        $list = (new FinanceLogic())->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $list
            ]
        ];
        return $response;
    }

    /**
     * 资金流水记录
     * @CtrlAnnotation(permissions=['/finance/fund'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getFundList(Input $input)
    {
        $input->verify(['fund_media_type']);

        $list = (new FinanceLogic())->getFundList(new FinanceListParam($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 财务对账报表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {

        $list = (new FinanceLogic())->getReport(new FundBalanceLogParam($input));

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 转账校对
     * @CtrlAnnotation(permissions=['/finance/transaction-check'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function transactionCheck(Input $input)
    {
        $data = (new FinanceLogic())->transactionCheck(new FundBalanceLogParam($input));

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 返货记录
     * @CtrlAnnotation(permissions=['/finance/return-goods-record'])
     * @param Input $input
     * @return array
     */
    public function returnGoodsRecord(Input $input)
    {
        $input->verify(['media_type']);
        $data = (new FinanceLogic())->getReturnGoodsWithCompanyAgency(new FundBalanceLogParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }


    /**
     * 新增截图任务
     * @CtrlAnnotation(permissions=['/finance/fund'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addScreenshotMission(Input $input)
    {
        $input->verify(['media_type', 'platform', 'year_month', 'type']);
        try {
            [$exclude_account_ids, $exclude_account] = (new FinanceLogic())->addScreenshotMission($input);
            if (count($exclude_account_ids) > 0 || count($exclude_account) > 0) {
                $response = [
                    'code' => ResponseCode::ERROR_WITH_DATA,
                    'message' => '',
                    'data' => [
                        'exclude_account_ids' => $exclude_account_ids,
                        'exclude_account' => $exclude_account
                    ]
                ];
            } else {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => '操作成功',
                ];
            }
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 截图任务列表
     * @CtrlAnnotation(permissions=['/finance/screenshot-mission'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function screenshotMission(Input $input)
    {
        $list = (new FinanceLogic())->screenshotMission($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 删除截图任务
     * @param Input $input
     * @return array
     */
    public function removeScreenshotMission(Input $input)
    {
        $input->verify(['mission_ids']);
        try {
            (new FinanceLogic())->removeScreenshotMission($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 截图任务参数列表
     * @param Input $input
     * @return array
     */
    public function screenshotMissionParamList(Input $input)
    {
        $list = (new FinanceLogic())->screenshotMissionParamList($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 截图任务账户列表
     * @param Input $input
     * @return array
     */
    public function screenshotMissionAccountList(Input $input)
    {
        $list = (new FinanceLogic())->screenshotMissionAccountList($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }


    /**
     * 代理商变更信息
     * @param Input $input
     * @return array
     */
    public function getAgencyChangeLog(Input $input)
    {
        $list = (new FinanceLogic())->getAgencyChangeLog(
            $input['page'], $input['rows'], $input['platform'],
            $input['media_type'], $input['account'], $input['agency_full_name'],
            $input['date'] ?? []
        );

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 导入代理商变更信息
     * @CtrlAnnotation(permissions=['/finance/agency'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importAgencyChangeLog(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importAgencyChangeLog($input['platform'], $input['media_type'], $data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    /**
     * 导出代理商变更信息
     * @CtrlAnnotation(permissions=['/finance/agency'], log_type='export')
     * @param Input $input
     * @return \App\Response\File
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function exportAgencyChangeLog(Input $input)
    {
        $file = (new FinanceLogic())->exportAgencyChangeLog($input['platform'], $input['media_type'], $input['account'], $input['agency_full_name'], $input['date'] ?? []);
        $response = Response::File($file);
        $response->downloadName('代理信息变更.xlsx');
        return $response;
    }

    public function downloadAgencyChangeLogSample()
    {
        return new CSV('代理信息-变更导入sample.xlsx', 'sample/agency_change_log_template.xlsx');
    }

    /**
     * @param Input $input
     * @return array
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importFundBalanceLog(Input $input)
    {
        $input->verify(['media_type']);
        $data_list = UploadTool::xlsx($this->request->files);
        $input_data = $input->getData();
        //$media_type = $input_data['media_type'];
        $skip_check = $input_data['skip_check'] ?? 0;

        $reimport = (new FinanceLogic())->importFundBalanceLog($data_list, $skip_check);
        if ($reimport->isNotEmpty()) {
            return [
                'code' => ResponseCode::ERROR_WITH_DATA,
                'message' => '导入失败',
                'data' => $reimport
            ];
        }
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功'
        ];
    }

    /**
     * 大客户账户充值
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importAccountRecharge(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);
        (new FinanceLogic())->importAccountRechargeLog($data_list);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功'
        ];
    }

    /**
     * 全账号及账号余额校验--任务
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function checkAccount(Input $input)
    {
        $input->verify(['begin_date', 'end_date', 'platform']);
        $logic = new FinanceLogic();
        $param = $input->getData();
        $param['is_check'] = true;
        $data = [
            'param' => json_encode($param),
            'user_id' => Container::getSession()->get('user_id'),
        ];
        $param = new FinanceCheckRecodeParam($data);
        return $logic->addCalculateTask($param);
    }

    /**
     * 开始对账--任务
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function checkBalance(Input $input)
    {
        $input->verify(['begin_date', 'end_date', 'platform']);
        $logic = new FinanceLogic();
        $data = [
            'param' => json_encode($input->getData()),
            'user_id' => Container::getSession()->get('user_id'),
        ];
        $param = new FinanceCheckRecodeParam($data);
        return $logic->addCalculateTask($param);
    }

    /**
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCheckTaskList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $user_id = Container::getSession()->get('user_id');
        $logic = new FinanceLogic();
        $list = $logic->getCheckTaskList($user_id, $input['page'], $input['rows']);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='export')
     * @return CSV
     */
    public function accountRechargeTemplate()
    {
        return new CSV('账号充值sample.xlsx', 'sample/account_recharge_template.xlsx');
    }

    /**
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='export')
     * @return CSV
     */
    public function fundBalanceTemplate()
    {
        return new CSV('账号余额sample.xlsx', 'sample/fund_balance_template.xlsx');
    }


    /**
     * 邮件结算
     * @CtrlAnnotation(permissions=['/finance/mail'], log_type='get')
     * @param $input
     * @return array
     */
    public function getMailList($input)
    {
        $list = (new FinanceLogic())->getMailList(new FinanceMailListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_FINANCE_ACCOUNT_CENTER);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function searchOptionsStandingBook(Input $input)
    {
        $input->verify(['column']);
        $list = (new FinanceLogic())->searchOptionsStandingBook($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $list
            ]
        ];
        return $response;
    }

    /**
     * 台账列表
     * @CtrlAnnotation(permissions=['/finance/account_center'], log_type='get')
     * @param $input
     * @return array
     */
    public function getStandingBookList($input)
    {
        $param = new FinanceStandingBookListParam($input);
        $param->user = Container::getSession()->name;
        $list = (new FinanceLogic())->getStandingBookList($param);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 导入台账列表
     * @CtrlAnnotation(permissions=['/finance/account_center'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importStandingBook(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new FinanceLogic())->importStandingBook($data_list);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
        return $response;
    }

    public function downloadStandingBookSample()
    {
        return new CSV('台账列表导入sample.xlsx', 'sample/fund_account_center_template.xlsx');
    }

    /**
     * 台账权限
     * @CtrlAnnotation(permissions=['/finance/permission'], log_type='get')
     * @param $input
     * @return array
     */
    public function getStandingBookPermission($input)
    {
        $list = (new FinanceLogic())->getStandingBookPermission(new FinanceStandingBookPermissionListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 添加/编辑台账权限
     * @CtrlAnnotation(permissions=['/finance/permission'], log_type='add')
     * @param $input
     * @return array
     */
    public function addStandingBookPermission(Input $input)
    {
        $input->verify(['company', 'names']);
        try {
            (new FinanceLogic())->addStandingBookPermission($input);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 确认资金流水记录 生成快照
     * @CtrlAnnotation(permissions=['/finance/fund'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function confirmFundList(Input $input)
    {
        $input->verify(['fund_media_type']);

        try {
            $time = (new FinanceLogic())->confirmFundList(new FinanceListParam($input));
            $message = "确认流水成功，确认时间：" . $time;
        } catch (\Throwable $e) {
            $message = "确认流水失败，失败原因：{$e->getMessage()}";
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => $message,
        ];
    }

    /**
     * 检查资金流水快照生成月份
     * @CtrlAnnotation(permissions=['/finance/fund'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getConfirmFundMonths(Input $input)
    {
        $input->verify(['platform', 'start_date', 'end_date']);

        $data = (new FinanceLogic())->getConfirmFundMonths($input['platform'], $input['start_date'], $input['end_date'], $input['statement'] ?? "");

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "获取成功",
            'data' => $data
        ];
    }

    /**
     * 获取代理分类列表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAgencyTypeList(Input $input)
    {
        $data = (new FinanceLogic())->getAgencyTypeList(
            $input['platform'] ?? "",
            $input['media_type'] ?? "",
            $input['agency_type'] ?? "",
            $input['keyword'] ?? ""
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "获取成功",
            'data' => $data
        ];
    }

    /**
     * 添加代理分类列表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAgencyType(Input $input)
    {
        $input->verify(['platform', 'media_type', 'agency_type', 'agency_full_name', 'start_date']);

        (new FinanceLogic())->addAgencyType(
            $input['platform'],
            $input['media_type'],
            $input['agency_type'],
            $input['agency_full_name'],
            $input['start_date']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "添加成功",
        ];
    }

    /**
     * 编辑代理分类列表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editAgencyType(Input $input)
    {
        $input->verify(['platform', 'media_type', 'agency_type', 'agency_full_name', 'start_date']);

        (new FinanceLogic())->editAgencyType(
            $input['platform'],
            $input['media_type'],
            $input['agency_type'],
            $input['agency_full_name'],
            $input['start_date']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "编辑成功",
        ];
    }

    /**
     * 删除代理分类列表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteAgencyType(Input $input)
    {
        $input->verify(['platform', 'media_type', 'agency_full_name', 'start_date']);

        (new FinanceLogic())->delAgencyType(
            $input['platform'],
            $input['media_type'],
            $input['agency_full_name'],
            $input['start_date']
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "删除成功",
        ];
    }

    /**
     * 获取财务对账的冻结状态
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReportFreezeStatus(Input $input)
    {
        $input->verify([
            'platform'
        ]);

        $redis_model = new FinanceReportModel();
        $list = $redis_model->getAll($input['platform']);
        $data = [];
        foreach ($list as $media_type => $freeze_month) {
            $data[] = [
                'media_type' => $media_type,
                'month' => date('Ym', $freeze_month)
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "获取成功",
            'data' => $data
        ];
    }

    /**
     * 编辑财务对账的冻结状态
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function changeReportFreezeStatus(Input $input)
    {
        $input->verify(['platform', 'media_type', 'is_freeze']);

        (new FinanceLogic())->changeReportFreezeStatus(
            $input['platform'],
            $input['media_type'],
            $input['is_freeze'],
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "操作成功",
        ];
    }

    /**
     * 获取大客户流水列表
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAccountRechargeList(Input $input)
    {
        $input->verify(['platform', 'media_type']);

        $param = new FinanceAccountRechargeParam($input->getData());
        $list = (new FinanceLogic())->getAccountRechargeList($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 删除大客户数据
     * @CtrlAnnotation(permissions=['/finance/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteAccountRecharge(Input $input)
    {
        $input->verify(['platform', 'media_type', 'month']);

        $param = new FinanceAccountRechargeParam($input->getData());
        (new FinanceLogic())->deleteAccountRecharge($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "删除成功",
        ];
    }
}
