<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\VideoScriptLogic;
use App\Param\Material\VideoScriptListParam;
use App\Param\Material\VideoScriptParam;
use App\Struct\Input;


class VideoScriptController extends Controller
{
    /**
     * 生成脚本推荐词
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function generateRecommendWords(Input $input): array
    {
        $input->verify(['name']);

        $logic = new VideoScriptLogic();
        $data = $logic->generateRecommendWords($input['name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 再次生成脚本推荐词
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function againGenerateRecommendWords(Input $input): array
    {
        $input->verify(['name', 'gpt_id', 'recommend_type']);

        $logic = new VideoScriptLogic();
        $data = $logic->againGenerateRecommendWords($input['name'], $input['gpt_id'], $input['recommend_type']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 生成视频脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function generateVideoScript(Input $input): array
    {
        $input->verify(['clique_id', 'clique_name', 'selling_points', 'word_number']);

        $logic = new VideoScriptLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->generateVideoScript($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 评价脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'])
     * @param Input $input
     * @return array
     */
    public function evaluateVideoScript(Input $input): array
    {
        $input->verify(['gpt_id', 'script_index', 'evaluate', 'clique_id', 'clique_name']);

        $logic = new VideoScriptLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->evaluateVideoScriptNew($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '评价成功',
            'data' => $data,
        ];
    }

    /**
     * 重新生成脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function againGenerateVideoScript(Input $input): array
    {
        $input->verify(['gpt_id', 'script_index', 'clique_id', 'clique_name']);

        $logic = new VideoScriptLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->againGenerateVideoScript($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '重新生成成功',
            'data' => $data,
        ];
    }

    /**
     * 视频脚本列表
     * @CtrlAnnotation(permissions=['/video-script/script-manage'])
     * @param Input $input
     * @return array
     */
    public function getVideoScriptList(Input $input): array
    {
        $logic = new VideoScriptLogic();
        $param = new VideoScriptListParam($input->getData());
        $data = $logic->getList($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 视频脚本混剪详情
     * @CtrlAnnotation(permissions=['/video-script/script-manage'])
     * @param Input $input
     * @return array
     */
    public function getVideoScriptInfo(Input $input): array
    {
        $input->verify(['clique_id']);

        $logic = new VideoScriptLogic();
        $data = $logic->getVideoScriptInfo($input["video_script_id"] ?? 0, $input["material_file_id"] ?? 0, $input['clique_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 添加视频脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addVideoScript(Input $input)
    {
        $input->verify(['script_name', 'synopsis', 'content']);

        $logic = new VideoScriptLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->addVideoScript($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
            'data' => $data
        ];
    }

    /**
     * 编辑视频脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editVideoScript(Input $input)
    {
        $input->verify(['id', 'content']);

        $logic = new VideoScriptLogic();
        $param = new VideoScriptParam($input->getData());
        $data = $logic->editVideoScript($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '编辑成功',
            'data' => $data
        ];
    }

    /**
     * 删除视频脚本
     * @CtrlAnnotation(permissions=['/video-script/script-manage'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteVideoScript(Input $input)
    {
        $input->verify(['id']);

        $logic = new VideoScriptLogic();
        $logic->removeVideoScript($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }
}
