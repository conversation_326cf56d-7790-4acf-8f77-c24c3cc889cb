<?php
/**
 * 素材文件管理
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialFileLogic;
use App\Param\Material\MaterialFileListParam;
use Exception;


class MaterialFileController extends Controller
{
    /**
     * 获取素材文件列表
     * @CtrlAnnotation(permissions=['/material/store'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input): array
    {
        $input->verify(['material_id', 'platform']);
        $material_file_logic = new MaterialFileLogic();
        $list_data = $material_file_logic->getList(new MaterialFileListParam($input));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }


    public function getMaterialFileListByMD5(Input $input): array
    {
        $input->verify(['md5_list']);
        $material_file_logic = new MaterialFileLogic();
        $list_data = $material_file_logic->getMaterialFileListByMD5($input['md5_list']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 删除一个素材文件
     * @CtrlAnnotation(permissions=['/material/store'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function remove(Input $input): array
    {
        $input->verify(['ids']);
        try {
            $material_theme_logic = new MaterialFileLogic();
            $material_theme_logic->remove($input['ids']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取素材文件列表-带权限
     * @CtrlAnnotation(permissions=['/material/store'])
     * @param Input $input
     * @return array
     */
    public function getSignatureListWithPermission(Input $input): array
    {
        $input->verify(['signature_list']);

        $material_file_logic = new MaterialFileLogic();
        $list_data = $material_file_logic->getSignatureListWithPermission($input['signature_list']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

}
