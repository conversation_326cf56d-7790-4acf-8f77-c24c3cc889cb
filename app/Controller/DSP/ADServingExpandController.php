<?php
/**
 * Created by <PERSON>pStorm.
 * User: Administrator
 * Date: 2020/07/24
 * Time: 14:04
 */

namespace App\Controller\DSP;


use App\Constant\KuaishouEnum;
use App\Constant\ResponseCode;
use App\Logic\DSP\ADServingExpandLogic;
use App\Param\TencentMaterialLabelParam;
use App\Param\TencentMaterialLabelSearchParam;
use App\Service\MediaAD\MediaBaidu;
use App\Service\MediaAD\MediaBilibili;
use App\Service\MediaAD\MediaKuaishou;
use App\Service\MediaAD\MediaTencentV3;
use App\Service\MediaAD\MediaToutiao;
use App\Service\MediaAD\MediaUc;
use App\Struct\Input;
use Throwable;

class ADServingExpandController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function refreshToutiaoAudience(Input $input)
    {
        $input->verify(['account_id']);
        try {
            (new ADServingExpandLogic())->refreshToutiaoAudience($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoPage(Input $input)
    {
        $input->verify(['platform']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoPage($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取快手行为类目词列表
     * @param Input $input
     * @return mixed
     */
    public function getKuaishouInterestCategoryList(Input $input)
    {
        $result = (new MediaKuaishou)->getInterestCategoryList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 原生锚点
     * @param Input $input
     * @return array
     */
    public function getToutiaoAnchorRelated(Input $input)
    {
        $input->verify(['platform', 'account_id']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoAnchorRelated($input['account_id'], $input['platform']);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取橙子建站站点列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoOrangeStation(Input $input)
    {
        $input->verify(['platform', 'account_id']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoOrangeStation($input['account_id'], $input['platform'], $input['page'] ?? 1);

            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 今日头条卡券id
     * @param Input $input
     * @return array
     */
    public function getToutiaoGamePackageBatchIdOption(Input $input)
    {
        $input->verify(['platform']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoGamePackageBatchIdOption($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'info' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 今日头条卡券id根据账号
     * @param Input $input
     * @return array
     */
    public function getToutiaoGamePackageOptionByAccountId(Input $input)
    {
        $input->verify(['platform', 'account_id']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoGamePackageOptionByAccountId($input['account_id'], $input['platform']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'info' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     */
    public function getToutiaoUnionComponent(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoUnionComponent($input['name'] ?? '', $input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 腾讯信息流(广点通)获取定向各大数据的接口
     * @param Input $input
     * @return array
     */
    public function getTencentTargetingTag(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getTencentTargetingTag($input['type'], $input['tag_spec'] ?? []);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 快手获取定向地域数据的接口
     * @return array
     */
    public function getKuaishouCityTag()
    {
        try {
            $result = (new ADServingExpandLogic())->getKuaishouCityTag();
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * B站获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getBiliBiliBusinessCategory(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBiliBiliBusinessCategory();
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'map' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * B站获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getBiliBiliLandingPageOptions(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBiliBiliLandingPageOptions($input['promotion_purpose_type'],$input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * B站获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getBiliBiliMiniGameOptions(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBiliBiliMiniGameOptions($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result['records']
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * B站获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getBiliBiliADSpaceIdOptions(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBiliBiliADSpaceIdOptions($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * B站获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getBiliBiliTargetingTag(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBiliBiliTargetingTag();
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'map' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * 快手获取定向地域数据的接口
     * @param Input $input
     * @return array
     */
    public function getKuaishouTargetingTag(Input $input)
    {
        try {
            $type = $input['type'];
            //强制转app行为为新的参数
            if ($type == KuaishouEnum::TARGETING_TAGS_TYPE_APP_INTEREST) {
                $type = KuaishouEnum::TARGETING_TAGS_TYPE_APP_INTEREST_ID;
            }
            $result = (new ADServingExpandLogic())->getKuaishouTargetingTag($type);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * 快手获取参数创意分类数据的接口
     * @return array
     */
    public function getKuaishouCreativeCategory()
    {
        try {
            $result = (new ADServingExpandLogic())->getKuaishouCreativeCategory();
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /*
     * 快手获取参数推荐标签数据的接口
     * @return array
     */
    public function getKuaishouExposeTags()
    {
        try {
            $result = (new ADServingExpandLogic())->getKuaishouExposeTags();
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 快手落地页列表
     * @param Input $input
     * @return array
     */
    public function getKuaishouPageList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getKuaishouPageList(
                $input['account_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getKuaishouBlueVTalentIdList(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ADServingExpandLogic())->getKuaishouBlueVTalentIdList($input['account_id'], $input['kol_user_type'] ?? 2);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => $data,
            ],
            'message' => '获取成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getPageList(Input $input)
    {
        $input->verify(['platform', 'account_id', 'game_id', 'promoted_object_type']);
        try {
            $result = (new ADServingExpandLogic())->getPageList(
                $input['account_id'],
                $input['promoted_object_type'],
                $input['platform'],
                $input['game_id'],
                $input['page_type'] ?? '',
                $input['is_need_object_id'] ?? true
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getWeChatPageList(Input $input)
    {
        $input->verify(['account_id', 'platform']);
        try {
            $result = (new ADServingExpandLogic())->getWeChatPageList(
                $input['platform'],
                $input['account_id'],
                $input['first_material_type'] ?? '',
                $input['first_material_width'] ?? '',
                $input['first_material_height'] ?? '',
                $input['first_material_num'] ?? '',
                $input['site_set'] ?? ''
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 蹊径落地页列表
     * @param Input $input
     * @return array
     */
    public function getTencentLandingPageList(Input $input)
    {
        $input->verify(['account_id', 'platform', 'game_id', 'promoted_object_type']);
        try {
            $result = (new ADServingExpandLogic())->getTencentLandingPageList(
                $input['platform'],
                $input['account_id'],
                $input['promoted_object_type'],
                $input['game_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 蹊径落地页列表
     * @param Input $input
     * @return array
     */
    public function getPlayablePageList(Input $input)
    {
        $input->verify(['account_id', 'platform', 'game_id']);
        try {
            $result = (new ADServingExpandLogic())->getPlayablePageList(
                $input['platform'],
                $input['account_id'],
                $input['promoted_object_type'],
                $input['game_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result['list']
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getWeChatPageListActualTime(Input $input)
    {
        $input->verify(['account_id']);
        (new ADServingExpandLogic())->getWeChatPageListActualTime(
            $input['account_id']
        );
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功'
        ];
        return $response;
    }

    /**
     * 根据账号更新百度转化列表
     * @param Input $input
     * @return array
     */
    public function getBaiduConvertListActualTime(Input $input)
    {
        $input->verify(['account_id']);
        (new ADServingExpandLogic())->getBaiduConvertListActualTime(
            $input['account_id']
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getProfileList(Input $input)
    {
        $input->verify(['platform', 'account_id', 'game_id']);
        try {
            $result = (new ADServingExpandLogic())->getProfileList(
                $input['platform'],
                $input['account_id'],
                $input['promoted_object_type'],
                $input['game_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getVideoNumberList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getVideoNumberList(
                $input['account_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getOfficialAccountList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getOfficialAccountList(
                $input['account_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getTencentCreativeTemplateRules(Input $input)
    {
        $input->verify(['account_id','creative_mode', 'site_config', 'other_setting']);

        try {
            $result = (new ADServingExpandLogic())->getTencentCreativeTemplateRules(
                $input['account_id'], $input['creative_mode'], $input['site_config'], $input['other_setting']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $result
                ]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度、百度搜索全量基木鱼落地页列表
     * @param Input $input
     * @return array
     */
    public function getBaiduTraceUrlList(Input $input)
    {
        $input->verify(['account_id', 'opt_from', 'show_type']);
        try {
            $result = (new ADServingExpandLogic())->getBaiduTraceUrlList($input['account_id'], $input['opt_from'], $input['show_type']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度搜索创意组件列表
     * @param Input $input
     * @return array
     */
    public function getSegmentList(Input $input)
    {
        $input->verify(['account_id', 'segment_type']);
        try {
            $result = (new ADServingExpandLogic())->getSegmentList(
                $input['account_id'],
                $input['segment_type'],
                $input['page'] ?? 1,
                $input['limit'] ?? 20);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度转化ID列表
     * @param Input $input
     * @return array
     */
    public function getBaiduConvertList(Input $input)
    {
        $input->verify(['account_id', 'trans_from', 'trans_type']);
        try {
            $result = (new ADServingExpandLogic())->getBaiduConvertList(
                $input['account_id'], $input['trans_from'], $input['trans_type'], $input['deep_trans_type'] ?? 0, $input['trans_name'] ?? '');
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度搜索人群列表
     * @param Input $input
     * @return array
     */
    public function getCrowdList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getCrowdList($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度搜索关键词出价策略列表
     * @param Input $input
     * @return array
     */
    public function getPriceStrategyList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getPriceStrategyList($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度搜索关键词出价策略列表
     * @param Input $input
     * @return array
     */
    public function getTargetPackageList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getTargetPackageList($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取百度词包
     * @param Input $input
     * @return array
     */
    public function getBaiduKeywordsPackageList(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getBaiduKeywordsPackageList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
            ];
        }
        return $response;
    }

    /**
     * 获取头条词包
     * @param Input $input
     * @return array
     */
    public function getToutiaoKeywordsPackageList(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getToutiaoKeywordsPackageList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
            ];
        }
        return $response;
    }

    /**
     * 获取快手词包
     * @param Input $input
     * @return array
     */
    public function getKuaishouKeywordsPackageList(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getKuaishouKeywordsPackageList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
            ];
        }
        return $response;
    }

    /**
     * 获取tencent词包
     * @param Input $input
     * @return array
     */
    public function getTencentKeywordsPackageList(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getTencentKeywordsPackageList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
            ];
        }
        return $response;
    }


    /**
     * 获取快手否词包
     * @param Input $input
     * @return array
     */
    public function getKuaishouNowordsPackageList(Input $input)
    {
        try {
            $result = (new ADServingExpandLogic())->getKuaishouNowordsPackageList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
            ];
        }
        return $response;
    }

    /**
     * 百度保存为词包
     * @param Input $input
     * @return array
     */
    public function saveBaiduKeywordsPackage(Input $input)
    {
        $input->verify(['keywords', 'name', 'media_type', 'keyword_type']);
        try {
            (new ADServingExpandLogic())->saveBaiduKeywordsPackage(
                $input['keywords'],
                $input['media_type'],
                $input['keyword_type'],
                $input['name']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功新建'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 头条保存为词包
     * @param Input $input
     * @return array
     */
    public function saveToutiaoKeywordsPackage(Input $input)
    {
        $input->verify(['keywords_list', 'name', 'media_type', 'keyword_type']);
        try {
            $input['keywords_list'] = explode("\n", trim($input['keywords_list']));
            (new ADServingExpandLogic())->saveToutiaoKeywordsPackage(
                $input['keywords_list'],
                $input['media_type'],
                $input['keyword_type'],
                $input['name']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功新建'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 快手保存为词包
     * @param Input $input
     * @return array
     */
    public function saveKuaishouKeywordsPackage(Input $input)
    {
        $input->verify(['keywords_list', 'name', 'media_type', 'keyword_type']);
        try {
            $input['keywords_list'] = explode("\n", trim($input['keywords_list']));
            (new ADServingExpandLogic())->saveToutiaoKeywordsPackage(
                $input['keywords_list'],
                $input['media_type'],
                $input['keyword_type'],
                $input['name']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功新建'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 腾讯保存为词包
     * @param Input $input
     * @return array
     */
    public function saveTencentKeywordsPackage(Input $input)
    {
        $input->verify(['keywords_list', 'name', 'media_type', 'keyword_type']);
        try {
            $input['keywords_list'] = explode("\n", trim($input['keywords_list']));
            (new ADServingExpandLogic())->saveToutiaoKeywordsPackage(
                $input['keywords_list'],
                $input['media_type'],
                $input['keyword_type'],
                $input['name']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功新建'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度删除词包
     * @param Input $input
     * @return array
     */
    public function deleteKeywordsPackage(Input $input)
    {
        $input->verify(['id']);
        try {
            (new ADServingExpandLogic())->deleteKeywordsPackage(
                $input['id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功删除'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 快手保存为否词词包
     * @param Input $input
     * @return array
     */
    public function saveKuaishouNowordsPackage(Input $input)
    {
        $input->verify(['nowords_list', 'name', 'media_type', 'noword_type']);
        try {
            $input['nowords_list'] = explode("\n", trim($input['nowords_list']));
            (new ADServingExpandLogic())->saveKuaishouNowordsPackage(
                $input['nowords_list'],
                $input['media_type'],
                $input['noword_type'],
                $input['name']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功新建'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除否词词包
     * @param Input $input
     * @return array
     */
    public function deleteNowordsPackage(Input $input)
    {
        $input->verify(['id']);
        try {
            (new ADServingExpandLogic())->deleteNowordsPackage(
                $input['id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功删除'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * @param Input $input
     * @return array
     */
    public function getPlayableUrlOptions(Input $input)
    {
        $input->verify(['platform', 'account_id_list']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new ADServingExpandLogic())->getPlayableUrlOptions($input['platform'], $input['account_id_list'], $input['playable_name'] ?? ''),
            'message' => '成功'
        ];
    }

    /**
     * 获取云游戏试玩素材
     * @param Input $input
     * @return array
     */
    public function getCloudGamePlayableOptions(Input $input)
    {
        $input->verify(['platform', 'account_id_list']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new ADServingExpandLogic())->getCloudGamePlayableOptions($input['platform'], $input['account_id_list'], $input['name'] ?? ''),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAllPlayableUrlOptions(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new ADServingExpandLogic())->getAllPlayableUrlOptions($input['account_id']),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAllCloudGamePlayableOptions(Input $input)
    {
        $input->verify(['platform', 'company']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new ADServingExpandLogic())->getAllCloudGamePlayableOptions($input['playable_name'] ?? '', $input['platform'], $input['account_id_list']),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getUCCity()
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getProvince(),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getUCCounty()
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getCounty(),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getUCInterest()
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getInterest(),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getUCIndustryList(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getIndustryList($input),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getUCLogo(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getUCLogo($input),
            'message' => '成功'
        ];
    }

    /**
     * 百度基木鱼落地页
     * @param Input $input
     * @return array
     */
    public function getUcLandingPageList(Input $input)
    {
        $input->verify(['account_id', 'opt_from']);
        try {
            $result = (new MediaUc())->getLandingPageList(
                $input['account_id'], $input['opt_from'], $input['key_word'] ?? '');
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => ['list' => $result],
                'message' => '获取成功',
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '获取失败' . $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取转化类型列表
     * @param Input $input
     * @return array
     */
    public function getUCConvertTypeList(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getConvertType($input),
            'message' => '成功'
        ];
    }

    /**
     * 根据指定的推广组id获取推广创意样式模板
     * @param Input $input
     * @return array
     */
    public function getUCCreativeTemplates(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaUc())->getCreativeTemplates('', ''),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getFeedDeliverySearchSuggestKeywordList(Input $input)
    {
        $input->verify(['ad_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaToutiao())->getFeedDeliverySearchSuggestKeywordList((int)$input['ad_id']),
            'message' => '成功'
        ];
    }

    /**
     * 获取快手关键词
     * @param Input $input
     * @return array
     */
    public function getKuaishouFeedDeliverySearchSuggestKeywordList(Input $input)
    {
        $input->verify(['ad_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaKuaishou())->getKuaishouFeedDeliverySearchSuggestKeywordList((int)$input['ad_id']),
            'message' => '成功'
        ];
    }

    /**
     * 获取快手否词
     * @param Input $input
     * @return array
     */
    public function getKuaishouFeedDeliverySearchSuggestNowordList(Input $input)
    {
        $input->verify(['ad_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaKuaishou())->getKuaishouFeedDeliverySearchSuggestNowordList((int)$input['ad_id']),
            'message' => '成功'
        ];
    }

    /**
     * 根据账号获取头条抖音号id
     * @param Input $input
     * @return array
     */
    public function getToutiaoIesIdList(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => (new MediaToutiao())->getToutiaoIesIdList((int)$input['account_id'], $input['platform'] ?? '')
            ],
            'message' => '成功'
        ];
    }

    /**
     * 获取头条多关键词兴趣列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoMultiInterestInterestKeywordList(Input $input)
    {
        $input->verify(['query_words_addr']);
        try {
            $response = [
                'data' => (new MediaToutiao())->getInterestInterestKeywordMultiList($input->getData()),
                'code' => ResponseCode::SUCCESS,
                'message' => '成功'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取头条多关键词行为列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoMultiInterestActionKeywordList(Input $input)
    {
        $input->verify(['query_words_addr', 'action_scene', 'action_days']);
        try {
            $response = [
                'data' => (new MediaToutiao())->getInterestActionKeywordMultiList($input->getData()),
                'code' => ResponseCode::SUCCESS,
                'message' => '成功'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 获取抖音达人类目
     * @param Input $input
     * @return array
     */
    public function getToutiaoAwemeMultiLevelCategoryList(Input $input)
    {
        try {
            $result = (new MediaToutiao())->getAwemeMultiLevelCategoryList($input['behaviors'] ?? []);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => ['list' => $result],
                'message' => '获取成功',
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '获取失败' . $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取抖音达人列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoAwemeAccountsList(Input $input)
    {
        try {
            if (($input['label_id'] ?? '') && $input['label_id']) {
                $result = (new MediaToutiao())->getAwemeAccountsInfo($input['behaviors'] ?? [], $input['label_id']);
            } else {
                $result = (new MediaToutiao())->getAwemeAccountsList($input['behaviors'] ?? [], $input['category_id'] ?? 0);
            }


            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => ['list' => $result],
                'message' => '获取成功',
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '获取失败' . $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 百度基木鱼落地页
     * @param Input $input
     * @return array
     */
    public function getBaiduLandingPageList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new MediaBaidu())->getLandingPageList(
                $input['account_id'], $input['key_word'] ?? '');
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => ['list' => $result],
                'message' => '获取成功',
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => '获取失败' . $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 根据账号获取快手主播列表
     * @param Input $input
     * @return array
     */
    public function getKuaishouLiveUsers(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => (new MediaKuaishou())->getKuaishouLiveUsers($input->getData())
            ],
            'message' => '成功'
        ];
    }


    /**
     * 根据账号获取快手铃铛列表查询
     * @param Input $input
     * @return array
     */
    public function getKuaishouLiveUserBindList(Input $input)
    {
        $input->verify(['account_id', 'live_user_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => (new MediaKuaishou())->getKuaishouLiveUserBindList($input->getData())
            ],
            'message' => '成功'
        ];
    }

    /***
     * @param Input $input
     * @return array
     */
    public function getBilibiliNativeMaterialFileList(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaBiliBili())->getBilibiliNativeMaterialFileList($input),
            'message' => '成功'
        ];
    }

    /***
     * @param Input $input
     * @return array
     */
    public function getHotMaterialFileList(Input $input)
    {
        $input->verify(['account_id', 'platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaToutiao())->getHotMaterialFileList($input->getData()),
            'message' => '成功'
        ];
    }

    public function getToutiaoHotMaterial(Input $input)
    {
        $input->verify(['account_id']);
        (new ADServingExpandLogic())->getToutiaoHotMaterial($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /***
     * @param Input $input
     * @return array
     */
    public function getHuxuanMaterialFileList(Input $input)
    {
        $input->verify(['account_id', 'platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaTencentV3())->getHuxuanMaterialFileList($input->getData()),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getKuaishouNativeMaterialFileList(Input $input)
    {
        $input->verify(['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (new MediaKuaishou())->getNativeMaterialFileList($input->getData()),
            'message' => '成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getKuaishouNativeMaterialFile(Input $input)
    {
        $input->verify(['account_id']);
        (new ADServingExpandLogic())->getKuaishouNativeMaterialFile($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoBirdClueList(Input $input)
    {
        $input->verify(['account_id', 'platform']);
        $list = (new ADServingExpandLogic())->getToutiaoBirdClueList($input['account_id'], $input['platform'], $input['name'] ?? "");
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => $list,
            ],
            'message' => '获取成功',
        ];
    }


    /**
     * 资产原生锚点创建，新增获取下载列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoNativeDownloadUrlList(Input $input)
    {
        $input->verify(['account_id', 'platform', 'game_id']);
        $list = (new ADServingExpandLogic())->getToutiaoNativeDownloadUrlList($input['account_id'], $input['platform'], $input['game_id'], $input['site_id'] ?? "");
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'list' => $list,
            ],
            'message' => '获取成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getBaiduProjectList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $data = (new ADServingExpandLogic())->getBaiduProjectList($input['account_id'], $input['platform'], $input['game_id']);
            $response = [
                'data' => $data,
                'code' => ResponseCode::SUCCESS,
                'message' => '成功'
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getBaiduSiteOptions(Input $input)
    {
        $input->verify(['platform', 'game_id', 'account_id', 'convert_type', 'convert_source_type']);
        $data = (new ADServingExpandLogic())->getBaiduSiteOptions($input['platform'], $input['game_id'], $input['account_id'],
            $input['convert_type'], $input['deep_external_action'] ?? '', $input['convert_source_type'], $input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '获取数据成功'
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function addBaiduProject(Input $input)
    {
        $input->verify([
            'platform', 'subject', 'account_id', 'site_id', 'project_feed_name',
            'bid_mode', 'ocpc_bid']);

        $data = (new ADServingExpandLogic())->addBaiduProject($input['platform'], $input['account_id'], $input['subject'],
            $input['project_feed_name'], $input['site_id'], $input['bid_mode'], $input['ocpc_bid'],
            $input['deep_ocpc_bid'], $input['trans_type_attribute']);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '新建成功'
        ];
    }

    public function getBaiduSceneConvertList(Input $input)
    {
        $input->verify(['account_id', 'trans_from', 'trans_type']);
        try {
            $result = (new ADServingExpandLogic())->getBaiduSceneConvertList(
                $input['account_id'], $input['trans_from'], $input['trans_type']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getUcConvertMonitors(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getUcConvertMonitors(
                $input['account_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => ['list' => $result['convertMonitorTypes']]
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoStarMaterialList(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $result = (new ADServingExpandLogic())->getToutiaoStarMaterialList(
                $input['account_id'],
                $input->getData(),
                $input['page'] ?? 1,
                $input['rows'] ?? 20,
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result
            ];
        } catch (Throwable $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoStarMaterial(Input $input)
    {
        $input->verify(['account_id']);
        (new ADServingExpandLogic())->getToutiaoStarMaterial($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoStarAccountList(Input $input)
    {
        $input->verify(['company','type']);
        $result = (new ADServingExpandLogic())->getToutiaoStarAccountList($input['company'],$input['type']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result,
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoStarIndustryComponentList(Input $input)
    {
        $input->verify(['account_id']);
        $result = (new ADServingExpandLogic())->getToutiaoStarIndustryComponentList($input->getData(), $input['page_id'] ?? 0,$input['rows'] ?? 50);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result ?? [],
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getToutiaoComponentList(Input $input)
    {
        $input->verify(['platform', 'account_id']);
        $result = (new ADServingExpandLogic())->getToutiaoComponentList($input['platform'], $input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result ?? [],
            'message' => '操作成功',
        ];
    }

    public function getToutiaoGiudeVideoAccountOption()
    {
        $result = (new ADServingExpandLogic())->getToutiaoGiudeVideoAccountOption();
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result ?? [],
            'message' => '操作成功',
        ];
    }

}
