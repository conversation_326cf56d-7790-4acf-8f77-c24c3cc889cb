<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Exception\AppException;
use App\Logic\DSP\DimensionMonitorRobotLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\ADServing\DimensionMonitorRobotLogSearchParam;
use App\Param\ADServing\DimensionMonitorRobotParam;
use App\Param\ADServing\DimensionMonitorRobotSearchParam;
use App\Struct\Input;

class DimensionMonitorRobotController extends Controller
{
    /*
    * 报表指标列表
    * @CtrlAnnotation(permissions=['/ad-create/dimension-monitor-robot'])
    * @return array
    * @throws Exception
    */
    public function filterPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_DIMENSION_MONITOR_ROBOT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    public function previewDimensionMonitorRobot(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new DimensionMonitorRobotLogic())->previewDimensionMonitorRobot($input->getData()),
        ];
    }

    public function getDimensionMonitorRobot(Input $input)
    {
        try {
            $ad_logic = new DimensionMonitorRobotLogic();
            $list_data = $ad_logic->getDimensionMonitorRobot(new DimensionMonitorRobotSearchParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addDimensionMonitorRobot(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => (new DimensionMonitorRobotLogic())->addDimensionMonitorRobot(new DimensionMonitorRobotParam($input->getData())) ? '成功' : '失败',
        ];
    }

    public function deleteDimensionMonitorRobot(Input $input)
    {
        $input->verify(['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => (new DimensionMonitorRobotLogic())->deleteDimensionMonitorRobot($input['id']) ? '成功' : '失败',
        ];
    }

    public function updateDimensionMonitorRobot(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new DimensionMonitorRobotLogic();
            $data = $ad_logic->updateDimensionMonitorRobot(
                $input['id'],
                new DimensionMonitorRobotParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getDimensionMonitorRobotLog(Input $input)
    {
        try {
            $ad_logic = new DimensionMonitorRobotLogic();
            $data = $ad_logic->getDimensionMonitorRobotLog(
                new DimensionMonitorRobotLogSearchParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}
