<?php
/**
 * Created by PhpStorm.
 * User: lzh
 * Date: 2021/5/28
 * Time: 14:43
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\CheckSdkParamLogic;
use App\Logic\DSP\CronExecLogLogic;
use App\Param\CheckSdkParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\Input;
use Valitron\Validator;


class CheckSdkParamController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/advertising/game-relation'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function check(Input $input)
    {
        $input->verify(['platform', 'uuid', 'uuid_type', 'log_types', 'signature']);
        $logic  = new CheckSdkParamLogic();
        $result = $logic->check(new CheckSdkParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $result
        ];
    }

    /**
     * 枚举类配置列表
     * @param Input $input
     * @return array
     */
    public function getCustomEnumerationList(Input $input)
    {
        $logic = new CheckSdkParamLogic();
        $result = $logic->getCustomEnumerationList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $result
        ];

    }

    /**
     * 新增枚举类配置
     * @param Input $input
     * @return array
     */
    public function addCustomEnumeration(Input $input)
    {
        $input->verify(['platform', 'table', 'field', 'enum_list']);
        try {
            $logic = new CheckSdkParamLogic();
            $logic->addCustomEnumeration($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑枚举类配置
     * @param Input $input
     * @return array
     */
    public function editCustomEnumeration(Input $input)
    {
        $input->verify(['id', 'platform', 'table', 'field', 'enum_list']);
        try {
            $logic = new CheckSdkParamLogic();
            $logic->editCustomEnumeration($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '编辑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}