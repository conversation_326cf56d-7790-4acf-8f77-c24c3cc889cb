<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Exception\AppException;
use App\Logic\DSP\ADLiveCostReportLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\HttpModel\Bpm\WorkProjectApproveInst\WorkProjectApproveInstModel;
use App\Param\ADLiveCostReportFilterParam;
use App\Response\CSV;
use App\Response\File;
use App\Response\Response;
use App\Service\UserService;
use App\Struct\Input;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use App\Utils\UploadTool;
use Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use RedisException;

class ADLiveCostController extends Controller
{
    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_LIVE_COST_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取直播成本
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new ADLiveCostReportFilterParam($input->getData());

        $permission_logic = new PermissionLogic();
        $param->game_permission = $permission_logic->getLoginUserGamePermission();
        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
        $param->leader_permission = $permission_logic->getLoginUserLeaderPermission();
        $param->user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $list = (new ADLiveCostReportLogic())->getReport($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 违规扣款/加班费的录入记录
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getSpecialFundLog(Input $input)
    {
        $data = (new ADLiveCostReportLogic())->getSpecialFundLog($input->getData(), $input['page'] ?: 1, $input['rows'] ?: 50);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '获取数据成功',
        ];
    }

    /**
     * 违规扣款/加班费信息导出
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return \App\Response\File
     */
    public function exportSpecialFundLog(Input $input)
    {
        $file = (new ADLiveCostReportLogic())->exportSpecialFundLog($input->getData());
        $response = Response::File($file);
        $response->downloadName('违规扣款-加班费录入.xlsx');
        return $response;
    }

    /**
     * 违规扣款/加班费的录入
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importSpecialFund(Input $input)
    {
//        $input->verify(['approve_user']);

        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        $data = (new ADLiveCostReportLogic())->importSpecialFund($data_list, $input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
            'data' => $data
        ];
    }

    /**
     * 导入订单指标
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importOrderField(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new ADLiveCostReportLogic())->importOrderField($data_list);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
    }

    /**
     * 违规扣款/加班费的修改
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function operateSpecialFund(Input $input)
    {
        $input->verify(['order_id', 'anchor_log_id']);

        $data = (new ADLiveCostReportLogic())->operateSpecialFund($input['order_id'], $input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data,
        ];
    }

    /**
     * 获取订单的违规扣款/加班费
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function getSpecialFundByOrderId(Input $input)
    {
        $input->verify(['order_id']);

        $data = (new ADLiveCostReportLogic())->getSpecialFundByOrderId($input['order_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data ?: [],
            'message' => '获取数据成功',
        ];
    }

    /**
     * 根据订单号获取各主播价格信息
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAnchorPriceByOrderId(Input $input)
    {
        $input->verify(['live_order_ids', 'anchor_ids']);
        $list = (new ADLiveCostReportLogic())->getAnchorPriceByOrderId($input['anchor_ids'], $input['live_order_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 星图订单关联主播
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function bindAnchorPrice(Input $input)
    {
        $input->verify(['price_list']);
        $data = (new ADLiveCostReportLogic())->bindAnchorPrice($input['price_list']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data,
        ];
    }

    /**
     * 删除内部订单
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function deleteADLiveOrder(Input $input)
    {
        $input->verify(['order_ids']);
        (new ADLiveCostReportLogic())->deleteADLiveOrder($input['order_ids']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 修改订单信息
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editADLiveOrder(Input $input)
    {
        $input->verify(['order_data']);
        (new ADLiveCostReportLogic())->editADLiveOrder($input['order_data']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 订单操作日志
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getOrderOperateLog(Input $input)
    {
        $list = (new ADLiveCostReportLogic())->getOrderOperateLog($input->getData(), $input['page'] ?: 1, $input['rows'] ?: 50);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_AD_LIVE_COST_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取达人单价列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthorPriceList(Input $input)
    {
        $list = (new ADLiveCostReportLogic())->getAuthorPriceList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 导入达人单价
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importAuthorPrice(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new ADLiveCostReportLogic())->importAuthorPrice($data_list, $this->request->files['file']['tmp_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
    }

    /**
     * 获取达人返款列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthorPaybackList(Input $input)
    {
        $list = (new ADLiveCostReportLogic())->getAuthorPaybackList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 导入达人返款
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importAuthorPayback(Input $input)
    {
        $data_list = UploadTool::xlsx($this->request->files);

        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        (new ADLiveCostReportLogic())->importAuthorPayback($data_list, $this->request->files['file']['tmp_name']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
    }

    /**
     * 获取达人返款列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteAuthorPayback(Input $input)
    {
        $input->verify(["trade_number", "flow_number"]);

        (new ADLiveCostReportLogic())->deleteAuthorPayback($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'])
     * @return CSV
     */
    public function specialFundTemplate()
    {
        return new CSV('违规扣款-加班费录入sample.xlsx', 'sample/special_fund_template.xlsx');
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'])
     * @return CSV
     */
    public function orderFieldTemplate()
    {
        return new CSV('订单指标数据补充sample.xlsx', 'sample/order_field_template.xlsx');
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'])
     * @return CSV
     */
    public function authorPriceTemplate()
    {
        return new CSV('达人单价导入sample.xlsx', 'sample/author_price_template.xlsx');
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'])
     * @return CSV
     */
    public function authorPaybackTemplate()
    {
        return new CSV('达人返款sample.xlsx', 'sample/author_payback_template.xlsx');
    }

    /**
     * 冻结/解冻上个月的消耗录入
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit)
     * @return array
     * @throws RedisException
     */
    public function freezeLastMonth()
    {
        $is_freeze = RedisCache::getInstance()->get(ADLiveCostReportLogic::FREEZE_KEY);

        // 计算这个月还剩下的秒数
        $begin_date = date('Y-m-01');
        $the_month_last_day = strtotime(date('Y-m-d', strtotime("$begin_date +1 month")));
        $timeout = $the_month_last_day - time();
        $timeout = max($timeout, 1); // 最小1

        // 解冻
        if ($is_freeze == 1) {
            RedisCache::getInstance()->set(ADLiveCostReportLogic::FREEZE_KEY, 0, $timeout);
        } else {
            RedisCache::getInstance()->set(ADLiveCostReportLogic::FREEZE_KEY, 1, $timeout);
        }


        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 获取冻结状态
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'])
     * @return array
     * @throws Exception
     */
    public function getFreezeLastStatus()
    {
        $is_freeze = RedisCache::getInstance()->get(ADLiveCostReportLogic::FREEZE_KEY);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'is_freeze' => $is_freeze == 1 ? 1 : 0,
            ],
        ];
    }

    /**
     * 获取返款二维码订单列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTradeQRCodeList(Input $input)
    {
        throw new AppException('功能已更新，请刷新页面');
        $list = (new ADLiveCostReportLogic())->getTradeQRCodeList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取返款二维码订单详情
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTradeQRCodeDetails(Input $input)
    {
        throw new AppException('功能已更新，请刷新页面');
        $input->verify(['trade_no']);

        $list = (new ADLiveCostReportLogic())->getTradeQRCodeDetails($input['trade_no']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 生成返款二维码
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function genTradeQRCode(Input $input)
    {
        if (!isset($input['trade_no'])) {
            throw new AppException('功能已更新，请刷新页面');
        }

        $data = (new ADLiveCostReportLogic())->genTradeQRCode($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 取消返款二维码
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function cancelTradeQRCode(Input $input)
    {
        throw new AppException('功能已更新，请刷新页面');
        $input->verify(['trade_no']);

        (new ADLiveCostReportLogic())->cancelTradeQRCode($input['trade_no']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '取消成功',
        ];
    }

    /**
     * 获取返款二维码订单列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getSettlementList(Input $input)
    {
        $list = (new ADLiveCostReportLogic())->getSettlementList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取结算订单详情
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getSettlementDetails(Input $input)
    {
        $input->verify(['trade_no']);

        $list = (new ADLiveCostReportLogic())->getSettlementDetails($input['trade_no']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 检查结算订单
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function checkSettlementOrder(Input $input)
    {
        $data = (new ADLiveCostReportLogic())->checkSettlementOrder($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }

    /**
     * 结算
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function settlement(Input $input)
    {
        $data = (new ADLiveCostReportLogic())->settlement($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '结算成功，请到结算管理中查看详情',
            'data' => $data
        ];
    }

    /**
     * 确认结算
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='update')
     * @param Input $input
     * @return array
     */
    public function confirmSettlement(Input $input)
    {
        $input->verify(['trade_no']);

        (new ADLiveCostReportLogic())->confirmSettlement($input['trade_no']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '确认结算成功',
        ];
    }

    /**
     * 取消结算
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function cancelSettlement(Input $input)
    {
        $input->verify(['trade_no']);

        (new ADLiveCostReportLogic())->cancelSettlement($input['trade_no']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '取消成功',
        ];
    }

    /**
     * 导出结算单
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='export')
     * @param Input $input
     * @return array
     */
    public function exportSettlementOld(Input $input)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $data = [
            [
                "start_date" => "2024-09-15",
                "end_date" => "2024-09-16",
                "author_id" => "酋长",
                "author_name" => "野兽领主-酋长",
                "aweme_account" => "野兽领主-酋长",
                "game_name" => "野兽领主",
                "live_time" => "8",
                "author_price" => "100/H",
                "price" => "2688",
                "commission" => "128",
                "cost" => "800",
                "pay_back" => "1632",
                "in" => "",
                "remark" => "",
            ],
            [
                "start_date" => "2024-09-15",
                "end_date" => "2024-09-16",
                "author_id" => "酋长",
                "author_name" => "野兽领主-酋长",
                "aweme_account" => "野兽领主-酋长",
                "game_name" => "野兽领主",
                "live_time" => "8",
                "author_price" => "100/H",
                "price" => "2688",
                "commission" => "128",
                "cost" => "800",
                "pay_back" => "1632",
                "in" => "",
                "remark" => "",
            ]
        ];

        $width = [
            "A" => 20,
            "B" => 20,
            "C" => 20,
            "D" => 20,
            "E" => 20,
            "F" => 20,
            "G" => 20,
            "H" => 20,
            "I" => 20,
            "J" => 20,
            "K" => 20,
            "L" => 20,
            "M" => 20,
            "N" => 20,
        ];

        $height = [
            2 => 30,
            3 => 30,
            4 => 30,
            5 => 30,
            6 => 35,
            7 => 30,
            8 => 30,
        ];

        // 设置合并单元格
        $sheet->mergeCells('A1:N1');

        // 设置单元格内容
        $sheet->setCellValue('A1', '信息服务执行单-' . date("Ym"));
        $sheet->getStyle('A1:N1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1:N1')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A1:N1')->getFont()->setBold(true);
        $sheet->getStyle('A1:N1')->getFont()->setSize(24);

        $sheet->mergeCells('A2:N2');
        $sheet->setCellValue('A2', '付款单位：江西贪玩');
        $sheet->mergeCells('A3:N3');
        $sheet->setCellValue('A3', '发票类型：增值税专用发票⬛     增值税普通发票⬜     无⬜     税率：（      ）');
        $sheet->mergeCells('A4:N4');
        $sheet->setCellValue('A4', '开票内容：信息服务费⬛     广告费⬜     技术服务费⬜     信息费⬜');
        $sheet->mergeCells('A5:N5');
        $sheet->setCellValue('A5', '结算方式：预付款⬜     月结⬛     半月结⬜     周结⬜     其他：（）');
        $sheet->mergeCells('A6:N6');
        $sheet->setCellValue('A6', '是否承担星图提现手续费：是□     否⬛');
        $sheet->mergeCells('A7:N7');
        $sheet->setCellValue('A7', '承担云账户打款税点：（      ）');

        $sheet->getStyle('A2:N2')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A3:N3')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A4:N4')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A5:N5')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A6:N6')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A7:N7')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        // 设置单元格背景色
        $sheet->getStyle('A8:N8')->getFill()->setFillType(Fill::FILL_SOLID);
        $sheet->getStyle('A8:N8')->getFill()->getStartColor()->setARGB('FFFF00'); // 黄色背景
        // 设置字体颜色
        $sheet->getStyle('D8:E8')->getFont()->setColor(new Color(Color::COLOR_RED)); // 红色字体
        $sheet->getStyle('K8:L8')->getFont()->setColor(new Color(Color::COLOR_RED)); // 红色字体

        $sheet->setCellValue('A8', '开播日期');
        $sheet->setCellValue('B8', '主播名');
        $sheet->setCellValue('C8', '直播账号');
        $sheet->setCellValue('D8', '直播间ID');
        $sheet->setCellValue('E8', '合同游戏');
        $sheet->setCellValue('F8', '计算工资的直播市场');
        $sheet->setCellValue('G8', '单价/价格');
        $sheet->setCellValue('H8', '价格类型');
        $sheet->setCellValue('I8', '星图下单金额');
        $sheet->setCellValue('J8', '主播提现金额');
        $sheet->setCellValue('K8', '主播工资');
        $sheet->setCellValue('L8', '应返款金额/应补款金额');
        $sheet->setCellValue('M8', '实际到账金额/需补款金额');
        $sheet->setCellValue('N8', '备注（加班/扣罚理由）');

        // 设置单元格边框
        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN, // 边框样式
                    'color' => ['argb' => 'FF000000'], // 边框颜色
                ],
            ],
        ];

        $sheet->getStyle('A8:N8')
            ->applyFromArray($styleArray)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER)
            ->setWrapText(true);

        foreach ($height as $row => $value) {
            $sheet->getRowDimension($row)->setRowHeight($value);
        }

        $index = 8;
        foreach ($data as $item) {
            $index++;
            $sheet->setCellValue("A$index", $item["start_date"] . "-" . $item["end_date"]);
            $sheet->setCellValue("B$index", $item["author_id"]);
            $sheet->setCellValue("C$index", $item["author_name"]);
            $sheet->setCellValue("D$index", $item["aweme_account"]);
            $sheet->setCellValue("E$index", $item["game_name"]);
            $sheet->setCellValue("F$index", $item["live_time"]);
            $sheet->setCellValue("G$index", $item["author_price"]);
            $sheet->setCellValue("H$index", $item["price"]);
            $sheet->setCellValue("I$index", $item["price"]);
            $sheet->setCellValue("J$index", $item["commission"]);
            $sheet->setCellValue("K$index", $item["cost"]);
            $sheet->setCellValue("L$index", $item["pay_back"]);
            $sheet->setCellValue("M$index", $item["in"]);
            $sheet->setCellValue("N$index", $item["remark"]);

            $sheet->getStyle("A$index:N$index")
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                ->setVertical(Alignment::VERTICAL_CENTER)
                ->setWrapText(true);

            $sheet->getStyle("A$index:N$index")->applyFromArray($styleArray);

            $sheet->getRowDimension($index)->setRowHeight(35);
        }

        $index++;
        $sheet->mergeCells("A{$index}:G{$index}");
        $sheet->setCellValue("A{$index}", "合计");
        $sheet->getRowDimension($index)->setRowHeight(35);
        $sheet->getStyle("A$index:N$index")
            ->applyFromArray($styleArray)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        $index++;
        $sheet->mergeCells("A{$index}:G{$index}");
        $sheet->setCellValue("A{$index}", "大写人民币");
        $sheet->setCellValue("B{$index}", "零元整"); // TODO 金额大写
        $sheet->getRowDimension($index)->setRowHeight(35);
        $sheet->getStyle("A$index:N$index")
            ->applyFromArray($styleArray)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        $company_list = [
            ["A" => "付款方：", "G" => "收款方："],
            ["A" => "地址：", "G" => "地址："],
            ["A" => "开户银行：", "G" => "开户银行："],
            ["A" => "银行账号：", "G" => "银行账号："],
            ["A" => "业务经办人：", "G" => "业务经办人："],
            ["A" => "领导签字：", "G" => "签字："],
            ["A" => "盖章：", "G" => "盖章："],
        ];

        foreach ($company_list as $item) {
            $index++;
            $sheet->setCellValue("A{$index}", $item["A"]);
            $sheet->mergeCells("B{$index}:F{$index}");
            $sheet->setCellValue("G{$index}", $item["G"]);
            $sheet->mergeCells("H{$index}:N{$index}");
            $sheet->getStyle("A{$index}:N{$index}")->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

            $sheet->getRowDimension($index)->setRowHeight(35);
        }

        foreach (range('A', 'N') as $column) {
            $sheet->getColumnDimension($column)->setWidth($width[$column]);
        }

        $rightArray = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_THIN, // 边框样式
                    'color' => ['argb' => 'FF000000'], // 边框颜色
                ],
            ],
        ];

        $bottomArray = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_THIN, // 边框样式
                    'color' => ['argb' => 'FF000000'], // 边框颜色
                ],
            ],
        ];

        // 应用边框样式到单元格
        $sheet->getStyle("A1:N$index")->applyFromArray($rightArray);
        $sheet->getStyle("A1:N$index")->applyFromArray($bottomArray);

        // 保存 Excel 文件
        $writer = new Xlsx($spreadsheet);
        $file = TMP_DIR . '/settlement-' . '.xlsx';
        $writer->save($file);
        return [];
    }

    /**
     * 打印结算单
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='export')
     * @param Input $input
     * @return array
     */
    public function exportSettlement(Input $input)
    {
        $input->verify(['settle_list']);

        // 导出的查询参数是多维数组 返回下载链接好处理
        $data = (new ADLiveCostReportLogic())->exportSettlement($input['settle_list'], $input['export_type'] ?? 1, $input['file_type'] ?? 'excel');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导出成功',
            'data' => $data['download_url'],
        ];
    }

    /**
     * 获取结算订单收款人列表
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getSettlementPayeeList(Input $input)
    {
        $input->verify(['trade_nos']);

        $list = (new ADLiveCostReportLogic())->getPayeeList($input['trade_nos']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 更新结算单开票时间
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateSettlementInvoiceTime(Input $input)
    {
        $input->verify(['trade_no', 'invoice_time']);

        (new ADLiveCostReportLogic())->updateSettlementInvoiceTime($input['trade_no'], $input['invoice_time']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthorList(Input $input)
    {
        $data = (new ADLiveCostReportLogic())->getAuthorList($input->getData(), $input['page'] ?? 1, $input['rows'] ?? 50);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '获取数据成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAuthor(Input $input)
    {
        $input->verify(['author_id', 'start_date']);

        (new ADLiveCostReportLogic())->addAuthor($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editAuthor(Input $input)
    {
        $input->verify(['author_id', 'start_date']);

        (new ADLiveCostReportLogic())->editAuthor($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function test(Input $input)
    {
        $input->verify(['data']);

        $inst_id = (new WorkProjectApproveInstModel())->addMcDonaldInst($input['data']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
            'data' => $inst_id
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function test1(Input $input)
    {
        $input->verify(['data']);

        $inst_id = (new WorkProjectApproveInstModel())->addAnchorPriceChangeInst($input['data']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功',
            'data' => $inst_id
        ];
    }

    /**
     * 更新结算标识备注
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateSettlementRemark(Input $input)
    {
        $input->verify(['trade_no', 'remark']);

        (new ADLiveCostReportLogic())->updateSettlementRemark($input['trade_no'], $input['remark']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功'
        ];
    }

    /**
     * 合并打印用章申请
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function approveChapter(Input $input)
    {
        $input->verify(['trade_nos']);

        $data = (new ADLiveCostReportLogic())->approveChapter($input['trade_nos']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '请跳转BPM新建审批实例',
            'data' => $data
        ];
    }

    /**
     * 上传主播费用附件
     * @CtrlAnnotation(permissions=['/adanalysis/live-cost-report'], log_type='other')
     * @param Input $input
     * @return array
     */
    public function uploadSpecialFundAttachment(Input $input): array
    {
        $file = UploadTool::commonFile($this->request->files, 'special_fund_attachment');

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $file,
            'message' => '操作成功',
        ];
    }
}
