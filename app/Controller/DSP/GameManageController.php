<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\GameManageLogic;
use App\Struct\Input;
use App\Utils\UploadTool;
use Common\EnvConfig;

class GameManageController extends Controller
{
    /**
     * 获取游戏出包列表（游戏列表）
     * @CtrlAnnotation(permissions=['/game-manage/game'])
     *
     * @param Input $input
     *
     * @return array
     */
    public function getGameList(Input $input): array
    {
        $data = (new GameManageLogic())->getGameList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 新建游戏（游戏出包）
     * @CtrlAnnotation(permissions=['/game-manage/game'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addGame(Input $input)
    {
        (new GameManageLogic())->addGame($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => []
        ];
    }


    /**
     * 编辑游戏（游戏出包）
     * @CtrlAnnotation(permissions=['/game-manage/game'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editGame(Input $input)
    {
        $input->verify(['game_id']);
        (new GameManageLogic())->editGame($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => []
        ];
    }

    /**
     * 获取游戏出包状态
     * @CtrlAnnotation(permissions=['/game-manage/game'])
     *
     * @param Input $input
     *
     * @return array
     */
    public function getGameStatus(Input $input)
    {
        $input->verify(['game_id']);
        $data = (new GameManageLogic())->getGameStatus($input['game_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];

    }

    /**
     * 获取游戏基础信息
     * @CtrlAnnotation(permissions=['/game-manage/game'])
     * @param Input $input
     * @return array
     */
    public function getGameInfo(Input $input)
    {
        $input->verify(['game_id']);
        $data = (new GameManageLogic())->getGameInfo($input['game_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 更新游戏基础信息
     * @CtrlAnnotation(permissions=['/game-manage/game'])
     * @param Input $input
     * @return array
     */
    public function updateGameInfo(Input $input)
    {
        $input->verify(['game_id', 'popWindowSwitch']);
        (new GameManageLogic())->updateGameInfo($input['game_id'], $input['popWindowSwitch']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => []
        ];
    }

    /**
     * 获取ICON列表
     * @CtrlAnnotation(permissions=['/game-manage/game-icon'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getRootGameIconList(Input $input)
    {
        $data = (new GameManageLogic())->getRootGameIconList($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 新增根游戏icon
     * @CtrlAnnotation(permissions=['/game-manage/game-icon'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addRootGameIcon(Input $input): array
    {
        $input->verify(['platform','root_game_id', 'icon_path', 'type']);
        (new GameManageLogic())->addRootGameIcon($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '新增成功',
        ];
    }

    /**
     * 删除根游戏icon
     * @CtrlAnnotation(permissions=['/game-manage/game-icon'], log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     */
    public function delRootGameIcon(Input $input): array
    {
        $input->verify(['id']);
        (new GameManageLogic())->delRootGameIcon($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 上传icon或背景图
     * @CtrlAnnotation(permissions=['/game-manage/game-icon'])
     *
     * @param Input $input
     *
     * @return array
     */
    public function uploadIcon(): array
    {
        try {
            // 因为有icon和背景图，所以这里不错尺寸校验，改由前端提示
            //$verify_options = ['height' => ['==', 512], 'width' => ['==', 512]];
            $file_path = UploadTool::image($this->request->files, EnvConfig::GAME_ICON_DIR_NAME, ['type' => ['image/png']]);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '上传成功',
                'data' => ['path' => $file_path]
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }

        return $response;
    }

    /**
     * 获取可操作的根游戏列表
     * @return array
     */
    public function getRootGamePermission(Input $input)
    {
        $input->verify(['platform']);
        // 目前是可以看到路由权限就可以编辑所有icon
        $data = (new GameManageLogic())->getRootGamePermission($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取游戏出包路由权限
     * @return array
     */
    public function getPermission(): array
    {
        $logic = new GameManageLogic();
        $data = $logic->getGameManageRoutePermission();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 切换游戏归属人
     * @CtrlAnnotation(permissions=['/game-manage/game'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function switchGameCreator(Input $input): array
    {
        $input->verify(['platform', 'game_ids', 'new_creator']);
        (new GameManageLogic())->switchGameCreator($input['platform'], $input['game_ids'], $input['new_creator']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => []
        ];
    }
}
