<?php
/**
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-09
 * Time: 11:42
 */

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Container;
use App\Model\SqlModel\DataMedia\OdsLiveUserNameLogModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Param\MediaAccountListParam;
use App\Param\MediaMajordomoAccountListParam;
use App\Param\RankPermissionParam;
use App\Param\UserListParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\Input;
use App\Logic\DSP\PermissionLogic;
use Common\EnvConfig;

class PermissionController extends Controller
{
    /**
     * 获取平台全部负责人
     * zeda.user.leader = 1
     * zeda.user.state = 1
     * zeda.user.rank_platform = $platform
     * @return array
     */
    public function platformLeader()
    {

        $logic = new PermissionLogic();
        $list = $logic->getPlatformLeader();

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $list
        ];
    }

    /**
     * 根据平台搜索子游戏列表
     * @param Input $input
     * @return array
     */
    public function platformGame(Input $input)
    {
        $input->verify(['platform'], true);

        $logic = new PermissionLogic();
        $list = $logic->getPlatformGame($input['platform'], $input['keyword'] ?? '');

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $list
        ];
    }

    /**
     * 根据平台搜索根游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function platformRootGame(Input $input)
    {
        $input->verify(['platform'], true);

        $logic = new PermissionLogic();
        $list = $logic->getPlatformRootGame($input['platform'], $input['keyword'] ?? '');

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $list
        ];
    }

    /**
     * 根据平台搜索渠道列表
     * @param Input $input
     *
     * @return array
     */
    public function platformAgentGroup(Input $input)
    {
        $input->verify(['platform'], true);

        $logic = new PermissionLogic();
        $list = $logic->getPlatformAgentGroup($input['platform'], $input['keyword'] ?? '');

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $list
        ];
    }

    /**
     * 获取level下用户列表(包括自己和下一级用户)
     *
     * @return array
     */
    public function userOptions()
    {
        $service = new UserService();
        $list = $service->getUserOptions(self::MODULE);
        $response = [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['list' => $list]
        ];

        return $response;
    }


    /**
     * 获取可以操作的媒体账户
     * @param Input $input
     * @return array
     */
    public function mediaAccountOptions(Input $input)
    {
        $mal = new MediaAccountListParam($input);
        $list = (new PermissionLogic())->getMediaAccountOptions($mal);
        $response = [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['list' => $list]
        ];

        return $response;
    }

    /**
     * 获取可以操作的管家媒体账户
     * @param Input $input
     * @return array
     */
    public function mediaMajordomoAccountOptions(Input $input)
    {
        $param = new MediaMajordomoAccountListParam($input);
        $list = (new PermissionLogic())->getMediaMajordomoAccountOptions($param);
        $response = [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['list' => $list]
        ];

        return $response;
    }

    public function getCliqueList(Input $input)
    {
        $logic = new PermissionLogic();
        $list = $logic->getCliqueList($input['keyword'] ?? '');

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $list
        ];
    }

    public function searchOptions(Input $input)
    {
        $input->verify(['column']);
        $list = (new PermissionLogic())->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        $response = [
            'code'    => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data'    => [
                'list' => $list
            ]
        ];
        return $response;
    }

    public function breadcrumbs()
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_EIGHT) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $breadcrumbs = $service->getBreadcrumbs($my_level, self::MODULE);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['breadcrumbs' => $breadcrumbs]
        ];
    }

    public function subordinate(Input $input)
    {
        $input->verify(['page', 'rows']);
        $param = new UserListParam($input->getData());
        $logic = new PermissionService();
        [$level, $rank_id] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $param->module = self::MODULE;
        $data = $logic->getSubordinateInfo($param, $level, $rank_id);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 获取某个等级下的权限列表（路由、数据权限）
     *
     *
     * @param Input $input
     *
     * @return array
     */
    public function rankPermissionDetail(Input $input)
    {
        $input->verify(['level', 'id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);

        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getRankPermissionDetail($level, $input['id'], self::MODULE);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 获取某个等级的平台列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function platform(Input $input)
    {
        $input->verify(['level', 'rank_id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getPlatform($level, $input['rank_id']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 获取负责人分组列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function agentLeaderGroup(Input $input)
    {
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['list' => (new PermissionService())->getAgentLeaderGroup()]
        ];
    }

    /**
     * 获取某个等级的题材
     *
     * @param Input $input
     *
     * @return array
     */
    public function materialTheme(Input $input)
    {
        $input->verify(['level', 'rank_id', 'platform']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getMaterialTheme($level, $input['rank_id'], $input['platform']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 获取某个等级的题材
     *
     * @param Input $input
     *
     * @return array
     */
    public function materialThemeForOutside(Input $input)
    {
        $input->verify(['platform']);

        $service = new PermissionService();
        $data = $service->getMaterialThemeForOutside($input['platform']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 可协作岗位
     *
     * @param Input $input
     *
     * @return array
     */
    public function coPositionList(Input $input)
    {
        $input->verify(['level', 'rank_id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getCoPositionList($level, $input['rank_id'], self::MODULE);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 可协作岗位和用户
     *
     * @param Input $input
     *
     * @return array
     */
    public function coUserList(Input $input)
    {
        $input->verify(['level', 'rank_id']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $service = new PermissionService();
        $data = $service->getCoUserList($level, (int)$input['rank_id'], self::MODULE);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 获取某个等级的某个平台下面的 game_list
     *
     * @param Input $input
     *
     * @return array
     */
    public function platformPermission(Input $input)
    {
        $input->verify(['platform', 'level', 'id']);
        $level = intval($input['level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $logic = new PermissionLogic();
        $data = $logic->getPlatformPermission($level, $input['id'], $input['platform']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data,
        ];
    }

    /**
     * 根据主游戏id获取子游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function getGameListByMainGameId(Input $input)
    {
        $input->verify(['platform', 'main_game_id']);
        $keyword = $input['keyword'] ?? '';
        $option = [
            ['game.platform = ?', $input['platform']],
            ['game.main_game_id = ?', $input['main_game_id']],
        ];

        // 获取登录用户的game权限
        $game_permission = (new PermissionLogic())->getLoginUserGamePermission();
        $res = (new V2DimGameIdModel())->getListLikeGame($keyword, $option, $game_permission);
        $data = $res->map(function ($item) {
            return [
                'label' => EnvConfig::PLATFORM_MAP[$item->platform] . "-{$item->game_id}-{$item->game_name}",
                'value' => $item->game_id,
            ];
        });
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 根据根游戏id获取主包游戏列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function getMainGameListByRootGameId(Input $input)
    {
        $input->verify(['level', 'id', 'root_game_id', 'platform']);

        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $logic = new PermissionLogic();
        $data = $logic->getMainGameByRootGameId($level, $input['id'], $input['root_game_id'], $input['platform']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'list' => $data,
            ]
        ];
    }

    /**
     * 获取渠道列表（根据平台、渠道组id去获取）
     *
     * @param Input $input
     *
     * @return array
     */
    public function agentList(Input $input)
    {
        $input->verify(['level', 'id', 'agent_group_id', 'platform']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $keyword = $input['keyword'] ?? '';
        $logic = new PermissionLogic();
        $data = $logic->getAgentListByGroup($level, $input['id'], $input['agent_group_id'], $input['platform'], $keyword);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'list' => $data,
            ]
        ];
    }

    /**
     * 获取渠道列表（根据平台、id三个字段去获取）
     *
     * @param Input $input
     *
     * @return array
     */
    public function leaderAgentList(Input $input)
    {
        $input->verify(['level', 'id', 'agent_leader', 'platform']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        $level = intval($input['level']);
        if ($my_level > $level) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $keyword = $input['keyword'] ?? '';
        $logic = new PermissionLogic();
        $data = $logic->getAgentListByLeader($level, $input['id'], $input['agent_leader'], $input['platform'], $keyword);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'list' => $data,
            ]
        ];
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @return array
     * @throws \Exception
     */
    public function addRankPermission(Input $input)
    {
        $input->verify(['name', 'platform_list', 'route_list', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $param = new RankPermissionParam($input->getData());
        $param->module = self::MODULE;

        $logic->addRankPermission($param);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * 编辑
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='edit')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function editRankPermission(Input $input)
    {
        $input->verify(['name', 'platform_list', 'route_list', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $param = new RankPermissionParam($input->getData());

        $logic->editRankPermission($param);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }


    /**
     * 删除
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function delRankPermission(Input $input)
    {
        $input->verify(['id', 'level']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= $input['level']) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }


        $logic = new PermissionLogic();
        $logic->delRankPermission($input['id'], $input['level']);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 获取用户的全部权限 编辑时用到
     *
     * @param Input $input
     *
     * @return array
     */
    public function rankPlatformList(Input $input)
    {
        $input->verify(['level', 'id']);
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level > $input['level']) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }
        $logic = new PermissionLogic();
        $service = new PermissionService();

        $route_permission_list = $service->getRankRoutePermissionList($input['level'], $input['id']);
        $platform_list = $logic->getRankPlatformList($input['level'], $input['id']);
        $co_position_list = $logic->getRankCoPositionList($input['level'], $input['id']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'route_permission_list' => $route_permission_list,
                'platform_list'         => $platform_list,
                'co_position_list'      => $co_position_list,
            ],
        ];
    }

    /**
     * 获取平台层的 "卡片" 和用户列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function platformList(Input $input)
    {
        if (!UserService::isSuperManager()) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }
        $keyword = $input['keyword'] ?? '';

        $service = new PermissionService();
        $card_list = $service->getPlatformList($keyword, self::MODULE);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => [],
            ],
        ];
    }

    /**
     * 部门列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于平台  直接拿登录用户的platform_id
        if ($my_level === UserService::LEVEL_PLATFORM) {
            $platform_id = Container::getSession()->getUserDetail(self::MODULE)['platform_id'];
        } else {
            // 否则需要前端传入
            $input->verify(['platform_id']);
            $platform_id = $input['platform_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentList($platform_id);
        $user_list = $service->getUserList(UserService::LEVEL_PLATFORM, $platform_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }

    /**
     * 部门分组列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentGroupList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于部门 则不需要传入部门id 直接拿登录用户的department_id
        if ($my_level === UserService::LEVEL_DEPARTMENT) {
            $department_id = Container::getSession()->getUserDetail(self::MODULE)['department_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_id']);
            $department_id = $input['department_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentGroupList($department_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT, $department_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }


    /**
     * 部门岗位列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentGroupPositionList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于部门分组 则不需要传入部门分组id 直接拿登录用户的department_group_id
        if ($my_level === UserService::LEVEL_DEPARTMENT_GROUP) {
            $department_group_id = Container::getSession()->getUserDetail(self::MODULE)['department_group_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_group_id']);
            $department_group_id = $input['department_group_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentGroupPositionList($department_group_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT_GROUP, $department_group_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }


    /**
     * 部门Worker列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentGroupPositionWorkerList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        // 如果用户level等于部门分组 则不需要传入部门分组id 直接拿登录用户的department_group_id
        if ($my_level === UserService::LEVEL_DEPARTMENT_GROUP_POSITION) {
            $department_group_position_id = Container::getSession()->getUserDetail(self::MODULE)['department_group_position_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_group_position_id']);
            $department_group_position_id = $input['department_group_position_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentGroupPositionWorkerList($department_group_position_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT_GROUP_POSITION, $department_group_position_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }

    /**
     * 6级
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentSixList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_SIX) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        if ($my_level === UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER) {
            $department_group_position_worker_id = Container::getSession()->getUserDetail(self::MODULE)['department_group_position_worker_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_group_position_worker_id']);
            $department_group_position_worker_id = $input['department_group_position_worker_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentSixList($department_group_position_worker_id);
        $user_list = $service->getUserList(UserService::LEVEL_DEPARTMENT_GROUP_POSITION_WORKER, $department_group_position_worker_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }

    /**
     * 7级
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentSevenList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_SEVEN) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        if ($my_level === UserService::LEVEL_SIX) {
            $department_six_id = Container::getSession()->getUserDetail(self::MODULE)['department_six_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_six_id']);
            $department_six_id = $input['department_six_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentSevenList($department_six_id);
        $user_list = $service->getUserList(UserService::LEVEL_SIX, $department_six_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }


    /**
     * 8级
     *
     * @param Input $input
     *
     * @return array
     */
    public function departmentEightList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_EIGHT) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        if ($my_level === UserService::LEVEL_SEVEN) {
            $department_seven_id = Container::getSession()->getUserDetail(self::MODULE)['department_seven_id'];
        } else {
            // 否则 需要前端传入
            $input->verify(['department_seven_id']);
            $department_seven_id = $input['department_seven_id'];
        }


        $service = new PermissionService();
        $card_list = $service->getDepartmentEightList($department_seven_id);
        $user_list = $service->getUserList(UserService::LEVEL_SEVEN, $department_seven_id);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => [
                'card_list' => $card_list,
                'user_list' => $user_list,
            ]
        ];
    }

    /**
     * 获取岗位下面的用户列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function userList(Input $input)
    {
        [$my_level] = UserService::getLoginUserLevelAndRankId(self::MODULE);
        if ($my_level >= UserService::LEVEL_EIGHT) {
            return [
                'code'    => ResponseCode::ACCESS_DENY,
                'message' => '无权限访问',
            ];
        }

        $input->verify(['department_eight_id']);

        $service = new PermissionService();
        $list = $service->getUserList(UserService::LEVEL_EIGHT, $input['department_eight_id']);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => ['list' => $list]
        ];

    }

    /**
     * 同步各级部门的权限
     * @CtrlAnnotation(permissions=['/permission/member'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     * @throws \Exception
     */
    public function syncRankPermission(Input $input)
    {
        $input->verify(['sync_type', 'position_list']);

        $logic = new PermissionLogic();
        $data = $input->getData();
        $data['module'] = self::MODULE;
        $param = new RankPermissionParam($data);
        if ($param->sync_type === 'add') {
            $logic->syncAddRankPermission($param);
        } else {
            $logic->syncDeleteRankPermission($param);
        }

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '操作成功'
        ];
    }

    /**
     * 获取直播间账号
     * @param Input $input
     * @return array
     */
    public function getAwemeAccountList(Input $input)
    {
        $input->verify(['media_type']);
        $keyword = $input['keyword'] ?? '';
        $limit = $input['limit'] ?? 50;

        // 抖音企业号使用头条的抖音号
        if (in_array($input['media_type'], [MediaType::DOUYIN_ENTERPRISE, MediaType::XINGTU_STAR])) {
            $input['media_type'] = MediaType::TOUTIAO;
        }

        $data = (new OdsLiveUserNameLogModel())->getList($input['media_type'], $keyword, '', '', '', '', 1, $limit);
        if ($data['total'] > 0) {
            $data = $data['list']->pluck('aweme_account')->unique()->toArray();
        } else {
            $data = [];
        }

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 直播间对接人
     * @param Input $input
     * @return array
     */
    public function getInterfacePersonList(Input $input)
    {
        $input->verify(['platform']);
        $keyword = $input['keyword'] ?? '';
        $option = [
            ['platform = ?', $input['platform']]
        ];
        $permission_logic = new PermissionLogic();
        $agent_permission = $permission_logic->getLoginUserAllAgentLeaderPermission();
        $data = (new V2DimSiteIdModel())->getListLikeAgentLeader($keyword, $option, $agent_permission)->pluck('agent_leader')->unique()->toArray();

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $data
        ];
    }

    /**
     * 检查是否授权飞书应用
     * @return array
     */
    public function checkIsAuth(): array
    {
        (new PermissionLogic)->checkIsAuth();
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '已授权'
        ];
    }

    /**
     * 获取授权链接
     * @param Input $input
     * @return array
     */
    public function getAuthUrl(Input $input): array
    {
        $input->verify([
            'platform' => ['required'],
        ]);
        $url = (new PermissionLogic)->getAuthUrl($input['platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $url
        ];
    }
}
