<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Logic\DSP\ADAnalysisRTATargetBindLogLogic;
use App\Struct\Input;

class ADAnalysisRTATargetBindLogController extends Controller
{
    public function getList(Input $input)
    {
        $data = (new ADAnalysisRTATargetBindLogLogic)->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

}
