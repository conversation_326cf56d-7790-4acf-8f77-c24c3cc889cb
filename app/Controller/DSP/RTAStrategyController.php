<?php

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\RTAStrategyLogic;
use App\Struct\Input;

class RTAStrategyController extends Controller
{
//    public function getTargetList(Input $input)
//    {
//        $input->verify(['page', 'rows']);
//        $logic = new RTAStrategyLogic();
//        $data = $logic->getTargetList($input);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

    /**
     * 获取RTA策略列表
     * @param Input $input
     * @return array
     */
    public function getStrategyList(Input $input)
    {
        $input->verify(['page', 'rows', 'media_type']);
        $platform_permission = (new PermissionLogic())->getLoginUserPlatformList();
        $data = (new RTAStrategyLogic())->getStrategyList($input, $platform_permission);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 新增策略
     * @CtrlAnnotation(permissions=['/rta/stategy'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addStrategy(Input $input)
    {
        try {
            (new RTAStrategyLogic())->addStrategy($input);
            return [
              'code' => ResponseCode::SUCCESS,
              'message' => '策略创建成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '创建失败 ' . $e->getMessage()
            ];
        }

    }

    /**
     * 编辑策略
     * @CtrlAnnotation(permissions=['/rta/stategy'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editStrategy(Input $input)
    {
        try {
            (new RTAStrategyLogic())->editStrategy($input);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '策略修改成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败 ' . $e->getMessage()
            ];
        }
    }

//    /**
//     * 获取模型详情
//     * @param Input $input
//     * @return array
//     */
//    public function getRTAModelDetail(Input $input)
//    {
//        $input->verify(['binding_psn_model_id', 'binding_model_type', 'media_type', 'rta_strategy_type']);
//        $data = (new RTAStrategyLogic())->getRTAModelDetail(
//            $input['binding_psn_model_id'],
//            $input['binding_model_type'],
//            $input['media_type'],
//            $input['rta_strategy_type']
//        );
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

    /**
     * 获取绑定广告列表
     * @param Input $input
     * @return array
     */
    public function getTargetBindList(Input $input)
    {
        switch ($input['media_type']) {
            case MediaType::TOUTIAO:
                $input->verify(['page', 'rows', 'rta_id']);
                break;
            case MediaType::TENCENT:
                $input->verify(['page', 'rows', 'out_target_id']);
                break;
            default:
                throw new AppException('不支持此媒体类型');
        }
        $logic = new RTAStrategyLogic();
        $data = $logic->getTargetBindList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }


//    public function addTarget(Input $input)
//    {
//        switch ($input['media_type']) {
//            case MediaType::TOUTIAO:
//                $input->verify(['rta_id', 'media_type', 'divide_type', 'divide_ids', 'remark', 'target_name', 'create_source', 'start_time', 'end_time'], true);
//                break;
//            case MediaType::TENCENT:
//                $input->verify(['media_type', 'divide_type', 'divide_ids', 'target_name', 'create_source', 'start_time', 'end_time']);
//                break;
//            default:
//                throw new AppException('不支持此媒体类型');
//        }
//        $logic = new RTAStrategyLogic();
//        $data = $logic->addTarget($input);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

//    public function editTarget(Input $input)
//    {
//        switch ($input['media_type']) {
//            case MediaType::TOUTIAO:
//                $input->verify(['rta_id', 'media_type', 'divide_type', 'divide_ids', 'remark', 'create_source', 'start_time', 'end_time'], true);
//                break;
//            case MediaType::TENCENT:
//                $input->verify(['rta_id', 'out_target_id', 'media_type', 'divide_type', 'divide_ids', 'target_name', 'create_source', 'start_time', 'end_time']);
//                break;
//            default:
//                throw new AppException('不支持此媒体类型');
//        }
//        $logic = new RTAStrategyLogic();
//        $data = $logic->editTarget($input);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

    /**
     *移除某条RTA关系
     * @param Input $input
     * @return array
     */
//    public function removeTarget(Input $input)
//    {
//        switch ($input['media_type']) {
//            case MediaType::TOUTIAO:
//                $input->verify(['rta_id', 'platform', 'divide_id', 'divide_type']);
//                break;
//            case MediaType::TENCENT:
//                $input->verify(['rta_id', 'out_target_id', 'platform', 'divide_id', 'divide_type']);
//                break;
//            default:
//                throw new AppException('不支持此媒体类型');
//        }
//        $logic = new RTAStrategyLogic();
//        $data = $logic->removeTarget($input);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

//    public function removeTargetByOuterTargetId(Input $input)
//    {
//        $input->verify(['out_target_id']);
//        $logic = new RTAStrategyLogic();
//        $data = $logic->removeTargetByOuterTargetId($input['out_target_id']);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

    /**
     * 解除广告和RTA策略绑定
     * @param Input $input
     * @return array
     */
//    public function removeTargetBind(Input $input)
//    {
//        switch ($input['media_type']) {
//            case MediaType::TOUTIAO:
//                $input->verify(['rta_id', 'majordomo_account_id', 'binding_target_id', 'target_type']);
//                break;
//            case MediaType::TENCENT:
//                $input->verify(['rta_id', 'out_target_id', 'binding_target_id', 'target_type']);
//                break;
//            default:
//                throw new AppException('不支持此媒体类型');
//        }
//
//        $logic = new RTAStrategyLogic();
//        $data = $logic->removeTargetBind($input);
//        return [
//            'code' => ResponseCode::SUCCESS,
//            'message' => '',
//            'data' => $data
//        ];
//    }

    /**
     * 获取可用RTA列表
     * @param Input $input
     * @return array
     */
    public function getToutiaoRTAIdList(Input $input)
    {
        $data = (new RTAStrategyLogic())->getToutiaoRTAIdList($input['keyword']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }
}
