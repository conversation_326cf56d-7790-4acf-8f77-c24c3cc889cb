<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\ADLiveAnalysisLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\ADLiveAnalysisReportFilterParam;
use App\Service\UserService;
use App\Struct\Input;
use Exception;

class ADLiveAnalysisController extends Controller
{
    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_LIVE_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取直播分析报表数据
     * @CtrlAnnotation(permissions=['/adanalysis/live-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {

        $param = new ADLiveAnalysisReportFilterParam($input->getData());

        $permission_logic = new PermissionLogic();
        $param->game_permission = $permission_logic->getLoginUserGamePermission();
        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
        $param->user_type = $permission_logic->getLoginUserType();
        $param->user_list = (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $list = (new ADLiveAnalysisLogic())->getReport($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_AD_LIVE_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

}
