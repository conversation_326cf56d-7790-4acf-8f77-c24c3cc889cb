<?php

namespace App\Controller\DSP;

use App\Constant\PlatId;
use App\Constant\ResponseCode;
use App\Logic\DSP\GameRelationLogic;
use App\Logic\DSP\PermissionLogic;
use App\Struct\Input;
use Common\EnvConfig;

class GameRelationController extends Controller
{
    /**
     * 获取游戏关系列表
     * @CtrlAnnotation(permissions=['/game-manage/game-relation'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getList(Input $input)
    {
        $data = (new GameRelationLogic())->getList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-relation'], log_type='add')
     * 游戏关系上传APK
     *
     * @param Input $input
     *
     * @return array
     */
    public function uploadAPK(Input $input)
    {
//        throw new AppException('暂不支持上传APK');
        $input->verify(['file', 'platform', 'media_type', 'game_id', 'mix_type']);
        (new GameRelationLogic())->uploadAPKForGameId($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
        ];
    }

    /**
     * 获取游戏关系路由权限
     *
     * @return array
     */
    public function getPermission()
    {
        $data = (new PermissionLogic())->getGameRelationRoutePermission();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 新增游戏
     *
     * @param Input $input
     *
     * @return array
     */
    public function addGame(Input $input)
    {
//        throw new AppException('暂不支持新增游戏');
        $input->verify([
            'add_type' => [
                'required',
                ['in', [1, 2]],
            ],
            'platform' => [
                'required',
                ['in', array_merge(array_values(EnvConfig::PLATFORM_MAP), array_keys(EnvConfig::PLATFORM_MAP))],
            ],
            'plat_id' => [
                'required',
                ['in', array_keys(PlatId::MAP)],
            ],
            'plat_name' => [
                'required',
                ['in', array_values(PlatId::MAP)],
            ],
            'root_game_id' => [
                'required',
            ],
            'root_game_name' => [
                'required',
            ],
            'main_game_id' => [
                'required',
            ],
            'main_game_name' => [
                'required',
            ],
            'game_id' => [
                'required',
            ],
            'game_name' => [
                'required',
            ],
            'os' => [
                'required',
            ],
//            'app_name' => [
//                'required',
//            ],
//            'app_package_name' => [
//                'required',
//            ]

        ]);
        (new GameRelationLogic())->handleAddGame($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '新增成功',
        ];
    }

    /**
     * 根据game_id获取apk上传列表
     *
     * @param Input $input
     *
     * @return array
     */
    public function getAPKListByGameId(Input $input)
    {
        $input->verify(['platform', 'game_id']);
        $data = (new GameRelationLogic())->getAPKListByGameId($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取新增游戏&显示apk上传&apk列表 平台
     *
     * @param Input $input
     *
     * @return array
     */
    public function getAddGamePlatform(Input $input)
    {
        $input->verify(['keyword'], true);
        $data = (new GameRelationLogic())->getAddGamePlatform($input['keyword']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $data
            ]
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-relation'], log_type='get')
     * @param Input $input
     *
     * @return array
     */
    public function getAPKSign(Input $input)
    {
        $input->verify(['file']);
        $data = (new GameRelationLogic())->getAPKSign($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/game-manage/game-relation'], log_type='get')
     * @param Input $input
     *
     * @return array
     */
    public function canSkipCheck(Input $input)
    {
        $input->verify(['platform', 'game_id']);
        $can_skip = (new GameRelationLogic)->canSkipCheck($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => ['can_skip' => $can_skip],
        ];
    }
}
