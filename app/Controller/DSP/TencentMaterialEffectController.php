<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\MaterialEffectLogic;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\TencentMaterialEffectLogic;
use App\Param\Material\TencentMaterialEffectBs4Param;
use App\Param\Material\TencentMaterialEffectReportFilterParam;
use App\Struct\Input;

class TencentMaterialEffectController extends Controller
{
    /**
     * 获取素材效果按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_TENCENT_MATERIAL_EFFECT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_TENCENT_MATERIAL_EFFECT);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 获取素材用户列表
     * @CtrlAnnotation(permissions=['/material/tencent-effect'])
     * @return array
     */
    public function userList()
    {
        $data = (new MaterialEffectLogic())->getUserList();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/material/tencent-effect'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new TencentMaterialEffectReportFilterParam($input->getData());

        $list = (new TencentMaterialEffectLogic())->getReport($param);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 获取缩略图bs4
     * @param Input $input
     * @return array
     */
    public function getTencentBase64(Input $input)
    {
        $input->verify(['files']);
        $list = (new TencentMaterialEffectLogic())->getBase64(new TencentMaterialEffectBs4Param($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];

        return $response;
    }

    /**
     * 获取缩略图在媒体的url
     * @param Input $input
     * @return array
     */
    public function getUrl(Input $input)
    {
        $input->verify(['file_type', 'signature']);
        $data = (new TencentMaterialEffectLogic())->getUrl($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

}