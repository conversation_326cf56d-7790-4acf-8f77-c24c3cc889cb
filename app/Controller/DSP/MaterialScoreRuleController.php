<?php
/**
 * 评分规则管理
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialScoreRuleLogic;
use App\Param\Material\MaterialScoreRuleListParam;
use App\Param\Material\MaterialScoreRuleParam;

class MaterialScoreRuleController extends Controller
{

    /**
     * 获取评分规则列表
     * @CtrlAnnotation(permissions=['/material/score-rule'])
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {

        $material_score_rule_logic = new MaterialScoreRuleLogic();
        $list_data = $material_score_rule_logic->getList(new MaterialScoreRuleListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加一个规则
     * @CtrlAnnotation(permissions=['/material/score-rule'], log_type='add')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function addRule(Input $input)
    {
        $input->verify(['name', 'platform']);
        try {
            $material_score_rule_logic = new MaterialScoreRuleLogic();
            $material_score_rule_logic->addRule(new MaterialScoreRuleParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新一个规则
     * @CtrlAnnotation(permissions=['/material/score-rule'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateRule(Input $input)
    {
        $input->verify(['name', 'platform']);
        try {
            $material_score_rule_logic = new MaterialScoreRuleLogic();
            $material_score_rule_logic->updateRule($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除一个规则
     * @CtrlAnnotation(permissions=['/material/score-rule'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function deleteRule(Input $input)
    {
        $input->verify(['id']);
        try {
            $material_score_rule_logic = new MaterialScoreRuleLogic();
            $material_score_rule_logic->removeRule($input['id'], $input['platform']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * @return array
     */
    public function getAll()
    {
        $material_score_rule_logic = new MaterialScoreRuleLogic();
        $list_data = $material_score_rule_logic->getAll();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}