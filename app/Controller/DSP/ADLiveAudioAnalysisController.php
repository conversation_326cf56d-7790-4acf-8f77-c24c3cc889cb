<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\ADLiveAudioAnalysisLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\ADLiveAudioAnalysisReportFilterParam;
use App\Service\UserService;
use App\Struct\Input;
use Exception;

class ADLiveAudioAnalysisController extends Controller
{
    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_LIVE_AUDIO_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取直播话术分析数据
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {

        $param = new ADLiveAudioAnalysisReportFilterParam($input->getData());

        $permission_logic = new PermissionLogic();
        $param->game_permission = $permission_logic->getLoginUserGamePermission();
        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
        $param->user_type = $permission_logic->getLoginUserType();
        $param->user_list = (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $list = (new ADLiveAudioAnalysisLogic())->getReport($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 根据任务ID获取直播话术分析详情
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReportDetailByDemandId(Input $input)
    {
        $input->verify(['demand_id']);

        $list = (new ADLiveAudioAnalysisLogic())->getReportDetailByDemandId($input['demand_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_AD_LIVE_AUDIO_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 星图任务支付尾款
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function prepayOrder(Input $input)
    {
        $input->verify(['order_ids']);

        (new ADLiveAudioAnalysisLogic())->prepayOrder($input['order_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，请查询操作记录获取订单最近状态',
        ];
    }

    /**
     * 星图任务直播详情
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function getOrderLiveDetail(Input $input)
    {
        $input->verify(['order_ids']);

        $data = (new ADLiveAudioAnalysisLogic())->getOrderLiveDetail($input['order_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，请查询操作记录获取订单最近状态',
            'data' => $data,
        ];
    }

    /**
     * 星图任务验收
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function confirmOrder(Input $input)
    {
        $input->verify(['order_data']);

        (new ADLiveAudioAnalysisLogic())->confirmOrder($input['order_data']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，请查询操作记录获取订单最近状态',
        ];
    }

    /**
     * 星图订单投诉
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function complaintOrder(Input $input)
    {
        $input->verify(['order_ids', 'complaint_types', 'complaint_reason']);

        (new ADLiveAudioAnalysisLogic())->complaintOrder($input['order_ids'], $input['complaint_types'], $input['complaint_reason']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，可前往操作记录查看结果',
        ];
    }

    /**
     * 星图任务取消
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function cancelOrder(Input $input)
    {
        $input->verify(['order_ids']);

        (new ADLiveAudioAnalysisLogic())->cancelOrder($input['order_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，请查询操作记录获取订单最近状态',
        ];
    }

    /**
     * 星图自动验收
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function autoConfirm(Input $input)
    {
        if (!in_array(Container::getSession()->name, ['中旭未来', '黄业鸿'])) {
            throw new AppException('没有权限操作');
        }

        (new ADLiveAudioAnalysisLogic())->autoConfirm(false, Container::getSession()->name);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功，请查询操作记录',
        ];
    }

    /**
     * 星图任务支付尾款操作记录
     * @CtrlAnnotation(permissions=['/adanalysis/live-audio-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getOperateLogList(Input $input)
    {
        $list = (new ADLiveAudioAnalysisLogic())->getOperateLogList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }
}
