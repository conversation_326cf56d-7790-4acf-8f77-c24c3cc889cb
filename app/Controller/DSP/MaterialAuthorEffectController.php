<?php
/**
 * 素材效果
 * User: gzz
 * Date: 2020/04/13
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\MaterialAuthorEffectLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\Material\MaterialAuthorEffectReportFilterParam;
use App\Struct\Input;

class MaterialAuthorEffectController extends Controller
{
    /**
     * 获取基本报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_MATERIAL_AUTHOR_EFFECT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/material/author-effect'])
     * @return array
     * @throws \Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_MATERIAL_AUTHOR_EFFECT);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 获取素材用户列表
     * @CtrlAnnotation(permissions=['/material/author-effect'])
     * @return array
     */
    public function userList()
    {
        $data = (new MaterialAuthorEffectLogic())->getUserList();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/material/author-effect'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new MaterialAuthorEffectReportFilterParam($input->getData());

        $list = (new MaterialAuthorEffectLogic())->getReport($param);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }
}