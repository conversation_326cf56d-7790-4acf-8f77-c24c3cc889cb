<?php
/**
 * Created by PhpStorm.
 * User: lzh
 * Date: 2021/5/28
 * Time: 14:43
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\CronExecLogLogic;
use App\Struct\Input;

class CronExecLogController extends Controller
{
    /**
     * 数据更新日志
     * @param Input $input
     * @return array
     */
    public function getDataUpdateLogList(Input $input)
    {
        $data = (new CronExecLogLogic())->getDataUpdateLogList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取历史任务
     * @param Input $input
     * @return array
     */
    public function getHistoryTask(Input $input)
    {
        $input->verify(['target_type', 'uuid']);
        $data = (new CronExecLogLogic())->getHistoryTask($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取执行sql详情
     * @param Input $input
     * @return array
     */
    public function getExecSql(Input $input)
    {
        $input->verify(['target_id']);
        $data = (new CronExecLogLogic())->getExecSql($input['target_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据exec_id获取详情
     * @param Input $input
     * @return array
     */
    public function getUpdateLogData(Input $input)
    {
        $input->verify(['target_type', 'target_id']);
        $data = (new CronExecLogLogic())->getUpdateLogData($input['target_type'], $input['target_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}