<?php
/**
 * 素材上传任务
 * User: gzz
 * Date: 2018/11/19
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Logic\DSP\MaterialPlatformSyncLogic;
use App\Model\SqlModel\Zeda\MaterialPlatformSyncLogModel;
use App\Model\SqlModel\Zeda\MaterialPlatformSyncModel;
use App\Model\SqlModel\Zeda\MaterialPlatformSyncThemeRelationModel;
use App\Param\Material\MaterialPushMediaTaskListParam;
use App\Param\MaterialPlatformSyncLogSearchParam;
use App\Param\MaterialPlatformSyncParam;
use App\Param\MaterialPlatformSyncSearchParam;
use App\Param\MaterialPlatformSyncThemeRelationSearchParam;
use App\Struct\Input;
use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\MaterialTaskLogic;

class MaterialPlatformSyncController extends Controller
{
    /**
     * 获取任务列表
     * @param Input $input
     * @return array
     */
    public function getMaterialPlatformSyncList(Input $input)
    {
        $list_data = (new MaterialPlatformSyncModel())->getMaterialPlatformSyncList(new MaterialPlatformSyncSearchParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    public function deleteMaterialPlatformSyncLog(Input $input)
    {
        $input->verify(['id']);

        $result = (new MaterialPlatformSyncModel())->deleteSyncLog($input['id']);
        $response = [
            'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
        ];
        return $response;
    }

    /**
     * 添加任务
     * @param Input $input
     * @return array
     */
    public function addMaterialPlatformSync(Input $input)
    {
        $input->verify(['target_platform']);
        try {
            (new MaterialPlatformSyncLogic())->addMaterialPlatformSync($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * @param Input $input
     * @return array
     */
    public function getMaterialPlatformSyncThemeRelationList(Input $input)
    {
        $list_data = (new MaterialPlatformSyncThemeRelationModel())->getMaterialPlatformSyncThemeRelationList(new MaterialPlatformSyncThemeRelationSearchParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    public function deleteMaterialPlatformSyncThemeRelationLog(Input $input)
    {
        $input->verify(['id']);

        $result = (new MaterialPlatformSyncThemeRelationModel())->deleteLog($input['id']);
        $response = [
            'code' => $result ? ResponseCode::SUCCESS : ResponseCode::FAILURE,
        ];
        return $response;
    }


    /**
     * @param Input $input
     * @return array
     */
    public function addMaterialPlatformSyncThemeRelation(Input $input)
    {
        $input->verify(['target_platform']);
        try {
            (new MaterialPlatformSyncLogic())->addMaterialPlatformSyncThemeRelation($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getMaterialPlatformSyncLogList(Input $input)
    {
        $list_data = (new MaterialPlatformSyncLogModel())->getMaterialPlatformSyncLogList(new MaterialPlatformSyncLogSearchParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }
}
