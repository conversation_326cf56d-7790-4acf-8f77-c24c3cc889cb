<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Logic\DSP\MaterialAnalysisLogic;
use App\Param\Material\MaterialFileAnalyseTaskParam;
use App\Struct\Input;

class MaterialAnalysisController extends Controller
{
    /**
     * 获取词云分析
     * @CtrlAnnotation(permissions=['/adanalysis/material-analysis'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLabelCloud(Input $input)
    {
        $input->verify(["signatures"]);
        $list = (new MaterialAnalysisLogic())->getLabelCloud($input["signatures"]);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 创建素材文件分析任务
     * @CtrlAnnotation(permissions=['/adanalysis/material-analysis'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addMaterialFileAnalyseTask(Input $input)
    {
        $input->verify(['signatures', 'analyse_type', 'insight_types']);

        $logic = new MaterialAnalysisLogic();
        $param = new MaterialFileAnalyseTaskParam($input->getData());
        $data = $logic->addMaterialFileAnalyseTask($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '生成成功',
            'data' => $data
        ];
    }

    /**
     * 素材分析任务列表
     * @CtrlAnnotation(permissions=['/adanalysis/material-analysis'])
     * @param Input $input
     * @return array
     */
    public function getMaterialFileAnalyseTaskList(Input $input): array
    {
        $logic = new MaterialAnalysisLogic();
        $data = $logic->getMaterialFileAnalyseTaskList($input);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 洞悉报告信息
     * @CtrlAnnotation(permissions=['/adanalysis/material-analysis'])
     * @param Input $input
     * @return array
     */
    public function getMaterialFileAnalyseTaskInfo(Input $input): array
    {
        $input->verify(['task_id']);

        $logic = new MaterialAnalysisLogic();
        $data = $logic->getMaterialFileAnalyseTaskInfo($input['task_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }
}
