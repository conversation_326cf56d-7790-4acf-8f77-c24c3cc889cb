<?php
/**
 * 素材效果
 * User: gzz
 * Date: 2020/04/13
 * Time: 11:22
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Exception\AppException;
use App\Logic\DSP\MaterialEffectLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\Material\MaterialEffectBs4Param;
use App\Param\Material\MaterialEffectReportFilterParam;
use App\Param\Material\MaterialSignatureCommentLogList;
use App\Struct\Input;

class MaterialEffectController extends Controller
{
    /**
     * 获取基本报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_MATERIAL_EFFECT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_MATERIAL_EFFECT);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 获取素材用户列表
     * @CtrlAnnotation(permissions=['/material/effect'])
     * @return array
     */
    public function userList()
    {
        $data = (new MaterialEffectLogic())->getUserList();
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
        return $response;
    }

    /**
     * @CtrlAnnotation(permissions=['/material/effect'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new MaterialEffectReportFilterParam($input->getData());

        $list = (new MaterialEffectLogic())->getReport($param);

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 获取缩略图bs4
     * @param Input $input
     * @return array
     */
    public function getBase64(Input $input)
    {
        $input->verify(['files']);
        $list = (new MaterialEffectLogic())->getBase64(new MaterialEffectBs4Param($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }

    /**
     * 获取缩略图在媒体的url
     * @param Input $input
     * @return array
     */
    public function getUrl(Input $input)
    {
        $input->verify(['media_type', 'file_type', 'signature']);
        $data = (new MaterialEffectLogic())->getUrl($input);
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
        return $response;
    }

    /**
     * 添加评论
     * @CtrlAnnotation(permissions=['/material/effect'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addComment(Input $input)
    {
        $input->verify(['platform', 'signature', 'comment']);
        try {
            (new MaterialEffectLogic())->addComment($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新评论
     * @CtrlAnnotation(permissions=['/material/effect'], log_type='update')
     * @param Input $input
     * @return array
     */
    public function updateComment(Input $input)
    {
        $input->verify(['id', 'comment']);
        try {
            (new MaterialEffectLogic())->updateComment($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除评论
     * @CtrlAnnotation(permissions=['/material/effect'], log_type='remove')
     * @param Input $input
     * @return array
     */
    public function removeComment(Input $input)
    {
        $input->verify(['id']);
        try {
            (new MaterialEffectLogic())->removeComment($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }


    /**
     * 获取评论列表
     * @CtrlAnnotation(permissions=['/material/store', '/material/effect'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCommentList(Input $input)
    {
        $list = (new MaterialEffectLogic())->getCommentList(new MaterialSignatureCommentLogList($input));

        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
        return $response;
    }
}