<?php

namespace App\Controller\DSP;


use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\ADLiveSettleReportLogic;
use App\Logic\DSP\PermissionLogic;
use App\Param\ADLiveSettleReportFilterParam;
use App\Struct\Input;
use Exception;

class ADLiveSettleController extends Controller
{
    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_LIVE_SETTLE_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取游点代运营结算
     * @CtrlAnnotation(permissions=['/adanalysis/live-settle-report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        $param = new ADLiveSettleReportFilterParam($input->getData());

//        $permission_logic = new PermissionLogic();
//        $param->game_permission = $permission_logic->getLoginUserGamePermission();
//        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
//        $param->leader_permission = $permission_logic->getLoginUserLeaderPermission();
//        $param->user_list = UserService::isSuperManager() ? [] : (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
        $list = (new ADLiveSettleReportLogic())->getReport($param);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取游点代运营结算配置
     * @param Input $input
     * @return array
     */
    public function getSettleRateConfig()
    {
        $data = (new ADLiveSettleReportLogic())->getSettleRateConfig();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 更新游点代运营结算配置
     * @CtrlAnnotation(permissions=['/adanalysis/live-settle-report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateSettleRateConfig(Input $input)
    {
        (new ADLiveSettleReportLogic())->updateSettleRateConfig($input->update_data ?: []);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

}