<?php

namespace App\Controller\DSP;

use App\Constant\MediaType;
use App\Constant\MIMEType;
use App\Constant\ResponseCode;
use App\Logic\DSP\GameMediaSDKLogic;
use App\Param\GameMediaSDKParam;
use App\Service\MediaAD\MediaBaidu;
use App\Struct\Input;
use App\Utils\UploadTool;
use Common\EnvConfig;

class GameMediaSDKController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $param = new GameMediaSDKParam($input);
        $logic = new GameMediaSDKLogic();
        $data = $logic->getList($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    public function getDataByGame(Input $input)
    {
        $input->verify(['platform', 'media_type', 'game_id']);

        $logic = new GameMediaSDKLogic();
        $data = $logic->getDataByGame($input['platform'], $input['media_type'], $input['game_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    public function uploadLogo(): array
    {
        $verify_options = [
            'type' => [MIMEType::IMAGE_JPEG, MIMEType::IMAGE_JPG, MIMEType::IMAGE_PNG],
            'height' => ['>=', 512],
            'width' => ['>=', 512],
            'scale' => 1
        ];

        $logic = new GameMediaSDKLogic();
        $image_url = $logic->uploadSdkImage($this->request->files, EnvConfig::SDK_INFO_NAME, $verify_options);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => [
                'file_path' => $image_url
            ]
        ];
    }

    public function uploadScreenShot(): array
    {
        $verify_options = [
            'type' => [MIMEType::IMAGE_JPEG, MIMEType::IMAGE_JPG, MIMEType::IMAGE_PNG],
            'height' => ['>=', 800],
            'width' => ['>=', 480],
        ];

        $logic = new GameMediaSDKLogic();
        $image_url = $logic->uploadSdkImage($this->request->files, EnvConfig::SDK_INFO_NAME, $verify_options);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => [
                'file_path' => $image_url
            ]
        ];
    }

    public function uploadCopyright(): array
    {
        $verify_options = [
            'type' => [MIMEType::IMAGE_JPEG, MIMEType::IMAGE_JPG, MIMEType::IMAGE_PNG, MIMEType::IMAGE_BMP],
            'size' => ['<=', 10 * 1024 * 1024],
        ];

        $logic = new GameMediaSDKLogic();
        $image_url = $logic->uploadSdkImage($this->request->files, EnvConfig::SDK_INFO_NAME, $verify_options);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '上传成功',
            'data' => [
                'file_path' => $image_url
            ]
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/sdk'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addSDK(Input $input)
    {
        $input->verify(['platform', 'media_type', 'game_id', 'appid']);
        $logic = new GameMediaSDKLogic();
        $logic->addSDK(
            $input['platform'],
            $input['media_type'],
            $input['game_id'],
            $input['appid'],
            $input['app_secret'] ?? '',
            (int)$input['user_action_set_id'],
            (int)$input['account_id'],
            $input['ext'] ?? '',
            $input['account_type'] ?? ''
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/sdk'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editSDK(Input $input)
    {
        $input->verify(['platform', 'media_type', 'game_id', 'appid']);
        $logic = new GameMediaSDKLogic();
        $ret = $logic->editSDK(
            $input['platform'],
            $input['media_type'],
            $input['game_id'],
            $input['appid'],
            $input['app_secret'] ?? '',
            (int)$input['user_action_set_id'],
            (int)$input['account_id'],
            $input['ext'] ?? '',
            $input['account_type'] ?? ''
        );

        if ($ret) {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功'
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '修改失败'
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/sdk'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function getToutiaoAppSecret(Input $input)
    {
        $input->verify(['platform', 'media_type', 'game_id']);
        $logic = new GameMediaSDKLogic();
        $ret = $logic->getToutiaoAppSecret(
            $input['platform'],
            $input['media_type'],
            $input['game_id'],
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'app_secret' => $ret
            ]
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/sdk'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getBilibiliGameList(Input $input)
    {
        $input->verify(['platform', 'account_id']);
        $logic = new GameMediaSDKLogic();
        $data = $logic->getBiliBiliGameList(
            $input['platform'],
            $input['account_id'],
            $input['keyword'] ?? ''
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}
