<?php

namespace App\Controller\DSP;


use App\Constant\MediaType;
use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\DSP\PermissionLogic;
use App\Param\ADAnalysisClickhouseReportParam;
use App\Param\ADAnalysisDetailFilterParam;
use App\Param\ADAnalysisReportFilterParam;
use App\Logic\DSP\ADAnalysisLogic;
use App\Param\ADAnalysisTencentTransferParam;
use App\Param\ADAnalysisToutiaoTransferParam;
use App\Service\MediaAD\MediaTencent;
use App\Service\MediaAD\MediaToutiao;
use App\Service\UserService;
use App\Struct\Input;
use App\Utils\Helpers;
use Exception;

class ADAnalysisController extends ApiController
{

    protected $api_method = ['updateAdStatus', 'updateAdBudget', 'updateAdBid', 'getADAudience'];

    /**
     * @return array
     * @throws Exception
     */
    public function reportPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取基本报表数据
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getReport(Input $input)
    {
        if (2 === (int)$input['database_type']) {
            $param = new ADAnalysisClickhouseReportParam($input->getData());
        } else {
            $param = new ADAnalysisReportFilterParam($input->getData());
        }

        $permission_logic = new PermissionLogic();
        $param->game_permission = $permission_logic->getLoginUserGamePermission();
        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
        $param->material_effect_permission = $permission_logic->getLoginUserMaterialEffectPermission();
//        $param->material_effect_visible_permission = $permission_logic->getLoginUserMaterialEffectVisiblePermission();
        $param->user_type = $permission_logic->getLoginUserType();
        $param->user_list = (new UserService())->getUserOptions('dsp')->pluck('name')->toArray();
//        $param->handleDifferentMediaType();
        $list = (new ADAnalysisLogic())->getReport($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取基本报表详情页数据
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getADAnalysisDetail(Input $input)
    {
        $param = new ADAnalysisDetailFilterParam($input->getData());
        $permission_logic = new PermissionLogic();
        $param->game_permission = $permission_logic->getLoginUserGamePermission();
        $param->agent_permission = $permission_logic->getLoginUserAgentPermission();
        $param->handleDifferentMediaType();
        $list = (new ADAnalysisLogic())->getADAnalysisDetail($param);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 获取媒体操作日志
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLog(Input $input)
    {
        $input->verify(['platform', 'media_type']);

        $list = (new ADAnalysisLogic())->getLog($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list,
        ];
    }

    /**
     * 修改广告操作状态
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdStatus(Input $input)
    {
        $input->verify(['ad_level', 'switch', 'update']);
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADStatus($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告组数据
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdGroup(Input $input)
    {
        $input->verify(['media_type', 'ad_id', 'platform', 'ad_group_name']);
        $result = (new ADAnalysisLogic())->updateDifferentADGroup($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告计划数据
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdPlan(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADPlan($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告预算
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdBudget(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'budget');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改一级广告预算
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateFirstClassAdBudget(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'ad1_budget');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改一级广告转化出价
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateFirstClassAdCpaBid(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'ad1_cpa_bid');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改一级广告ROI系数
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateFirstClassAdRoiGoal(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateFirstClassRoiGoal($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改账户预算
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAccountBudget(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'account_budget');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告出价
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdBid(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'cpa_bid');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告深度转化出价
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateDeepBid(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'deep_bid');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改广告深度转化出价
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateROI(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateDifferentADBudgetOrBidOrROI($data, 'roi');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 获取基本报表按钮权限
     * @return array
     */
    public function getButtonPermission()
    {
        $data = (new PermissionLogic())->getButtonPermissionByRouteId(RouteID::DSP_AD_ANALYSIS_REPORT);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 删除各媒体二三级广告
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function deleteADAnalysisAD(Input $input)
    {
//        throw new AppException('此功能禁用');
        if (1 !== (int)$input['confirm_delete']) {
            throw new AppException('请确认并勾选已知媒体广告删除操作为高风险操作');
        }
        (new ADAnalysisLogic())->deleteADAnalysisAD($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功投放队列处理，请稍后在操作记录中查看结果',
        ];
    }

    /**
     * 新增数值条件监控规则绑定
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function bindCalcRules(Input $input)
    {
        try {
            $input->verify(['insert_type', 'ad2_info', 'bind_rules']);
            (new ADAnalysisLogic())->bindCalcRules($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '绑定成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 新增数值条件监控规则绑定
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function bindAccountCalcRules(Input $input)
    {
        try {
            $input->verify(['insert_type', 'account_info', 'bind_rules']);
            (new ADAnalysisLogic())->bindAccountCalcRules($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '绑定成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 新增广告三级数值条件监控规则绑定
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function bindAD3CalcRules(Input $input)
    {
        try {
            $input->verify(['insert_type', 'ad3_info', 'bind_rules']);
            (new ADAnalysisLogic())->bindAD3CalcRules($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '绑定成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 新增广告三级数值条件监控规则绑定
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function bindAD1CalcRules(Input $input)
    {
        try {
            $input->verify(['insert_type', 'ad1_info', 'bind_rules']);
            (new ADAnalysisLogic())->bindAD1CalcRules($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '绑定成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除监控规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function unbindCalcRules(Input $input)
    {
        $input->verify(['ad2_ids', 'unbind_rules']);
        try {
            (new ADAnalysisLogic())->unbind($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '解绑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除广告三级监控规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function unbindAD3CalcRules(Input $input)
    {
        $input->verify(['ad3_ids', 'unbind_rules']);
        try {
            (new ADAnalysisLogic())->unbindAD3Calc($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '解绑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除广告一级监控规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function unbindAD1CalcRules(Input $input)
    {
        $input->verify(['ad1_ids', 'unbind_rules']);
        try {
            (new ADAnalysisLogic())->unbindAD1Calc($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '解绑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除账号监控规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function unbindAccountCalcRules(Input $input)
    {
        $input->verify(['account_ids', 'unbind_rules']);
        try {
            (new ADAnalysisLogic())->unbindAccountCalc($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '解绑成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取单个ad2_id的监控规则列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCalcRulesListByAD2Id(Input $input)
    {
        $input->verify(['ad2_id']);
        $data = (new ADAnalysisLogic())->getCalcRulesByADId($input['ad2_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取单个ad1_id的监控规则列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCalcRulesListByAD1Id(Input $input)
    {
        $input->verify(['ad1_id']);
        $data = (new ADAnalysisLogic())->getCalcRulesByAD1Id($input['ad1_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取单个ad3_id的监控规则列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCalcRulesListByAD3Id(Input $input)
    {
        $input->verify(['ad3_id']);
        $data = (new ADAnalysisLogic())->getCalcRulesByAD3Id($input['ad3_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取单个账号的监控规则列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCalcRulesListByAccountId(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ADAnalysisLogic())->getCalcRulesByAccountId($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 重新绑定数值条件规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function rebindCalcRules(Input $input)
    {
        (new ADAnalysisLogic())->rebindCalcRules($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * 重新绑定三级广告数值条件规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function rebindAD3CalcRules(Input $input)
    {
        $input->verify(['data']);
        (new ADAnalysisLogic())->rebindAD3CalcRules($input['data']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * 重新绑定一级广告数值条件规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function rebindAD1CalcRules(Input $input)
    {
        $input->verify(['data']);
        (new ADAnalysisLogic())->rebindAD1CalcRules($input['data']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * 重新绑定数值条件规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function rebindAccountCalcRules(Input $input)
    {
        (new ADAnalysisLogic())->rebindAccountCalcRules($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * 单独解绑一个ad2_id的一个数值匹配规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function unbindSingleAD2IdCalcRule(Input $input)
    {
        $input->verify(['ad2_id', 'media_type', 'platform', 'calc_rule_id']);
        (new ADAnalysisLogic())->unbindSingleAD2IdCalcRule($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '解绑成功',
        ];
    }

    /**
     * 单独解绑一个ad3_id的一个数值匹配规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function unbindSingleAD3CalcRule(Input $input)
    {
        $input->verify(['ad3_id', 'media_type', 'platform', 'calc_rule_id']);
        (new ADAnalysisLogic())->unbindSingleAD3IdCalcRule($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '解绑成功',
        ];
    }

    /**
     * 单独解绑一个ad1_id的一个数值匹配规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function unbindSingleAD1CalcRule(Input $input)
    {
        $input->verify(['ad1_id', 'media_type', 'platform', 'calc_rule_id']);
        (new ADAnalysisLogic())->unbindSingleAD1IdCalcRule($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '解绑成功',
        ];
    }

    /**
     * 单独解绑一个account_id的一个数值匹配规则
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function unbindSingleAccountIdCalcRule(Input $input)
    {
        $input->verify(['account_id', 'media_type', 'platform', 'calc_rule_id']);
        (new ADAnalysisLogic())->unbindSingleAccountIdCalcRule($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '解绑成功',
        ];
    }

    /**
     * 获取标签信息
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLabelsInfo(Input $input)
    {
        $input->verify(['material_id', 'platform', 'label_type']);
        $data = (new ADAnalysisLogic())->getLabelsName($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 格式化腾讯行为兴趣值
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function formatTencentBehaviorOrInterest(Input $input)
    {
        $input->verify(['account_id', 'data']);
        $data = (new ADAnalysisLogic())->formatBehaviorOrInterest($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    public function setAPK(Input $input)
    {
        (new ADAnalysisLogic())->setAPK($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
        ];
    }

    /**
     * 获取腾讯商业兴趣中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTencentBusinessInterestCN(Input $input)
    {
        $input->verify(['account_id', 'gdt_business_interest']);
        $data = (new ADAnalysisLogic())->getTencentBusinessInterestCN($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取腾讯app行为定向中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTencentAppBehaviorCN(Input $input)
    {
        $input->verify(['account_id', 'gdt_app_behavior']);
        $data = (new ADAnalysisLogic())->getTencentAppBehaviorCN($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取腾讯联盟流量包中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTencentUnionPositionPackageCN(Input $input)
    {
        $input->verify(['account_id', 'gdt_union_position_package']);
        $data = (new ADAnalysisLogic())->getTencentUnionPositionPackageCN($input->getData(), 'UNION_PACKAGE_TYPE_INCLUDE');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取腾讯联盟流量排除包中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTencentExcludeUnionPositionPackageCN(Input $input)
    {
        $input->verify(['account_id', 'gdt_exclude_union_position_package']);
        $data = (new ADAnalysisLogic())->getTencentUnionPositionPackageCN($input->getData(), 'UNION_PACKAGE_TYPE_EXCLUDE');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取腾讯推广目标中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTencentPromotedObject(Input $input)
    {
//        $input->verify(['account_id', 'gdt_ad3_promoted_object_id']);
//        $data = (new ADAnalysisLogic())->getTencentPromotedObject($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
//            'data' => $data,
            'data' => []
        ];
    }

    /**
     * 修改定向
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAdTargeting(Input $input)
    {
        $input->verify(['media_type', 'targeting_id', 'ad_info']);
        $result = (new ADAnalysisLogic())->updateDifferentAdTargeting($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 获取头条二三级广告预览二维码链接
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getToutiaoAdDetailQRCodeUrl(Input $input)
    {
        $input->verify(['ad_level', 'account_id', 'ad_id', 'port_version']);
        $data = (new ADAnalysisLogic())->getToutiaoAdDetailQRCodeUrl($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据广告计划ID获取评论
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getCommentsByAD2Id(Input $input)
    {
        $input->verify(['ad_id']);
        $data = (new ADAnalysisLogic())->getCommentsByAD2Id($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取转账集合
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTransferSet(Input $input)
    {
        $input->verify(['account_ids', 'media_type']);
        $data = (new ADAnalysisLogic())->getTransferSet($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 根据可转帐key获取账号列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAccountIdListByTransferableKey(Input $input)
    {
        $input->verify(['transferable_key', 'account_ids', 'media_type']);
        $data = (new ADAnalysisLogic())->getAccountIdListByTransferableKey($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 刷新账号余额信息
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function refreshAccountBalance(Input $input)
    {
        $input->verify(['account_ids', 'media_type']);
        try {
            (new ADAnalysisLogic())->refreshAccountBalance($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '刷新成功',
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '刷新失败:' . $e->getMessage(),
            ];
        }
    }

    /**
     * 头条转账
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function toutiaoTransfer(Input $input)
    {
        $input->verify(['operate_type', 'transfer_info']);
        $input->operator = Container::getSession()->get('name');
        $param = new ADAnalysisToutiaoTransferParam($input->getData());
        $failed_account_ids = (new MediaToutiao())->transfer($param);
        return [
            'code' => $failed_account_ids ? ResponseCode::SUCCESS_WITH_FAILURE : ResponseCode::SUCCESS,
            'message' => $failed_account_ids ? '操作部分失败，请前往转账操作记录查看详情' : '操作成功'
        ];
    }

    /**
     * TODO::迁移到MediaAD
     * 腾讯转账
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function tencentTransfer(Input $input)
    {
        $input->verify(['operate_type', 'transfer_info']);
        $input->operator = Container::getSession()->get('name');
        $param = new ADAnalysisTencentTransferParam($input->getData());
        $failed_account_ids = (new MediaTencent())->transfer($param);
        return [
            'code' => $failed_account_ids ? ResponseCode::SUCCESS_WITH_FAILURE : ResponseCode::SUCCESS,
            'message' => $failed_account_ids ? '操作部分失败，请前往转账操作记录查看详情' : '操作成功'
        ];
    }

    /**
     * 绑定RTA策略
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function bindRTATarget(Input $input)
    {
        //绑定操作必须选同一个媒体
        switch ($input['media_type']) {
            case MediaType::MP:
            case MediaType::TENCENT:
                $input->verify(['rta_id', 'media_type', 'out_target_id', 'target_name', 'ad_info'], true);
                break;
            case MediaType::TOUTIAO:
                $input->verify(['rta_id', 'media_type', 'target_name', 'ad_info'], true);
                break;
            default :
                throw new AppException('不支持此媒体类型');
        }

//        $input['editor_id'] = Container::getSession()->get('user_id');
//        $input['editor_name'] = Container::getSession()->get('name');
//        $input['leader_permission'] = (new PermissionLogic())->getLoginUserLeaderPermission();

        $input_data = $input->getData();

        (new ADAnalysisLogic())->bindRTATarget($input_data);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功投递到绑定队列顺序执行'
        ];
    }

    /**
     * 解绑RTA策略
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function unbindRTATarget(Input $input)
    {
        $input->verify(['target_type', 'unbind_info']);

//        $input['editor_id'] = Container::getSession()->get('user_id');
//        $input['editor_name'] = Container::getSession()->get('name');
//        $input['leader_permission'] = (new PermissionLogic())->getLoginUserLeaderPermission();

        $input_data = $input->getData();
        (new ADAnalysisLogic())->unbindRTATarget($input_data);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功投递到解绑队列顺序执行'
        ];
    }

    public function getToutiaoBindRTASet(Input $input)
    {
        $input->verify(['account_ids']);
        $data = (new ADAnalysisLogic())->getToutiaoBindRTASet($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取快手定向中文
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getKuaishouTargetTranslate(Input $input)
    {
        $input->verify([
            'account_id' => ['required'],
            'target' => ['required']
        ]);

        $data = (new ADAnalysisLogic())->getKuaishouTargetTranslate($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 修改基本报表一级投放时段
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateCampaignSchedule(Input $input)
    {
        $result = (new ADAnalysisLogic())->updateCampaignSchedule($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 创建头条起量任务
     * @param Input $input
     * @return array
     */
    public function createToutiaoRaiseTask(Input $input)
    {
        $input->verify([
            "account_info" => ["required", "array"],
            "budget_mode" => ["required"],
            "allocated_budget" => ["required", "numeric"],
        ]);
        $result = (new ADAnalysisLogic())->createToutiaoRaiseTask($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 停止头条起量任务
     * @param Input $input
     * @return array
     */
    public function stopToutiaoRaiseTask(Input $input)
    {
        $input->verify([
            "update_data" => ["required", "array"],
        ]);

        $msg = (new ADAnalysisLogic())->stopToutiaoRaiseTask($input->getData());

        return [
            'code' => $msg ? ResponseCode::FAILURE : ResponseCode::SUCCESS,
            'message' => $msg ?: '任务停止成功'
        ];
    }

    /**
     * 获取起量任务数据
     * @param Input $input
     * @return array
     */
    public function getToutiaoRaiseTaskData(Input $input)
    {
        $input->verify([
            "account_id" => ["required", "numeric"]
        ]);
        $result = (new ADAnalysisLogic())->getToutiaoRaiseTaskData($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 获取广告定向
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getADAudience(Input $input)
    {
        $input->verify([
            "audience_md5" => ["required"]
        ]);
        $result = (new ADAnalysisLogic())->getADAudience($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改头条一级投放时段
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateFirstClassScheduleTime(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateFirstClassScheduleTime($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改头条二级投放时段(未启用)
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateSecondClassScheduleTime(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateSecondClassScheduleTime($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 修改腾讯3.0三级名称
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateThirdClassAdName(Input $input)
    {
        $data = $input->getData();
        $data['editor_id'] = Container::getSession()->get('user_id');
        $data['editor_name'] = Container::getSession()->get('name');
        $result = (new ADAnalysisLogic())->updateThirdClassAdName($data);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 更新广告起量
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateADRaise(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TOUTIAO]]
            ],
            'account_id' => ['required'],
            'ad_id' => ['required'],
            'platform' => ['required'],
            'raise_info' => ['required', 'array'],
            'port_version' => [
                ['in', ['2.0']]
            ]
        ]);

        try {
            (new ADAnalysisLogic())->updateDifferentADRaise($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '起量设置成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }

    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getADRaiseStatus(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TOUTIAO]]
            ],
            'ad_id' => ['required'],
            'account_id' => ['required'],
            'port_version' => [
                ['in', ['2.0']]
            ]
        ]);

        try {
            $result = (new ADAnalysisLogic())->getADRaiseStatus($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getADRaisePlan(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TOUTIAO]]
            ],
            'ad_id' => ['required'],
            'account_id' => ['required'],
            'port_version' => [
                ['in', ['2.0']]
            ]
        ]);
        if (MediaType::TOUTIAO !== (int)$input['media_type']) {
            throw new AppException('仅头条类型可使用起量');
        }

        try {
            $result = (new ADAnalysisLogic())->getADRaisePlan($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
                'data' => $result
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function stopRaisingAD(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TOUTIAO]]
            ],
            'ad_id' => ['required'],
            'account_id' => ['required'],
            'port_version' => [
                ['in', ['2.0']]
            ]
        ]);

        try {
            (new ADAnalysisLogic())->stopRaisingAD($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '关闭成功'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getADRaiseReport(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TOUTIAO]]
            ],
            'ad_id' => ['required'],
            'account_id' => ['required'],
            'port_version' => [
                ['in', ['2.0']]
            ]
        ]);

        $data = (new ADAnalysisLogic())->getADRaiseReport($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 更新腾讯广告起量
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateTencentADRaise(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TENCENT]]
            ],
//            'account_id' => ['required'],
//            'ad_id' => ['required'],
//            'platform' => ['required'],
            'raise_info' => ['required', 'array'],
            'auto_acquisition_enabled' => ['required', 'integer'],
            'auto_acquisition_budget' => ['integer'],
            'port_version' => [
                ['in', ['3.0']]
            ]
        ]);

        $result = (new ADAnalysisLogic())->updateTencentADRaise($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result
        ];
    }

    /**
     * 批量更新腾讯广告起量
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function batchUpdateTencentADRaise(Input $input): array
    {
        $input->verify([
            'media_type' => [
                'required',
                ['in', [MediaType::TENCENT]]
            ],
//            'account_id' => ['required'],
//            'ad_id' => ['required'],
//            'platform' => ['required'],
            'raise_info' => ['required', 'array'],
            'auto_acquisition_enabled' => ['required', 'integer'],
            'auto_acquisition_budget' => ['integer'],
            'port_version' => [
                ['in', ['3.0']]
            ]
        ]);

        try {
            (new ADAnalysisLogic())->batchUpdateTencentADRaise($input->getData());
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '起量设置开始异步执行,稍后请在操作记录查看成功状态'
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }

    }

    /**
     * 基本报表创建素材清理任务
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function createADAnalysisMaterialClearTask(Input $input): array
    {
        $data = (new ADAnalysisLogic())->createADAnalysisMaterialClearTask($input->getData());
        $msg = '任务创建成功';
        $code = ResponseCode::SUCCESS;
        if (true === $data['part_of_success']) {
            $msg = '部分任务创建失败，请在操作记录查看';
            $code = ResponseCode::SUCCESS_WITH_FAILURE;
        }
        return [
            'code' => $code,
            'data' => $data['return_data'],
            'message' => $msg
        ];
    }

    /**
     * 获取基本报表素材清理任务结果列表
     * @CtrlAnnotation(permissions=['/adanalysis/report'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function getClearTaskResultList(Input $input)
    {
        $input->verify([
            'account_id' => [
                'required',
                'numeric'
            ],
            'task_id' => [
                'required',
                'numeric'
            ]
        ]);

        $data = (new ADAnalysisLogic())->getClearTaskResultList($input['account_id'], $input['task_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data
        ];
    }
}