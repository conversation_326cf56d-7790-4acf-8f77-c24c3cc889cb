<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\MediaChannelAgentLogic;
use App\Struct\Input;

class MediaChannelAgentController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/advertising/media-channel-agent'], log_type='get')
     * 获取媒体渠道包列表
     * @param Input $input
     * @return array
     */
    public function getList(Input $input)
    {
        $input->verify(['media_type']);
        $data = (new MediaChannelAgentLogic())->getList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-channel-agent'], log_type='get')
     * 请求子平台获取打包状态
     * @param Input $input
     * @return array
     */
    public function checkPackageStatus(Input $input)
    {
        $input->verify(['platform', 'site_id']);
        $status = (new MediaChannelAgentLogic())->checkPackageStatus($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'status' => $status
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/media-channel-agent'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function updateAndroidChannelPackage(Input $input)
    {
        $input->verify(['media_type']);

        (new MediaChannelAgentLogic())->updateAndroidChannelPackage($input->getData());

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '更新成功'
        ];
    }
}