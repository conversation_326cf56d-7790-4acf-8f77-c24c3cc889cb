<?php
/**
 * Created by PhpStorm.
 * User: lzh
 * Date: 2022/3/24
 * Time: 10:09
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\SubscriptionServiceLogic;
use App\Struct\Input;

class SubscriptionServiceController extends Controller
{
    /**
     * @param Input $input
     * @return array
     */
    public function searchOptions(Input $input): array
    {
        $input->verify(['column']);

        $logic = new SubscriptionServiceLogic();
        $list = $logic->searchOptions($input['column'], $input['keyword'] ?? '', $input['filter'] ?? []);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => [
                'list' => $list
            ]
        ];
    }

    /**
     * @param Input $input
     */
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $list = (new SubscriptionServiceLogic())->getList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '查询成功',
            'data' => $list
        ];
    }

    /**
     * 订阅服务 新增订阅服务
     * @CtrlAnnotation(permissions=['/delivery/sub-scription-service'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function add(Input $input): array
    {
        $input->verify(['subscription_id', 'permission']);
        try {
            (new SubscriptionServiceLogic())->add($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 订阅服务 编辑订阅服务
     * @CtrlAnnotation(permissions=['/delivery/sub-scription-service'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function edit(Input $input): array
    {
        $input->verify([
            'id' => ['required'],
            'permission' => ['required'],
        ]);
        try {
            (new SubscriptionServiceLogic)->edit($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;

    }

    /**
     * 订阅服务 编辑订阅服务状态
     * @CtrlAnnotation(permissions=['/delivery/sub-scription-service'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editStatus(Input $input): array
    {
        $input->verify([
            'ids' => ['required'],
            'status' => ['required'],
        ]);
        try {
            (new SubscriptionServiceLogic)->editStatus($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;

    }

    public function checkIsAuth(): array
    {
        (new SubscriptionServiceLogic)->checkIsAuth();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '已授权'
        ];
    }

    public function getAuthUrl(Input $input): array
    {
        $input->verify([
            'platform' => ['required'],
        ]);
        $url = (new SubscriptionServiceLogic)->getAuthUrl($input['platform']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $url
        ];
    }
}