<?php
/**
 * 文案控制器
 * User: zzh
 * Date: 2020/02/21
 * Time: 15:45
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\WordLogic;
use App\Param\WordListParam;
use App\Struct\Input;

/**
 * 文案控制器
 * Class WordController
 * @package App\Controller
 */
class WordController extends Controller
{
    /**
     * 获取文案列表
     * @param Input $input
     * @return array
     */
    public function getWordContentList(Input $input)
    {
        $word_logic = new WordLogic();
        $list_data = $word_logic->getWordContentList(new WordListParam($input));
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
        return $response;
    }

    /**
     * 添加一条文案
     * @param Input $input
     * @return array
     */
    public function addWordContent(Input $input)
    {
        $input->verify(['content_text']);
        try {
            $word_logic = new WordLogic();
            $result = $word_logic->addWordContent($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '新建成功',
                'data' => [
                    'id' => $result
                ]
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 添加一批文案
     * @param Input $input
     * @return array
     */
    public function addWordList(Input $input)
    {
        $input->verify(['content_list']);
        try {
            $word_logic = new WordLogic();
            $result = $word_logic->addWordList($input);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '新建成功',
                'data' => [
                    'id' => $result
                ]
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除一个文案
     * @param Input $input
     * @return array
     */
    public function deleteWord(Input $input)
    {
        $input->verify(['id']);
        try {
            $word_logic = new WordLogic();
            $word_logic->deleteWord($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}