<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Exception\AppException;
use App\Logic\DSP\WechatAppLogic;
use App\Struct\Input;

class WechatAppController extends Controller
{
    public function getList(Input $input)
    {
        $input->verify(['page', 'rows']);
        $logic = new WechatAppLogic();
        $data = $logic->getList($input['page'], $input['rows'], $input['platform'], $input['app_id'], $input['app_secret'], $input['account_name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/advertising/wechat-app'], log_type='edit')
     * @param Input $input
     * @return array
     * @throws \Exception
     */
    public function editAppSecret(Input $input)
    {
        $input->verify(['platform', 'app_id', 'value']);
        $logic = new WechatAppLogic();
        try {
            $logic->editAppSecret($input['platform'], $input['app_id'], $input['value']);
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功',
            ];
        } catch (AppException $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage(),
            ];
        }
    }

}
