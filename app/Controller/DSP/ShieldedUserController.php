<?php

/**
 * 屏蔽用户管理
 * https://djy36bihoz.feishu.cn/docs/doccnvtuT16HQXV9om2MjFqb7we#oN7j0c
 */


namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Logic\DSP\PermissionLogic;
use App\Logic\DSP\ShieldedLogic;
use App\Struct\Input;


class ShieldedUserController extends Controller
{

    /**
     * 屏蔽关键词列表
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-user'], log_type='get')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function list(Input $input)
    {
        $input->verify(['page', 'rows']);

        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 50;
        $logic = new ShieldedLogic();
        $data = $logic->getUserList($page, $rows);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 添加屏蔽用户
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-user'], log_type='add')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function add(Input $input)
    {
        $input->verify(['create_type', 'banned_type', 'keywords']);

        $banned_type = $input['banned_type'];
        $create_type = $input['create_type'];
        $str_keywords = $input['keywords'];

        $logic = new ShieldedLogic();
        $logic->addUser($banned_type, $create_type, $str_keywords);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功'
        ];
    }

    /**
     * 删除屏蔽关键用户
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-user'], log_type='delete')
     * @param Input $input
     * @return array
     * @throws AppException
     */
    public function delete(Input $input)
    {
        $input->verify(['banned_type', 'keyword']);

        $banned_type = $input['banned_type'];
        $keyword = $input['keyword'];

        $logic = new ShieldedLogic();
        $logic->deleteUser($banned_type, $keyword);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功'
        ];
    }

    /**
     * 获取屏蔽用户权限
     *
     * @CtrlAnnotation(permissions=['/comment/shielded-user'])
     * @return array
     * @throws AppException
     */
    public function permission()
    {
        $logic = new PermissionLogic();
        $route_id = RouteID::DSP_SHIELDED_USER;

        $data = $logic->getShieldedRoutePermission($route_id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }
}
