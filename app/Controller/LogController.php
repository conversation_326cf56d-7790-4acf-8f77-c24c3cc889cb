<?php
/**
 * 贪玩数据库管理
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;


use App\Constant\ResponseCode;
use App\Struct\Input;
use App\Logic\LogLogic;
use App\Param\LogListParam;

class LogController extends Controller
{
    /**
     * 日志列表
     *
     * @param Input $input
     *
     * @return array
     * <AUTHOR>
     */
    public function list(Input $input)
    {
        $input->verify(['start_date', 'end_date', 'page', 'rows']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getList($param)
        ];
    }

    /**
     * 日志列表
     *
     * @param Input $input
     *
     * @return array
     * <AUTHOR>
     */
    public function total(Input $input)
    {
        $input->verify(['start_date', 'end_date', 'page', 'rows']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getTotal($param)
        ];
    }

    /**
     * 对外日志列表
     *
     * @param Input $input
     *
     * @return array
     * <AUTHOR>
     */
    public function getOutLogList(Input $input)
    {
        $input->verify(['start_date', 'end_date', 'page', 'rows']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getOutList($param)
        ];
    }

    public function getOutLogResponseData(Input $input)
    {
        $input->verify(['log_id']);
        return (new LogLogic())->getOutLogResponseData($input['log_id']);
    }

    public function getLogResponseData(Input $input)
    {
        $input->verify(['log_id']);
        [$detail, $request_message] = (new LogLogic())->getLogResponseData($input['log_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'detail' => $detail,
                'request_message' => $request_message
            ]
        ];
    }

    public function getLogRequestMessage(Input $input)
    {
        $input->verify(['log_id']);
        $response_data = [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => [
                'detail' => (new LogLogic())->getLogRequestMessage($input['log_id']),
            ]
        ];

        return $response_data;
    }

    public function typeStatisticInfo(Input $input)
    {
        $input->verify(['start_date', 'end_date']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getTypeStatisticInfo($param)
        ];
    }

    public function departmentStatisticInfo(Input $input)
    {
        $input->verify(['start_date', 'end_date']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getDepartmentStatisticInfo($param)
        ];
    }

    public function dateDateStatisticList(Input $input)
    {
        $input->verify(['start_date', 'end_date']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getDateStatisticList($param)
        ];
    }

    public function logPercentDetail(Input $input)
    {
        $input->verify(['start_date', 'end_date']);
        $param = new LogListParam($input->getData());
        $logic = new LogLogic();
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->getLogPercentDetail($param)
        ];
    }


}
