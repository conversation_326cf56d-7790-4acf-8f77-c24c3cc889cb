<?php


namespace App\Controller;


use App\Constant\ResponseCode;
use App\Logic\AuthorGroupLogic;
use App\Struct\Input;

class AuthorGroupController extends Controller
{
    /**
     * 添加创作者分组
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addAuthorGroup(Input $input)
    {
        $input->verify(['name', 'authors']);

        $id = (new AuthorGroupLogic())->addAuthorGroup($input['name'], $input['authors']);

        if ($id > 0) {
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功',
            ];
        } else {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => '添加失败',
            ];
        }
    }

    /**
     * 修改创作者分组
     * @CtrlAnnotation(permissions=['/admin'], log_type='edit')
     *
     * @param Input $input
     *
     * @return array
     */
    public function editAuthorGroup(Input $input)
    {
        $input->verify(['id', 'name', 'authors']);

        (new AuthorGroupLogic())->editAuthorGroup($input['id'], $input['name'], $input['authors']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * @param Input $input
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @return array
     */
    public function getAuthorGroupList(Input $input)
    {
        $page = $input['page'] ?? 1;
        $rows = $input['rows'] ?? 20;
        $data = (new AuthorGroupLogic())->authorGroupList($page, $rows);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @return array
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     */
    public function getAllAuthorList()
    {
        $data = (new AuthorGroupLogic())->getAllAuthorList();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/admin'], log_type='delete')
     * @param Input $input
     *
     * @return array
     */
    public function deleteAuthorGroup(Input $input)
    {
        $input->verify(['id']);

        (new AuthorGroupLogic())->deleteAuthorGroup($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/admin'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getAuthorGroupDetail(Input $input)
    {
        $input->verify(['id']);

        $data = (new AuthorGroupLogic())->getAuthorGroupDetail($input['id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (array)$data
        ];
    }
}
