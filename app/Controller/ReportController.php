<?php

/**
 * 标签
 * User: zz
 * Date: 2018/10/23 0026
 * Time: 9:46
 */

namespace App\Controller;


use App\Constant\ResponseCode;
use App\Response\Redirect;
use App\Logic\DMS\ReportLogic;
use App\Struct\Input;
use Common\EnvConfig;

class ReportController extends Controller
{
    protected $pass_method = ['report', 'reportQbj', 'getWeChatDailyReport'];

    public function getWeChatDailyReport(Input $input)
    {
        $input->verify(['code']);
        $logic = new ReportLogic();
        $list = $logic->getWeChatReport($input['code']);

        if ($list === false) {
            $response = [
                'code'    => ResponseCode::FAILURE,
                'message' => '请重新打开链接'
            ];
        } else {
            $response = [
                'code'    => ResponseCode::SUCCESS,
                'message' => '',
                'data'    => $list
            ];
        }

        return $response;
    }

    public function report(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $start_time = $input['state'];
        $logic = new ReportLogic();
        $open_id = $logic->getOpenId($code);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/report.html?code=' . $code;

        return new Redirect($redirect_url);
    }

    public function reportQbj(Input $input)
    {
        $input->verify(['code', 'state']);

        $code = $input['code'];
        $start_time = $input['state'];
        $config_name = 'zx_qbj';
        $logic = new ReportLogic();
        $open_id = $logic->getOpenId($code, $config_name);
        if ($open_id !== false) {
            $code = $logic->bindCode($open_id, $start_time);
        } else {
            $code = -1;
        }
        $redirect_url = EnvConfig::WX_API_DOMAIN . '/report.html?code=' . $code;

        return new Redirect($redirect_url);
    }
}
