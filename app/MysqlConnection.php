<?php
/**
 * Created by PhpStorm.
 * User: melody
 * Date: 2019-08-05
 * Time: 10:10
 */

namespace App;

use App\Exception\AppException;
use App\Model\SqlModel\Database\ZDConnection;
use App\Model\SqlModel\Database\ZDGrammar;
use App\Struct\DBService\DBServiceConnection;
use App\Struct\DBService\DBServiceConnector;
use Common\EnvConfig;
use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Container\Container;
use Illuminate\Database\Connectors\ConnectionFactory;

class MysqlConnection
{

    /**
     * @var Capsule
     */
    static private $instance;


    /**
     * @param $connection_name
     *
     * @return ZDConnection
     * <AUTHOR>
     */
    static public function getConnection($connection_name = 'default')
    {
        $config = EnvConfig::MYSQL[$connection_name] ?? null;
        if (!$config) {
            throw new AppException('找不到该数据库配置');
        }

        $database = $config['database'];
        $redirect = $config['redirect'] ?? false;
        if ($redirect) {
            $connection = static::$instance->getConnection($redirect);
        } else {
            $connection = static::$instance->getConnection($connection_name);
        }

        if (EnvConfig::DEBUG) {
            $connection->enableQueryLog();
        }

        /* @var ZDConnection $connection */
        $connection->use($database);
        return $connection;
    }

    static public function setConnection()
    {
        ZDConnection::resolverFor('mysql', function ($connection, $database, $prefix, $config) {
            $connection = new ZDConnection($connection, $database, $prefix, $config);
            $grammar = new ZDGrammar();
            $grammar->init();
            $connection->setQueryGrammar($grammar);
            return $connection;
        });

        $capsule = new Capsule;
        foreach (EnvConfig::MYSQL as $connection_name => $db_config) {
            $capsule->addConnection($db_config, $connection_name);
        }

        static::$instance = $capsule;
    }

    /**
     * 建立MySQL连接
     * 如果ATTR_PERSISTENT设置为true，则只能建立起一个长连接，如果需要建立起多个长连接，则将ATTR_PERSISTENT设置为不重复的非数字字符串
     *
     * @param array $config
     * @param boolean $use_new
     * @return ZDConnection
     * @throws \Exception
     */
    static public function createConnection(array $config, bool $use_new = true)
    {
        if ($use_new) {
            $options = $config['options'] ?? [];
            if (isset($options[\PDO::ATTR_PERSISTENT]) && $options[\PDO::ATTR_PERSISTENT] === true) {
                $options[\PDO::ATTR_PERSISTENT] = uniqid();
            }
            $config['options'] = $options;
        }

        ZDConnection::resolverFor('mysql', function ($connection, $database, $prefix, $config) {
            $connection = new ZDConnection($connection, $database, $prefix, $config);
            $grammar = new ZDGrammar();
            $grammar->init();
            $connection->setQueryGrammar($grammar);
            return $connection;
        });

        $container = new Container();
        $factor = new ConnectionFactory($container);
        return $factor->make($config);
    }

    static public function setDBServiceConnection()
    {
        ZDConnection::resolverFor('dbservice', function ($connection, $database, $prefix, $config) {
            $connection = new DBServiceConnection($connection, $database, $prefix, $config);
            $grammar = new ZDGrammar();
            $grammar->init();
            $connection->setQueryGrammar($grammar);
            return $connection;
        });
        $capsule = new Capsule;
        $container = $capsule->getContainer();
        $container->instance('db.connector.dbservice', new DBServiceConnector);
        foreach (EnvConfig::MYSQL as $connection_name => $db_config) {
            $db_config['driver'] = 'dbservice';
            $capsule->addConnection($db_config, $connection_name);
        }

        static::$instance = $capsule;
    }

    private function __construct()
    {
    }

    private function __clone()
    {

    }

    private function __wakeup()
    {

    }

    static public function getManager()
    {
        return self::$instance->getDatabaseManager();
    }

}