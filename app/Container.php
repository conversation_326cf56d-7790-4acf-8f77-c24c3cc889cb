<?php

namespace App;

use App\Struct\Session;
use App\Struct\WebSocketManager;
use App\Utils\Snowflake;
use ReflectionMethod;
use Swoole\Coroutine;
use Swoole\Http\Request;
use Swoole\WebSocket\Server;

class Container
{
    static private $pool = [];

    static private $server;

    static private $ws_manager;

    static private $ctrl_annotations = [];

    /**
     * @var Snowflake
     */
    static private $snowflake = [];

    static public function setServer(Server $server)
    {
        self::$server = $server;
    }

    /**
     * @return Server
     * <AUTHOR>
     */
    static public function getServer()
    {
        return self::$server;
    }


    static public function setSnowflake(Snowflake $snowflake)
    {
        self::$snowflake[self::$server->worker_id] = $snowflake;
    }

    /**
     * 获取当前work_id内的唯一id
     *
     * @return int id
     * <AUTHOR>
     */
    static public function getUUID()
    {
        /** @var Snowflake $snowflake */
        $snowflake = self::$snowflake[self::$server->worker_id];
        return $snowflake->getId();
    }

    static public function setSession(Session $session)
    {
        self::$pool[self::$server->worker_id][Coroutine::getuid()]['session'] = $session;
    }

    static public function setRequest(Request $request)
    {
        self::$pool[self::$server->worker_id][Coroutine::getuid()]['request'] = $request;
    }

    /**
     * @return Session
     * <AUTHOR>
     */
    static public function getSession()
    {
        return self::$pool[self::$server->worker_id][Coroutine::getuid()]['session'];
    }


    /**
     * @return Request | null
     * <AUTHOR>
     */
    static public function getRequest()
    {
        return self::$pool[self::$server->worker_id][Coroutine::getuid()]['request'] ?? null;
    }

    /**
     * <AUTHOR>
     */
    static public function clearSession()
    {
        unset(self::$pool[self::$server->worker_id][Coroutine::getuid()]);
    }

    /**
     * <AUTHOR>
     */
    static public function clearAll()
    {
        self::$pool = [];
    }

    static public function setWSManager($ws_manager)
    {
        self::$ws_manager = $ws_manager;
    }

    /**
     * @return WebSocketManager
     * <AUTHOR>
     */
    static public function getWSManager()
    {
        return self::$ws_manager;
    }

    static public function setCtrlAnnotation()
    {
        $ctrl_annotations = [];

        $file_module['common'] = scandir(ROOT_DIR . '/app/Controller');
        foreach ($file_module['common'] as $key => $filename) {
            // 跳过两个特殊目录   continue跳出循环
            if ($filename == "." || $filename == "..") {
                continue;
            } elseif (is_dir(ROOT_DIR . '/app/Controller/' . $filename)) {
                $file_module[$filename] = scandir(ROOT_DIR . '/app/Controller/' . $filename);
                unset($file_module['common'][$key]);
            }
        }
        foreach ($file_module as $module => $filenames) {
            foreach ($filenames as $filename) {
                // 跳过两个特殊目录   continue跳出循环
                if ($filename == "." || $filename == "..") {
                    continue;
                }
                //截取文件名，我只需要文件名不需要后缀;然后存入数组。如果你是需要后缀直接$v即可
                $file = substr($filename, 0, strpos($filename, "."));
                if ($module === 'common') {
                    $class_name = '\\App\\Controller\\' . $file;
                } else {
                    $class_name = '\\App\\Controller\\' . $module . '\\' . $file;

                }
                $reflectionClass = new \ReflectionClass($class_name);
                $methods = $reflectionClass->getMethods(ReflectionMethod::IS_PUBLIC);
                foreach ($methods as $method) {
                    $annotation_matched = preg_match('/@CtrlAnnotation\s*\((.*)\)/i', $method->getDocComment(), $annotation_matches);
                    if ($annotation_matched === 1) {
                        $controller_name = $method->class;
                        $ctrl_annotations[$controller_name][$method->name] = [
                            'permissions' => [],
                            'log_type' => '',
                            'throttle' => 0,
                        ];
                        $permission_matched = preg_match('/permissions\s*=\s*\[(.*)]/i', $annotation_matches[1], $permission_matches);
                        if ($permission_matched === 1) {
                            $permissions = explode(',', $permission_matches[1]);
                            $ctrl_annotations[$controller_name][$method->name]['permissions'] = array_map(function ($item) {
                                return trim($item, '\'" ');
                            }, $permissions);
                        }
                        $log_type_matched = preg_match('/log_type\s*=\s*["\'](.*?)["\']/i', $annotation_matches[1], $log_type_matches);
                        if ($log_type_matched === 1) {
                            $ctrl_annotations[$controller_name][$method->name]['log_type'] = $log_type_matches[1];
                        }

                        $throttle_matched = preg_match('/throttle\s*=\s*(\d+)/i', $annotation_matches[1], $throttle_matches);
                        if ($throttle_matched === 1) {
                            $ctrl_annotations[$controller_name][$method->name]['throttle'] = (int)$throttle_matches[1];
                        }
                    }
                }
            }
        }
        self::$ctrl_annotations = $ctrl_annotations;
    }

    /**
     * @return array [
     *     'controller_name' => [
     *         'method_name' => [
     *              'permissions' => [],
     *              'log_type' => '',
     *         ]
     *     ]
     * ]
     * <AUTHOR>
     */
    static public function getCtrlAnnotation()
    {
        return self::$ctrl_annotations;
    }
}