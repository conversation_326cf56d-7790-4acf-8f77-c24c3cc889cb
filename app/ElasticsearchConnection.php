<?php
namespace App;

use Common\EnvConfig;
use Elasticsearch\Client;
use App\Exception\AppException;
use Elasticsearch\ClientBuilder;

/**
 * Class ElasticsearchConnection
 */
class ElasticsearchConnection
{
    /**
     * @var Client
     */
    static private $instance;

    /**
     * @param string $connection_name
     * @return Client
     */
    static public function getConnection(string $connection_name = 'default'): Client
    {
        $config = EnvConfig::ELASTICSEARCH[$connection_name] ?? null;
        if (!$config) {
            throw new AppException('找不到该es配置');
        }

        $hosts = $config['host'] ?? null;
        if (!$hosts) {
            throw new AppException('es配置错误');
        }

        return self::$instance[$connection_name];
    }

    /**
     * @link https://www.elastic.co/guide/en/elasticsearch/client/php-api/current/connection_pool.html#_simpleconnectionpool
     */
    static public function setConnection()
    {
        foreach (EnvConfig::ELASTICSEARCH as $connection_name => $config) {
            static::$instance[$connection_name] = ClientBuilder::create()
                ->setHosts([$config])
                ->includePortInHostHeader(true)
                ->build();
        }
    }

    private function __construct()
    {
    }

    private function __clone()
    {

    }

    private function __wakeup()
    {

    }

}