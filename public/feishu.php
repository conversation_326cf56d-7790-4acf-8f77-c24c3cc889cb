<?php
/**
 * Created by PhpStorm.
 * User: lzh
 * Date: 2022/4/1
 * Time: 15:33
 */

use App\Constant\MediaType;
use App\Model\SqlModel\Zeda\MediaDeveloperAccountModel;
use App\MysqlConnection;
use App\Service\FeiShuService;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';

date_default_timezone_set('PRC');

$from_json = file_get_contents('php://input');
$request = array_merge($_GET, $_POST);
Helpers::getLogger('feishu')->info("request data", ['from_data' => $from_json, 'request' => $request]);

if (empty($from_json)) {
    exit('非法访问feiShu');
}

$from_data = json_decode($from_json, true);
if (!isset($from_data['encrypt'])) {
    exit('请求参数出错');
}

$app_id = $_GET['app_id'] ?? '';

if (!isset($app_id)) {
    exit("请求参数出错 app_id is require");
}

// 设置MySQL连接对象
MysqlConnection::setConnection();

//查询encrypt_key
$media_developer_account_model = new MediaDeveloperAccountModel();
$app_info = $media_developer_account_model->getAppInfo(MediaType::FEISHU, $app_id);
$ext_info = json_decode($app_info->ext, true);
$encrypt_key = $ext_info['encrypt_key'] ?? '';
if (empty($encrypt_key)) {
    Helpers::getLogger('feishu')->error("{$app_id} encrypt_key no exist", $from_data);
    exit("请求参数出错 encrypt_key no exist");
}

$encrypt = base64_decode($from_data['encrypt']);
$iv = substr($encrypt, 0, 16);
$encrypted_event = substr($encrypt, 16);

$decrypted_json = openssl_decrypt($encrypted_event, 'AES-256-CBC', hash('sha256', $encrypt_key, true), OPENSSL_RAW_DATA, $iv);
$data = json_decode($decrypted_json, true);

if (isset($data['challenge'])) {
    exit(json_encode(['challenge' => $data['challenge']]));
}

if (isset($data['event'])) {
    (new FeiShuService())->handleEvent($data);
    exit(json_encode(['success']));
}