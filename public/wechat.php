<?php

use App\MysqlConnection;
use App\Service\WechatService;
use App\Utils\Wechat\ErrorCode;
use App\Utils\Wechat\WXBizMsgCrypt;
use Common\EnvConfig;

require dirname(__DIR__) . '/common/init.php';

date_default_timezone_set('PRC');

//if (isset($_GET["signature"])) {
//    $signature = $_GET["signature"];
//    $timestamp = $_GET["timestamp"];
//    $nonce = $_GET["nonce"];
//
//    $token = EnvConfig::WECHAT['qingbaoju']['token'];
//    $tmpArr = array($token, $timestamp, $nonce);
//    sort($tmpArr, SORT_STRING);
//    $tmpStr = implode($tmpArr);
//    $tmpStr = sha1($tmpStr);
//
//    if ($tmpStr == $signature) {
//        exit($_GET['echostr']);
//    } else {
//        exit('false');
//    }
//}

$from_xml = file_get_contents('php://input');

if (empty($from_xml)) {
    exit('非法访问');
}

$msg_sign = $_GET['msg_signature'];
$timestamp = $_GET['timestamp'];
$nonce = $_GET['nonce'];

//gzh_account是配置 URL 的时候带的公众号账号参数 默认是qingbaoju
$gzh_account = $_GET['gzh_account'] ?? 'qingbaoju';

if (!isset(EnvConfig::WECHAT[$gzh_account])) {
    exit('非法访问');
}

$pc = new WXBizMsgCrypt(EnvConfig::WECHAT[$gzh_account]['token'], EnvConfig::WECHAT[$gzh_account]['encoding_aes_key'], EnvConfig::WECHAT[$gzh_account]['appid']);
// 第三方收到公众号平台发送的消息
$msg = '';
$err_code = $pc->decryptMsg($msg_sign, $timestamp, $nonce, $from_xml, $msg);
if ($err_code !== ErrorCode::$OK) {
    exit('非法请求');
}
$data = (array)simplexml_load_string($msg, 'SimpleXMLElement', LIBXML_NOCDATA);
if (!isset($data['MsgType'])) {
    echo '非法回调';
    die;
}

// 设置MySQL连接对象
MysqlConnection::setConnection();

if ($data['MsgType'] === 'event') {
    $return_msg = (new WechatService())->handleEvent($data);
    if ($return_msg !== 'success') {
        $encrypt_msg = '';
        $err_code = $pc->encryptMsg($return_msg, $timestamp, $nonce, $encrypt_msg);
        if ($err_code === ErrorCode::$OK) {
            exit($encrypt_msg);
        }
    }
}

if ($gzh_account === 'zx_qbj' && ($data['MsgType'] === 'text' || $data['MsgType'] === 'voice')) {
    $time = date('Y-m-d H:i:s', time());
    if ($data['MsgType'] === 'text') {
        file_put_contents('/tmp/test.log', "$time 处理文本消息\n", FILE_APPEND);
        $return_msg = (new WechatService())->handleText($data);
    } else {
        file_put_contents('/tmp/test.log', "$time 处理语音消息\n", FILE_APPEND);
        $return_msg = (new WechatService())->handleVoice($data);
    }


    if ($return_msg !== 'success') {
        $encrypt_msg = '';
        $err_code = $pc->encryptMsg($return_msg, $timestamp, $nonce, $encrypt_msg);
        if ($err_code === ErrorCode::$OK) {
            exit($encrypt_msg);
        }
    }
}


exit('success');
