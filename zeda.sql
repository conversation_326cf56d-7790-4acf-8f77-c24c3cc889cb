/*
 Navicat Premium Data Transfer

 Source Server         : test-server
 Source Server Type    : MySQL
 Source Server Version : 50712
 Source Host           : *************:3306
 Source Schema         : zeda

 Target Server Type    : MySQL
 Target Server Version : 50712
 File Encoding         : 65001

 Date: 21/08/2019 09:44:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department` VALUES (1, 1, '测试部门1', 1542287361, 1565234540);
INSERT INTO `department` VALUES (2, 1, '测试部门2', 1565234485, 1565234485);
INSERT INTO `department` VALUES (3, 1, '测试部门4', 1565251643, 1565251909);
INSERT INTO `department` VALUES (4, 1, '测试部门3', 1565251680, 1565251680);
INSERT INTO `department` VALUES (5, 1, '运营部', 1565699837, 1565699878);

-- ----------------------------
-- Table structure for department_group
-- ----------------------------
DROP TABLE IF EXISTS `department_group`;
CREATE TABLE `department_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台ID',
  `department_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属部门ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department_group
-- ----------------------------
INSERT INTO `department_group` VALUES (1, 1, 1, '测试部门分组1', 1565234861, 1565234942);
INSERT INTO `department_group` VALUES (3, 1, 1, '测试部门分组2', 1565251942, 1565251950);
INSERT INTO `department_group` VALUES (4, 1, 5, '负责组', 1565699854, 1565699864);
INSERT INTO `department_group` VALUES (5, 1, 5, 'test组', 1565702135, 1565702135);
INSERT INTO `department_group` VALUES (6, 1, 3, '测试部门分组3', 0, 0);

-- ----------------------------
-- Table structure for department_group_position
-- ----------------------------
DROP TABLE IF EXISTS `department_group_position`;
CREATE TABLE `department_group_position`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台ID',
  `department_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属部门ID',
  `department_group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属部分分组id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '岗位名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门分组岗位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department_group_position
-- ----------------------------
INSERT INTO `department_group_position` VALUES (2, 1, 1, 1, '修改岗位1', 1565251958, 1565251975);
INSERT INTO `department_group_position` VALUES (3, 1, 1, 1, '测试岗位1', 1565251967, 1565251967);
INSERT INTO `department_group_position` VALUES (4, 1, 5, 4, '运营负责人', 1565699923, 1565699926);
INSERT INTO `department_group_position` VALUES (5, 1, 5, 4, '运营组员', 1565700410, 1565700410);
INSERT INTO `department_group_position` VALUES (6, 1, 5, 5, 'test岗位', 1565702149, 1565702149);

-- ----------------------------
-- Table structure for dmp_device_task
-- ----------------------------
DROP TABLE IF EXISTS `dmp_device_task`;
CREATE TABLE `dmp_device_task`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `dimension_filter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `behavior_filter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `start_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `end_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `operator_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `state` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未开始1计算中2已完成3已过期4已终止',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dmp_device_task
-- ----------------------------
INSERT INTO `dmp_device_task` VALUES (1, 'dsfs', '{}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1564588800, 1564675199, 1, 2, 1564658256, 1564658256);
INSERT INTO `dmp_device_task` VALUES (2, '阿三放大士大夫', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564729497, 1564735943);
INSERT INTO `dmp_device_task` VALUES (3, '撒发生大', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564675200, 1564761599, 1, 1, 1564736103, 1564738112);
INSERT INTO `dmp_device_task` VALUES (4, '啊士大夫', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564675200, 1564761599, 1, 1, 1564738310, 1564738310);
INSERT INTO `dmp_device_task` VALUES (5, '啊士大夫', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564675200, 1564761599, 1, 1, 1564738310, 1564738310);
INSERT INTO `dmp_device_task` VALUES (6, '啊士大夫', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564675200, 1564761599, 1, 1, 1564738310, 1564738310);
INSERT INTO `dmp_device_task` VALUES (7, '撒旦发', '{}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564738310, 1564738310);
INSERT INTO `dmp_device_task` VALUES (8, 'test1740', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"官网\",\"短信\"],\"condition\":\"in\"},\"root_game_name\":{\"column\":\"root_game_name\",\"name\":\"根游戏\",\"type\":\"string\",\"value\":[\"贪玩蓝月\"],\"condition\":\"in\"}}', '{\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1564675200, 1564761599, 1, 1, 1564738778, 1564738778);
INSERT INTO `dmp_device_task` VALUES (9, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564738778, 1564738778);
INSERT INTO `dmp_device_task` VALUES (10, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564738778, 1564738778);
INSERT INTO `dmp_device_task` VALUES (11, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564738778, 1564738778);
INSERT INTO `dmp_device_task` VALUES (12, 'test1826', '{\"agent_group_name\":{\"column\":\"agent_group_name\",\"name\":\"渠道组\",\"type\":\"string\",\"value\":[\"竞价投放\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"神泣之光\",\"倩女西游\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_reg_device\":{\"column\":\"new_reg_device\",\"name\":\"新注册设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"game_sum_pay_money\":{\"column\":\"game_sum_pay_money\",\"name\":\"设备付费金额(元)\",\"type\":\"int\",\"value\":[\"30\"],\"condition\":\"gt\"}}', 1561910400, 1562515199, 1, 1, 1564738778, 1564738778);
INSERT INTO `dmp_device_task` VALUES (13, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564745140, 1564745140);
INSERT INTO `dmp_device_task` VALUES (14, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564745140, 1564745140);
INSERT INTO `dmp_device_task` VALUES (15, '撒旦发', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1564675200, 1564761599, 1, 1, 1564745140, 1564745140);
INSERT INTO `dmp_device_task` VALUES (16, 'sdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 1, 1564990153, 1564990153);
INSERT INTO `dmp_device_task` VALUES (17, 'sdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 1, 1564990153, 1564990153);
INSERT INTO `dmp_device_task` VALUES (18, 'sdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 0, 1564994290, 1564994290);
INSERT INTO `dmp_device_task` VALUES (19, 'sdfasdfa', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 1, 1564994358, 1564994358);
INSERT INTO `dmp_device_task` VALUES (20, 'asdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 1, 1564994485, 1564994485);
INSERT INTO `dmp_device_task` VALUES (21, 'zxcvzxc', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1564934400, 1565020799, 1, 1, 1564994712, 1564994712);
INSERT INTO `dmp_device_task` VALUES (22, 'asdfsad', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 1, 1565074114, 1565074114);
INSERT INTO `dmp_device_task` VALUES (23, 'sadfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 1, 1565074165, 1565074165);
INSERT INTO `dmp_device_task` VALUES (24, 'sadfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 1, 1565074237, 1565074237);
INSERT INTO `dmp_device_task` VALUES (25, 'sadfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\",\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\",\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\",\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1565020800, 1565107199, 1, 1, 1565074293, 1565074293);
INSERT INTO `dmp_device_task` VALUES (26, 'safa', '{}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 1, 1565074357, 1565074357);
INSERT INTO `dmp_device_task` VALUES (27, 'safa', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"官网\",\"短信\"],\"condition\":\"in\"},\"root_game_name\":{\"column\":\"root_game_name\",\"name\":\"根游戏\",\"type\":\"string\",\"value\":[\"贪玩蓝月\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1565020800, 1565107199, 1, 1, 1565074391, 1565074391);
INSERT INTO `dmp_device_task` VALUES (28, 'asdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 0, 1565084212, 1565084212);
INSERT INTO `dmp_device_task` VALUES (29, 'asdfasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"}}', 1565020800, 1565107199, 1, 1, 1565084212, 1565084212);
INSERT INTO `dmp_device_task` VALUES (30, 'asdfsad', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1565020800, 1565107199, 1, 1, 1565084554, 1565084554);
INSERT INTO `dmp_device_task` VALUES (31, 'asdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1565020800, 1565107199, 1, 1, 1565084665, 1565084665);
INSERT INTO `dmp_device_task` VALUES (32, 'asdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\",\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\",\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\",\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\",\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\",\"2\"],\"condition\":\"lt\"}}', 1565020800, 1565107199, 1, 1, 1565084665, 1565084665);
INSERT INTO `dmp_device_task` VALUES (33, 'sdafasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"lt\"}}', 1565107200, 1565193599, 1, 1, 1565084665, 1565084665);
INSERT INTO `dmp_device_task` VALUES (34, 'sdafasdf', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\",\"今日头条\"],\"condition\":\"in\"},\"main_game_name\":{\"column\":\"main_game_name\",\"name\":\"主游戏\",\"type\":\"string\",\"value\":[\"贪玩\",\"贪玩\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"1\",\"1\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"43\",\"43\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\",\"2\"],\"condition\":\"lt\"}}', 1565107200, 1565193599, 1, 1, 1565084665, 1565084665);
INSERT INTO `dmp_device_task` VALUES (35, 'test', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"今日头条\",\"百度信息流\",\"爱奇艺\"],\"condition\":\"in\"},\"agent_leader\":{\"column\":\"agent_leader\",\"name\":\"负责人\",\"type\":\"string\",\"value\":[\"步温慧\",\"邱娇娃\",\"区婉欣\",\"李杰\"],\"condition\":\"in\"},\"agent_group_name\":{\"column\":\"agent_group_name\",\"name\":\"渠道组\",\"type\":\"string\",\"value\":[\"string\"],\"condition\":\"not_in\"},\"platform\":{\"column\":\"platform\",\"name\":\"平台\",\"type\":\"string\",\"value\":[\"TW\"],\"condition\":\"in\"},\"root_game_name\":{\"column\":\"root_game_name\",\"name\":\"根游戏\",\"type\":\"string\",\"value\":[\"贪玩蓝月\",\"一品官老爷\",\"武动九天\",\"龙腾传世\",\"王者修仙\"],\"condition\":\"in\"},\"os\":{\"column\":\"os\",\"name\":\"系统\",\"type\":\"string\",\"value\":[\"安卓\",\"IOS\"],\"condition\":\"in\"}}', '{\"new_reg_device\":{\"column\":\"new_reg_device\",\"name\":\"新注册设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"7\"],\"condition\":\"gt\"},\"game_sum_pay_money\":{\"column\":\"game_sum_pay_money\",\"name\":\"设备付费金额(元)\",\"type\":\"int\",\"value\":[\"100\"],\"condition\":\"gt\"}}', 1564588800, 1565193599, 1, 1, 1565667142, 1565667142);
INSERT INTO `dmp_device_task` VALUES (36, 'sfdfa', '{\"ad_platform_name\":{\"column\":\"ad_platform_name\",\"name\":\"投放媒体\",\"type\":\"string\",\"value\":[\"其他\",\"今日头条\",\"百度信息流\",\"爱奇艺\",\"旭力\"],\"condition\":\"in\"},\"agent_leader\":{\"column\":\"agent_leader\",\"name\":\"负责人\",\"type\":\"string\",\"value\":[\"罗艺\",\"钟幸哲\",\"周程施\"],\"condition\":\"in\"},\"agent_id\":{\"column\":\"agent_id\",\"name\":\"渠道ID\",\"type\":\"int\",\"value\":[\"12313\"],\"condition\":\"in\"},\"platform\":{\"column\":\"platform\",\"name\":\"平台\",\"type\":\"string\",\"value\":[\"TW\"],\"condition\":\"not_in\"}}', '{\"is_reg\":{\"column\":\"is_reg\",\"name\":\"设备是否注册\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"new_pay_device\":{\"column\":\"new_pay_device\",\"name\":\"新付费设备\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"10\",\"25\"],\"condition\":\"between\"},\"game_sum_pay_money\":{\"column\":\"game_sum_pay_money\",\"name\":\"设备付费金额(元)\",\"type\":\"int\",\"value\":[\"100\"],\"condition\":\"gt\"}}', 1564588800, 1565193599, 1, 1, 1565667142, 1565667142);
INSERT INTO `dmp_device_task` VALUES (37, 'dsfs', '{}', '{\"gt2game\":{\"column\":\"gt2game\",\"name\":\"玩过2款以上游戏\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1565625600, 1565711999, 1, 1, 1565667142, 1565667142);
INSERT INTO `dmp_device_task` VALUES (38, 'qweq', '{}', '{\"gt2game\":{\"column\":\"gt2game\",\"name\":\"玩过2款以上游戏\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"retain_device\":{\"column\":\"retain_device\",\"name\":\"设备留存天数\",\"type\":\"int\",\"value\":[\"2\"],\"condition\":\"gt\"},\"last_login_day\":{\"column\":\"last_login_day\",\"name\":\"最后登陆距今(天)\",\"type\":\"int\",\"value\":[\"7\"],\"condition\":\"gt\"},\"game_sum_pay_money\":{\"column\":\"game_sum_pay_money\",\"name\":\"子游戏充值金额(元)\",\"type\":\"int\",\"value\":[\"100\",\"150\"],\"condition\":\"between\"},\"muid_sum_pay_money\":{\"column\":\"muid_sum_pay_money\",\"name\":\"全平台充值金额(元)\",\"type\":\"int\",\"value\":[\"1000\"],\"condition\":\"gt\"}}', 1564588800, 1564761599, 1, 1, 1565680228, 1565680228);
INSERT INTO `dmp_device_task` VALUES (39, 'dfs', '{\"agent_leader\":{\"column\":\"agent_leader\",\"name\":\"负责人\",\"type\":\"string\",\"value\":[\"林紫莹\",\"李胜东\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"123123\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"131321\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"is_reg\":{\"column\":\"is_reg\",\"name\":\"设备是否注册\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"},\"gt2game\":{\"column\":\"gt2game\",\"name\":\"玩过2款以上游戏\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1565625600, 1565711999, 1, 1, 1565680228, 1565680228);
INSERT INTO `dmp_device_task` VALUES (40, '撒打发士大夫', '{\"agent_leader\":{\"column\":\"agent_leader\",\"name\":\"负责人\",\"type\":\"string\",\"value\":[\"林紫莹\",\"李胜东\"],\"condition\":\"in\"},\"campaign_id\":{\"column\":\"campaign_id\",\"name\":\"投放计划ID(1级)\",\"type\":\"int\",\"value\":[\"123123\"],\"condition\":\"in\"},\"adgroup_id\":{\"column\":\"adgroup_id\",\"name\":\"广告组ID(2级)\",\"type\":\"int\",\"value\":[\"131321\"],\"condition\":\"in\"}}', '{\"is_active\":{\"column\":\"is_active\",\"name\":\"设备是否激活\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"},\"is_reg\":{\"column\":\"is_reg\",\"name\":\"设备是否注册\",\"type\":\"bool\",\"value\":[],\"condition\":\"no\"},\"gt2game\":{\"column\":\"gt2game\",\"name\":\"玩过2款以上游戏\",\"type\":\"bool\",\"value\":[],\"condition\":\"yes\"}}', 1566057600, 1566143999, 1, 1, 1565919060, 1565919060);

-- ----------------------------
-- Table structure for dmp_tag
-- ----------------------------
DROP TABLE IF EXISTS `dmp_tag`;
CREATE TABLE `dmp_tag`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `dimension_filter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '维度筛选',
  `behavior_filter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行为筛选',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '日志类型',
  `ip` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `desc` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of log
-- ----------------------------
INSERT INTO `log` VALUES (1, 1, 2130706433, '用户admin登录了系统', 1, 1563787222);
INSERT INTO `log` VALUES (2, 1, 2130706433, '用户admin登录了系统', 1, 1563787224);
INSERT INTO `log` VALUES (3, 1, 2130706433, '用户admin登录了系统', 1, 1563787328);
INSERT INTO `log` VALUES (4, 1, 2130706433, '用户admin登录了系统', 1, 1563788525);
INSERT INTO `log` VALUES (5, 1, 2130706433, '用户admin登录了系统', 1, 1563874771);
INSERT INTO `log` VALUES (6, 1, 2130706433, '用户admin登录了系统', 1, 1563876362);
INSERT INTO `log` VALUES (7, 1, 2130706433, '用户admin登录了系统', 1, 1563934154);
INSERT INTO `log` VALUES (8, 1, 2130706433, '用户admin登录了系统', 1, 1563959224);
INSERT INTO `log` VALUES (9, 1, 2130706433, '用户admin登录了系统', 1, 1563961616);
INSERT INTO `log` VALUES (10, 1, 2130706433, '用户admin登录了系统', 1, 1563962035);
INSERT INTO `log` VALUES (11, 1, 2130706433, '用户admin登录了系统', 1, 1563972171);
INSERT INTO `log` VALUES (12, 1, 2130706433, '用户admin登录了系统', 1, 1564025409);
INSERT INTO `log` VALUES (13, 1, 2130706433, '用户admin登录了系统', 1, 1564041753);
INSERT INTO `log` VALUES (14, 1, 2130706433, '用户admin登录了系统', 1, 1564048185);
INSERT INTO `log` VALUES (15, 1, 2130706433, '用户admin登录了系统', 1, 1564059300);
INSERT INTO `log` VALUES (16, 1, 2130706433, '用户admin登录了系统', 1, 1564111051);
INSERT INTO `log` VALUES (17, 1, 2130706433, '用户admin登录了系统', 1, 1564134700);
INSERT INTO `log` VALUES (18, 1, 2130706433, '用户admin登录了系统', 1, 1564291468);
INSERT INTO `log` VALUES (19, 1, 2130706433, '用户admin登录了系统', 1, 1564365850);
INSERT INTO `log` VALUES (20, 1, 2130706433, '用户admin登录了系统', 1, 1564373407);
INSERT INTO `log` VALUES (21, 1, 2130706433, '用户admin登录了系统', 1, 1564467238);
INSERT INTO `log` VALUES (22, 1, 2130706433, '用户admin登录了系统', 1, 1564477446);
INSERT INTO `log` VALUES (23, 1, 2130706433, '用户admin登录了系统', 1, 1564486045);
INSERT INTO `log` VALUES (24, 1, 2130706433, '用户admin登录了系统', 1, 1564489323);
INSERT INTO `log` VALUES (25, 1, 2130706433, '用户admin登录了系统', 1, 1564540875);
INSERT INTO `log` VALUES (26, 1, 2130706433, '用户admin登录了系统', 1, 1564554282);
INSERT INTO `log` VALUES (27, 1, 2130706433, '用户admin登录了系统', 1, 1564556301);
INSERT INTO `log` VALUES (28, 1, 2130706433, '用户admin登录了系统', 1, 1564556379);
INSERT INTO `log` VALUES (29, 1, 2130706433, '用户admin登录了系统', 1, 1564556409);
INSERT INTO `log` VALUES (30, 1, 2130706433, '用户admin登录了系统', 1, 1564557225);
INSERT INTO `log` VALUES (31, 1, 2130706433, '用户admin登录了系统', 1, 1564564267);
INSERT INTO `log` VALUES (32, 1, 2130706433, '用户admin登录了系统', 1, 1564564303);
INSERT INTO `log` VALUES (33, 1, 2130706433, '用户admin登录了系统', 1, 1564574919);

-- ----------------------------
-- Table structure for platform
-- ----------------------------
DROP TABLE IF EXISTS `platform`;
CREATE TABLE `platform`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台名称',
  `create_time` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '游戏平台表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of platform
-- ----------------------------
INSERT INTO `platform` VALUES (1, '贪玩', 1565162729, 1565162729);

-- ----------------------------
-- Table structure for rank_agent
-- ----------------------------
DROP TABLE IF EXISTS `rank_agent`;
CREATE TABLE `rank_agent`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道id',
  `agent_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '渠道名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `agent_group_id` int(10) NOT NULL COMMENT '渠道组id',
  `agent_group_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '渠道组名称',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级渠道权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_agent_group
-- ----------------------------
DROP TABLE IF EXISTS `rank_agent_group`;
CREATE TABLE `rank_agent_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  `agent_group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道组id',
  `agent_group_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级渠道组权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_agent_leader
-- ----------------------------
DROP TABLE IF EXISTS `rank_agent_leader`;
CREATE TABLE `rank_agent_leader`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `agent_leader` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '渠道负责人',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级渠道负责人权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_agent_platform
-- ----------------------------
DROP TABLE IF EXISTS `rank_agent_platform`;
CREATE TABLE `rank_agent_platform`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级渠道权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_main_game
-- ----------------------------
DROP TABLE IF EXISTS `rank_main_game`;
CREATE TABLE `rank_main_game`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `main_game_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主游戏id',
  `main_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主包游戏名称',
  `root_game_id` int(10) NOT NULL COMMENT '对应的根游戏id',
  `root_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '对应的根游戏名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级主游戏权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_platform
-- ----------------------------
DROP TABLE IF EXISTS `rank_platform`;
CREATE TABLE `rank_platform`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `rank_id` int(10) UNSIGNED NOT NULL COMMENT '不同等级的主键id',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_root_game
-- ----------------------------
DROP TABLE IF EXISTS `rank_root_game`;
CREATE TABLE `rank_root_game`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `root_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '根游戏名称',
  `root_game_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '根游戏id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级根游戏权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rank_route_list
-- ----------------------------
DROP TABLE IF EXISTS `rank_route_list`;
CREATE TABLE `rank_route_list`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级 详细见代码',
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id 比如平台id 部门id 分组id  岗位id等等',
  `route_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路由id列表 \',\'分割',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rank_route_list
-- ----------------------------
INSERT INTO `rank_route_list` VALUES (1, 1, 1, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20');

-- ----------------------------
-- Table structure for rank_route_permission
-- ----------------------------
DROP TABLE IF EXISTS `rank_route_permission`;
CREATE TABLE `rank_route_permission`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `rank_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '不同等级的主键id',
  `route_permission_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '路由权限id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级维度权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色描述',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属用户id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, '测试角色', '仅仅测试', 1, 0, 0);
INSERT INTO `role` VALUES (2, '测试接口2', '不知道不明了不想要为什么我的。。。', 1, 1566200065, 1566200065);
INSERT INTO `role` VALUES (3, '撒旦发生', '阿斯蒂芬撒旦', 1, 1566200940, 1566200940);
INSERT INTO `role` VALUES (4, '从v下', '现在才v', 1, 1566200952, 1566200952);

-- ----------------------------
-- Table structure for role_agent
-- ----------------------------
DROP TABLE IF EXISTS `role_agent`;
CREATE TABLE `role_agent`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `agent_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道id',
  `agent_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '渠道名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  `agent_group_id` int(10) NOT NULL COMMENT '渠道组id',
  `agent_group_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '渠道组名称',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户渠道权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_agent
-- ----------------------------
INSERT INTO `role_agent` VALUES (1, 2, 102561, '神马搜索-起邦D06-安卓', 1566202106, 1566202106, 'TW', 21, '竞价投放', 17, '神马搜索');
INSERT INTO `role_agent` VALUES (2, 2, 102287, '神马搜索-贪玩神马-6-ios', 1566202106, 1566202106, 'TW', 21, '竞价投放', 17, '神马搜索');

-- ----------------------------
-- Table structure for role_agent_group
-- ----------------------------
DROP TABLE IF EXISTS `role_agent_group`;
CREATE TABLE `role_agent_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  `agent_group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '渠道组id',
  `agent_group_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分组名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户渠道组权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_agent_group
-- ----------------------------
INSERT INTO `role_agent_group` VALUES (1, 2, 18, '排期媒体', 22, '媒体自然量', 1566202106, 1566202106, 'TW');
INSERT INTO `role_agent_group` VALUES (2, 2, 18, '排期媒体', 21, '竞价投放', 1566202106, 1566202106, 'TW');
INSERT INTO `role_agent_group` VALUES (3, 2, 18, '排期媒体', 14, 'CPS', 1566202106, 1566202106, 'TW');

-- ----------------------------
-- Table structure for role_agent_leader
-- ----------------------------
DROP TABLE IF EXISTS `role_agent_leader`;
CREATE TABLE `role_agent_leader`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `agent_leader` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '渠道负责人',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户渠道负责人权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_agent_leader
-- ----------------------------
INSERT INTO `role_agent_leader` VALUES (105, 3, '黄立宾', 1566200941, 1566200941, 'TW');
INSERT INTO `role_agent_leader` VALUES (106, 4, '黄楚欣', 1566200952, 1566200952, 'TW');
INSERT INTO `role_agent_leader` VALUES (107, 2, '黎洁雯', 1566202106, 1566202106, 'TW');
INSERT INTO `role_agent_leader` VALUES (108, 2, '黄炜毫', 1566202106, 1566202106, 'TW');

-- ----------------------------
-- Table structure for role_agent_platform
-- ----------------------------
DROP TABLE IF EXISTS `role_agent_platform`;
CREATE TABLE `role_agent_platform`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `ad_platform_id` int(10) NOT NULL COMMENT '广告平台id',
  `ad_platform_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '广告平台名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户渠道权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_agent_platform
-- ----------------------------
INSERT INTO `role_agent_platform` VALUES (21, 3, 20, '预装', 1566200941, 1566200941, 'TW');
INSERT INTO `role_agent_platform` VALUES (22, 4, 19, '快手', 1566200953, 1566200953, 'TW');
INSERT INTO `role_agent_platform` VALUES (23, 2, 24, '技术测试', 1566202106, 1566202106, 'TW');
INSERT INTO `role_agent_platform` VALUES (24, 2, 26, '短信', 1566202106, 1566202106, 'TW');

-- ----------------------------
-- Table structure for role_main_game
-- ----------------------------
DROP TABLE IF EXISTS `role_main_game`;
CREATE TABLE `role_main_game`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `main_game_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主游戏id',
  `main_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主包游戏名称',
  `root_game_id` int(10) NOT NULL COMMENT '对应的根游戏id',
  `root_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '对应的根游戏名称',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户主游戏权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role_platform
-- ----------------------------
DROP TABLE IF EXISTS `role_platform`;
CREATE TABLE `role_platform`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `role_id` int(10) UNSIGNED NOT NULL COMMENT '角色id',
  `platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_platform
-- ----------------------------
INSERT INTO `role_platform` VALUES (2, 3, 'TW');
INSERT INTO `role_platform` VALUES (3, 4, 'TW');
INSERT INTO `role_platform` VALUES (4, 2, 'TW');

-- ----------------------------
-- Table structure for role_root_game
-- ----------------------------
DROP TABLE IF EXISTS `role_root_game`;
CREATE TABLE `role_root_game`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `root_game_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '根游戏名称',
  `root_game_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '根游戏id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '平台标识符',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 125 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户根游戏权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_root_game
-- ----------------------------
INSERT INTO `role_root_game` VALUES (120, 3, '大天使2', 169, 1566200940, 1566200940, 'TW');
INSERT INTO `role_root_game` VALUES (121, 4, '红月传说2', 165, 1566200952, 1566200952, 'TW');
INSERT INTO `role_root_game` VALUES (122, 2, '古云传奇H5', 173, 1566202106, 1566202106, 'TW');
INSERT INTO `role_root_game` VALUES (123, 2, '大天使2', 169, 1566202106, 1566202106, 'TW');
INSERT INTO `role_root_game` VALUES (124, 2, '神戒', 167, 1566202106, 1566202106, 'TW');

-- ----------------------------
-- Table structure for route
-- ----------------------------
DROP TABLE IF EXISTS `route`;
CREATE TABLE `route`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '权限id',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '权限标题',
  `uri` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '权限uri',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '该记录是否有效1：有效、0：无效',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级权限ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of route
-- ----------------------------
INSERT INTO `route` VALUES (1, '首页', '/home', 1, 0);
INSERT INTO `route` VALUES (2, '数据地图', '/home/<USER>', 1, 1);
INSERT INTO `route` VALUES (3, '数据看版', '/home/<USER>', 1, 1);
INSERT INTO `route` VALUES (4, '投放数据分析', '/market', 1, 0);
INSERT INTO `route` VALUES (5, '投放总览', '/market/overview', 1, 4);
INSERT INTO `route` VALUES (6, '留存情况', '/market/retain', 1, 4);
INSERT INTO `route` VALUES (7, '付费情况', '/market/payment', 1, 4);
INSERT INTO `route` VALUES (8, '运营数据分析', '/operation', 1, 0);
INSERT INTO `route` VALUES (9, '运营总览', '/operation/overview', 1, 8);
INSERT INTO `route` VALUES (10, '留存情况', '/operation/retain', 1, 8);
INSERT INTO `route` VALUES (11, '付费情况', '/operation/payment', 1, 8);
INSERT INTO `route` VALUES (12, '权限管理', '/permission', 1, 0);
INSERT INTO `route` VALUES (13, '成员管理', '/permission/member', 1, 12);
INSERT INTO `route` VALUES (14, '角色管理', '/permission/role', 1, 12);
INSERT INTO `route` VALUES (15, 'DMP工具', '/dmp', 1, 0);
INSERT INTO `route` VALUES (16, '设备导出', '/dmp/device', 1, 15);
INSERT INTO `route` VALUES (17, '个性化管理', '/individuation', 1, 0);
INSERT INTO `route` VALUES (18, '看板展示', '/individuation/dashboard', 1, 17);
INSERT INTO `route` VALUES (19, 'KPI导入', '/individuation/kpi', 1, 17);
INSERT INTO `route` VALUES (20, '用户反馈', '/individuation/feedback', 1, 17);

-- ----------------------------
-- Table structure for route_permission
-- ----------------------------
DROP TABLE IF EXISTS `route_permission`;
CREATE TABLE `route_permission`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `route_id` int(10) UNSIGNED NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权限元名称',
  `cate` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类权限型 1=维度筛选 2=维度 3=指标 后续可能增加 以代码为准',
  `ext` json NOT NULL COMMENT '各个不同类型权限的扩展字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '指标表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of route_permission
-- ----------------------------
INSERT INTO `route_permission` VALUES (1, 5, '平台', 1, '{\"type\": \"游戏\", \"column\": \"platform\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (2, 5, '根游戏', 1, '{\"type\": \"游戏\", \"column\": \"root_game_name\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (3, 5, '主游戏', 1, '{\"type\": \"游戏\", \"column\": \"main_game_name\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (4, 5, '子游戏', 1, '{\"type\": \"游戏\", \"column\": \"game_name\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (5, 5, '系统', 1, '{\"type\": \"游戏\", \"column\": \"os\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (6, 5, '投放媒体', 1, '{\"type\": \"媒体\", \"column\": \"ad_platform_name\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (7, 5, '投放计划ID', 1, '{\"type\": \"媒体\", \"column\": \"campaign_id\", \"condition\": \"in\", \"column_type\": \"int\"}');
INSERT INTO `route_permission` VALUES (8, 5, '广告组ID', 1, '{\"type\": \"媒体\", \"column\": \"adgroup_id\", \"condition\": \"in\", \"column_type\": \"int\"}');
INSERT INTO `route_permission` VALUES (9, 5, '创意ID', 1, '{\"type\": \"媒体\", \"column\": \"adcre_id\", \"condition\": \"in\", \"column_type\": \"int\"}');
INSERT INTO `route_permission` VALUES (10, 5, '负责人', 1, '{\"type\": \"渠道\", \"column\": \"agent_leader\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (11, 5, '渠道组', 1, '{\"type\": \"渠道\", \"column\": \"agent_group_name\", \"condition\": \"in\", \"column_type\": \"string\"}');
INSERT INTO `route_permission` VALUES (12, 5, '渠道ID', 1, '{\"type\": \"渠道\", \"column\": \"agent_id\", \"condition\": \"in\", \"column_type\": \"int\"}');
INSERT INTO `route_permission` VALUES (13, 5, '广告位ID', 1, '{\"type\": \"渠道\", \"column\": \"site_id\", \"condition\": \"in\", \"column_type\": \"int\"}');
INSERT INTO `route_permission` VALUES (14, 5, '投放计划ID(1级)', 2, '{\"type\": \"平台侧\", \"column\": \"campaign_id\"}');
INSERT INTO `route_permission` VALUES (15, 5, '广告组ID(2级)', 2, '{\"type\": \"平台侧\", \"column\": \"adgroup_id\"}');
INSERT INTO `route_permission` VALUES (16, 5, '创意ID(3级)', 2, '{\"type\": \"平台侧\", \"column\": \"adcre_id\"}');
INSERT INTO `route_permission` VALUES (17, 5, '负责人', 2, '{\"type\": \"市场侧\", \"column\": \"agent_leader\"}');
INSERT INTO `route_permission` VALUES (18, 5, '渠道组', 2, '{\"type\": \"市场侧\", \"column\": \"agent_group_id\"}');
INSERT INTO `route_permission` VALUES (19, 5, '渠道', 2, '{\"type\": \"市场侧\", \"column\": \"agent_id\"}');
INSERT INTO `route_permission` VALUES (20, 5, '广告位', 2, '{\"type\": \"市场侧\", \"column\": \"site_id\"}');
INSERT INTO `route_permission` VALUES (21, 5, '平台', 2, '{\"type\": \"运营侧\", \"column\": \"platform\"}');
INSERT INTO `route_permission` VALUES (22, 5, '根游戏', 2, '{\"type\": \"运营侧\", \"column\": \"root_game_id\"}');
INSERT INTO `route_permission` VALUES (23, 5, '主游戏', 2, '{\"type\": \"运营侧\", \"column\": \"main_game_id\"}');
INSERT INTO `route_permission` VALUES (24, 5, '子游戏', 2, '{\"type\": \"运营侧\", \"column\": \"game_id\"}');
INSERT INTO `route_permission` VALUES (25, 5, '系统', 2, '{\"type\": \"运营侧\", \"column\": \"os\"}');
INSERT INTO `route_permission` VALUES (26, 5, '设备展示', 3, '{\"type\": \"跟踪转化链\", \"column\": \"device_show_num\"}');
INSERT INTO `route_permission` VALUES (27, 5, '设备点击', 3, '{\"type\": \"跟踪转化链\", \"column\": \"device_click_num\"}');
INSERT INTO `route_permission` VALUES (28, 5, '设备激活', 3, '{\"type\": \"跟踪转化链\", \"column\": \"device_activate_num\"}');
INSERT INTO `route_permission` VALUES (29, 5, '注册设备', 3, '{\"type\": \"跟踪转化链\", \"column\": \"reg_device_num\"}');
INSERT INTO `route_permission` VALUES (30, 5, '注册账号', 3, '{\"type\": \"跟踪转化链\", \"column\": \"reg_user_num\"}');
INSERT INTO `route_permission` VALUES (31, 5, '创建角色', 3, '{\"type\": \"跟踪转化链\", \"column\": \"create_role_num\"}');
INSERT INTO `route_permission` VALUES (32, 5, '设备点击率', 3, '{\"type\": \"跟踪转化链\", \"column\": \"device_click_percent\"}');
INSERT INTO `route_permission` VALUES (33, 5, '设备激活率', 3, '{\"type\": \"跟踪转化链\", \"column\": \"device_activate_percent\"}');
INSERT INTO `route_permission` VALUES (34, 5, '设备注册率', 3, '{\"type\": \"跟踪转化链\", \"column\": \"reg_device_percent\"}');
INSERT INTO `route_permission` VALUES (35, 5, '账号注册率', 3, '{\"type\": \"跟踪转化链\", \"column\": \"reg_user_percent\"}');
INSERT INTO `route_permission` VALUES (36, 5, '总投放消耗', 3, '{\"type\": \"跟踪转化链\", \"column\": \"advert_cost_money\"}');
INSERT INTO `route_permission` VALUES (37, 5, '账号单价', 3, '{\"type\": \"跟踪转化链\", \"column\": \"per_user_cost\"}');
INSERT INTO `route_permission` VALUES (38, 5, '游戏老设备（展示）', 3, '{\"type\": \"跟踪转化链\", \"column\": \"game_old_device_show_num\"}');
INSERT INTO `route_permission` VALUES (39, 5, '游戏老设备（激活）', 3, '{\"type\": \"跟踪转化链\", \"column\": \"game_old_device_click_num\"}');
INSERT INTO `route_permission` VALUES (40, 5, '次留数', 3, '{\"type\": \"留存\", \"column\": \"user_day_2_stay_num\"}');
INSERT INTO `route_permission` VALUES (41, 5, '次留率', 3, '{\"type\": \"留存\", \"column\": \"user_day_2_stay_percent\"}');
INSERT INTO `route_permission` VALUES (42, 5, '次留成本', 3, '{\"type\": \"留存\", \"column\": \"per_user_day_2_stay_cost\"}');
INSERT INTO `route_permission` VALUES (43, 5, '首日付费人数', 3, '{\"type\": \"付费\", \"column\": \"day_1_pay_user_num\"}');
INSERT INTO `route_permission` VALUES (44, 5, '首日付费率', 3, '{\"type\": \"付费\", \"column\": \"day_1_pay_user_percent\"}');
INSERT INTO `route_permission` VALUES (45, 5, '首日付费额度', 3, '{\"type\": \"付费\", \"column\": \"day_1_pay_moneyd\"}');
INSERT INTO `route_permission` VALUES (46, 5, '首日LTV', 3, '{\"type\": \"付费\", \"column\": \"day_1_ltv\"}');
INSERT INTO `route_permission` VALUES (47, 5, '首日回本率', 3, '{\"type\": \"付费\", \"column\": \"day_1_roi\"}');
INSERT INTO `route_permission` VALUES (48, 5, '首日付费成本', 3, '{\"type\": \"付费\", \"column\": \"day_1_pay_user_cost\"}');
INSERT INTO `route_permission` VALUES (49, 3, '广告消耗', 4, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (50, 3, '游戏收入', 4, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (51, 3, '本月/日游戏收入', 5, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (52, 3, '本月/日新增账号', 5, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (53, 3, '本月/日广告消耗', 5, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (54, 3, '本月/日新增账号收入', 5, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (55, 3, '游戏维度明细', 6, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (56, 3, '投放维度明细', 6, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (57, 3, '消耗', 7, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (58, 3, 'ARPU', 7, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (59, 3, '新增账号单价', 7, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (60, 3, '新增账号质量', 7, '{\"column\": \"\"}');
INSERT INTO `route_permission` VALUES (61, 3, '各渠道组分布', 7, '{\"column\": \"\"}');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `account` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `mobile` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0:=被禁用  1=正常',
  `create_time` int(11) UNSIGNED NOT NULL,
  `update_time` int(11) UNSIGNED NOT NULL,
  `last_login_time` int(11) UNSIGNED NOT NULL COMMENT '最后登陆时间',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 4 COMMENT '管理等级，共5级 越低权限越高， 0表示超管',
  `route_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单栏权限id',
  `role_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `platform_id` int(10) UNSIGNED NOT NULL COMMENT '平台id',
  `department_id` int(10) UNSIGNED NOT NULL COMMENT '部门id',
  `department_group_id` int(10) UNSIGNED NOT NULL COMMENT '部门分组id',
  `department_group_position_id` int(10) UNSIGNED NOT NULL COMMENT '部分分组岗位id',
  `creator` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `editor` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编辑者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Compact;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2y$10$eumNSQ3kDlzpnOEd2M09VermLI9jdHbDRZmwM2NeBBxvHjXPOipt2', '超管', '', '18023496333', 1, 1540869597, 1565661952, 1566102070, 0, '', 0, 0, 0, 0, 0, 'admin', 'admin');
INSERT INTO `user` VALUES (2, 'daihuanqi', '$2y$10$kSwSlfEsd/fQMnsoPy.NZOJoYISaohNT.6ky1a1zTG/hwxYcFLoMO', 'daihuanqi', '/upload/images/cb33031e97634162635673bbcfda6454.jpeg', '13276545645', 1, 1565936391, 1566179957, 1566286289, 0, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20', 0, 1, 1, 1, 2, '超管', '超管');
INSERT INTO `user` VALUES (3, 'test1', '$2y$10$bE3NlgBnzrHpR8TJHIErV.BaJTZHNCYxYXVggSlE7iYJUJ6BIfpQu', 'test1', '', '13276543343', 1, 1565942368, 1565942368, 0, 1, '1,2,3,4,5,6,7,8,9,10,11,12,13,14', 0, 1, 1, 1, 2, '超管', '');
INSERT INTO `user` VALUES (5, 'test2', '$2y$10$gqICWKQ7Iv7Iw7XISSVnQOce1wcRYEzmWBuGgv2z5QB2twMFlGjt.', 'test2', '', '15427655766', 1, 1565942559, 1565942559, 0, 1, '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16', 0, 1, 1, 1, 2, '超管', '');
INSERT INTO `user` VALUES (6, 'test1726', '$2y$10$i5Ix2WsnQPxXhhvtqxv8qO5T.2fY383.VsO9ow01ppOaRbxleIMiu', 'test1726', '', '18023497362', 1, 1565919060, 1565919060, 0, 4, '1,2,3,12,13,14,15,16', 0, 1, 1, 1, 2, '超管', '');
INSERT INTO `user` VALUES (7, 'test1721', '$2y$10$Kl2b9hNCQ5y11oOkjba6R.I0AmftTkX4NEfd2GI6inV0drNAhG7o6', 'test1721', '', '18023497362', 1, 1565919060, 1565919060, 0, 4, '1,2,3,12,13,14,15,16', 0, 1, 1, 1, 2, '超管', '');
INSERT INTO `user` VALUES (8, 'test1722', '$2y$10$s1obHyWZ4GWj74BKCaPBNuyto5k1BQfwco4wL4sR5KZ55Ob3FK3Gm', 'test1722', '', '18023497362', 1, 1565919060, 1565919060, 0, 4, '1,2,3,12,13,14,15,16', 0, 1, 1, 1, 2, '超管', '');
INSERT INTO `user` VALUES (9, 'test1753', '$2y$10$ef1sss5BnM8Fb9UNlZhuPOEBpsiHa5XUjHhcBbBY7Pot6350tkuKi', 'test1753', '', '12345678900', 0, 1565919060, 1566204630, 0, 4, '1,2,3,15,16', 0, 1, 1, 1, 2, '超管', '超管');
INSERT INTO `user` VALUES (10, 'test1158', '$2y$10$VT./jGUnDGwv1dxNvF.zQ.wlAfIxW4cVIOnwFkRE6PaOG3sHAJM2G', 'test1158', '', '18023496555', 0, 1566102070, 1566204625, 0, 4, '1,2,3,8,9,10,11,15,16,17,18,19,20', 0, 1, 1, 1, 2, '超管', '超管');

SET FOREIGN_KEY_CHECKS = 1;
